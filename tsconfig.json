{
  "compilerOptions": {
    "noImplicitAny": false,
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "target": "es5",
    "jsx": "react",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "isolatedModules": false,
    "noEmit": true,
    "baseUrl": ".",
    "paths": {
      "src/*": ["src/*"],
      "doraemon": ["node_modules/@zcy/doraemon"],
    }
  },
  "include": ["src", "types"],
  "exclude": ["node_modules"]
}
