/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-04-26 15:37:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 17:42:03
 * @FilePath: /zcy-announcement-v2-front/mockData/index.js
 * @Description: 
 */
const Mock = require('mockjs');

module.exports = {
  "/announcement/api/small/exportAnnouncementInfo": function () {
    return {
      "success": true,
      "result": {
        "taskUuid": '1234567890',
      }
    }
  },
  "/announcement/api/small/pollingAnnouncementInfo": function () {
    return {
      "success": true,
      "result": {
        "exportFileUrl": 'http://www.baidu.com',
      }
    }
  },
  "/announcement/api/small/showExportButton": function () {
    return {
      "success": true,
      "result": true,
    }
  },
  "/announcement/api/small/listSelfAnnouncement": function () {
    return {
      "success": true,
      "result": {
        "data": [],
      }
    }
  },
  "/user/privileges/isResourcePermit": function () {
    return {
      "success": true,
      "result": {
        "isPermit": true,
        "accessType": 2,
        "error": "user not authorization",
        "errorType": 403
      },
      "error": null
    }
  },
  "/api/test": function () {
    const data = Mock.mock({
      'list|1-10': [{
        name: Mock.Random.cname(),
        'age|18-60': 1,
        email: Mock.mock('@EMAIL()'),
      }]
    })
    return data.list
  },
  "/announcement/api/config/form/listYear": function(){
    return {
      "success": true,
      "result": [2023,2022,2021,2020],
      "error": null
    } 
  },
  "/api/biz-bidding/doctor-manhattan/project-manage/sync-metadata": function(){
    return true
  },
  "/announcement/sensitive/detailAnnouncementSensitive": function(){
    return {
      "success": true,
      "result": 
        {
          "attachmentResultList": [
            {
              "fileName": "111.pdf",
              "fileUrl": "http://www.baidu.com",
              "fileSensitiveStatus": 3,
              "sensitiveWords": ""
            },
            {
              "fileName": "2222.pdf",
              "fileUrl": "http://www.baidu.com",
              "fileSensitiveStatus": 2,
              "sensitiveWords": "打人，大炮"
            }
          ],
          "id": -41963246,
          "announcementId": -90744011,
          "announcementTitle": "voluptate sed ullamco in est",
          "statusName": "通过",
          "sensitiveWords": "杀人,放火",
          "announcementStatusName": "consectetur enim eiusmod cillum",
          "announcementStatusCode": "do culpa",
          "announcementSerialNum": "eu",
          "announcementReleasedAt": "1982-06-08 12:51:43",
          "annBigType": 29323697,
          "exitSensitiveTitleAndContent": true,
        },
      "error": "qui dolore reprehenderit"
    }
  },
  "/announcement/api/nonGovernmentProcurement": function(){
    return {
      "success": true,
      "result": {
        "needPopUpWindow": true,
        "whetherToSettleIn": false,
      }
    }
  },
  "/announcement/api/small/barButtons": function(){
    return {
      "success": true,
      "result": {
        "canCreate": true,
        "canGenerateAnnualData": true,
        "canDisplayAnnualSearch": true,
        "canDisplayOrgNameSearch": true,
        "isResponsibleBudgetUnit": true,
      }
    }
  },
    // 书签列表
    "/api/opPlatform/prodOperation/announcement/bookmark/list": function(){
      return {
        "success": false,
        "result": {
          "pageNum": 1,
          "pageSize": 20,
          "pages": 10,
          "total": 200,
          "data": [
            {
              "id": -71402666,
              "code": "projectOverview",
              "name": "项目概况",
              "type": "输入框",
              "stdlibId": 61169403,
              "stdlibCode": "eu",
              "stdlibName": "dolor eu officia laboris",
              "status": "ut",
              "createName": "捷捷",
              "createTime": "2011-10-20 17:59:06",
              "modifyName": "quis Duis et velit",
              "modifyTime": "1983-09-15 15:42:35",
              "buttonList": [
                {
                  "code": "edit",
                  "name": "编辑"
                },
                {
                  "code": "disable",
                  "name": "停用"
                },
                {
                  "code": "view",
                  "name": "查看"
                },
              ],
            },
            {
              "id": 43730412,
              "code": "bidTime",
              "name": "投标日期",
              "type": "日期时间",
              "stdlibId": -10260636,
              "stdlibCode": "dolor deserunt Lorem quis voluptate",
              "stdlibName": "sunt culpa enim",
              "status": "Duis proident eiusmod enim eu",
              "createName": "哈哈",
              "createTime": "1974-06-27 21:37:08",
              "modifyName": "consectetur velit do",
              "modifyTime": "1971-12-19 03:37:38",
              "buttonList": [
                {
                  "code": "edit",
                  "name": "编辑"
                },
                {
                  "code": "disable",
                  "name": "停用"
                },
                {
                  "code": "view",
                  "name": "查看"
                },
                {
                  "code": "addChild",
                  "name": "添加子书签"
                },
                {
                  "code": "template",
                  "name": "模板引用情况"
                },
              ],
            },
            {
              "id": -35000569,
              "code": "sunt culpa",
              "name": "sed nulla",
              "type": "eu laboris esse",
              "stdlibId": 2425607,
              "stdlibCode": "Ut aute",
              "stdlibName": "ad culpa cupidatat voluptate sed",
              "status": "aliqua nisi",
              "createName": "enim occaecat quis dolor",
              "createTime": "1995-05-20 16:28:31",
              "modifyName": "consectetur et",
              "modifyTime": "2010-10-07 04:31:11"
            }
          ]
        },
        "code": "dolor consectetur",
        "message": "minim id"
      }
    },
    "/api/opPlatform/prodOperation/announcement/bookmark/save": function(){
      return {
        "success": true,
        "result": true,
        "message": "success"
      }
    },
    // 字典
    "/api/opPlatform/prodOperation/announcement/dict": function(){
      return {
        "success": true,
        "result": {
            "componentType": [
                {
                    "value": 1,
                    "label": "下拉列表"
                },
                {
                    "value": 2,
                    "label": "日期选择控件"
                },
                {
                    "value": 5,
                    "label": "数值输入框"
                },
                {
                    "value": 6,
                    "label": "单行文本输入框"
                },
                {
                    "value": 7,
                    "label": "多行文本输入框"
                },
                {
                    "value": 10,
                    "label": "Table表格面板输入"
                },
                {
                    "value": 11,
                    "label": "间隔文本框"
                },
                {
                    "value": 13,
                    "label": "时间选择控件"
                },
                {
                    "value": 14,
                    "label": "日期时间选择控件"
                },
                {
                    "value": 25,
                    "label": "下拉单选列表（支持自定义）"
                },
                {
                    "value": 999,
                    "label": "自定义控件"
                }
            ],
            "bizType": [
                {
                    "value": 1,
                    "label": "政采"
                }
            ],
            "stdLibTag": [
                {
                    "value": "announcementContent",
                    "label": "正文渲染"
                },
                {
                    "value": "manualAnnouncementForm",
                    "label": "表单渲染"
                },
                {
                    "value": "push",
                    "label": "推送"
                },
                {
                    "value": "customize",
                    "label": "自定义"
                }
            ],
            "stdLibStatus": [
                {
                    "value": 1,
                    "label": "启用"
                },
                {
                    "value": 0,
                    "label": "停用"
                }
            ]
        },
        "code": null,
        "message": null,
        "error": null
      }
    },
    // 请求书签
    "/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark": function(){
      return {
        "success": true,
        "result": {
          "pageNo": 1,
          "pageSize": 10,
          "pages": 1,
          "total": 20,
          "data": [
            {
              "code": "aliquip",
              "name": "eu",
              "type": "输入框"
            },
            {
              "code": "dolor",
              "name": "aliqua sint et",
              "type": "输入框"
            },
            {
              "code": "minim laboris",
              "name": "ea",
              "type": "输入框"
            }
          ]
        },
        "code": "mollit laborum",
        "message": "dolore in laborum nostrud eiusmod"
      }
    },
    "/api/opPlatform/prodOperation/announcement/stdlib/showUsedInfo": function() {
      return {
        "result": [
          {
            "bookmark": "项目名称",
            "templates": [
              {
                "templateId": 28868542,
                "templateCode": "quis esse",
                "defaultTemplateFileId": 54600497,
                "templateName": "do velit exercitation",
                "templateLevelName": "Lorem ut ex aliqua",
                "templateFileVersion": "laborum cupidatat minim id"
              }
            ]
          },
          {
            "bookmark": "项目名称",
            "templates": [
              {
                "templateId": 111111,
                "templateCode": "222222",
                "defaultTemplateFileId": 54600497,
                "templateName": "do velit exercitation",
                "templateLevelName": "Lorem ut ex aliqua",
                "templateFileVersion": "laborum cupidatat minim id"
              }
            ]
          },
        ]
      }
    },
    
  }
  
