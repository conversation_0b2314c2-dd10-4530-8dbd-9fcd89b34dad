import { request, message } from 'doraemon';

const initialConfig = {
  onError: (res) => {
    if (res.message) return message.error(res.message);
    if (res.error) return message.error(res.error);
  },
};


/**
 * @description:
 * @param {string} url 请求url
 * @param {object} axOption axios配置
 * @param {object} usrOption 个性化配置
 * @return {Promise}
 */
export default (url, axOption = {}, usrOption = initialConfig) => {
  const headers = {};

  const option = {
    ...axOption,
    ...usrOption,
    headers,
  };

  return new Promise((resolve, reject) => {
    request(url, option)
      .then((res) => {
        const { success } = res;
        if (!success) {
          typeof option.onError === 'function' && option.onError(res);
        }
        resolve(res);
      })
      .catch(reject);
  });
};
