/**
 * 代码顺序执行
 * 避免过多回调
 */
export default class Sequence {
  constructor() {
    this.middlewares = [];
  }

  use(middleware) {
    if (typeof middleware !== 'function') {
      throw new TypeError('执行必须为方法');
    }
    this.middlewares.push(middleware);
  }

  exec(next) {
    const middlewares = this.middlewares;
    let index = -1;
    return dispatch(0);
    function dispatch(i, options) {
      if (i <= index) {
        return Promise.reject(new Error('next方法执行了多次'));
      }
      index = i;
      let fn = middlewares[i];
      if (i === middlewares.length) {
        fn = next;
      }
      if (!fn) {
        return Promise.resolve();
      }
      try {
        return Promise.resolve(fn(opt => dispatch(i + 1, opt), options));
      } catch (err) {
        return Promise.reject(err);
      }
    }
  }
}
