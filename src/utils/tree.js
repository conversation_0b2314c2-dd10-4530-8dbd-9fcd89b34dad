/*
 * @Author: your name
 * @Date: 2021-07-19 10:43:22
 * @LastEditTime: 2021-07-19 11:20:44
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /zcy-announcement-v2-front/src/utils/tree.js
 */
// 处理树形数据，使其符合组件要求
export function resolveTree(tree) {
  if (!tree) return [];

  const newTree = [];
  tree.forEach((item) => {
    newTree.push({
      title: item.text || item.name,
      value: item.code,
      children:
        item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
          ? resolveTree(item.children)
          : undefined,
      disabled: !item.leaf,
    });
  });
  return newTree;
}

// 不包含禁用处理
export function changeSourceTree(tree) {
  if (!tree) return [];

  const newTree = [];
  tree.forEach((item = {}) => {
    newTree.push({
      title: item.text || item.name,
      value: item.code,
      children:
        item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
          ? changeSourceTree(item.children)
          : undefined,
    });
  });
  return newTree;
}

export const formatTreeData = (data, { labelKey = 'name', valueKey = 'code' }) => {
  return data.map((i) => {
    i.value = String(i[valueKey]);
    i.label = i[labelKey];
    i.title = i[labelKey];
    if (i.children) {
      i.children = formatTreeData(i.children, { labelKey, valueKey });
    }
    return i;
  });
};


export const findItemByTree = (tree, val) => {
  const temp = [...tree];
  while (temp.length) {
    const item = temp.shift();
    if (item.value === `${val}`) return item;
    if (item?.children?.length) {
      temp.push(...item.children); 
    }
  }
  return null;
};
