import React from 'react';
import {
  Alert,
  Modal,
} from 'doraemon';
// import { listSensitiveWords } from 'src/routes/Flow/services';
import request from './request';

const listSensitiveWordsMessage = data => request('/announcement/api/listSensitiveWordsMessage', {
  method: 'POST',
  data,
});


const SensitiveEditorStyle = `
.blockWord {
  background: #ffefe6;
  border: 1px solid #ffa27a;
  -webkit-user-modify: read-only;
}

.remindWord {
  border: 1px solid #ffc88a;
  background-color: #fff6ea;
  -webkit-user-modify: read-only;
}
`;

const getSensitiveWords = ({
  announcementType, content, district,
}) => {
  return listSensitiveWordsMessage({
    announcementType, content, district,
  }).then((res) => {
    if (!res.success) return;
    const blockWords = res?.result?.blockSensitiveWordDtoList ?? [];
    const remindWords = res?.result?.remindSensitiveWordDtoList ?? [];
    const hasBlock = !!blockWords?.length;
    const hasRemind = !!remindWords?.length;
    const remindMessageHtml = res?.result?.remindMessage ?? '';
    const blockMessageHtml = res?.result?.blockMessage ?? '';
    
    return {
      hasBlock,
      hasRemind,
      blockWords,
      remindWords,
      remindMessageHtml,
      blockMessageHtml,
    };
  });
}; 

const attachSensitiveWordsTag = (blockWords, remindWords, content = '') => {
  const changeWords = [
    // TODO 使用正则替换
    ...(blockWords ?? []).map((item) => {
      const strValue = `<span class="blockWord">${item.emitWord}</span>`;
      return ({
        key: item.emitWord,
        value: strValue,
      });
    }),
    ...(remindWords ?? []).map((item) => {
      const strValue = `<span class="remindWord">${item.emitWord}</span>`;
      return ({
        key: item.emitWord,
        value: strValue,
      });
    }),
  ];

  const contentWithSensitiveTag = changeWords.reduce((res, { key, value }) => {
    res = res.replace(new RegExp(key, 'g'), value);
    return res;
  }, clearSensitiveTag(content));

  return {
    changeWords,
    contentWithSensitiveTag,

  };
};

const clearSensitiveTag = (content) => {
  return content
    .replace(/<span class="blockWord">(.*)<\/span>/gi, '$1')
    .replace(/<span class="remindWord">(.*)<\/span>/gi, '$1');
};

// 将标红的字符串替换成未标红的样子
const backSensitiveWords = (content = '', words = []) => {
  return words.reduce((res, { key, value }) => {
    res = res.replace(new RegExp(value, 'g'), key);
    return res;
  }, content);
};

const renderAlert = (hasBlock, hasRemind) => {
  return (
    <React.Fragment>
      {(hasRemind) && (
        <Alert
          type="warning"
          showIcon
          iconType="exclamation-circle-o"
          message="该公告含有提示级敏感词，请检查公告标题、正文、附件名称，建议返回并移除相关敏感词后再次生成公告！"
          style={{ marginBottom: 10 }}
        />
      )}
      {(hasBlock) && (
        <Alert
          type="error"
          showIcon
          iconType="exclamation-circle-o"
          message="该公告含有禁止级敏感词，禁止提交公告！请检查公告标题、正文、附件名称，移除相关敏感词后再次生成公告！"
          style={{ marginBottom: 10 }}
        />
      )}
    </React.Fragment>
  );
};

const showSensitiveMsg = (hasBlock, hasRemind, { blockMessageHtml, remindMessageHtml }) => {
  if (hasBlock) {
    Modal.error({
      title: '提交失败',
      content: (
        /* eslint-disable-next-line react/no-danger */
        <span dangerouslySetInnerHTML={{ __html: blockMessageHtml || '' }} />
      ),
    });
    return;
  }

  if (hasRemind) {
    return new Promise((resolve) => {
      Modal.warning({
        title: '警告',
        content: (
        /* eslint-disable-next-line react/no-danger */
          <div dangerouslySetInnerHTML={{ __html: remindMessageHtml || '' }} />
        ),
        okText: '继续提交',
        okCancel: true,
        onOk: () => resolve(true),
        onCancel: () => resolve(false),
      });
    });
  }
};


export { 
  renderAlert,
  getSensitiveWords, 
  backSensitiveWords, 
  attachSensitiveWordsTag, 
  showSensitiveMsg, 
  clearSensitiveTag,
  SensitiveEditorStyle,
};
