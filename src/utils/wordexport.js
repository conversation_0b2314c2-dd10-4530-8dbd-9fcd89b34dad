import FileSaver from 'file-saver';

/**
 * html标签转word导出
 * @description
 * 保存的doc格式参考：https://github.com/markswindoll/jQuery-Word-Export/blob/master/jquery.wordexport.js ，依赖FileSaver.js
 * @param {*} options
 */
class WordExport {
  save = (options = {
    fileName: 'download',
    content: 'hello world',
    styles: '',
    href: location.href,
  }) => {
    // 注意：word文档标记头部不能包含制表符
    const doc = `Mime-Version: 1.0
Content-Base: ${options.href}
Content-Type: Multipart/related; boundary="NEXT.ITEM-BOUNDARY";type="text/html"

--NEXT.ITEM-BOUNDARY
Content-Type: text/html; charset="utf-8"
Content-Location: ${options.href}

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style></style>
</head>
<body>
  ${options.content}
</body></html>
--NEXT.ITEM-BOUNDARY--
`;
    const blob = new Blob([doc], {
      type: 'application/msword;charset=utf-8',
    });
    FileSaver(blob, `${options.fileName}.doc`);
  }
}

export default WordExport;

