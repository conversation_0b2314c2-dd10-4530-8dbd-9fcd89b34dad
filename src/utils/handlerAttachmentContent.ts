import { getDownLoadUrl } from 'src/routes/DynamicAnnouncementEdit/services/common';

const bizCode = '1014';

const getFileSize = (size) => {
  let fileSize = '';
  if (size) {
    size = parseInt(size, 10);
    fileSize = '0.1 KB';
    const fileSizeK = (size / 1024).toFixed(1);
    if (+fileSizeK.split('.')[1] > 0) {
      fileSize = `${fileSizeK} KB`;
    }
    if (+fileSizeK.split('.')[0] > 1000) {
      const fileSizeM = (size / 1024 / 1024).toFixed(1);
      fileSize = `${fileSizeM} M`;
    }
  }
  return fileSize;
};

const handlerAttachmentContent = (
  content, 
  files = [], 
  isReformationEnglishAnnouncement = false,
  isShowReservedQuotaAttachment = false
) => {
  if (!files?.length) return Promise.resolve(content);
  const getUrlTasks = files.map((fl: any) => getDownLoadUrl({
    fileId: fl.fileId,
    bizCode,
  }).then((res) => {
    return res.result;
  }));

  return Promise.all(getUrlTasks).then((urls = []) => {
    // 普通类型附件
    const liHtml = urls.reduce((res, url, index) => {
      const file: any = files[index];
      if (file?.fileCode !== 'percentIrregularityStateAttachments') {
        const li = `
        <li>
          <p style="
            display:inline-block;
            text-indent: 0;
            margin: 0;
          ">
            <a style="
              color: #0000EE;
            " href="${url}">${file.name}</a>
            <span style="margin-left:40px;">(${getFileSize(file.size)})</span>
          </p>
        </li>
        `;
        res += li;
        return res;
      }
      return res;
    }, '');

    const ulHtml = `
      <ul class="fjxx" style="
        text-indent: 0;
        list-style-type: none;
        font-family: MicrosoftYaHei;
        font-size: 16px;
        color: #0000EE;
        letter-spacing: 0;
        line-height: 32px;
        font-weight: 400;
        margin: 0;
        padding: 0;
      ">
        ${liHtml}
      </ul>
    `;

    const PRE_FJXX_HTML = `
      <divider></divider>
      <p style="
        font-size: 16px;
        color: #000000;
        letter-spacing: 0;
        line-height: 40px;
        font-weight: 700;
        margin: 0;
        padding: 0;
        margin-top: 40px;
      ">
        <strong>
          <span>附件信息${isReformationEnglishAnnouncement ? ' / Attachment information ' : ''}:</span>
        </strong>
      </p>
    `;

    // 未达到规定预留份额比例的情况说明
    const attachment1 = `
      <divider></divider>
      <p style="
        font-size: 16px;
        color: #000000;
        letter-spacing: 0;
        line-height: 40px;
        font-weight: 700;
        margin: 0;
        padding: 0;
        margin-top: 8px;
      ">
        <strong>
          <span>未达到规定预留份额比例的情况说明:</span>
        </strong>
      </p>
    `;
    const liHtml1 = urls.reduce((res, url, index) => {
      const file: any = files[index];
      if (file?.fileCode === 'percentIrregularityStateAttachments') {
        const li = `
        <li>
          <p style="
            display:inline-block;
            text-indent: 0;
            margin: 0;
          ">
            <a style="
              color: #0000EE;
            " href="${url}">${file.name}</a>
            <span style="margin-left:40px;">(${getFileSize(file.size)})</span>
          </p>
        </li>
        `;
        res += li;
        return res;
      }
      return res;
    }, '');
    const ulHtml1 = `
      <ul class="fjxx" style="
        text-indent: 0;
        list-style-type: none;
        font-family: MicrosoftYaHei;
        font-size: 16px;
        color: #0000EE;
        letter-spacing: 0;
        line-height: 32px;
        font-weight: 400;
        margin: 0;
        padding: 0;
      ">
        ${liHtml1}
      </ul>
    `;

    let reservedQuotaAttachmentHtml = `
      ${attachment1}
      ${ulHtml1}
    `
    if(!isShowReservedQuotaAttachment) {
      reservedQuotaAttachmentHtml = '';
    }


    return `
      ${content}
      ${PRE_FJXX_HTML}
      ${ulHtml}
      ${reservedQuotaAttachmentHtml}
    `;
  });  
};


export default handlerAttachmentContent;
