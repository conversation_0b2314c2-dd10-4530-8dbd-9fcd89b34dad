import CaOperateModal, { ModalTypeEnum } from '@zcy/zcy-ca-operate-modal-back';

const noop = () => {
  // This is intention
};

/**
 *
 * @param {signContent} 签名摘要
 * @param {doneCallback} 回调函数
 */
export const goCASign = ({ signContent, doneCallback = noop }) => {
  CaOperateModal.show({
    bizCode: 'ANNOUNCEMENT_SIGN_SIGN',
    type: ModalTypeEnum.SIGN_BATCH,
    extParams: {
      signContents: [signContent],
    },
    doneCallback,
  });
};
