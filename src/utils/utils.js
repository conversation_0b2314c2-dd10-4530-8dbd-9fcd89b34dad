/**
 * 工具类函数文件
 */
import { Modal } from 'doraemon';
import { PUBLISH_TYPES } from 'src/constants';

function getRelation(str1, str2) {
  if (str1 === str2) {
    console.warn('Two path are equal!');  // eslint-disable-line
  }
  const arr1 = str1.split('/');
  const arr2 = str2.split('/');
  if (arr2.every((item, index) => item === arr1[index])) {
    return 1;
  } else if (arr1.every((item, index) => item === arr2[index])) {
    return 2;
  }
  return 3;
}

function getRenderArr(routes) {
  let renderArr = [];
  renderArr.push(routes[0]);
  for (let i = 1; i < routes.length; i += 1) {
    let isAdd = false;
    // 是否包含
    isAdd = renderArr.every(item => getRelation(item, routes[i]) === 3);
    // 去重
    renderArr = renderArr.filter(item => getRelation(item, routes[i]) !== 1);
    if (isAdd) {
      renderArr.push(routes[i]);
    }
  }
  return renderArr;
}

/**
 * Get router routing configuration
 * { path:{name,...param}}=>Array<{name,path ...param}>
 * @param {string} path
 * @param {routerData} routerData
 */
export function getRoutes(path, routerData) {
  let routes = Object.keys(routerData).filter(routePath =>
    routePath.indexOf(path) === 0 && routePath !== path);

  routes = routes.map(item => item.replace(path, ''));

  const renderArr = getRenderArr(routes);

  const renderRoutes = renderArr.map((item) => {
    const exact = !routes.some(route => route !== item && getRelation(route, item) === 1);
    return {
      ...routerData[`${path}${item}`],
      key: `${path}${item}`,
      path: `${path}${item}`,
      exact,
    };
  });
  return renderRoutes;
}

// Generate four random hex digits.
function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
}
// Generate a pseudo-GUID by concatenating random hexadecimal.
export function guid() {
  return (`${S4() + S4()}-${S4()}-${S4()}-${S4()}-${S4()}${S4()}${S4()}`);
}

// 无数据时用短线代替
export function fixNull(value) {
  if (value) {
    return value;
  }
  return '-';
}

export const handleReqError = function handleReqError(options) {
  const { type, message, cb, ...rest } = options;
  Modal[`${type}`]({
    title: message || '服务器开小差，请稍后重试～',
    onOk: () => {
      typeof cb === 'function' && cb();
    },
    ...rest,
  });
};
/**
 * 金额格式化
 * @param {number} money 金额
*/
export function moneyFormat(money) {
  if (money === 0 || money < 0) {
    return '0.00';
  }
  if (!money || isNaN(money)) {
    return '-';
  }
  const moneyFloat = (money / 100).toFixed(2);
  const moneyList = (moneyFloat.toString()).split('.');
  const integerStr = moneyList[0];
  const decimalStr = moneyList[1] ? moneyList[1] : '00';
  const integerStrLen = integerStr.length;
  const mod = parseInt((integerStrLen / 3), 10);
  if (mod === 0) {
    return `${integerStr}.${decimalStr}`;
  }
  let newIntStr = '';
  for (let i = 0; i < (mod + 1); i += 1) {
    const afterStr = integerStr.slice(0, integerStrLen - (i * 3));
    const afterStrLen = afterStr.length;
    const dval = afterStrLen - 3;
    if (dval > 0) {
      newIntStr = `,${afterStr.slice(dval, afterStrLen)}${newIntStr}`;
    } else {
      newIntStr = afterStr + newIntStr;
    }
  }
  return `${newIntStr}.${decimalStr}`;
}
export const formateOrgIds = (val) => {
  const NULLTABLE = [null, undefined, ''];
  if (NULLTABLE.includes(val)) {
    return undefined;
  }
  if (Array.isArray(val)) {
    return val;
  }
  if (String(val).indexOf(',') > -1) {
    return String(val).split(',');
  }
  return [val];
};

export const handleBtnByUiRule = btns => btns.map(((ele, index) => {
  if (index + 1 === btns.length) {
    return ele;
  }
  if (ele.type === 'primary') {
    return {
      ...ele,
      type: undefined,
    };
  }
  return ele;
}));

// 格式化对象值为string，踢出前后空格
export const handleValueTrimByObj = (obj) => {
  if (!obj) return null;
  return Object.keys(obj).reduce((res, k) => {
    if (typeof obj[k] === 'string') {
      res[k] = obj[k].trim();
    } else {
      res[k] = obj[k];
    }
    return res;
  }, {});
};

export const getPubType = (val) => {
  if (!val) return '-';
  return PUBLISH_TYPES.find(ele => ele.v === +val)?.k ?? '-';
};

export const showTextByCount = (text, max = 10) => {
  if (!text) return '-';
  if (text.length <= max) return text;
  return text.slice(0, max) + '…';
};


export const getDistrictCodeArr = (code) => {
  if (!code) return [];
  const res = ['000000'];
  const s1 = code.slice(0, 2);
  const s2 = code.slice(2, 4);
  const s3 = code.slice(4, 6);
  if (s1 === '00') return res;
  res.push(`${s1}0000`);
  if (s2 === '00') return res;
  res.push(`${s1}${s2}00`);
  if (s3 === '00') return res;
  res.push(`${s1}${s2}${s3}`);
  return res;
};

// 获取地址栏参数
export const getUrlParam = (parameter) => {
  const url = window.location.href;
  const regExp = new RegExp('[?&]' + parameter + '=([^&]*)');
  const match = url.match(regExp);
  if (match) {
    return decodeURIComponent(match[1]);
  } 
  return null;
};
