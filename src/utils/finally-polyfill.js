/* eslint-disable */
// 参考 https://github.com/Advanced-Frontend/Daily-Interview-Question/issues/109
import pushLog from 'utils/pushLog';

export default () => {
  try {
    window.Promise && !('finally' in Promise) && (() => {
      Promise.prototype.finally = function(callback) {
        const P = this.constructor;
        callback = typeof callback === 'function' ? callback : function() {
          // This is intentional
        };
        return this.then(
          value  => P.resolve(callback()).then(() => value),
          reason => P.resolve(callback()).then(() => { throw reason })
        );
      };
    })();
  } catch (error) {
    pushLog(JSON.stringify(error), 'info');
  }
};
