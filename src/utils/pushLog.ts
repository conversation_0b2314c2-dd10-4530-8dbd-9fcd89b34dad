export const enum SentryTypeEnum {
  debug = 'debug',
  info = 'info',
  warning = 'warning',
  error = 'error',
}
 
// 上报错误至 Sentry
export default (message: any, type: SentryTypeEnum) => {
  try {
    const win = window as any;
    if (win.Sentry) {
      win.Sentry.withScope(scope => {
        scope.setTag('isInitiative', true);
        scope.setLevel(type);
        if (type === SentryTypeEnum.error) {
          win.Sentry.captureException(message);
        } else {
          win.Sentry.captureMessage(message, type);
        }
      });
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(`该项目暂未接入 Sentry：${error}`);
  }
};
