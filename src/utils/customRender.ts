/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-07-11 16:24:08
 * @FilePath: /zcy-announcement-v2-front/src/utils/customRender.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import ReactDOM from 'react-dom';

export const customRender: (
  Component: any,
  immediately?: boolean
) => Array<Function> = (Component, immediately = true, visible = true) => {
  const DIV = document.createElement('div');

  const closeRender = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(DIV);
    if (unmountResult && DIV?.parentNode) {
      DIV.parentNode.removeChild(DIV);
    }
  };
  // 将 close 属性透传到组件内部
  const CloneComponent = React.cloneElement(Component, {
    close: closeRender,
    onCancel: closeRender,
    onOk: (args) => {
      Component.props.onOk(args, closeRender);
    },
    visible,
  });

  const startRender = () => {
    setTimeout(() => {
      ReactDOM.render(CloneComponent, DIV);
    }, 0);
  };

  immediately && startRender();

  return [startRender, closeRender];
};
