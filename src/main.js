/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-04-29 19:30:28
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-23 14:47:00
 * @FilePath: /zcy-announcement-v2-front/src/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* eslint-disable no-underscore-dangle */
import ReactDOM from 'react-dom';

/** *
 * 'babel-polyfill' 修复IE9的问题
 * 'raf/polyfill' 修复IE9的动画问题
 * 'event-source-polyfill' dev下热加载问题
 */
import 'raf/polyfill';
import dva from 'dva';
import onError from 'src/common/error';
import createLoading from 'dva-loading';
import 'doraemon/lib/style/v2-compatible-reset';
import 'src/common/systemConfig';
import 'console-polyfill';
import finallyPolyfill from 'utils/finally-polyfill';
import './style/atom.less';
import './main.less';
import { createBrowserHistory } from 'history';

if (__DEV__) {
  require('event-source-polyfill');
}

if (module.hot) {
  module.hot.accept();
}

finallyPolyfill();


let app;
function initApp() {
  app = dva({
    history: createBrowserHistory({
      basename: window.__POWERED_BY_QIANKUN__ ? '/digital-integrated-index/announcement-front' : '/announcement-front/',
    }),
    onError,
  });

  app.use(createLoading());

  /**
 * 页面内部权限model
 */
  app.model(require('src/common/models/app').default);
  app.model(require('src/common/models/privileges').default);

  app.router(require('./router').default);
}


if (window.__POWERED_BY_QIANKUN__) {
  // eslint-disable-next-line camelcase, no-undef
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;
}

export async function bootstrap() {
  //
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount({ container, onHistoryChange }) {
  initApp();
  window.onHistoryChange = onHistoryChange;
  app.start(container ? container.querySelector('#root') : document.getElementById('root'));
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(props.container ? props.container.querySelector('#root') : document.getElementById('root'));
  app._models.forEach((model) => {
    app.unmodel(model.namespace);
  });
}

if (!window.__POWERED_BY_QIANKUN__) {
  initApp();
  app.start('#root');
}
