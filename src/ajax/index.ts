
import { message, request as <PERSON>cyRe<PERSON> } from 'doraemon';
import type { RequestFunctionParams } from 'yapi-to-typescript';

// ZcyRequest.setDefaults({ timeout: 30000 });
 
type ZcyRequestPayload = RequestFunctionParams & {
  params?: any;
};
 
export default function request<T>(
  payload: ZcyRequestPayload,
  /**
   * errorMsgShow 为true， 走统一的报错，false为项目自行处理
   */
  errorMsgShow = true,
): Promise<T> {
  if (payload.method === 'GET') {
    payload.params = payload.data;
  }
  return new Promise((resolve, reject) => {
    ZcyRequest(payload.path, payload)
      .then(res => {
        const { success, message: _message, error } = res || {};
        if (!success) {
          errorMsgShow && message.error(_message || error || '服务器错误!');
        }
        resolve(res);
      })
      .catch(error => {
        if (
          (error?.status <= 504 && error?.status >= 500) ||
          (error?.status >= 400 && error?.status < 422)
        ) {
          message.error(error?.data?.message || error?.data?.error || '服务器开小差了，请稍后再试');
        }
        reject(error);
      });
  });
}
