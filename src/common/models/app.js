import {
  fetchData,
  fetchMessageRecordApi,
  sendSMSApi,
} from '../services/app';

export default {
  namespace: 'appCommon',

  state: {
    cache: {},
    SMSRecordList: [], // 短信推送结果列表
    // orgList: [], // 短信推送机构列表
    // receiverList: [], // 短信推送人员列表
  },
  effects: {
    * fetchData({ payload }, { call }) {
      const response = yield call(fetchData, { ...payload });
      return response.result;
    },
    // 获取短信推送记录
    *fetchMessageRecord({ payload }, { call, put }) {
      const response = yield call(fetchMessageRecordApi, payload);
      yield put({
        type: 'update',
        payload: {
          SMSRecordList: response.result || [],
        },
      });
      return response;
    },
    * sendSMS({ payload }, { call }) {
      const response = yield call(sendSMSApi, payload);
      return response;
    },
  },
  reducers: {
    update(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    setCache(state, action) {
      const { cache } = state;
      return {
        ...state,
        cache: {
          ...cache,
          ...action.payload,
        },
      };
    },
  },
};
