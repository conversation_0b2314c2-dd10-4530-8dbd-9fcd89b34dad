import { stringify } from 'qs';
import { request } from 'doraemon';

export async function checkUrlPermission(params) {
  return request(`/user/privileges/isResourcePermit?${stringify(params)}`);
}

export async function checkPagePermission(params) {
  return request(`/api/privileges/getElements?${stringify(params)}`);
}
export async function fetchData(params) {
  const { url, ...rest } = params;
  return request(url, {
    params: {
      ...rest,
    },
  });
}

// 获取当前登录的环境信息(政采云平台 or 上海环境)
export async function fetchCurrentEnvApi() {
  return request('/announcement/api/currentEnvConfig', {
  });
}

// 获取短信推送记录
export async function fetchMessageRecordApi(params) {
  return request('/announcement/api/notify/recordList', {
    method: 'GET',
    params,
  });
}

// 获取短信推送的机构列表
export async function fetchOrgListApi(params) {
  return request('/announcement/api/notify/orgList', {
    method: 'GET',
    params,
  });
}

// 获取短信推送的人员列表
export async function fetchReceiverListApi(params) {
  return request('/announcement/api/notify/receiverList', {
    method: 'GET',
    params,
  });
}

// 发送短息
export async function sendSMSApi(params) {
  return request('/announcement/api/notify/send', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}
