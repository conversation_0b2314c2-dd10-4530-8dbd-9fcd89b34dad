import React from 'react';
import {
  Select,
  SystemConfig,
  message,
} from 'doraemon';

const { Option } = Select;
const {
  setItemConfig,
  setSelectConfig,
} = SystemConfig;

const ItemConfig = {
  text: {
    rules: [
      { max: 30, message: '最大长度为30' },
    ],
  },
};
const selectConfig = {
  name: {
    url: '/api/select',
  },
  objectionKeys: {
    url: '/announcement/api/obtainCreateAnnouncementForm',
    transform: (options) => {
      return options.map(d => <Option value={d.key} key={d.key}>{d.name}</Option>);
    },
    getOptions: (data) => {
      if (data.success) {
        return data.result;
      } 
      message.error(data.error);
      return [];
    },
  },
  name2: {
    options: [{
      value: '1',
      text: '文字1',
    }, {
      value: '2',
      text: '文字2',
    }],
  },
};
setItemConfig(ItemConfig);
setSelectConfig(selectConfig);
