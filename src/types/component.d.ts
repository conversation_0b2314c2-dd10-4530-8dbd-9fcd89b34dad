import type { FormComponentProps, WrappedFormUtils } from '@zcy/doraemon/lib/form/Form';
import type { ModalFuncProps as ZCYModalFuncProps } from '@zcy/doraemon/lib/modal/Modal';
import type { RadioChangeEvent as ZCYRadioChangeEvent } from '@zcy/doraemon/lib/radio';
import type { ColumnProps } from '@zcy/doraemon/lib/table/interface';
import type { ItemParam as ZCYItemParam } from '@zcy/doraemon/lib/zcy-timeline/interface';
import type { RouteComponentProps } from 'dva/router';
import React from 'react';
import type { FormsItemType } from 'src/types/common';

export type RFC<P = {}> = React.FC<P & RouteComponentProps>;
/** 表单组件 */
export type FFC<P = {}, R extends { [K in keyof R]?: string | undefined } = {}> = React.FC<
  P & FormComponentProps & RouteComponentProps<R>
>;

/** 纯表单组件 */
export type PureFFC<P = {}> = React.FC<P & FormComponentProps>;

export type getFieldsValueType = WrappedFormUtils['getFieldsValue'];

export type setFieldsValueType = WrappedFormUtils['setFieldsValue'];

export type resetFieldsType = WrappedFormUtils['resetFields'];

export type validateFieldsType = WrappedFormUtils['validateFields'];

export type getFieldsErrorType = WrappedFormUtils['getFieldsError'];

export type getFieldDecoratorType = WrappedFormUtils['getFieldDecorator'];

export type validateFieldsAndScrollType = WrappedFormUtils['validateFieldsAndScroll'];

export type ColumnPropsType<T = any> = ColumnProps<T>;

export type ItemParam = ZCYItemParam;

export type RadioChangeEvent = ZCYRadioChangeEvent;

export type HistoryPushType = History.push;

export type HistoryBlockType = History.block;

export type HistoryBackType = History['back'];

// doraemon 组件 Modal 未声明 closable 属性
export type ModalFuncProps = ZCYModalFuncProps & { closable: boolean };

export interface PreviewDrawerPropsType {
  close?: () => void;
  directoryData: Array<FormsItemType>; // 左侧目录结构数据
  previewContent: string; // 富文本内容
}
