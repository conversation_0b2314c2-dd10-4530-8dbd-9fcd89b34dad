export interface RequestResponseTypes<T> {
  message?: string;
  error?: string;
  result: T;
  success: boolean;
}

export interface WuxiangCustomerWidgetProps<TExtraParams> {
  editAble?: boolean;
  extraParams?: TExtraParams;
  getPopupContainer?: () => HTMLElement;
  id?: string;
  isDestroyed?: boolean;
  onChange?: (v: any) => void;
  placeholder?: string;
  value?: any;
  widgetCode?: string;
}

export interface WuxiangSchemaProps {
  id?: string;
}

export interface RouterTipModalParamsType<T = object, P = any> {
  historyPush: HistoryPushType;
  historyBlock?: HistoryBlockType;
  historyBack: HistoryBackType;
  queryObject: T;
  callback?: () => Promise<P>;
  title?: string;
  pathname?: string;
}
export interface FormsItemType {
  code: string;
  name: string;
  id?: string;
  widget?: string;
  idPrefix?: string;
  elOffsetTop?: number;
  newCode?: string;
}

export interface LayoutPropsType {
  projectId?: string;
  layoutmode?: string;
  [k: string]: string;
}

export type HistoryPushType = History.push;

export type HistoryBlockType = History.block;

export type HistoryBackType = History.back;
