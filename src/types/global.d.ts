/**
 * 所有 window 类型放置于此处
 */
declare interface Window {
  __DEV__: any;
  module: {
    hot: any;
  };
  envHref: any;
  currentUserDistrict: any;
  g_UTM: any;
  UE: any
}

declare interface IBaseProps {
  location: {
    pathname: string;
  };

  dispatch: (params: { type: string; payload?: Record<string, any> }) => Promise<any>;
}

declare type PickListItem<T> = T extends Array<infer P>
    ? P
    : never;
