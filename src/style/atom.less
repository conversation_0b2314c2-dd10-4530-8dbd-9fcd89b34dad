/* eslint-disable */
/* 内置padding、margin 的 四个方向 的0～10、12、16、20 */
/* 内置padding、margin 的  20、12、8 */
.zcy-pd-l-20 {
  padding-left: 20px;
}

.zcy-pd-r-20 {
  padding-right: 20px;
}

.zcy-pd-b-20 {
  padding-bottom: 20px;
}

.zcy-pd-t-20 {
  padding-top: 20px;
}

.zcy-pd-l-16 {
  padding-left: 16px;
}

.zcy-pd-r-16 {
  padding-right: 16px;
}

.zcy-pd-b-16 {
  padding-bottom: 16px;
}

.zcy-pd-t-16 {
  padding-top: 16px;
}

.zcy-pd-l-12 {
  padding-left: 12px;
}

.zcy-pd-r-12 {
  padding-right: 12px;
}

.zcy-pd-b-12 {
  padding-bottom: 12px;
}

.zcy-pd-t-12 {
  padding-top: 12px;
}

.zcy-pd-l-10 {
  padding-left: 10px;
}

.zcy-pd-r-10 {
  padding-right: 10px;
}

.zcy-pd-b-10 {
  padding-bottom: 10px;
}

.zcy-pd-t-10 {
  padding-top: 10px;
}

.zcy-pd-l-9 {
  padding-left: 9px;
}

.zcy-pd-r-9 {
  padding-right: 9px;
}

.zcy-pd-b-9 {
  padding-bottom: 9px;
}

.zcy-pd-t-9 {
  padding-top: 9px;
}

.zcy-pd-l-8 {
  padding-left: 8px;
}

.zcy-pd-r-8 {
  padding-right: 8px;
}

.zcy-pd-b-8 {
  padding-bottom: 8px;
}

.zcy-pd-t-8 {
  padding-top: 8px;
}

.zcy-pd-l-7 {
  padding-left: 7px;
}

.zcy-pd-r-7 {
  padding-right: 7px;
}

.zcy-pd-b-7 {
  padding-bottom: 7px;
}

.zcy-pd-t-7 {
  padding-top: 7px;
}

.zcy-pd-l-6 {
  padding-left: 6px;
}

.zcy-pd-r-6 {
  padding-right: 6px;
}

.zcy-pd-b-6 {
  padding-bottom: 6px;
}

.zcy-pd-t-6 {
  padding-top: 6px;
}

.zcy-pd-l-5 {
  padding-left: 5px;
}

.zcy-pd-r-5 {
  padding-right: 5px;
}

.zcy-pd-b-5 {
  padding-bottom: 5px;
}

.zcy-pd-t-5 {
  padding-top: 5px;
}

.zcy-pd-l-4 {
  padding-left: 4px;
}

.zcy-pd-r-4 {
  padding-right: 4px;
}

.zcy-pd-b-4 {
  padding-bottom: 4px;
}

.zcy-pd-t-4 {
  padding-top: 4px;
}

.zcy-pd-l-3 {
  padding-left: 3px;
}

.zcy-pd-r-3 {
  padding-right: 3px;
}

.zcy-pd-b-3 {
  padding-bottom: 3px;
}

.zcy-pd-t-3 {
  padding-top: 3px;
}

.zcy-pd-l-2 {
  padding-left: 2px;
}

.zcy-pd-r-2 {
  padding-right: 2px;
}

.zcy-pd-b-2 {
  padding-bottom: 2px;
}

.zcy-pd-t-2 {
  padding-top: 2px;
}

.zcy-pd-l-1 {
  padding-left: 1px;
}

.zcy-pd-r-1 {
  padding-right: 1px;
}

.zcy-pd-b-1 {
  padding-bottom: 1px;
}

.zcy-pd-t-1 {
  padding-top: 1px;
}

.zcy-pd-l-0 {
  padding-left: 0;
}

.zcy-pd-r-0 {
  padding-right: 0;
}

.zcy-pd-b-0 {
  padding-bottom: 0;
}

.zcy-pd-t-0 {
  padding-top: 0;
}

.zcy-pd-20 {
  padding: 20px;
}

.zcy-pd-12 {
  padding: 12px;
}

.zcy-pd-8 {
  padding: 8px;
}

.zcy-mg-l-24 {
  margin-left: 24px;
}

.zcy-mg-r-24 {
  margin-right: 24px;
}

.zcy-mg-b-24 {
  margin-bottom: 24px;
}

.zcy-mg-t-24 {
  margin-top: 24px;
}

.zcy-mg-l-20 {
  margin-left: 20px;
}

.zcy-mg-r-20 {
  margin-right: 20px;
}

.zcy-mg-b-20 {
  margin-bottom: 20px;
}

.zcy-mg-t-20 {
  margin-top: 20px;
}

.zcy-mg-l-16 {
  margin-left: 16px;
}

.zcy-mg-r-16 {
  margin-right: 16px;
}

.zcy-mg-b-16 {
  margin-bottom: 16px;
}

.zcy-mg-t-16 {
  margin-top: 16px;
}

.zcy-mg-l-12 {
  margin-left: 12px;
}

.zcy-mg-r-12 {
  margin-right: 12px;
}

.zcy-mg-b-12 {
  margin-bottom: 12px;
}

.zcy-mg-t-12 {
  margin-top: 12px;
}

.zcy-mg-l-10 {
  margin-left: 10px;
}

.zcy-mg-r-10 {
  margin-right: 10px;
}

.zcy-mg-b-10 {
  margin-bottom: 10px;
}

.zcy-mg-t-10 {
  margin-top: 10px;
}

.zcy-mg-l-9 {
  margin-left: 9px;
}

.zcy-mg-r-9 {
  margin-right: 9px;
}

.zcy-mg-b-9 {
  margin-bottom: 9px;
}

.zcy-mg-t-9 {
  margin-top: 9px;
}

.zcy-mg-l-8 {
  margin-left: 8px;
}

.zcy-mg-r-8 {
  margin-right: 8px;
}

.zcy-mg-b-8 {
  margin-bottom: 8px;
}

.zcy-mg-t-8 {
  margin-top: 8px;
}

.zcy-mg-l-7 {
  margin-left: 7px;
}

.zcy-mg-r-7 {
  margin-right: 7px;
}

.zcy-mg-b-7 {
  margin-bottom: 7px;
}

.zcy-mg-t-7 {
  margin-top: 7px;
}

.zcy-mg-l-6 {
  margin-left: 6px;
}

.zcy-mg-r-6 {
  margin-right: 6px;
}

.zcy-mg-b-6 {
  margin-bottom: 6px;
}

.zcy-mg-t-6 {
  margin-top: 6px;
}

.zcy-mg-l-5 {
  margin-left: 5px;
}

.zcy-mg-r-5 {
  margin-right: 5px;
}

.zcy-mg-b-5 {
  margin-bottom: 5px;
}

.zcy-mg-t-5 {
  margin-top: 5px;
}

.zcy-mg-l-4 {
  margin-left: 4px;
}

.zcy-mg-r-4 {
  margin-right: 4px;
}

.zcy-mg-b-4 {
  margin-bottom: 4px;
}

.zcy-mg-t-4 {
  margin-top: 4px;
}

.zcy-mg-l-3 {
  margin-left: 3px;
}

.zcy-mg-r-3 {
  margin-right: 3px;
}

.zcy-mg-b-3 {
  margin-bottom: 3px;
}

.zcy-mg-t-3 {
  margin-top: 3px;
}

.zcy-mg-l-2 {
  margin-left: 2px;
}

.zcy-mg-r-2 {
  margin-right: 2px;
}

.zcy-mg-b-2 {
  margin-bottom: 2px;
}

.zcy-mg-t-2 {
  margin-top: 2px;
}

.zcy-mg-l-1 {
  margin-left: 1px;
}

.zcy-mg-r-1 {
  margin-right: 1px;
}

.zcy-mg-b-1 {
  margin-bottom: 1px;
}

.zcy-mg-t-1 {
  margin-top: 1px;
}

.zcy-mg-l-0 {
  margin-left: 0;
}

.zcy-mg-r-0 {
  margin-right: 0;
}

.zcy-mg-b-0 {
  margin-bottom: 0;
}

.zcy-mg-t-0 {
  margin-top: 0;
}

.zcy-mg-20 {
  margin: 20px;
}

.zcy-mg-12 {
  margin: 12px;
}

.zcy-mg-8 {
  margin: 8px;
}

/* 内置字体大小 */
.zcy-fs-12 {
  font-size: 12px;
}

.zcy-fs-15 {
  font-size: 15px;
}

.zcy-fs-16 {
  font-size: 16px;
}

.zcy-fs-18 {
  font-size: 18px;
}

.zcy-fs-20 {
  font-size: 20px;
}

/* 内置字重 */
.zcy-fw-normal {
  font-weight: normal;
}

.zcy-fw-bold {
  font-weight: bold;
}

.zcy-fw-bolder {
  font-weight: bolder;
}

.zcy-fw-400 {
  font-weight: 400;
}

.zcy-fw-500 {
  font-weight: 500;
}

.zcy-fw-600 {
  font-weight: 600;
}

.zcy-fw-700 {
  font-weight: 700;
}

.zcy-ta-left {
  text-align: left;
}

.zcy-ta-center {
  text-align: center;
}

.zcy-ta-right {
  text-align: right;
}

.zcy-va-t {
  vertical-align: top;
}

.zcy-va-tt {
  vertical-align: text-top;
}

.zcy-va-b {
  vertical-align: bottom;
}

.zcy-va-tb {
  vertical-align: text-bottom;
}

.zcy-va-m {
  vertical-align: middle;
}

.zcy-va-i {
  vertical-align: inherit;
}

/* 内置字体颜色 */
.zcy-c-ffffff {
  color: #fff;
}

.zcy-c-0 {
  color: #000;
}

.zcy-c-333333 {
  color: #333;
}

.zcy-c-666666 {
  color: #666;
}

.zcy-c-999999 {
  color: #999;
}

.zcy-c-3177fd {
  color: #3177fd;
}

/* 内置背景颜色 */
.zcy-bg-c-ffffff {
  background-color: #fff;
}

.zcy-bg-c-0 {
  background-color: #000;
}

.zcy-bg-c-333333 {
  background-color: #333;
}

.zcy-bg-c-666666 {
  background-color: #666;
}

.zcy-bg-c-999999 {
  background-color: #999;
}

.zcy-bg-c-3177fd {
  background-color: #3177fd;
}

/* ---------- */
.zcy-cs-p {
  cursor: pointer;
}

.zcy-cs-na {
  cursor: not-allowed;
}

/* ---------- */
.zcy-p-s {
  position: static;
}

.zcy-p-r {
  position: relative;
}

.zcy-p-a {
  position: absolute;
}

/* ---------- */
.zcy-dp-n {
  display: none;
}

.zcy-dp-b {
  display: block;
}

.zcy-dp-i {
  display: inline;
}

.zcy-dp-ib {
  display: inline-block;
}

.zcy-flex {
  display: flex;
}

.zcy-line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.zcy-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.hide {
  display: none;
}