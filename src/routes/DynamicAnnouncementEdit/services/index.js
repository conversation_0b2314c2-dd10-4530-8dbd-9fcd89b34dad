import request from 'src/utils/request';
import commonApi from './common';

// 面向中小企业预留项目执行情况公告 - 公告内容 - 导出按钮展示
export async function getShowExportButton(params) {
  return request('/announcement/api/small/showExportButton', {
    method: 'GET',
    params,
  });
}

// 面向中小企业预留项目执行情况公告 - 获取导出任务
export async function getExportAnnouncementInfoTask(params) {
  return request('/announcement/api/small/exportAnnouncementInfo', {
    method: 'GET',
    params,
  });
}

// 面向中小企业预留项目执行情况公告 - 轮询导出任务
export async function pollingAnnouncementInfoTask(params) {
  return request('/announcement/api/small/pollingAnnouncementInfo', {
    method: 'GET',
    params,
  });
}

export default {
  commonApi,
};
