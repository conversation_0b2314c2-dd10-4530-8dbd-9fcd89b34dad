import React, { useState } from 'react';
import {
  Modal,
  Input, Form,
  message,
} from 'doraemon';
import { submitRevocableAnnApi } from '../../services/common';

const FormItem = Form.Item;

const CancelAnnoModal = ({ visible, showCancelLoading, handleCancel, annId, onSuccess }) => {
  const [remark, setRemark] = useState('');
  // 执行取消公告
  const doCancelAnno = async () => {
    const { success, error } = await submitRevocableAnnApi({
      announcementId: annId,
      remark,
    });
    if (!success) {
      message.error(error);
      return;
    }
    message.success('取消成功！');
    onSuccess();
  };
  return (
    <div>
      <Modal
        destroyOnClose
        visible={visible}
        title="取消公告"
        onOk={doCancelAnno}
        onCancel={handleCancel}
        confirmLoading={showCancelLoading}
      >
        <FormItem label="公告取消原因" labelCol={{ span: 4 }} wrapperCol={{ span: 18 }} >
          <Input.TextArea 
            style={{ width: '405px' }}
            onChange={(ev) => {
              setRemark(ev.target.value);
            }}
            maxLength={255}
            placeholder="请输入"
          />
        </FormItem>
      </Modal>
    </div>
  );
};

export default CancelAnnoModal;
