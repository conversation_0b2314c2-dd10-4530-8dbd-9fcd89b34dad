import React, { Component } from 'react';
import { Panel, Tag } from 'doraemon';
// import Tag from './tag';
import './marking.less';

const { CheckableTag } = Tag;

class Marking extends Component {
  handelTagClick = (state) => {
    this.props.onChange(state);
  }

  render() {
    const { markingList, disabled = false, showSelected = false, autoShowALl = true } = this.props;
    // autoShowALl false:自动标识isChoosed=true才显示,true:自动标识全显示
    // 自动生成标识
    const markingListAuto = markingList.filter((item) => {
      if (autoShowALl) {
        return item.type === 1;
      }
      return item.type === 1 && item.isChoosed;
    });
    // 手动选择标识
    const markingListHandel = markingList.filter(
      item => item.type === 2 && (showSelected ? item.isChoosed : true)
    );
    const title = <span>公告标识<span className="marking-title-note">（该标识仅用于展示及搜索使用，不纳入公告内容）</span></span>;
    if (markingList.length > 0) {
      return (
        <Panel
          title={title}
        >
          {
            markingListHandel.length > 0 && (
              <div>
                <div className="marking-type-note">手动选取的标识：</div>
                {
                  markingListHandel.map(item => (
                    <CheckableTag
                      key={item.id}
                      checked={item.isChoosed}
                      disabled={disabled}
                      onChange={() => {
                        if (!disabled) {
                          this.handelTagClick(item);
                        }
                      }}
                    >
                      {item.name}
                    </CheckableTag>
                  ))
                }
              </div>
            )
          }
          {
            markingListAuto.length > 0 && (
              <div>
                <div className="marking-type-note">自动生成的标识：</div>
                {
                  markingListAuto.map(item => (
                    <CheckableTag
                      key={item.name}
                      checked
                      disabled
                    >
                      {item.name}
                    </CheckableTag>
                  ))
                }
              </div>
            )
          }
        </Panel>
      );
    }
    return null;
  }
}

export default Marking;
