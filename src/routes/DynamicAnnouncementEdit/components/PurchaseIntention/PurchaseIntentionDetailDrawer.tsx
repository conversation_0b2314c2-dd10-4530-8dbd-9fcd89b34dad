/* eslint-disable react/jsx-closing-tag-location */
import { Drawer, Form, FormGrid, Panel, Input, DatePicker, InputAmount, Popover, Icon } from '@zcy/doraemon';
import React, { FC } from 'react';
import ModalWrap from 'src/components/ModalWrap';
import './index.less';

interface PurchaseIntentionDetailDrawerProps {
  visible: boolean;
  form: any;
  onClose: Function;
  onOk: (values: any) => void;
  index?: number;
  record: {
    sectionNo: string;
    purchaseProjectName: string;
    budgetPrice: string;
    estimatedPurchaseTime: string;
    remark: string;
    purchaseRequirementDetail: {
      bidName: string;
      bidCount: string;
      bidMainTarget: string;
      bidRequirement: string;
    }
  }
}

const PurchaseIntentionDetailDrawer: FC<PurchaseIntentionDetailDrawerProps> = (props) => {
  const { visible, form, onClose, onOk, record, index } = props;

  const basicInfoFormColumns = [
    {
      label: '序号',
      render: () => {
        return form.getFieldDecorator('sectionNo', {
          initialValue: record?.sectionNo,
          rules: [{ required: true, message: '请输入序号' }],
        })(<Input placeholder="请输入" />);
      },
    },
    {
      label: '采购项目名称',
      render: () => {
        return form.getFieldDecorator('purchaseProjectName', {
          initialValue: record?.purchaseProjectName,
          rules: [{ required: true, message: '请输入采购项目名称' }],
        })(<Input placeholder="请输入" />);
      },
    },
    {
      label: '预算金额 (万元)',
      render: () => {
        return form.getFieldDecorator('budgetPrice', {
          initialValue: record?.budgetPrice,
          rules: [{ required: true, message: '请输入预算金额 (万元)' }],
        })(<InputAmount 
          includeThousandsSeparator
          placeholder="请输入"
          align="left"
          color="orange"
          min="0"
          max="99999999999"
          sourceUnit="元"
          targetUnit="万元"
          precisionUnit="分"
        />);
      },
    },
    {
      label: '预计采购时间 (填写到月)',
      render: () => {
        return form.getFieldDecorator('estimatedPurchaseTime', {
          initialValue: record?.estimatedPurchaseTime,
          rules: [{ required: true, message: '请选择预计采购时间' }],
        })(<DatePicker.MonthPicker placeholder="yyyy年MM月" />);
      },
    },
    {
      label: '备注',
      extra: '可补充说明落实政府采购政策功能情况于备注',
      render: () => {
        return form.getFieldDecorator('remark', {
          initialValue: record?.remark,
        })(<Input.TextArea placeholder="请输入" maxLength={1000} />);
      },
    },
  ];

  const requirementDetailColumns = [
    {
      label: '采购标的名称',
      render: () => {
        return form.getFieldDecorator('purchaseRequirementDetail.bidName', {
          initialValue: record?.purchaseRequirementDetail?.bidName,
          rules: [{ required: true, message: '请输入采购标的名称' }],
        })(<Input placeholder="请输入" />);
      },
    },
    {
      label: '采购标的数量',
      render: () => {
        return form.getFieldDecorator('purchaseRequirementDetail.bidCount', {
          initialValue: record?.purchaseRequirementDetail?.bidCount,
          rules: [{ required: true, message: '请输入采购标的数量' }],
        })(<Input placeholder="请输入" />);
      },
    },
    {
      label: '主要功能或目标',
      render: () => {
        return form.getFieldDecorator('purchaseRequirementDetail.bidMainTarget', {
          initialValue: record?.purchaseRequirementDetail?.bidMainTarget,
          rules: [{ required: true, message: '请输入主要功能或目标' }],
        })(<Input.TextArea placeholder="请输入" maxLength={1000} />);
      },
    },
    {
      label: (<span>
        需满足的要求
        <Popover
          placement="top"
          content="如质量、服务、安全、时限等"
        >
          <Icon type="question-circle-o" />
        </Popover>
      </span>),
      render: () => {
        return form.getFieldDecorator('purchaseRequirementDetail.bidRequirement', {
          initialValue: record?.purchaseRequirementDetail?.bidRequirement,
          rules: [{ required: true, message: '请输入需满足的要求' }],
        })(<Input.TextArea placeholder="请输入" maxLength={1000} />);
      },
    },
  ];

  const handleOnOk = () => {
    form.validateFieldsAndScroll((error, values) => {
      if (error) return;

      onOk?.({ ...values, index });
      onClose?.();
    });
  };

  return (
    <Drawer
      width={640}
      openConfirmBtn
      title="新增意向公开明细"
      visible={visible}
      onCancel={onClose}
      onClose={onClose}
      onOk={handleOnOk}
      className="purchase-intention-detail-drawer"
    >
      <Panel title="基本信息">
        <FormGrid 
          bordered
          column={1}
          layout="horizontal"
          formGridItem={basicInfoFormColumns}
        />
      </Panel>
      <Panel title="采购需求概况">
        <FormGrid 
          bordered
          column={1}
          layout="horizontal"
          formGridItem={requirementDetailColumns}
        />
      </Panel>
    </Drawer>
  );
};

export default ModalWrap(Form.create()(PurchaseIntentionDetailDrawer));
