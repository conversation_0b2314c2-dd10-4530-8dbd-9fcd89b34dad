import React, { Component } from 'react';
import { Upload, Button, Modal } from 'doraemon';
import _ from 'lodash';
import pushLog from 'utils/pushLog';
import PurchaseIntentionDetailDrawer from './PurchaseIntentionDetailDrawer';


export default class TableExtraButton extends Component {
  // 下载导入模板
  downloadTemplate = () => {
    const { schema } = this.props;
    // 模拟 form 表单提交
    const form = document.createElement('form');
    document.body.appendChild(form);
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'formField';
    input.value = JSON.stringify(schema);
    form.appendChild(input);
    form.method = 'post';
    form.action = '/announcement/api/transterExcelTemplate';
    form.submit();
    document.body.removeChild(form);
    try {
      if (!schema || Object.keys((schema ?? {})).length === 0) {
        pushLog(JSON.stringify({
          type: 'dynamic',
          schema,
        }), 'info');
      }
    } catch (err) {
      //
    }
  }

  // 导入
  onUploadChange = async ({
    file,
  }) => {
    const {
      schema,
      dataFormat,
      form: { setFieldsValue, getFieldValue },
      isAutoFreshSectionNo,
    } = this.props;
    if (file.status === 'done') {
      const { error, result = [], success } = file.response;
      if (!success) {
        Modal.error({
          title: '提示',
          content: '导入表格内容校验未通过，请修改后再次导入',
          okText: '下载错误详情',
          closable: true,
          onOk: () => {
            this.downloadText(error, '导入错误详情.txt');
          },
        });
      } else {
        const { code } = schema || {};
        const originValue = getFieldValue(code) || [];
        const formativeData = dataFormat ? await dataFormat(result, schema) : {};
        let importValue = [];
        if (!_.isEmpty(formativeData)) {
          importValue = formativeData[code] || [];
        }
        if (isAutoFreshSectionNo) {
          let idx = 0;
          originValue.forEach((item) => {
            item.sectionNo = idx + 1;
            idx += 1;
          });
          importValue.forEach((item) => {
            item.sectionNo = idx + 1;
            idx += 1;
          });
        }
        if (this.props?.schema?.code === 'monitorReservedProjectInfo') {
          Modal.warning({
            content: (
              <div>
                1. 因存在字段聚合，请注意检查并核对“合同信息”列数据导入一致性
                <br />
                2. 预留金额/预留比例列均按保留2位小数取定，超长位数自动四舍五入
              </div>
            ),
          });
        }
        setFieldsValue({
          [code]: originValue.concat(importValue),
        });
      }
    }
  }

  downloadText = (text, filename) => {
    const element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
    element.setAttribute('download', filename);
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }

  render() {
    const { schema, showAddBtn, insertOrUpdateIntentionDetailData } = this.props;

    return (
      <React.Fragment>
        {
          // 53 号文暂不支持模板导入
          !showAddBtn
            ? (
              <>
                <a
                  style={{ marginRight: 10, lineHeight: '28px' }}
                  onClick={this.downloadTemplate}
                >
                  下载导入模板
                </a>
                <Upload
                  showUploadList={false}
                  action="/announcement/api/excel2Bookmark"
                  onChange={this.onUploadChange}
                  maxFileSize={10 * 1024 * 1024}
                  className="upload"
                  successPrompt={false}
                  data={{
                    // fields: JSON.stringify(this.props.tableInfo.metadataDtos || []),
                    formField: JSON.stringify(schema),
                  }}
                >
                  <Button
                    style={{ marginRight: 10 }}
                    type="primary"
                    ghost
                  >
                    导入
                  </Button>
                </Upload>
              </>
            )
            : null
        }
        {
          showAddBtn && (
            <Button
              onClick={() => {
                PurchaseIntentionDetailDrawer.show({
                  onOk: insertOrUpdateIntentionDetailData,
                });
              }}
            >
              新增
            </Button>
          )
        }
      </React.Fragment>
    );
  }
}
