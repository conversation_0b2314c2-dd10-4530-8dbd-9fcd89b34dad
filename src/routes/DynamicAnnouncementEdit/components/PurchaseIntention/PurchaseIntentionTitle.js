import React, { Component } from 'react';
import { Input, Select } from 'doraemon';

const { Option } = Select;

const yearOpts = [];
const yearMap = {};
for (let i = 50; i >= 0; i -= 1) {
  yearMap[`${2050 - i}`] = `${2050 - i}年`;
  yearOpts.push(`${2050 - i}`);
}
const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

export default class PurchaseIntentionTitle extends Component {
  onChange = (propValue, value, type) => {
    const { onChange } = this.props;
    if (type === 'org' || type === 'batch') {
      onChange({
        ...propValue,
        [type]: value.target.value,
      });
    } else {
      onChange({
        ...propValue,
        [type]: value,
      });
    }
  }

  render() {
    const { value = {}, extraParams = {}, alwaysReadOnly } = this.props;

    const { preview } = extraParams;
    return (
      <span style={{ width: '100%' }}>
        <Input
          disabled={alwaysReadOnly}
          value={value.org || undefined}
          style={{ width: '200px' }}
          placeholder="单位名称"
          onChange={(v) => {
            this.onChange(value, v, 'org');
          }}
        />&nbsp;
        <Select
          disabled={preview}
          value={value.year || undefined}
          style={{ width: '100px' }}
          placeholder="年份"
          onChange={(v) => {
            this.onChange(value, v, 'year');
          }}
        >
          {yearOpts.map(year => (
            <Option key={year} value={year}>{year}</Option>
          ))}
        </Select>
        &nbsp;年&nbsp;
        <Select
          disabled={preview}
          value={value.startMonth || undefined}
          style={{ width: '100px' }}
          placeholder="开始月份"
          onChange={(v) => {
            this.onChange(value, v, 'startMonth');
          }}
        >
          {months.map(month => (
            <Option key={month} value={`${month}`}>{month}</Option>
          ))}
        </Select>
        &nbsp;
        月至
        &nbsp;
        <Select
          disabled={preview}
          value={value.endMonth || undefined}
          style={{ width: '100px' }}
          placeholder="结束月份"
          onChange={(v) => {
            this.onChange(value, v, 'endMonth');
          }}
        >
          {months.map(month => (
            <Option key={month} value={`${month}`}>{month}</Option>
          ))}
        </Select>
        &nbsp;
        月
        {/* 采购意向批次信息 */}
        {
          preview && !value.batch ? null : (
            <React.Fragment>
              &nbsp;
              <Input
                disabled={preview}
                value={value.batch || undefined}
                style={{ width: '160px' }}
                maxLength={10}
                placeholder="批次信息（选填）"
                onChange={(v) => {
                  this.onChange(value, v, 'batch');
                }}
              />
              &nbsp;
            </React.Fragment>
          )
        }
        政府采购意向
      </span>
    );
  }
}
