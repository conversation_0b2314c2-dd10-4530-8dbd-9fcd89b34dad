import React from 'react';
import {
  Panel,
} from 'doraemon';
import WorkflowSteps from '@zcy/workflow-steps';
import { isEmpty } from 'lodash';

export default function AnnSteps({
  districtCode,
  processDefineKey,
  bizId,
  conditions,
}) {
  // 公告步骤条
  return (
    <Panel
      title="操作流程"
    >
      {/* 组件内部没有对 props 进行监听 */}
      {
        !isEmpty(conditions) && processDefineKey && districtCode && (
          <WorkflowSteps
            processDefineKey={processDefineKey}
            districtCode={districtCode}
            bizId={bizId}
            config={{ conditions }}
          />
        )
      }
    </Panel>
  );
}
