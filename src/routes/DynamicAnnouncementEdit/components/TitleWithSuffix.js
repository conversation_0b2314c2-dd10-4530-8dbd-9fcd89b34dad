import React, { Component } from 'react';
import { Input } from 'doraemon';

export default class TitleWithSuffix extends Component {
  onChange = (e) => {
    const val = e.target.value;
    const { onChange, titleSuffix } = this.props;
    if (val) {
      onChange(`${val}${titleSuffix || ''}`);
    } else {
      onChange(undefined);
    }
  }
  render() {
    const { titleSuffix, value, ...rest } = this.props;
    const v = value ? value.replace(titleSuffix, '') : undefined;
    return (
      <span style={{ width: '100%' }}>
        <Input
          style={{ width: '60%' }}
          placeholder="请输入"
          value={v}
          {...rest}
          onChange={this.onChange}
        />
        &nbsp;
        {titleSuffix}
      </span>
    );
  }
}
