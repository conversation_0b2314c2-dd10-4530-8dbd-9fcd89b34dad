import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { Modal, Radio, message, Form } from 'doraemon';
import { reservedProjectDetailsForGuiZhouPost, reservedProjectDetailsPost } from 'src/api/announcement/api/config/form';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};


const UploadMonitorEnterprisesModal = ({
  onCancel,
  visible,
  year,
  orgName,
  orgId,
  env,
  ...rest
}) => {
  const getDefFilterType = () => {
    return env === 'gz' ? 2 : 1;
  };
  const [loading, setLoading] = useState(false);
  const [filterType, setFilterType] = useState(getDefFilterType());
  const onOk = () => {
    if (!year) return message.error('请选择目标年度');
    if (!orgId) return message.error('请选择目标单位');
    setLoading(true);
    const fetchPromise = env === 'gz' ? reservedProjectDetailsForGuiZhouPost({
      year,
      institutionId: orgId,
    }) : reservedProjectDetailsPost({
      year,
      institutionId: orgId,
      type: filterType,
    });
    fetchPromise.then(res => {
      setLoading(false);
      if (!res.success) return;
      if (rest.onSubmit && typeof rest.onSubmit === 'function') {
        rest.onSubmit(res.result ?? []);
        onCancel?.();
      }
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (visible) setFilterType(getDefFilterType());
  }, [visible]);

  return (
    <Modal
      title="设置预留中小企业项目筛选口径"
      visible={visible}
      {...rest}
      onOk={onOk}
      onCancel={onCancel}
      okText="确认"
      okButtonProps={
        { loading }
      }
    >
      <FormItem
        {...formItemLayout}
        label="目标年度"
      >
        <p>{year || '-'}</p>
      </FormItem>
      <FormItem
        {...formItemLayout}
        label="目标单位"
      >
        <p>{orgName || '-'}</p>
      </FormItem>
      <FormItem
        {...formItemLayout}
        label="筛查口径"
      >
        <RadioGroup disabled={env === 'gz'} onChange={e => setFilterType(e.target.value)} value={filterType}>
          <Radio value={1}>按计划年度</Radio>
          <Radio value={2}>按合同年度</Radio>
        </RadioGroup>
      </FormItem>
    </Modal>
  );
};

const useUploadMonitorEnterprisesModal = (args) => {
  const DIV = document.createElement('div');
  const openModal = (props) => {
    setTimeout(() => {
      ReactDOM.render(
        <UploadMonitorEnterprisesModal {...args} {...props} onCancel={closeModal} />,
        DIV,
      );
    }, 0);
  };

  const closeModal = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(DIV);
    if (unmountResult && DIV?.parentNode) {
      DIV.parentNode.removeChild(DIV);
    }
  };
  return [openModal, closeModal];
};

export default UploadMonitorEnterprisesModal;
export { useUploadMonitorEnterprisesModal };
