import { MultiCascader } from 'doraemon';
import React, { FC } from 'react';
import { MultiCascaderProps } from '@zcy/doraemon/lib/multi-cascader';

export interface CascaderCompPropsTypes {
  value?: any;
  onChange?: (val: { key: string | number; label: string | number } | undefined) => void;
  canEdit: boolean;
  options?: Pick<MultiCascaderProps, 'options'>[];
  cascaderProps?: Omit<MultiCascaderProps, 'options'>;
}

const CascaderComp: FC<CascaderCompPropsTypes> = React.forwardRef<any, CascaderCompPropsTypes>(
  (props, ref) => {
    const { value, canEdit, options, onChange, cascaderProps } = props;

    const realValue = value ? value.map(ele => ele.key) : undefined;

    return (
      <>
        {canEdit ? (
          <MultiCascader
            {...cascaderProps}
            ref={ref}
            options={options}
            value={realValue}
            changeOnSelect
            onChange={(_, __, allSelectedOptions) => {
              if (allSelectedOptions && allSelectedOptions.length) {
                const values = allSelectedOptions.map(ele => ({
                  key: ele.value,
                  label: ele.label,
                }));
                onChange &&
                  onChange(values);
              } else {
                onChange && onChange(undefined);
              }
            }}
          />
        ) : (
          <span ref={ref}>{(value?.map(ele => ele.label).join('、')) || '-'}</span>
        )}
      </>
    );
  }
);
export default CascaderComp;
