import React, { Component } from 'react';
import { message, request } from 'doraemon';
import <PERSON>rComp from './CascaderComp';

interface IProps {
  value: any;
  editAble: boolean;
  year: number;
  districtCode: string;
  changeOnSelect: boolean;
  useOnlyLeafNode: boolean;
  onChange: (val: any) => void;
  getPopupContainer: (node: any) => any;
}
interface IState {
  treeList: any;
}

export default class WXGoalGpcatalogComp extends Component<IProps, IState> {
  state = {
    treeList: [],
  };

  componentDidMount() {
    this.getGpcatalogTree();
  }

  getGpcatalogTree = () => {
    const { year, districtCode } = this.props;
    request('/api/gpcontents/contentsManager/tree', {
      params: {
        year,
        districtCode,
      },
    })
      .then(res => {
        if (!res.success) {
          message.error('采购目录数据获取失败');
          return;
        }
        const treeList = res.data && res.data.data;
        if (!treeList.length) message.error('暂无采购目录数据，请检查当年目录配置');
        this.setState({
          treeList: this.sortOptions(treeList || []),
        });
      })
      .catch(() => message.error('采购目录数据获取失败'));
  };

  sortOptions = data => {
    if (!data || !data.length) return [];
    const newData: any = [];
    for (let i = 0; i < data.length; i += 1) {
      const obj: any = {};
      const item = data[i];
      const { node } = item;
      obj.value = node.code;
      obj.label = `${node.code}${node.name}`;
      if (item.data && typeof item.data === 'object' && item.data.length > 0) {
        obj.children = this.sortOptions(item.data);
        if (this.props.useOnlyLeafNode) {
          obj.disableCheckbox = !!(obj.children?.length);
        }
      }
      newData.push(obj);
    }
    return newData;
  };

  handleOnChange = val => {
    this.props.onChange(
      val || undefined
    );
  };

  render() {
    const {
      value,
      editAble,
      changeOnSelect,
      useOnlyLeafNode,
      getPopupContainer = node => node.parentNode,
    } = this.props;
    const { treeList } = this.state;

    return (
      <CascaderComp
        canEdit={editAble}
        value={value}
        options={treeList}
        onChange={this.handleOnChange}
        cascaderProps={{
          changeOnSelect,
          isChangeForLeaf: useOnlyLeafNode,
          getPopupContainer,
        }}
      />
    ); 
  }
}
