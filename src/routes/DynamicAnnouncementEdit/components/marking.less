.marking-title-note {
  color: #999898;
  font-weight: normal;
  font-size: 14px;
}

.marking-type-note {
  color: #606060;
  padding: 5px 0;
  margin-bottom: 5px;
}

// .marking-tag {
//   display: inline-block;
//   user-select: none;
//   transition: all .3s cubic-bezier(.215, .61, .355, 1);
//   cursor: pointer;
//   white-space: nowrap;
//   line-height: 22px;
//   height: 30px;
//   padding: 4px 10px;
//   border-radius: 6px;
//   border: 1px solid #d9d9d9;
//   margin-right: 20px;
//   margin-bottom: 10px;
//   &.checked {
//     background-color: #3177fd;
//     border-color: #fff;
//     color: #fff;
//   }
//   &.disabled {
//     cursor: not-allowed;
//   }
// }
