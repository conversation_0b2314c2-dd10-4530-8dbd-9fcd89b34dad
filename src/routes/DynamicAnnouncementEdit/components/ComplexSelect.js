/* eslint-disable no-extra-boolean-cast */
import React, { Component } from 'react';
import {
  Input,
  Select,
} from 'doraemon';
import _ from 'lodash';
import SelectItem from './SelectItem';

const InputGroup = Input.Group;
const Option = Select.Option;

const PURCHASEPLANCODE = 'purchasePlan';

export default class ComplexSelect extends Component {
  state = {
    preSelect: '1',
  };
  onPreSelectChange = (e) => {
    const {
      // value = {},
      selectCode,
      valueCode,
    } = this.props;
    this.setState({
      preSelect: e,
    });
    this.onChange({
      // ...value,
      [selectCode]: e,
      [valueCode]: undefined,
    });
  }
  onSelectChange = (v) => {
    const {
      valueCode,
      selectCode,
      mode = '',
    } = this.props;
    if (mode === 'multiple') {
      this.onChange({
        [selectCode]: '1',
        [valueCode]: v,
      });
    } else {
      this.onChange({
        [selectCode]: '1',
        [valueCode]: [v],
      });
    }
  }
  onInputChange = (v) => {
    const {
      selectCode,
      valueCode,
    } = this.props;
    this.onChange({
      [selectCode]: '0',
      [valueCode]: [{
        label: v.target.value,
      }],
    });
  }
  onChange = (v) => {
    const {
      valueCode,
    } = this.props;
    if (!v[valueCode] || (Array.isArray(v[valueCode]) && v[valueCode].length === 0)) {
      this.props.onChange(undefined);
      return;
    }
    this.props.onChange(v);
  }
  renderComponent = () => {
    const {
      selectCode, // "是否"字段的 code
      valueCode,
      value = {},
      mode,
      ...rest
    } = this.props;
    const { preSelect } = this.state;
    let initValue;
    if (mode === 'multiple') {
      initValue = value[valueCode];
    } else {
      const label = _.get(value[valueCode], '[0].label', '');
      const key = _.get(value[valueCode], '[0].key', '');
      initValue = !!key ? {
        label,
        key,
      } : undefined;
    }
    const selectComp = (
      <SelectItem
        {...rest}
        style={{ width: 225 }}
        placeholder=""
        value={initValue}
        onChange={this.onSelectChange}
        mode={mode}
      />
    );
    const inputComp = (
      <Input
        placeholder="请输入"
        onChange={this.onInputChange}
        // value={_.get(value[valueCode], '[0].label', '') || undefined}
        value={this.formatContent(value[valueCode])}
        style={{
          minWidth: 225,
          width: 'auto',
        }}
      />
    );
    switch (value[selectCode] === undefined ? preSelect : value[selectCode]) {
    case '1':
      return selectComp;
    case '0':
      return inputComp;
    default:
      return selectComp;
    }
  }

  /**
   * @function formatContent
   * @param {Array} valueList
   * 用于转换当用户选择了“否“，并在 Input 中输入多个以逗号分隔的内容时，数据的回填问题
   */
  formatContent = (valueList) => {
    if (_.isEmpty(valueList)) {
      return undefined;
    }
    const result = [];
    valueList.forEach((item = {}) => {
      result.push(item.label);
    });
    return result.join(',');
  }

  render() {
    const {
      selectCode, // "是否"字段的 code
      value = {},
      getPopupContainer,
      isLockSelectOption = false, // 表单的配置
      extraParams: {
        isForcedControlConfigFromAnnouncement,
      },
      widgetCode,
    } = this.props;
    // 若为 是否关采购平台计划 则优先读取公告中心的配置
    const disabled =
      (widgetCode === PURCHASEPLANCODE)
        ? (isForcedControlConfigFromAnnouncement || isLockSelectOption)
        : isLockSelectOption;

    const { preSelect } = this.state;
    return (
      <InputGroup compact>
        <Select
          disabled={disabled}
          onChange={this.onPreSelectChange}
          style={{ width: 60 }}
          getPopupContainer={getPopupContainer}
          placeholder=""
          value={value[selectCode] === undefined ? preSelect : value[selectCode]}
        >
          <Option value="1">是</Option>
          <Option value="0">否</Option>
        </Select>
        {
          this.renderComponent()
        }
      </InputGroup>
    );
  }
}
