/* eslint-disable no-new-func */
import React, { Component } from 'react';
import { Select, request } from 'doraemon';
import _ from 'lodash';

const Option = Select.Option;
const getData = res => res.result;
const getItems = options => (options ? options.map(option => (
  <Option key={option.v} value={option.v}>{option.k}</Option>
)) : []);
const cacheKey = Symbol.for('cacheKey');
if (!window[cacheKey]) {
  window[cacheKey] = {};
}
const cache = window[cacheKey];

export default class SelectItem extends Component {
  state = {
    options: [],
    url: '',
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.url !== this.props.url) {
      this.loadData(nextProps.url);
    }
  }
  componentDidMount() {
    this.loadData();
  }
  componentWillUnmount() {
    this.setState = () => { };
  }
  loadData = async (nUrl) => {
    const { url, value } = this.props;
    let { getOptions = getData } = this.props;
    if (typeof getOptions === 'string') {
      getOptions = new Function('response, _', getOptions);
    }
    if (!nUrl && !url) {
      return;
    }
    const res = cache[nUrl || url] ? cache[nUrl || url] : await request(nUrl || url);
    cache[nUrl || url] = res;
    const options = getOptions(res, _);
    const optionsMap = this.dealData(options || []);
    if (value && nUrl && url && !optionsMap[value.key]) { // 重新加载数据之后如果下拉没有值，清空值
      this.props.onChange(undefined);
    }
    this.setState({
      options,
      url: nUrl || url,
    });
  }
  timmer = null;
  onSearch = (keyWord = '') => {
    if (this.timmer) {
      clearTimeout(this.timmer);
    }
    this.timmer = setTimeout(async () => {
      const { url } = this.state;
      const { remoteParams } = this.props;
      let { getOptions = getData } = this.props;
      if (typeof getOptions === 'string') {
        getOptions = new Function('response, _', getOptions);
      }
      const res = await request(url, {
        params: {
          [remoteParams]: keyWord,
        },
      });
      const options = getOptions(res, _);
      this.dealData(options || []);
      this.setState({
        options,
      });
    }, 700);
  }
  dealData = (opts) => {
    const {
      displayName = 'k',
      displayCode = 'v',
    } = this.props;
    const map = {};
    opts.map((opt) => {
      opt.k = _.get(opt, displayName);
      opt.v = _.get(opt, displayCode);
      map[opt.v] = opt;
    });
    return map;
  }
  render() {
    const { transform = getItems, search, value, ...rest } = this.props;
    const { options } = this.state;
    delete rest.url;
    delete rest.getOptions;
    // 前端搜索
    if (search === 'local') {
      rest.showSearch = true;
      rest.optionFilterProp = 'children';
    }
    // 远程搜索
    if (search === 'remote') {
      rest.showSearch = true;
      rest.filterOption = false;
      rest.onSearch = this.onSearch;
    }
    return (
      <Select
        value={(value === null) ? undefined : value}
        labelInValue
        {...rest}
      >
        {transform(options)}
      </Select>
    );
  }
}
