/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-03-04 14:30:58
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-08-21 15:54:45
 * @FilePath: /zcy-announcement-v2-front/src/routes/DynamicAnnouncementEdit/components
 * /ContractField/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Table, Button, Input } from 'doraemon';
import { cloneDeep } from 'lodash';
import './index.less';

const ContractField = (props) => {
  const { onChange } = props;
  const dataSource = props.value ? cloneDeep(props.value) : [];
  const onChangeTextArea = (e, index, key) => {
    const val = e.target.value;
    dataSource[index][key] = val;
    onChange([...dataSource]);
  };
  
  const columns = [{
    title: <span className="doraemon-form-item-required">合同名称</span>,
    dataIndex: 'contractName',
    key: 'contractName',
    render: (text, _, index) => {
      return (
        <Input.TextArea
          maxLength={200}
          defaultValue={text}
          placeholder="请输入"
          onChange={e => onChangeTextArea(e, index, 'contractName')}
        />
      );
    },
  }, {
    title: <span className="doraemon-form-item-required">合同编号</span>,
    dataIndex: 'contractNo',
    key: 'contractNo',
    render: (text, _, index) => {
      return (
        <Input.TextArea
          defaultValue={text}
          maxLength={200}
          placeholder="请输入"
          onChange={e => onChangeTextArea(e, index, 'contractNo')}
        />
      );
    },
  }, {
    title: <span className="doraemon-form-item-required">合同链接</span>,
    dataIndex: 'contractUrl',
    key: 'contractUrl',
    render: (text, _, index) => {
      return (
        <Input.TextArea
          defaultValue={text}
          maxLength={500}
          placeholder="请输入"
          onChange={e => onChangeTextArea(e, index, 'contractUrl')}
        />
      );
    },
  }, {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    render: (_, __, index) => {
      return (
        <a onClick={() => {
          dataSource.splice(index, 1);
          onChange(dataSource);
        }}
        >删除
        </a>
      );
    },
  }];
  
  
  return (
    <React.Fragment>
      <div className="contractField-buttonGroup">
        <Button type="secondary" 
          onClick={() => onChange([...dataSource, {
            contractName: '',
            contractNo: '',
            contractUrl: '',
          }])}
        >新增
        </Button>
      </div>
      <Table 
        rowKey="key"
        dataSource={(dataSource ?? []).map((ele, index) => ({
          ...ele,
          key: index,
        }))}
        pagination={false}
        columns={columns}
      />
    </React.Fragment>
  );
};

export default ContractField;
