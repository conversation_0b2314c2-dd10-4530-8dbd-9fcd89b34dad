import React from 'react';
import { fixNull } from 'src/utils/utils';
import {
  Panel,
  FormGrid,
} from 'doraemon';
import './index.less';

export default function PurchaseExtInfo({
  purchaseExtInfo = {},
}) {
  const PurchaseExtInfoConfig = [{
    label: '采购单位名称',
    render: () => {
      return fixNull(purchaseExtInfo.purchaseName);
    },
    show: true,
  }, {
    label: '采购单位编码',
    render: () => {
      return fixNull(purchaseExtInfo.purchaserOrgCode);
    },
    show: true,
  }, {
    label: '采购单位联系人',
    render: () => {
      return fixNull(purchaseExtInfo.purchaserContactPerson);
    },
    show: true,
  }, {
    label: '采购单位联系人电话',
    render: () => {
      return fixNull(purchaseExtInfo.purchaserContactPhone);
    },
    show: true,
  }, {
    label: '主管部门名称',
    render: () => {
      return fixNull(purchaseExtInfo.parentPurchaseName);
    },
    show: true,
  }, {
    label: '主管部门编码',
    render: () => {
      return fixNull(purchaseExtInfo.parentPurchaserOrgCode);
    },
    show: true,
  }, {
    label: '主管部门联系人',
    render: () => {
      return fixNull(purchaseExtInfo.parentPurchaserContactPerson);
    },
    show: true,
  }, {
    label: '主管部门联系人电话',
    render: () => {
      return fixNull(purchaseExtInfo.parentPurchaserContactPhone);
    },
    show: true,
  }]?.filter(({ show }) => show);

  return (
    <Panel
      title="单位信息"
      className="announcement-info-panel"
    >
      <FormGrid
        bordered
        formGridItem={PurchaseExtInfoConfig}
      />
    </Panel>
  );
}
