import React from 'react';
import { fixNull } from 'src/utils/utils';
import {
  Panel,
  FormGrid,
} from 'doraemon';
import TagLine from 'src/components/TagLine';
import './index.less';


const isXinJiang = (district = '') => {
  return /^65(\d+){4}$/.test(district);
};

const isBingTuan = (district = '') => {
  return /^66(\d+){4}$/.test(district);
};

const isTest = (district = '') => {
  return /^99(\d+){4}$/.test(district);
};

const getItemShowVal = (announcementType, value) => {
  if (announcementType === 10016) {
    if (!value) return false;
  }
  return true;
};


export default function AnnouncementInfo({
  annBasicInfo = {},
}) {
  const announcementConfig = [{
    label: '发布单位区划',
    render: () => {
      return fixNull(annBasicInfo.creatorOrgDistrictName);
    },
    show: true,
  }, {
    label: '公告区划',
    render: () => {
      return fixNull(annBasicInfo.districtName);
    },
    show: true,
  }, {
    label: '项目名称',
    render: () => {
      return fixNull(annBasicInfo.projectName);
    },
    show: getItemShowVal(annBasicInfo?.announcementType, annBasicInfo.projectName),
  }, {
    label: '项目编号',
    render: () => {
      return fixNull(annBasicInfo.projectCode);
    },
    show: getItemShowVal(annBasicInfo?.announcementType, annBasicInfo.projectCode),

  }, {
    label: '公告类型',
    render: () => {
      return fixNull(annBasicInfo.announcementTypeName);
    },
    show: true,
  }, {
    label: '采购方式',
    render: () => {
      return fixNull(annBasicInfo.procurementMethodName);
    },
    show: getItemShowVal(annBasicInfo?.announcementType, annBasicInfo.procurementMethodName),
  }, {
    label: '采购目录',
    render: () => {
      // 待花花发布完后可去除 gpCatalog
      return fixNull(annBasicInfo.gpCatalogName || annBasicInfo.gpCatalog);
    },
    show: getItemShowVal(annBasicInfo?.announcementType,
      annBasicInfo.gpCatalogName || annBasicInfo.gpCatalog),

  }, {
    label: '项目总额',
    render: () => {
      return fixNull(annBasicInfo.amount);
    },
    show: getItemShowVal(annBasicInfo?.announcementType, annBasicInfo.amount),
  }, {
    label: '发布单位',
    render: () => {
      return fixNull(annBasicInfo.creatorOrgName);
    },
    show: true,
  }, {
    label: '数据来源',
    render: () => {
      return fixNull(annBasicInfo.annSourceDesc);
    },
    show: true,
  }, {
    label: '单一来源编号',
    render: () => {
      return fixNull(annBasicInfo?.singleSourceCode);
    },
    show: annBasicInfo?.announcementType === 3012, // 仅单一来源的展示
  },
  {
    label: '联系人',
    render: () => {
      return fixNull(annBasicInfo?.contactPerson);
    },
    show: annBasicInfo?.announcementType === 10016 && (
      isXinJiang(annBasicInfo?.district) || 
      isBingTuan(annBasicInfo?.district) || 
      isTest(annBasicInfo?.district)
    ), // 采购意向
    key: 'contactPerson',
  }, {
    label: '联系电话',
    render: () => {
      return fixNull(annBasicInfo?.contactPhone);
    },
    show: annBasicInfo?.announcementType === 10016 && (
      isXinJiang(annBasicInfo?.district) || 
      isBingTuan(annBasicInfo?.district) || 
      isTest(annBasicInfo?.district)
    ), // 采购意向
    key: 'contactPhone',
  },
  ]?.filter(({ show }) => show);

  return (
    <Panel
      title="公告信息"
      className="announcement-info-panel"
    >
      <TagLine tags={(annBasicInfo?.announcementTagDtoList ?? [])} />
      <FormGrid
        bordered
        formGridItem={announcementConfig}
      />
    </Panel>
  );
}
