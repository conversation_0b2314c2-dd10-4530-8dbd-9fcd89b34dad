import React, { Component } from 'react';
import {
  <PERSON>cy<PERSON><PERSON><PERSON><PERSON>b,
  <PERSON>cy<PERSON>pin,
  Modal,
  Panel,
  Alert,
  message,
  request,
  Button,
  InputAmount,
  Progress,
} from '@zcy/doraemon';
// 
import { getQueryParamObj } from '@zcy/utils';
import { FormPageWrap, MainForm } from '@zcy/form-page';
import loadEditor from '@zcy/zcy-ueditor-front';
import { connect } from 'dva';
import _ from 'lodash';
import Sequence from 'src/utils/sequence';
import pushLog from 'src/utils/pushLog';
import tracer from 'src/utils/tracer';
import { fetchCurrentEnvApi } from 'src/common/services/app';
import { canSubmitAnnouncementGet } from 'src/api/announcement/api';
import { renderAlert, getSensitiveWords, attachSensitiveWordsTag, showSensitiveMsg, SensitiveEditorStyle, clearSensitiveTag } from 'src/utils/listSensitiveWords';
// 业务组件
import AnnSteps from '../components/AnnSteps';
import ReformationTag from 'src/components/ReformationTag';
import { useUploadMonitorEnterprisesModal } from '../components/UploadMonitorEnterprisesModal';
import FormUpload from '../../Flow/views/components/form-upload';
import TableExtraButton from '../components/PurchaseIntention/TableExtraButton';
// 自定义无相组件
import PurchaseIntentionTitle from '../components/PurchaseIntention/PurchaseIntentionTitle';
import TitleWithSuffix from '../components/TitleWithSuffix';
import ContractField from '../components/ContractField';
import ComplexSelect from '../components/ComplexSelect';
import GpCatalogCascader from '../components/GpCatalogCascader';
import PurchaseIntentionDetailDrawer from '../components/PurchaseIntention/PurchaseIntentionDetailDrawer';
// 
import { breadcrumbConfig } from '../configs/breadcrumb';
import { checkCanPublishAnnouncement } from '../services/common';
import { getShowExportButton, getExportAnnouncementInfoTask, pollingAnnouncementInfoTask } from '../services';
import { NO50ANN } from '../../Flow/configs/const';
import { formatUrlParams } from '../configs/utils';
import { FormPageWrapConfig } from '../configs/formConfig';
import { LcyDrainageModal } from '@/components/LcyDrainageModal';
import './index.less';
import handlerAttachmentContent from 'src/utils/handlerAttachmentContent';
import moment from 'moment';

const AMOUNT = 4000000;
const SINGLEANNOUNCEMENTCODE = '3012';

// 页面来源
const FORM_PAGE_MAP = {
  // 采购意向-单一来源公告
  // 仅用于确认返回按钮跳转链接,固定为列表页
  PURCHASE: 'PURCHASE',
  // 采购意向公告,控制无相表单-创建采购意向-意向公开明细组件
  PUBLIC_PURCHASE: 'PUBLIC_PURCHASE',
};

const CONTRACT_FIELD_DETAIL_LIST = [
  {
    key: 'contractName',
    name: '合同名称',
  },
  {
    key: 'contractNo',
    name: '合同编号',
  },
  {
    key: 'contractUrl',
    name: '合同链接',
  }
];

@connect(({ dynamicCreate, flow }) => ({
  dynamicCreate,
  flow,
}))
@FormPageWrap(FormPageWrapConfig)
export default class DynamicCreate extends Component {
  constructor(props) {
    super(props);
    this.routerInfo = {};// 路由参数信息
    this.state = {
      preview: false, // 用于标记页面状态，true：表单填写页面，false：富文本编辑器页面
      // eslint-disable-next-line react/no-unused-state
      editable: false, // 预览页是否可编辑
      supportCheckerMultiSelect: false, // 审核组件中审核人是否多选
      config: {}, // 公告配置参数
      hasRemind: false, // 是否为仅提醒型敏感程度
      hasBlock: false, // 是否为阻塞流程型敏感程度
      // 标题的禁止级敏感词
      sensitiveWords: [], // 敏感词集合
      supervisionInfo: {}, // 财政监管相关信息
      workflowInfo: {}, // 工作流条件相关信息
      isPubTypeDisabled: false, // 分布类型是否可编辑
      fetchHtmlTemplateLoading: false, // 获取预览公告页面主接口状态
      isSecondEdit: false, // 是否可二次编辑
      printContent: '', // 暂存无相表单print接口content
      isShowDrainageModal: false,
      whetherToSettleIn: false,
      isLeCaiYun: false, // 是否乐采云环境
    };
    this.tempAnnId = null;
    this.formPage = '';
    // /src/routes/DynamicAnnouncementEdit/configs/breadcrumb.js
    // putHandler、saveHandler、generatorHandler 在breadcrumb.js中使用
    this.putHandler = _.throttle(() => this.handleSubmit(true).catch((error) => {
      pushLog(JSON.stringify(error), 'error');
    }), 3000, {
      trailing: false,
    }); // 提交
    this.saveHandler = _.throttle(() => this.handleSubmit(false).catch((error) => {
      pushLog(JSON.stringify(error), 'error');
    }), 3000, {
      trailing: false,
    }); // 保存
    this.generatorHandler = _.throttle(() => this.onHandleCreate().catch((error) => {
      pushLog(JSON.stringify(error), 'error');
    }), 3000, {
      trailing: false,
    }); // 生成公告
  }

  whereDistrictShowReservedQuotaAttachmentRequest = async () => {
    try {
      const { result } = await request(
        `/announcement/api/config/form/needValidDistrictResPerAndAmount?announcementType=${this.routerInfo.announcementType}`,
      );
      this.districtPrefixList = result?.districtPrefixList || [];
      this.proportionLimit = Number(result?.proportionLimit || 0);
      this.smMonitorItemAmountLimit = Number(result?.smMonitorItemAmountLimit || 0);
    } catch (error) {
      this.districtPrefixList = [];
      this.proportionLimit = 0;
      this.smMonitorItemAmountLimit = 0;
    }
  }

  // 载入页面时,请求数据
  componentDidMount() {
    try {
      this.getRouterInfo();
      this.clearStore();
      this.getEnv();
      this.initEditor();
      this.fetchWorkflowInfo();
      this.getShowExport();
      this.whereDistrictShowReservedQuotaAttachmentRequest()
    } catch (error) {
      pushLog(JSON.stringify(error), 'error');
    }
  }

  componentWillUnmount() {
    try {
      window.UE?.delEditor('uEditor', {
        autoHeight: false,
      });
    } catch (error) {
      pushLog(JSON.stringify(error), 'info');
    }
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo: {
          fileList: [],
          defaultFileList: [],
        },
      },
    });
  }

  // 清除相关的缓存
  // 主要清空flow.fileInfo相关信息
  clearStore = () => {
    this.props.dispatch({
      type: 'flow/clearStore',
    });
  };

  // 设置资格预审业务附件默认值(成都区划)
  setDefalutBizEnclosure = () => {
    pushLog(JSON.stringify({
      msg: 'tag-setDefalutBizEnclosure',
    }), 'info');
    const { setFieldsValue, getFieldsValue } = this.props.form;
    const { businessAttachments } = getFieldsValue(['businessAttachments']);
    // 当该字段不为空时，无需设置默认值
    if (Array.isArray(businessAttachments) && !businessAttachments.length) {
      setFieldsValue({
        businessAttachments: [
          {
            attachmentType: {
              key: 'tenderPrequalificationAttach',
            },
          },
          {
            attachmentType: {
              key: 'tenderPrequalificationAttachPdf',
            },
          },
        ],
      });
    }
  };

  // 表单初始化成功之后调用
  onFormInit = async () => {
    // render接口返回的出参 customerParams
    const { data = {}, customerParams = {}, form: { setFieldsValue } } = this.props;
    const { district } = data;
    if (district.key) {
      this.handleNonGovDrainageModal(district.key);
    }
    try {
      Object.keys(data).forEach((formKey) => {
        // 过滤掉空数组及空对象
        if (['[]', '{}'].includes(JSON.stringify(data[formKey]))) {
          // TODO：如果没有相关日志信息可删除
          pushLog(JSON.stringify({
            msg: 'tag-存在[]、{}相关数据',
          }), 'info');
          setFieldsValue({
            [formKey]: undefined,
          });
        }
      });
    } catch (error) {
      pushLog(JSON.stringify(error), 'error');
    }

    const { success, result } = await this.fetchAnnConfig({
      announcementType: this.routerInfo.announcementType,
      districtCode: data?.district?.key,
    });

    if (success) {
      if (result?.secondEdit && this.actionType === 'edit') {
        this.setState({
          isSecondEdit: true,
        });
      }
    }

    this.getFixedTimeFlag(data?.district?.key);

    // 修改原有逻辑,新增生成公告调用单独监管信息接口,
    // 编辑页面调用详情接口返回的监管信息
    if (this.actionType === 'create') {
      this.fetchSupervisionInfo(data?.district?.key);
    }

    if (!_.isEmpty(customerParams.attachments)) {
      const defaultFileList = customerParams.attachments.map(item => ({
        ...item,
        noPublic: !item.isShow,
        status: 'done',
      }));
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo: {
            fileList: [],
            defaultFileList,
          },
        },
      });

    }
    if(!_.isEmpty(customerParams.supplementaryAttachments) && this.calculateIsShowReservedQuotaAttachment()) {
      const supplementaryAttachments = (customerParams.supplementaryAttachments || []).map(item => ({
        ...item,
        noPublic:!item.isShow,
        status: 'done',
      }));
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo3: {
            fileList3: [],
            defaultFileList3: supplementaryAttachments,
          },
        },
      });
    }
  }

  // 获取 url 上的信息
  getRouterInfo = () => {
    const { location, match } = this.props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    this.routerInfo = formatUrlParams(params);
    this.formPage = params.formPage;
    // actionType 用于判断是新增公告还是列表编辑页跳转过来的 'create' 新增 'edit' 编辑
    this.actionType = match.params.actionType;
  }

  // 初始化富文本编辑器
  initEditor = () => {
    try {
      loadEditor().then((res) => {
        if (res) {
          this.ueditor = window.UE.getEditor('uEditor', {
            allowDivTransToP: false,
            disabledTableInTable: false,
            // 注入敏感词提示css
            initialStyle: SensitiveEditorStyle,
          });
        }
        if (!res) return pushLog('初始化富文本编辑失败', 'warning');
      });
    } catch (error) {
      pushLog(JSON.stringify(error), 'info');
    }
  }

  // 获取环境信息
  getEnv = () => {
    fetchCurrentEnvApi().then((envRes) => {
      if (envRes?.success) {
        const tenant = _.get(envRes, 'result.tenant', '') || '';
        this.env = tenant;
      }
    });
  }

  // 获取公告配置参数
  fetchAnnConfig = async (data) => {
    const res = await request('/announcement/relation/getAnnouncementConfig', {
      method: 'post',
      data,
    });
    if (!res.success) {
      return res;
    }
  
    // expiryPeriod, // 失效时间
    // secondEdit, // 是否二次编辑
    // customizedTitle, // 是否自定义标题
    // titlePrefix, // 标题后缀
    // titleSuffix, // 标题前缀
    // isNotify, // 是否短信提醒
    // hasAttachment, // 附件是否必填
    // isForcedControl, // 是否关联平台采购计划的强控配置项
    // attachmentShow, // 附件是否展示

    this.setState({
      config: {
        ...this.state.config,
        ...(res.result || {}),
      },
    });
    return res;
  }
  // 设置UUId植
  setUUIdValue = (value, callback) => {
    // 这个流程有人在用，2024.01.12
    const {
      dispatch,
      form: { setFieldsValue },
    } = this.props;
    dispatch({
      type: 'dynamicCreate/setValuesApi',
      payload: {
        projectCode: value,
      },
    }).then((res) => {
      if (res.success && res.result.projectUid) {
        const { projectUid } = res.result;
        setFieldsValue({
          projectUid,
        });
      } else {
        setFieldsValue({
          projectUid: '',
        });
      }
      callback(res.error || undefined);
    }).catch(() => {
      callback();
    });
  }

  // 通过districtCode判断发布类型是否禁用/选择定时发布
  getFixedTimeFlag = async (data) => {
    try {
      const res = await request(`/announcement/open/getFixedTimeFlag?districtCode=${data}`);
      if (!res.success) {
        message.error(res.error);
        return;
      }
      // result: true不可选，false可选
      const { result } = res || {};
      this.setState({
        isPubTypeDisabled: result,
      });
      if (result) {
        const {
          form: {
            getFieldsValue,
          },
          setIniValues,
        } = this.props;
        setIniValues({
          ...getFieldsValue(),
          pubType: {
            key: '1',
            label: '定时发布',
          },
        });
      }
    } catch (res) {
      const { data: { error } = {} } = res || {};
      message.error(error);
    }
  }

  // 获取工作流条件参数
  fetchWorkflowInfo = () => {
    const {
      dispatch,
    } = this.props;
    dispatch({
      type: 'dynamicCreate/fetchWorkflowInfo',
      payload: {
        id: this.routerInfo?.annId,
        announcementType: this.routerInfo?.announcementType,
      },
    }).then((res) => {
      if (res?.success) {
        const infos = _.get(res, 'result', {}) || {};
        this.setState({
          workflowInfo: infos,
        });
      }
    });
  }

  /**
   * 获取财政部门（监管）相关信息
   * 如果对应的字段返回值了，则该字段会被禁用（配合表单中心一起配置）
   * 区划修改时，这个也要重新获取
   */
  fetchSupervisionInfo = (districtCode) => {
    const {
      dispatch,
      form: {
        setFieldsValue,
      },
    } = this.props;
    dispatch({
      type: 'dynamicCreate/fetchSupervision',
      payload: {
        districtCode:
          districtCode || this.routerInfo.districtCode || window.currentUserDistrict.code,
        announcementType: this.routerInfo.announcementType,
      },
    }).then((res) => {
      if (res && res.success) {
        const infos = _.get(res, 'result.envs', {}) || {};
        this.setState({
          supervisionInfo: infos,
        });
        setFieldsValue(infos);
      }
    });
  }

  // 生成公告调用接口获取 HTML 片段
  // print接口
  fetchHtmlTemplate = (formData) => {
    const { dispatch, formStructData = {} } = this.props;
    const { code, version: viewVersion, pageCode } = formStructData;
    const curExtParam = {
      districtCode: formData?.district?.key,
      announcementType: this.routerInfo.announcementType,
      isReformation: this.routerInfo.isReformation,
      isReformationEnglishAnnouncement: this.routerInfo.isReformationEnglishAnnouncement,
      annSmallMediumFlag: this.routerInfo.annSmallMediumFlag,
      smallAnnouncementYear: this.routerInfo.smallAnnouncementYear,
    };
    return dispatch({
      type: 'dynamicCreate/printFormDataOss',
      payload: {
        formData,
        viewCode: code,
        viewVersion,
        printRetType: 'html',
        pageCode,
        extParam: curExtParam,
      },
    });
  }

  // 校验当天是否为工作日，若为非工作日，则给予提示
  validateIsWorkday = (date, districtCode) => {
    request(
      `/announcement/api/checkWorkDay?districtCode=${districtCode}&date=${date}`
    ).then((res) => {
      if (res && res.success) {
        if (!res.result) {
          Modal.warning({
            content: '当前选择日期为非工作日',
          });
        }
      }
    });
  }

  // 审核提交是否多选
  getAuditConfig = async (districtCode) => {
    const res = await request('/announcement/config/getAnnouncementCheckConfig', {
      params: {
        districtCode:
          districtCode || this.routerInfo.districtCode || window.currentUserDistrict.code,
        announcementType: this.routerInfo.announcementType,
      },
    });
    this.setState({
      supportCheckerMultiSelect: _.get(res, 'result.supportCheckerMultiSelect', false) || false,
    });
  }

  /**
   * 为了表单数据回填等操作方便，富文本编辑器页面和表单填写页面使用同一页面
   * 通过 display：none 来操控互相隐藏
   * preview，用于标记页面状态
   * true：富文本编辑器页面
   * false：表单页面
   */
  changePageStatus = () => {
    this.setState({
      preview: !this.state.preview,
    });
  }

  getAlertInfo = () => {
    const {
      form: { getFieldsValue },
    } = this.props;
    // 是否为浙江省
    const {
      district = {},
    } = getFieldsValue(['district']);
    const { config = {} } = this.state;
    const regRule = /^(33)/;
    const isZJProvince = regRule.test(district.key);
    // 是否为特殊的公告类型
    const isSpecialAnn = !!NO50ANN.includes(this.routerInfo.announcementType);
    if (isZJProvince && isSpecialAnn && !!config.secondEdit) {
      return (
        <Alert
          message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，请遵循模板要求勿自行调整，否则将影响相关公告发布！"
          type="warning"
          className="editor-alert-info"
        />
      );
    }
    if (!config.secondEdit) {
      return (
        <Alert
          message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，公告样式及内容不允许修改！"
          type="warning"
          className="editor-alert-info"
        />
      );
    }
  }

  /**
   * 无相表单调用，render提示弹窗
   */
  renderMsgModal = ({
    type = 'warning',
    title,
    content,
  }) => {
    return Modal[type]({
      title,
      content,
    });
  }

  // 无相表单-采购意向公开公告
  renderPurchaseIntentionExtraButton = (schema, showAddBtn = false, isAutoFreshSectionNo = false) => {
    const { dataFormat, form } = this.props;
    // 兼容代码, 待无相支持动态新增控制按钮后删除
    // __todo__
    if (this.formPage === FORM_PAGE_MAP.PUBLIC_PURCHASE) {
      const dom = document.querySelector('.FormDivItempublicNoticeOfPurchaseIntentionDetailTable .doraemon-btn-secondary');
      if (dom?.style) {
        dom.style.display = 'none';
      }
      return null;
    }
    return <TableExtraButton 
      showAddBtn={showAddBtn}
      schema={schema}
      dataFormat={dataFormat}
      form={form}
      insertOrUpdateIntentionDetailData={this.insertOrUpdateIntentionDetailData}
      isAutoFreshSectionNo={isAutoFreshSectionNo}
    />;
  }

  // 无相表单-中小企业
  // 目前仅贵州会传入env值。
  // env === 'gz' 区分业务逻辑分流
  handleUploadMonitorEnterprisesModal = (schema, env) => {
    const { dataFormat, form } = this.props;
    const { setFieldsValue, getFieldsValue } = form;
    const values = getFieldsValue();
    const [open] = useUploadMonitorEnterprisesModal({
      visible: true,
      year: values.year,
      orgId: values.purchaseId,
      orgName: values.purchaseName,
      env,
    });
    open({
      onSubmit: (data) => {
        const v = getFieldsValue();
        const update = () => {
          const formatData = data.map(ele => ({
            ...ele,
            reservedType: ele.reservedType ? {
              label: ele.reservedType.label,
              key: ele.reservedType.key,
            } : undefined,
          }));
          dataFormat({
            [schema.code]: formatData,
          }, schema).then((res) => {
            const { code } = schema || {};
            setFieldsValue({
              [code]: res[code] || [],
            });
          });
        };
        if (v?.monitorReservedProjectInfo?.length) {
          return Modal.confirm({
            title: '是否覆盖当前项目数据？',
            onOk: () => {
              update();
            },
          });
        }
        return update();
      },
    });
  }

  handleMonitorEnterprisesCalc = () => {
    // 计算公示公告中的预留项目明细
    Modal.confirm({
      content: '是否覆盖中小&小微企业预留金额及占比？',
      onOk: () => {
        const { form } = this.props;
        const { setFieldsValue } = form;

        let monitorAmount = this.renderMonitorEnterprisesReservedItemsNoticeByKey('moitorItemAmount') || '';
        let smallAmount = this.renderMonitorEnterprisesReservedItemsNoticeByKey('smMonitorItemAmount') || '';
        let smallPercent = this.renderMonitorEnterprisesReservedItemsNoticeByKey('reservedPercent') || '';
  
        monitorAmount = monitorAmount === '-' ? '' : monitorAmount;
        smallAmount = smallAmount === '-' ? '' : smallAmount;
        smallPercent = smallPercent === '-' ? '' : smallPercent;
  
        setFieldsValue({ monitorAmount, smallAmount, smallPercent });
      },
    });
  }

  // 面向中小企业预留项目执行情况公告 - 获取导出按钮展示状态
  getShowExport = async () => {
    const res = await getShowExportButton({ annId: this.routerInfo.annId });
    this.setState({
      showExportButton: res.success ? res.result : false,
    });
  }

  pollingAnnouncementInfo = async (taskUuid, modal) => {
    const res = await pollingAnnouncementInfoTask({ taskUuid });
    if (res?.success && res?.result?.exportFileUrl) {
      modal?.update({ content: <Progress size="small" percent={100} /> });
      modal?.destroy();

      this.setState({ loading: false });
      return Modal.success({ 
        title: '数据导出成功，请下载',
        okText: '下载',
        closable: true,
        onOk: () => {
          window.open(res?.result?.exportFileUrl, '_blank');
        },
      });
    } else {
      setTimeout(() => {
        this.pollingAnnouncementInfo(taskUuid);
      }, 500);
    }
  }

  // 面向中小企业预留项目执行情况公告 - 导出
  handleOnExport = async () => {
    try {
      this.setState({ loading: true });
      const res = await getExportAnnouncementInfoTask({ annId: this?.routerInfo?.annId });
  
      if (res.success) {
        const modal = Modal.info({
          className: 'sme-export-modal',
          title: '正在导出数据，请勿关闭当前页面',
          content: <Progress size="small" percent={0} />,
        });
        // 等待 modal 实例创建后再调用
        setTimeout(() => {
          this.pollingAnnouncementInfo(res.result.taskUuid, modal);
        }, 500);
      } else {
        throw new Error('获取导出任务失败');
      }
    } catch (error) {
      this.setState({ loading: false });
      message.error('导出失败');
    }
  }

  // 无相表单-面向中小企业预留项目执行情况公告-面向中小企业预留项目明细-右上角按钮组-渲染调用 
  renderMonitorEnterprisesReservedItemsNotice = (schema, env) => {
    const { config = {} } = this.state;
    const { enableSummaryCalculation = false } = config;
    return (
      <React.Fragment>
        <Button type="secondary"
          style={{ marginRight: 10 }}
          onClick={() => this.handleUploadMonitorEnterprisesModal(schema, env)}
        >快速导入项目明细
        </Button>
        {
          enableSummaryCalculation ? (
            <Button type="secondary"
              style={{ marginRight: 10 }}
              onClick={() => this.handleMonitorEnterprisesCalc(schema)}
            >汇总计算
            </Button>
          ) : null
        }
      </React.Fragment>
    );
  }

  // 无相表单-面向中小企业预留项目执行情况公告-面向中小企业预留信息-渲染调用 & 采购意向公告-预算金额-渲染调用
  renderMonitorEnterprisesReservedItemsNoticeByKey = (key) => {
    const { form } = this.props;
      const { getFieldValue } = form;
      const calcSumByMonitorReservedProjectInfo = (k) => {
      const monitorReservedProjectInfo = getFieldValue('monitorReservedProjectInfo') || getFieldValue('publicNoticeOfPurchaseIntentionPurchaseTargetTable');
      if (!monitorReservedProjectInfo) return '-';
      if (monitorReservedProjectInfo.length === 0) return '-';
      const total = monitorReservedProjectInfo.reduce((sum, cur) => {
        const val = cur[k];
        if (val === undefined || val === '') return sum;
        sum += +val;
        return sum;
      }, 0);
      return total.toFixed(2);
    };

    if (['moitorItemAmount', 'smMonitorItemAmount','bidBudgetPrice'].includes(key)) {
      return calcSumByMonitorReservedProjectInfo(key);
    }

    if (['reservedPercent'].includes(key)) {
      const monitorAmount = calcSumByMonitorReservedProjectInfo('moitorItemAmount');
      const smallAmount = calcSumByMonitorReservedProjectInfo('smMonitorItemAmount');
      if (monitorAmount === '-' || +monitorAmount === 0 || smallAmount === '-') {
        return '-';
      }
      return ((100 * smallAmount) / monitorAmount).toFixed(2);
    }
  }

  // 无相表单-面向中小企业预留项目执行情况公告-渲染调用 
  renderContractFieldDetail = (values) => {
    if (!values || values.length === 0) return '-';
    return (
      <React.Fragment>
        {
          values.map((ele, index) => ({
            ...ele,
            key: index,
          })).map((ele, idx) => (
            <div key={ele.index}>
              {
                idx === 0 ? null : (
                  <p style={{ background: '#ccc', height: '1px', margin: '8px 0' }} />
                )
              }
              <p>
                <span>合同名称：{ele.contractName || '-'}</span>
              </p>
              <p>
                <span>合同编号：{ele.contractNo || '-'}</span>
              </p>
              <p>
                <span>合同链接：{ele.contractUrl || '-'}</span>
              </p>
            </div>
          ))
        }
      </React.Fragment>
    );
  }

  // 面包屑返回
  handelBack = () => {
    /**
     * preview 页面状态
     * true：富文本编辑器页面，false：表单填写页面
     * 通过区分 preview 来区分两个返回按钮
     *
     * actionType: 页面操作来源
     * create：点击创建公告进来的
     * edit：点击编辑进来的
     */
    const {
      config = {},
    } = this.state;
    if (this.actionType === 'create' || (this.actionType === 'edit' && !config.secondEdit)) {
      if (!this.state.preview) {
        Modal.confirm({
          title: '确认离开当前页面？',
          content: '如果返回，您更改的数据将会丢失',
          onOk: () => {
            this.goPageBack();
          },
        });
      } else {
        this.changePageStatus();
      }
    }

    if (this.actionType === 'edit' && !!config.secondEdit) {
      this.goPageBack();
    }
  }

  goPageBack = (announcementId) => {
    const { history,
      form: { getFieldsValue },
    } = this.props;
    if (this.formPage === FORM_PAGE_MAP.PURCHASE) {
      const url = '/announcement/list/3012';
      history.replace(url);
      return;
    }
    if (history.length <= 1 && announcementId) {
      const {
        district = {},
      } = getFieldsValue(['district']);
      const districtId = district.key;
      history.replace(
        `/dynamic/detail?announcementType=${this.routerInfo.announcementType}&formPageCode=${this.routerInfo.pageCode}&annId=${announcementId}&districtId=${districtId}&annBigType=${this.routerInfo.annBigType}`
      );
      return;
    } 
    history.goBack();
  }

  // 处理采购意向公告标题
  handleTitle = (annTitle) => {
    if (_.isEmpty(annTitle)) {
      return;
    }
    let title = '';
    if (annTitle.startMonth === annTitle.endMonth) {
      title = `${annTitle.org}${annTitle.year}年${annTitle.startMonth}月${annTitle.batch || ''}政府采购意向`;
    } else {
      title = `${annTitle.org}${annTitle.year}年${annTitle.startMonth}月至${annTitle.endMonth}月${annTitle.batch || ''}政府采购意向`;
    }
    return title;
  }

  validateAndFormatSubmitData = async () => {
    return new Promise(async (resolve) => {
      const {
        form: {
          validateFieldsAndScroll,
        },
      } = this.props;
      const {
        config = {},
      } = this.state;
      let pass = false;
      let errorMes;
      let formData;

      const { getFieldValue } = this.props.form;
      const res = await getSensitiveWords({
        announcementType: this.routerInfo.announcementType,
        content: getFieldValue('title'),
        district: getFieldValue('district').key,
      });
      if (!res) return;
      this.setState({
        sensitiveWords: res.blockWords,
      }, () => {
        validateFieldsAndScroll({ force: true }, (error, values) => {
          if (this.routerInfo.pageCode === 'winningBidNotice') {
            // 上海中标成交结果校验
            if (!values.GoodsInfoTab && !values.infoOfEngSubMatter && !values.infoOfServiceObject) {
              errorMes = `${['货物类主要标的信息', '工程类主要标的信息', '服务类主要标的信息'].join('、')}三个中至少必须填写一个！`;
              pass = false;
              resolve({
                pass,
                formData,
                errorMes,
              });
              return;
            }
            // 上海中标成交结果校验
            if (
              (values.GoodsInfoTab || values.infoOfEngSubMatter || values.infoOfServiceObject) &&
              !values.winningBidItemInfo) {
              errorMes = '中标/成交结果不可为空!';
              pass = false;
              resolve({
                pass,
                formData,
                errorMes,
              });
              return;
            }
          }
  
          // 【面向中小企业预留项目执行情况公告】中面向中小企业预留项目明细中的合同信息的值做必填校验
          if (this.routerInfo.pageCode === 'monitorEnterprisesReservedItemsNotice') {
            values?.monitorReservedProjectInfo?.forEach((i) => {
              i.contractInfo?.forEach((item) => {
                CONTRACT_FIELD_DETAIL_LIST.forEach(({ key, name }) => {
                  const value = item?.[key];
                  if (value === null || value === undefined || value === '') {
                    errorMes = `面向中小企业预留项目明细中的合同信息内的${name}的值不可为空!`;
                    pass = false;
                    resolve({
                      pass,
                      formData,
                      errorMes,
                    });
                  }

                  const { getFieldsValue } = this.props.form;


                  let isNeedValidate = false;
                  const { district } = getFieldsValue(["district"]);
                  // 查看是否命中区划
                  if (
                    this?.districtPrefixList?.some((districtCode) =>
                      district?.key?.startsWith(districtCode)
                    )
                  ) {
                    isNeedValidate = true;
                  }
                  const specialReg = /^[!@#$%^&*()_+=\[\]{};':"\\|,.<>/?，。！？；：""''（）【】《》]+$/
                  const linkReg = /^(?:(?:http|https):\/\/)?(?:www\.)?(?:([\w-]+\.)+[\w-]+|(?:\d{1,3}\.){3}\d{1,3})(?::\d{1,5})?(?:\/[^?#]*)?(?:\?[^#]*)?(?:#.*)?$/i;

                  if ((key === "contractName" || key === "contractNo") && isNeedValidate) {
                    if(specialReg.test(value)) {
                      errorMes = `面向中小企业预留项目明细中的合同信息内的${name}的值不可包含特殊字符!`;
                      pass = false;
                      resolve({
                        pass,
                        formData,
                        errorMes,
                      });
                    }
                  }
                  
                  if(key === "contractUrl" && isNeedValidate) {
                    if(!linkReg.test(value)) {
                      errorMes = `面向中小企业预留项目明细中的合同信息内的${name}格式不正确!`;
                      pass = false;
                      resolve({
                        pass,
                        formData,
                        errorMes,
                      });
                    }
                  }
                });
              })
            });
          }

          if (error) {
            pass = false;
            resolve({
              pass,
              formData,
              errorMes,
            });
          }
          let title = values.title;
          // 采购意向公告需要拼一个 title 给后端
          if (!_.isEmpty(values.annTitle)) {
            title = this.handleTitle(values.annTitle);
          }

          // 附件处理（附件走原来逻辑，不通过表单配置）
          const fileList = [
            ...this.props.flow.fileInfo.defaultFileList,
            ...this.props.flow.fileInfo.fileList,
          ];
          // 检测是否存在上传中的文件
          const uploadingFileNumber = fileList.filter(item => item.status === 'uploading').length;
          if (uploadingFileNumber !== 0) {
            errorMes = `还有${uploadingFileNumber}个文件在上传中，请等待文件上传完毕！`;
            pass = false;
            resolve({
              pass,
              formData,
              errorMes,
            });
          }
          // 文件是必填项，则验证是否有已成功上传的文件
          const validFile = fileList.filter(item => item.status === 'done') || [];
          // 安徽定制化需求:单一来源预算总金额(元) 大于等于400万必填
          if (this.routerInfo.announcementType === SINGLEANNOUNCEMENTCODE && this.env === 'ah' && config.attachmentShow) {
            if (Number(values?.totalBudgetPrice) >= AMOUNT && validFile.length === 0) {
              errorMes = '请上传文件！';
              pass = false;
              resolve({
                pass,
                formData,
                errorMes,
              });
            }
          } else if (Boolean(config.hasAttachment) && validFile.length === 0) {
            errorMes = '请上传文件！';
            pass = false;
            resolve({
              pass,
              formData,
              errorMes,
            });
          }

          // 所有附件均发至外网
          if (config.attachmentExternalDisplayRules === 2
             && validFile.some(file => file.noPublic)) {
            errorMes = '请勾选外网展示！';
            pass = false;
            resolve({
              pass,
              formData,
              errorMes,
            });
          } 

          const attachments = [];
          validFile.forEach((fl) => {
            attachments.push({
              fileId: fl.fileId,
              isShow: !(fl.noPublic === true),
              name: fl.name,
              size: fl.size,
            });
          });

          const supplementaryAttachments = []
          try {
            if(this.calculateIsShowReservedQuotaAttachment()) {
              const fileList3 = [
               ...this.props.flow.fileInfo3.defaultFileList3,
               ...this.props.flow.fileInfo3.fileList3,
              ]
              const uploadingFileNumber = fileList3.filter(item => item.status === 'uploading').length;
              if (uploadingFileNumber !== 0) {
                errorMes = `还有${uploadingFileNumber}个文件在上传中，请等待文件上传完毕！`;
                pass = false;
                resolve({
                  pass,
                  formData,
                  errorMes,
                });
              }
              const alreadyUploadFiles = fileList3.filter(item => item.status === 'done') || [];
              if(config?.attachmentShow && alreadyUploadFiles.length === 0) {
                errorMes = '请上传未达到规定预留份额比例的情况说明的附件！';
                pass = false;
                resolve({
                  pass,
                  formData,
                  errorMes,
                });
                
              }
              alreadyUploadFiles.forEach((fl) => {
                supplementaryAttachments.push({
                  fileId: fl.fileId,
                  isShow: !(fl.noPublic === true),
                  name: fl.name,
                  size: fl.size,
                  fileCode: 'percentIrregularityStateAttachments',
                  fileDesc: '未达到规定预留份额比例的情况说明'
                });
              });
            }
          } catch (error) {
          }


          formData = {
            ...values,
            title,
            attachments,
            supplementaryAttachments
          };
          pass = true;

          resolve({
            pass,
            formData,
            errorMes,
          });
        });
      });
    });
  }
  // 判断弹窗是否 生成公告
  onHandleCreate = async () => {
    // 显示乐彩云弹窗代码
    // const { getFieldValue } = this.props.form;
    // const districtCode = getFieldValue('district').key;
    // await this.handlePopupAndSubmit(
    //   districtCode, 
    //   () => {
    if (this.state.isSecondEdit) {
      Modal.confirm({
        title: '将使用最新数据覆盖公告内容，是否继续？',
        content: '',
        onOk: () => {
          this.handleCreate();
        },
      });
    } else {
      this.handleCreate();
    }
    //   }
    // );
  }
  // 生成公告
  handleCreate = async () => {
    if (this.state.fetchHtmlTemplateLoading) return;
    this.setState({
      fetchHtmlTemplateLoading: true,
    });

    const seq = new Sequence();

    seq.use(async (next) => {
      const { pass, formData, errorMes } = await this.validateAndFormatSubmitData();
      if (pass) {
        next(formData);
      } else {
        message.error(errorMes || '请完善信息！');
        this.setState({
          fetchHtmlTemplateLoading: false,
        });
      }
    });

    seq.use(async (next, formData) => {
      const { error, result } = await checkCanPublishAnnouncement({
        district: formData?.district?.key,
        annBigType: this?.routerInfo?.annBigType,
      });
      if (result) {
        next(formData);
      } else {
        message.error(error);
        this.setState({
          fetchHtmlTemplateLoading: false,
        });
      }
    });

    seq.use(async (next, formData) => {
      const configRes = await this.fetchAnnConfig?.({
        announcementType: this.routerInfo.announcementType,
        districtCode: formData?.district?.key,
        metadata: formData,
      }) || {};
      if (configRes.success) {
        next({ formData, configRes });
      } else {
        this.setState({
          fetchHtmlTemplateLoading: false,
        });
        message.error(configRes.error);
      }
    });

    seq.use(async (next, { formData, configRes }) => {
      const announcementType = this.routerInfo.announcementType;
      const { config = {} } = this.state;
      const { enableSummaryCalculation = false } = config;

      // 意向公开（53号文透明度规划）校验逻辑
      if (+announcementType === 10016, (formData || {})?.hasOwnProperty('isNo53Document')) {
        // pubType: 1：定时发布；2：审核完成发布；3：手动发布
        const { releasedAt, projectDetailList } = formData;
        const releasedMoment = _.isNil(releasedAt) ? moment() : moment(releasedAt);

        const checkRes = projectDetailList?.some((item) => {
          const itemMoment = moment(item?.estimatedPurchaseTime);
          return releasedMoment?.month() === itemMoment?.month();
        });

        if (checkRes) {
          return Modal.warning({
            okCancel: true,
            title: '原则上意向公开时间不得晚于采购活动开始前30天，请确认是否继续发布公告',
            onOk: () => {
              next({ formData, configRes });
            },
            onCancel: () => {
              this.setState({
                fetchHtmlTemplateLoading: false,
              });
            },
          });
        }
      }

      // 面向中小企业预留项目执行情况公告
      if (+announcementType === 14001 && enableSummaryCalculation) {
        const monitorAmount = this.renderMonitorEnterprisesReservedItemsNoticeByKey('moitorItemAmount');
        const smallAmount = this.renderMonitorEnterprisesReservedItemsNoticeByKey('smMonitorItemAmount');
        const smallPercent = this.renderMonitorEnterprisesReservedItemsNoticeByKey('reservedPercent');
        if (
          +monitorAmount === +formData.monitorAmount && 
          +smallAmount === +formData.smallAmount && 
          +smallPercent === +formData.smallPercent
        ) {
          return next({ formData, configRes });
        } else {
          return Modal.confirm({
            content: '面向中小、小微企业预留总金额及占比，与项目明细汇总结果不一致，存在被质疑风险，是否继续？',
            onOk: () => {
              next({ formData, configRes });
            },
            onCancel: () => {
              this.setState({
                fetchHtmlTemplateLoading: false,
              });
            },
          });
        }
      }

      next({ formData, configRes });
    });

    seq.exec((next, { formData, configRes }) => {
      // 非采购意向公告，需要调用获取参数接口，拿到公告标题前缀，并将值注入到 title 字段
      // title 字段是表单中定义的 code，不建议修改，因为这边的代码里写死了是向 “title” 字段注入
      // 公告标题的后缀，可以直接在表单中心配置，不读取接口返回值
      this.props.form?.setFieldsValue({
        title: _.get(configRes, 'result.titlePrefix', '') || '',
      });
      // 生成公告给出后端提示信息,阻塞生成
      this.fetchHtmlTemplate(formData).then((res = {}) => {
        const printContent = res?.result?.htmlContext || '';
        if (res.success) {
          this.listSensitiveWords({
            title: formData.title,
            englishTitle: formData.englishTitle,
            content: printContent,
            district: formData.district.key,
            type: 'handleCreate',
          }).then((isValid) => {
            if (!isValid) return;
            this.changePageStatus();
          });
          this.setState({
            printContent,
          });
        }
      }).finally(() => {
        this.setState({
          fetchHtmlTemplateLoading: false,
        });
      });
    });
  }

  // true：提交 false：暂存
  handleSubmit = async (isSubmit) => {
    try {
      tracer({
        utmCD: ['cBreadcrumb', isSubmit ? 'dSubmitBtn' : 'dSaveBtn'],
        business: {
          keyWord: this.state.annBigType,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
    const {
      submit,
      save,
      processDefineKey,
    } = this.props;
    const {
      config = {},
    } = this.state;

    const { pass, formData, errorMes } = await this.validateAndFormatSubmitData();
    if (pass) {
      await this.getAuditConfig(formData.district.key);
      const content = this.ueditor.getContent();
      this.listSensitiveWords({
        title: formData.title,
        englishTitle: formData.englishTitle,
        content,
        district: formData.district.key,
      }).then(async (isValid) => {
        if (!isValid) return;
        const { supportCheckerMultiSelect, printContent } = this.state;
        const submitContent = clearSensitiveTag(content);
        if (!(submitContent?.length)) {
          pushLog(JSON.stringify({
            type: '手工公告-content为空（submitContent）',
          }), 'warning');
        }
        if (isSubmit) {
          // 将外网展示的附件拼到公告正文中，仅在提交操作的时候需要拼
          const needShowFiles = formData.attachments.filter(fl => fl.isShow).concat(formData.supplementaryAttachments);
          let finalContent = submitContent;
          if (!_.isEmpty(needShowFiles)) {
            finalContent = await handlerAttachmentContent(
              finalContent, 
              needShowFiles, 
              Boolean(Number(this.routerInfo.isReformationEnglishAnnouncement)),
              this.calculateIsShowReservedQuotaAttachment()
            );
          }
          if (!(finalContent?.length)) {
            pushLog(JSON.stringify({
              type: '手工公告-content为空（finalContent）',
            }), 'warning');
          }

          if (this.actionType === 'create' && this.tempAnnId) {
            const canSubmitRes = await canSubmitAnnouncementGet({
              announcementId: this.tempAnnId,
            });
            if (!canSubmitRes?.success) return;
          }

          if (this.actionType === 'edit') {
            const canSubmitRes = await canSubmitAnnouncementGet({
              announcementId: this.routerInfo.annId,
            });
            if (!canSubmitRes?.success) return;
          }
          
          submit(
            {
              config: {
                processDefineKey,
              },
              isUserRadio: !supportCheckerMultiSelect,
              showUpload: false,
              tips: config.isNotify ? (
                <Alert
                  message="本公告已开启短信通知服务，请于本公告发布成功后，在列表页选择通知的对象。"
                  type="warning"
                  style={{ marginBottom: '10px' }}
                />
              ) : undefined,
            },
            {
              content: finalContent,
              attachments: formData.attachments,
              supplementaryAttachments: formData?.supplementaryAttachments || [],
              title: formData.title,
              annAppCode: this.routerInfo.annAppCode,
              orderId: this.routerInfo.orderId,
              originContent: printContent,
              englishTitle: formData.englishTitle,
            },
            {
              callback: (res) => {
                if (res?.success) {
                  // 公告发布成功后，跳转到列表页
                  this.goPageBack(res.result);
                }
              },
            }
          );
        } else {
          save({
            content: submitContent,
            attachments: formData.attachments,
            supplementaryAttachments: formData?.supplementaryAttachments || [],
            title: formData.title,
            annAppCode: this.routerInfo.annAppCode,
            orderId: this.routerInfo.orderId,
            originContent: printContent,
            englishTitle: formData.englishTitle,
          }, {
            needValidate: true,
            callback: (res) => {
              if (res?.success && res.result) {
                this.tempAnnId = res.result;
              }
            },
          });
        }
      });
    } else {
      message.error(errorMes || '请完善信息！');
    }
  }

  // 敏感词校验 + 富文本内容处理及渲染
  listSensitiveWords = ({ title, englishTitle, content, district, type }) => {
    return getSensitiveWords({
      announcementType: this.routerInfo.announcementType,
      content: `${title}--${englishTitle}--${content}`,
      district,
    }).then((res) => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;

      const {
        contentWithSensitiveTag,
      } = attachSensitiveWordsTag(blockWords, remindWords, content);

      this.ueditor.setContent(contentWithSensitiveTag);
      if (!this.state.config?.secondEdit) {
        this.ueditor.setDisabled('fullscreen');
      }
      this.setState({
        hasRemind,
        hasBlock,
      }, () => {
        // 敏感词信息更新之后触发一下校验
        this.props.form.validateFieldsAndScroll({ force: true });
      });

      // 如果是生成公告校验敏感词时，不需要 message 提示
      if (type === 'handleCreate') {
        return Promise.resolve(true);
      }
      if (hasBlock || hasRemind) {
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }
      return Promise.resolve(true);
    });
  }
  // 标题校验方法，给页面配置中调用的
  validateTitle = (title, callback) => {
    const { sensitiveWords = [] } = this.state;
    const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
      if (title.includes(cur.emitWord)) {
        arr.push(cur.emitWord);
      }
      return arr;
    }, []);
    if (titleSensitiveWords.length) {
      callback(new Error(`请修改敏感词：${titleSensitiveWords.join(',')}`));
    }
  }

  // 效验时间差值不大于五天
  validateDateDifferenceValue = (date, distCode, type) => {
    const { dispatch, location, form: { getFieldValue, setFieldsValue } } = this.props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    const { announcementType } = params;
    setFieldsValue({ [type]: date });
    const saleStartDate = getFieldValue('saleStartDate');
    const saleEndDate = getFieldValue('saleEndDate');
    const submitEndDate = getFieldValue('submitEndDate');
    dispatch({
      type: 'dynamicCreate/validateDateDifferenceValueApi',
      payload: {
        saleStartDate,
        saleEndDate,
        submitEndDate,
        districtCode: distCode,
        announcementType,
      },
    }).then((res = {}) => {
      if (res.result) {
        const { validateResult } = res.result;
        if (validateResult) {
          message.error(validateResult);
        }
      }
    });
  }


  handlePopupAndSubmit = async (districtCode, callback) => {
    try {
      const { dispatch } = this.props;
      const res = await dispatch({
        type: 'flow/handleNonGovernmentProcurement',
        payload: {
          districtCode,
          announcementType: this.routerInfo.announcementType,
          ifManualAnnouncement: this.actionType === 'create' ? !this.routerInfo.projectCode : false,
        },
      });

      if (res.success) {
        const { needPopUpWindow, whetherToSettleIn, isLeCaiYun } = res.result;

        this.setState({
          isShowDrainageModal: needPopUpWindow,
          whetherToSettleIn,
          isLeCaiYun,
        }, () => {
          // 确保状态更新后才执行回调
          if (callback && !needPopUpWindow) {
            callback();
          }
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }

  handleNonGovDrainageModal = async (districtCode) => {
    await this.handlePopupAndSubmit(districtCode);
  }

  // 意向公告-新增或编辑意向公开明细
  insertOrUpdateIntentionDetailData = (values) => {
    const index = values?.index;
    const { getFieldsValue, setFieldsValue } = this.props?.form;
    const { projectDetailList = [] } = getFieldsValue?.();
    let nextValue = [...projectDetailList];

    if (_.isNil(index)) { // create
      nextValue.push(values);
    } else { // update
      nextValue?.splice(index, 1, values);
    }

    setFieldsValue({
      projectDetailList: nextValue,
    })
  }

  // 无相表单 - 意向公开公告【53号文透明度规划】
  renderPurchaseRequirementDetail = (value) => {
    const { bidName, bidCount, bidMainTarget, bidRequirement  } = value;

    return <div className='purchase-requirement-detail'>
      <div className='bid-info-row'><span className='row-label'>采购标的名称：</span>{bidName || '-'}</div>
      <div className='bid-info-row'><span className='row-label'>采购标的数量：</span>{bidCount || '-'}</div>
      <div className='bid-info-row'><span className='row-label'>主要功能或目标：</span>{bidMainTarget || '-'}</div>
      <div className='bid-info-row'><span className='row-label'>需满足的要求：</span>{bidRequirement || '-'}</div>
    </div>
  }

  // 无相表单 - 意向公开公告 - 明细操作列
  renderIntentionDetailTableOperation = ({ record, index, deleteRecord }) => {
    return [{
      label: '编辑',
      onRowClick: () => {
        PurchaseIntentionDetailDrawer.show({
          index,
          record,
          onOk: this.insertOrUpdateIntentionDetailData,
        });
      }
    }, {
      label: '删除',
      popConfirm: {
        title: '确定要删除该数据吗？',
        onConfirm: () => deleteRecord(index),
      },
    }]
  };

  renderMoney = (value) => {
    return <div style={{ textAlign: 'right' }}>
      <InputAmount
        readOnly
        value={value}
        align='right'
        color='orange'
        sourceUnit='元'
        targetUnit='万元'
      />
    </div>
  }

  
  calculateIsShowReservedQuotaAttachment = () => {
    const { getFieldsValue } = this.props.form;
    const { district, monitorReservedProjectInfo } = getFieldsValue(["district", "monitorReservedProjectInfo"]);
    // 查看是否命中区划
    if (
      !this?.districtPrefixList?.some((districtCode) =>
        district?.key?.startsWith(districtCode)
      )
    ) {
      return false;
    }
    // 非面向中小企业预留项目执行情况公告类型的公告不展示附件
    if (!this.routerInfo?.announcementType === "14001") {
      return false;
    }

    const res = monitorReservedProjectInfo?.some(
      ({ reservedPercent, moitorItemAmount, smMonitorItemAmount = 0 }) => {
        return (
          reservedPercent >= this.smMonitorItemAmountLimit &&
          (moitorItemAmount === 0 ||
            (smMonitorItemAmount / moitorItemAmount) * 100 >= this.proportionLimit)
        );
      }
    );
    return !res
  }


  

  render() {
    const {
      loading,
      processDefineKey,
      form: { getFieldsValue },
      history,
    } = this.props;
    const {
      preview,
      config = {},
      hasRemind,
      hasBlock,
      supervisionInfo = {},
      workflowInfo = {},
      isPubTypeDisabled,
      fetchHtmlTemplateLoading,
      isShowDrainageModal,
      whetherToSettleIn, 
      isLeCaiYun,
    } = this.state;
    const {
      district = {},
      totalBudgetPrice,
    } = getFieldsValue(['district', 'totalBudgetPrice']);

    // 安徽定制化需求:单一来源预算总金额(元) 大于400万必填
    const uploadRequired =
      (this.routerInfo.announcementType === SINGLEANNOUNCEMENTCODE && this.env === 'ah')
        ? (Number(totalBudgetPrice) >= AMOUNT)
        : config.hasAttachment;

        
    // 未达到规定预留份额比例的情况说明-附件是否需要展示
    const isShowReservedQuotaAttachment = this.calculateIsShowReservedQuotaAttachment();

    return (
      <div className="dynamic-create-announcement">
        <ZcySpin spinning={!!loading || fetchHtmlTemplateLoading}>
          <ZcyBreadcrumb
            {...breadcrumbConfig(this)}
          />
          {parseInt(this.routerInfo.isReformation, 10) ? (
            <ReformationTag 
              tagTitle="对标改革采购实施计划"
            />
          ) : null}
          <AnnSteps
            processDefineKey={processDefineKey}
            districtCode={district?.key}
            bizId={this.routerInfo?.annId}
            conditions={workflowInfo}
          />
          <MainForm
            customComponent={{
              annTitle: PurchaseIntentionTitle,
              TitleWithSuffix,
              ComplexSelect,
              contractField: ContractField,
              GpCatalogMultiCascader: GpCatalogCascader,
            }}
            pageComponent={this}
            extraParams={{
              isPubTypeDisabled,
              preview,
              defaultPreview: this.defaultPreview,
              actionType: this.actionType,
              secondEdit: !!config.secondEdit,
              announcementType: this.routerInfo.announcementType,
              // 上海预算一体化，用于展示项目信息默认值
              purchaseProjectCode: this.routerInfo.purchaseProjectCode,
              // smallAnnouncementYear为上海中小企业年度数据特有参数
              year: this.routerInfo.year || this.routerInfo.smallAnnouncementYear,
              // 是否禁用公告内容-项目信息-项目编号字段,仅支持上海
              // 1禁用,非1不禁用
              disabledProjectNo: this.routerInfo.disabledProjectNo,
              supervisionInfo,
              // 来自公告的 是否关联平台采购计划的强控配置项
              // isForcedControl: 1 表示强控，禁用
              // isForcedControl: 0 表示非强控，不禁用
              isForcedControlConfigFromAnnouncement: !!config.isForcedControl,
              // 项目采购过来的公告，带了orderId，禁用采购计划
              disabledProcurementPlan: !!config.isForcedControl || this.routerInfo.orderId,
              enabledChangeMonitorEnterprises: !!config.enableSummaryCalculation,
              // 上海-项目采购过来的公告
              orderId: this.routerInfo.orderId,
              formPage: this.formPage,
              singleForManualForCPTPP: this.routerInfo.singleForManualForCPTPP,
              pageComponent: this,
            }}
          />
          <Panel
            title="公告内容"
            style={{ display: preview ? 'block' : 'none' }}
          >
            {
              renderAlert(hasBlock, hasRemind)
            }
            {
              this.getAlertInfo()
            }
            <div id="uEditor" style={{ clear: 'both' }} />
          </Panel>
          {
            config?.attachmentShow && (
              <FormUpload
                required={uploadRequired}
                districtCode={district?.key}
                announcementType={this.routerInfo.announcementType}
                editable={!preview || !!config.secondEdit}
                attachmentExternalDisplayRules={config.attachmentExternalDisplayRules}
                attachmentUploadDesc={config.attachmentUploadDesc}
                isShowReservedQuotaAttachment={isShowReservedQuotaAttachment}
              />
            )
          }
          {isShowDrainageModal && (
            <LcyDrainageModal
              visible={isShowDrainageModal}
              whetherToSettleIn={whetherToSettleIn}
              annBigType={this.routerInfo.annBigType}
              okText={whetherToSettleIn ? '立即跳转"乐采云平台"' : '跳转到乐采云登录页面'}
              handleBack={() => {
                history.goBack();
              }}
              isLeCaiYun={isLeCaiYun}
            />
          )}
        </ZcySpin>
      </div>
    );
  }
}
