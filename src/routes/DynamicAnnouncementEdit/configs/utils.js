/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-17 11:27:03
 * @FilePath: /zcy-announcement-v2-front/src/routes/DynamicAnnouncementEdit/configs/utils.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 获取 url 上的信息
export const formatUrlParams = (params) => {
  const announcementTypeName = params.announcementTypeName ? unescape(params.announcementTypeName) : '';
  const bindProcurementPlan = params.bindProcurementPlan ?
    unescape(params.bindProcurementPlan) : undefined;
  return {
    announcementTypeName,
    announcementType: params.announcementType,
    orderId: params.orderId,
    projectCode: params.projectCode,
    projectName: params.projectName,
    districtCode: params.districtId,
    annBigType: params.annBigType,
    annAppCode: params.appCode,
    ids: params.ids,
    title: params.title,
    pageCode: params.formPageCode,
    annId: params.annId,
    serialType: params.serialType,
    purchaseProjectCode: params.purchaseProjectCode,
    year: params.year,
    disabledProjectNo: params.disabledProjectNo,
    bindProcurementPlan,
    isReformation: params.isReformation,
    isReformationEnglishAnnouncement: params.isReformationEnglishAnnouncement,
    annSmallMediumFlag: params.annSmallMediumFlag,
    singleForManualForCPTPP: JSON.parse(params.singleForManualForCPTPP || false),
    ifManualAnnouncement: !params.projectCode,
    smallAnnouncementYear: params.smallAnnouncementYear,
  };
};
