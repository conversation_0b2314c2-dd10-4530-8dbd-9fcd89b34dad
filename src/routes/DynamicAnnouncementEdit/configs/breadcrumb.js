import React from 'react';
import { Button } from 'doraemon';
import './index.less';

export const breadcrumbConfig = (component) => {
  const {
    routerInfo = {},
    handelBack,
    putHandler,
    saveHandler,
    handleOnExport,
    generatorHandler,
  } = component;
  const { preview, showExportButton } = component.state;
  const canBack = component.props.history.length > 1;

  const createStatusButton = (
    <span key="createStatusButton" className="config-top-operation-button">
      { canBack ? <Button onClick={handelBack}>返回</Button> : null }
      { showExportButton ? <Button key="exportButton" onClick={handleOnExport}>导出</Button> : null }
      <Button type="primary" onClick={generatorHandler}>生成公告</Button>
    </span> 
  );
  const previewStatusButton = (
    <span key="previewStatusButton" className="config-top-operation-button">
      { canBack ? <Button onClick={handelBack}>返回</Button> : null }
      { showExportButton ? <Button key="exportButton" onClick={handleOnExport}>导出</Button> : null }
      <Button onClick={saveHandler}>保存</Button>
      <Button type="primary" onClick={putHandler}>提交</Button>
    </span>
  );
  return {
    routes: [{
      label: routerInfo.announcementTypeName,
    }, {
      label: '创建',
    }],
    customContent: preview ? previewStatusButton : createStatusButton,
  };
};
