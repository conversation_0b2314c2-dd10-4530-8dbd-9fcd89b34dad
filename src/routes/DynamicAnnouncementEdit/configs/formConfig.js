/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-06-05 15:45:47
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-16 11:28:49
 * @FilePath: /zcy-announcement-v2-front/src/routes/DynamicAnnouncementEdit/configs/formConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getQueryParamObj } from '@zcy/utils';
import { formatUrlParams } from './utils';

export const FormPageWrapConfig = {
  prefix: '/announcement/api',
  getFormParams: (props) => {
    const { location, match } = props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    const routerInfo = formatUrlParams(params);
    const { pageCode, ...rest } = routerInfo;
    const curAlias = [
      {
        code: 'districtCode',
        value: params.districtId || window.currentUserDistrict.code,
      },
      {
        code: 'isReformationEnglishAnnouncement',
        value: routerInfo.isReformationEnglishAnnouncement,
      },
    ];
    return {
      appCode: 'zcy.notice',
      pageCode,
      id: params.annId,
      pageType: match.params.actionType === 'create' ? 'add' : 'edit', // 页面类型 add | edit | detail | audit
      alias: curAlias,
      useLatestView: false, // 非必须  是否使用最新视图  默认为 false
      extParam: {
        ...rest,
      }, // 非必须  扩展参数
    };
  },
};
