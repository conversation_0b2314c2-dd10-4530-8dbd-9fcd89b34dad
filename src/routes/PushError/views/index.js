import React, { Component } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal,
  message,
  request,
} from 'doraemon';
import moment from 'moment';
import { connect } from 'dva';
import { path } from '@zcy/utils';
import { routerRedux } from 'dva/router';
import { getTableConfig, getCustomItem } from '../config';

import './index.less';

const { confirm } = Modal;

const ROUTES = [
  {
    label: '业务查询',
  },
  {
    label: '公告推送失败查询',
  },
];

@connect(({ loading, pushError }) => ({
  tableQueryParams: pushError.tableQueryParams,
  list: pushError.list,
  listLoading: loading.effects['pushError/fetchList'],
}))


export default class PushError extends Component {
  state = {
    webSites: [],
    districtData: [],
    annTypes: [],
  }
  // 载入页面时,请求数据
  componentDidMount() {
    this.getWebSites();
    this.getDistTree();
    this.getAnnTypes();
    const { history } = this.props;
    const tableQueryParams = path.getQueryParamObj(history.location.search);
    if (tableQueryParams.date) {
      tableQueryParams.date = [tableQueryParams.date, tableQueryParams.date];
    }
    this.fetchList(tableQueryParams);
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'pushError/update',
      payload: {
        list: {},
      },
    });
  }
  getWebSites = async () => {
    const res = await request('/announcement/pushStatistics/listTarget');
    this.setState({
      webSites: res.result,
    });
  }
  getDistTree = async () => {
    const res = await request('/announcement/pushStatistics/listProvince');
    this.setState({
      districtData: res.result,
    });
  }
  getAnnTypes = async () => {
    const res = await request('/announcement/config/annTypes');
    this.setState({
      annTypes: res.result,
    });
  }

  // 加载列表数据
  loadList = () => {
    const { tableQueryParams } = this.props;
    this.fetchList(tableQueryParams);
  }

  // 请求列表数据
  fetchList = (params) => {
    if (!params.targetKey) {
      message.destroy();
      message.error('请选择推送网站');
      return;
    }
    this.props.dispatch({
      type: 'pushError/fetchList',
      payload: {
        ...params,
      },
    });
  }

  // 搜索
  onSearch = (params) => {
    this.props.dispatch({
      type: 'pushError/update',
      payload: {
        tableQueryParams: {
          ...params,
        },
      },
    });
    this.fetchList(params);
  }


  // 删除
  handleDelete = () => {
    message.destroy();
    confirm({
      title: '正在操作删除,是否继续操作？',
      onOk: () => {
        message.success('删除成功');
        this.loadList();
      },
    });
  }

  render() {
    const { webSites, districtData, annTypes } = this.state;
    const { listLoading, tableQueryParams, dispatch, history } = this.props;
    // initSearchParams 搜索项默认回填参数
    const { tab } = tableQueryParams;
    const initSearchParams = path.getQueryParamObj(history.location.search);
    if (initSearchParams.date) {
      initSearchParams.date = [moment(initSearchParams.date), moment(initSearchParams.date)];
    }
    const customItem = getCustomItem({ webSites, districtData, annTypes });
    const tabs = {
      defaultActiveKey: tab,
      activeKey: tab,
      tabList: [
        {
          label: '推送失败列表',
        },
      ],
    };
    const table = getTableConfig(this);
    return (
      <ZcySpin spinning={!!listLoading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={[
            {
              label: '返回',
              onClick: () => {
                dispatch(routerRedux.push('/push-static/list'));
              },
            },
          ]}
        />
        <ZcyList
          tabs={tabs}
          tabKey="tab"
          table={table}
          customItem={customItem}
          onSearch={this.onSearch}
          initSearchParams={initSearchParams}
        />
      </ZcySpin>
    );
  }
}
