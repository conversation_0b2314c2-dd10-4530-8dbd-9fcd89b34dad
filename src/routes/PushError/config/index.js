import React from 'react';
import {
  Select,
  DatePicker,
  Popover,
} from 'doraemon';
import {
  bizDatetimeFormat,
  bizStringFormat,
} from '@zcy/utils';

const { Option } = Select;
const { RangePicker } = DatePicker;

// 获得搜索项配置
export const getCustomItem = ({ webSites, districtData, annTypes }) => {
  return [
    {
      label: '推送网站',
      id: 'targetKey',
      render: () => (
        <Select>
          {webSites.map(site => <Option key={site.code} value={site.code}>{site.name}</Option>)}
        </Select>
      ),
    },
    {
      label: '时间',
      id: 'date',
      render: () => <RangePicker />,
    },
    {
      label: '区划名称',
      id: 'districtPrefix',
      render: () => (
        <Select>
          {districtData.map(site => <Option key={site.code} value={site.code}>{site.name}</Option>)}
        </Select>
      ),
    },
    {
      label: '公告类型',
      id: 'announcementType',
      render: () => (
        <Select>
          {annTypes.map(site => <Option key={site.code} value={`${site.code}`}>{site.name}</Option>)}
        </Select>
      ),
    },
  ];
};
const getText = (text, width) => {
  return (
    <Popover content={text} overlayClassName="PopoverCls">
      <div className="nowrap"
        style={{
          width: width - 20,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {bizStringFormat(text)}
      </div>
    </Popover>
  );
};
// 获得表格配置项
export const getTableConfig = (component) => {
  const { props } = component;
  const { list = {}, tableQueryParams } = props;
  const { pageSize, pageNo } = tableQueryParams;

  const getColumns = () => {
    return [
      {
        title: '日期',
        dataIndex: 'createAt',
        key: 'createAt',
        width: 120,
        render: text => (
          bizDatetimeFormat(text)
        ),
      },
      {
        title: '区划',
        dataIndex: 'districtName',
        key: 'districtName',
        render: text => (
          bizStringFormat(text)
        ),
      },
      {
        title: '公告类型',
        dataIndex: 'announcementTypeName',
        key: 'announcementTypeName',
        render: text => (
          bizStringFormat(text)
        ),
      },
      {
        title: '公告标题',
        dataIndex: 'title',
        key: 'title',
        width: 250,
        render: text => (
          getText(text, 250)
        ),
      },
      {
        title: '推送失败原因',
        dataIndex: 'error',
        key: 'error',
        width: 300,
        render: text => (
          getText(text, 300)
        ),
      },
      {
        title: '操作',
        dataIndex: 'handle',
        key: 'handle',
        width: 100,
        render: (t, record) => (
          <a href={`#/detail/${record.announcementType}/${record.annId}`}>查看公告</a>
        ),
      },
    ];
  };

  const columns = getColumns();

  const { data = [], total } = list;

  return {
    columns,
    dataSource: data,
    pagination: {
      pageSize,
      current: pageNo,
      showSizeChanger: true,
      total,
    },
    rowKey: 'id',
  };
};
