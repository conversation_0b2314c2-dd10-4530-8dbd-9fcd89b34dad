import { request } from 'doraemon';
import moment from 'moment';

const urls = {
  list: '/announcement/pushStatistics/pagePushErrorAnn', // 列表
};

export async function reqList(params) {
  const { date, ...rest } = params;

  return request(urls.list, {
    params: {
      ...rest,
      startDate: (Array.isArray(date) && date.length) ?
        moment(date[0]).startOf('day').format('YYYY-MM-DD') : undefined,
      endDate: (Array.isArray(date) && date.length) ?
        moment(date[1]).endOf('day').format('YYYY-MM-DD') : undefined,
    },
  });
}

