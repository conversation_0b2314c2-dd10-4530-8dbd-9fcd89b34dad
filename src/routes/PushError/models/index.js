import { Modal } from 'doraemon';
import {
  reqList,
} from '../services';

/**
 * 请求错误信息统一处理
 * @param res
 */
const responseErrorHandle = (res) => {
  const { success, message } = res;
  if (!success) {
    Modal.error({
      content: message,
    });
  }
};

export default {
  namespace: 'pushError',
  state: {
    tableQueryParams: {
      pageNo: 1,
      pageSize: 10,
    },
    list: {},
  },
  effects: {
    * fetchList({ payload }, { call, put }) {
      const res = yield call(reqList, payload);
      yield put({
        type: 'update',
        payload: {
          list: res.result,
        },
      });
      responseErrorHandle(res);
      return res;
    },
  },
  reducers: {
    update(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

