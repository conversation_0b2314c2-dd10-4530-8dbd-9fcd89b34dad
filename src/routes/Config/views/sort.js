import React, { Component } from 'react';
import { connect } from 'dva';
import { ZcyBreadcrumb, Panel, Form, Spin, message, Modal, Empty } from 'doraemon';
import AnnTypeList from '../components/AnnTypeList';
import NoContent from '../components/NoContent';
import { SORTTYPE, getSortGlobalBtn, getModalForm, sortRoutes, configModalConst } from '../config';
import { batchSaveRelationAnnouncementTypes, batchCancelRelationAnnouncementTypes } from '../services';

import './index.less';
import DistrictCascader from 'src/components/DistrictCascader';

const { confirm } = Modal;

@Form.create()

@connect(({ openConfig, publishConfig, sortConfig }) => ({
  openConfig,
  publishConfig,
  sortConfig,
}))

class classificationConfig extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      isEdit: false, // 是否为编辑
      selectAnnTypeId: '', // 已选择数据
      allAnnTypes: [], // 所有公告类型
      // eslint-disable-next-line react/no-unused-state
      saveData: {}, // 保存数据
      visible: false,
      changeCode: '', // 默认区划
      codeName: '', // 默认区划
      disable: false, // 区化是否可选
      modalConfig: {},
    };
  }

  componentDidMount() {
    // this.getSortSelectAnnTypesData();
  }

  typeData = (data) => {
    data.map((i) => {
      i.name = i.label;
      i.typeId = i.value;
      if (i.children) {
        this.typeData(i.children);
      }
    });
    return data;
  }

  // 获取展示数据
  getSortSelectAnnTypesData = () => {
    const { changeCode } = this.state;
    this.setState({ loading: true });
    // 详情展示数据
    this.props.dispatch({
      type: 'sortConfig/getListRelatedAnnTypes',
      payload: { distCode: changeCode },
    }).then(() => {
      this.setState({ loading: false });
    });
  }

  // 编辑获取所有及以选择数据
  getSortAllAnnTypesData = () => {
    const { changeCode } = this.state;
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'sortConfig/getAnnouncementData',
      payload: { distCode: changeCode },
    }).then(() => {
      return (
        // 获取所有公告类型类型
        this.props.dispatch({
          type: 'sortConfig/getTreeData',
          payload: {},
        })
      );
    }).then(() => {
      const { announcementData, announcementTypeData } = this.props.sortConfig;
      this.setState({
        isEdit: true,
        loading: false,
        selectAnnTypeId: announcementData.result ? String(announcementData.result) : '', // 编辑回填选中的类型
        allAnnTypes: this.typeData(announcementTypeData), // 所有类型
      });
    });
  }

  // 编辑
  onEdit = () => {
    this.getSortAllAnnTypesData();
    this.setState({ disable: true });
  }
  // 保存
  onSave = () => {
    const { selectAnnTypeId, changeCode } = this.state;
    const pamse = {
      annTypes: selectAnnTypeId,
      distCode: changeCode,
    };
    this.props.dispatch({
      type: 'sortConfig/save',
      payload: JSON.stringify(pamse),
    }).then(() => {
      const { success, error } = this.props.sortConfig.saveData;
      if (success) {
        message.success('保存成功');
        this.onClose();
      } else {
        message.error(error);
      }
    });
  }
  // 取消编辑
  onClose = () => {
    this.setState({
      isEdit: false,
      disable: false,
    }, this.getSortSelectAnnTypesData);
  }

  saveAnnType = (id, isCheck) => {
    const { selectAnnTypeId } = this.state;
    const selectTypeId = (selectAnnTypeId !== '') ? selectAnnTypeId.split(',') : [];
    // let selectTypeId = this.state.selectAnnTypeId.split(",");
    if (isCheck) {
      selectTypeId.push(id.toString());
    } else {
      selectTypeId.forEach((item, index) => {
        if (item === id) {
          selectTypeId.splice(index, 1);
        }
      });
    }
    this.setState({
      selectAnnTypeId: selectTypeId.join(','),
    });
  }

  checkTypeExistSaveAnnType(callback, id, isCheck) {
    const { announcementData } = this.props.sortConfig;
    const typeArr = announcementData ? announcementData.result : '';
    if (typeArr.includes(Number(id))) {
      confirm({
        title: '注意：该公告类型正在使用中',
        content: '取消关联后，在【公告发布配置】、【公告开放配置】、【公告分组配置】、【公告站点配置】及配置向导中的相关设置将全部失效，请确认是否继续？',
        okText: '继续取消关联',
        cancelText: '取消',
        onOk: () => {
          callback(id, isCheck);
        },
      });
    } else {
      callback(id, isCheck);
    }
  }

  // 选择公告类型
  onChangeTag = (id, name, preIsCheck) => {
    preIsCheck ?
      this.checkTypeExistSaveAnnType(this.saveAnnType, id, !preIsCheck) :
      this.saveAnnType(id, !preIsCheck);
  }

  // 同步到其他区划 二次确认逻辑
  singleSynchronizeHandler = ({ changeCode, values }) => {
    this.props.dispatch({
      type: 'sortConfig/getSaveCode',
      payload: JSON.stringify({
        targetDist: values.targetDist,
        sourceDist: changeCode,
      }),
    }).then(() => {
      const { success, error } = this.props.sortConfig.saveCodeList;
      if (success) {
        message.success('同步成功');
        this.handleCancel();
        this.getSortSelectAnnTypesData();
      } else {
        error && message.error(error);
      }
    });
  }

  // 批量添加关联 二次确认逻辑
  batchAddHandler = ({ values }) => {
    batchSaveRelationAnnouncementTypes(values).then(({ success, error }) => {
      if (success) {
        message.success('配置成功');
        this.handleCancel();
        this.getSortSelectAnnTypesData();
      } else {
        error && message.error(error);
      }
    });
  };

  // 批量删除关联 二次确认逻辑
  batchDletHandler = ({ values }) => {
    batchCancelRelationAnnouncementTypes(values).then(({ success, error }) => {
      if (success) {
        message.success('配置成功');
        this.handleCancel();
        this.getSortSelectAnnTypesData();
      } else {
        error && message.error(error);
      }
    });
  };

  warning = (values) => {
    const {
      changeCode,
      modalConfig: {
        tip,
        okText,
        okHandler,
      },
    } = this.state;
    // eslint-disable-next-line no-underscore-dangle
    const _this = this;
    confirm({
      content: tip,
      okText,
      cancelText: '取消',
      onOk() {
        _this[okHandler]?.({ values, changeCode });
      },
    });
  }

  changeShowModalHandler = () => {
    this.setState({
      visible: true,
    }, () => {
      // 获取所有公告类型类型
      this.props.dispatch({
        type: 'sortConfig/getTreeData',
        payload: {},
      });
    });
  }
  // 弹框显示
  showModal = (type) => {
    this.setState({
      modalConfig: Object.assign({ type }, configModalConst[type] || {}),
    }, this.changeShowModalHandler);
  }
  // 弹框保存
  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (err) {
        return message.warning('请完善信息');
      }
      this.warning(values);
    });
  }
  // 弹框隐藏
  handleCancel = () => {
    this.setState({
      visible: false,
    }, () => {
      this.props?.form?.resetFields();
    });
  }

  // 区划选择
  onChangeCode = (value, selectedOptions) => {
    this.setState({
      changeCode: value[value.length - 1],
      codeName: selectedOptions[selectedOptions.length - 1].name,
    }, () => {
      this.getSortSelectAnnTypesData();
    });
  }

  contentRender = () => {
    // 详情展示数据：getSelectedAnnTypesData, 编辑所有类型数据：allAnnTypes， 编辑回显选中数据：selectAnnTypeId
    const { getSelectedAnnTypesData } = this.props.sortConfig;
    const allAnnTypes = this.state.allAnnTypes.filter(i => i.typeId !== 7);


    if (!this.state.changeCode) {
      return (
        <Empty
          description="请选择目标区划"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div className="operat-btn">
        <div>
          <span style={{ marginLeft: 90 }}>分类名称</span>
          <span style={{ marginLeft: 44 }}> 关联公告模板（类型）</span>
          <div className="content-sort">
            {
              (!this.state.isEdit && getSelectedAnnTypesData.length < 1) ? <NoContent type="wufachakan" text="暂未添加，请编辑添加公告类型" /> : null
            }
            <AnnTypeList
              annTypesData={this.state.isEdit ? allAnnTypes : getSelectedAnnTypesData}
              isEdit={this.state.isEdit}
              selectAnnTypeId={this.state.isEdit ? this.state.selectAnnTypeId : null}
              onChangeTag={this.onChangeTag}
            />
          </div>
        </div>
      </div>
    );
  }

  render() {
    const { 
      visible, disable,
      changeCode, codeName, isEdit, modalConfig, 
    } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { announcementTypeData } = this.props.sortConfig;
    return (
      <div className="sortCls">
        <Spin spinning={this.state.loading}>
          <ZcyBreadcrumb
            routes={sortRoutes}
            globalBtn={getSortGlobalBtn({
              type: SORTTYPE, // 关联页面
              isEdit,
              changeCode,
              onSave: this.onSave,
              onClose: this.onClose,
              showModal: this.showModal,
              onEdit: this.onEdit,
            })}
            extraContent={
              <DistrictCascader
                localKey="sort"
                disabled={disable}
                onChangeCode={this.onChangeCode}
              />
            }
          />
          <Panel>
            {this.contentRender()}
            {
              visible && (
                <Modal
                  key={modalConfig.type}
                  title={modalConfig.title}
                  visible
                  onOk={this.handleOk}
                  onCancel={this.handleCancel}
                  width={640}
                  destroyOnClose
                >
                  {getModalForm({
                    type: modalConfig.type,
                    getFieldDecorator,
                    codeName,
                    announcementTypeData,
                  })}
                </Modal>
              )
            }
          </Panel>
        </Spin>
      </div>
    );
  }
}
export default classificationConfig;
