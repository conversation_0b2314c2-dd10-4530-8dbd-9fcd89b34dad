import React, { Component } from 'react';
import { connect } from 'dva';
import { Empty, ZcyBreadcrumb, Tabs, Panel, Form, Spin, message, Modal } from 'doraemon';
import AnnTypeList from '../components/AnnTypeList';
import NoContent from '../components/NoContent';
import { PUBLISHTYPE, getSortGlobalBtn, getModalForm, publishRoutes, configModalConst } from '../config';
import { batchSavePublicAnnouncementTypes, batchCancelPublicAnnouncementTypes } from '../services';
import { formatTreeData } from 'src/utils/tree';

import './index.less';
import userIdentity from 'src/utils/roleType';
import DistrictCascader from 'src/components/DistrictCascader';

const { TabPane } = Tabs;
const { confirm } = Modal;

@Form.create()
@connect(({ openConfig, publishConfig }) => ({
  openConfig,
  publishConfig,
  /*  
    采购单位类型
  */
  originatorTypeList: openConfig.originatorTypeList,
}))

class announcementPublishConfig extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      originatorType: '01', // tabID
      isEdit: false, // 是否为编辑
      selectAnnTypeId: '', // 已选择数据
      allAnnTypes: [], // 所有公告类型
      saveData: {}, // 保存数据
      /**
       * 是否有相关联的公告类型数据
      */
      isRelative: true,
      visible: false,
      changeCode: null, // 默认区划
      codeName: null, // 默认区划
      modalConfig: {},
    };
  }

  componentDidMount() {
    this.fetchOriginatorTypeList();
    if (!userIdentity.isAdmin) {
      this.checkIsRelative();
    }
  }

  /**
   * 获取采购单位类型数据源
   */
  fetchOriginatorTypeList=() => {
    this.props.dispatch({
      type: 'openConfig/getOriginatorTypeList',
    });
  }

  /**
   * 获取是否有关联已发布的公告类型，有关联数据就额外获取当前角色下的所有关联公告类型数据
  */
  checkIsRelative() {
    this.props.dispatch({
      type: 'openConfig/checkExistRelatedAnnTypes',
      payload: { districtCode: this.state.changeCode },
    }).then(({ result = false } = {}) => {
      if (result) {
        this.setState({
          isRelative: true,
        });
        this.getPublishSelectAnnTypesData();
      } else {
        this.setState({
          isRelative: false,
        });
      }
    });
  }

  /**
   * 获取当前采购单位下的所有关联已发布的公告类型数据
   * 当前页数据信息展示、
  */
  getPublishSelectAnnTypesData = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'publishConfig/getSelectedAnnTypesData',
      payload: { userType: this.state.originatorType, distCode: this.state.changeCode },
    }).then(() => {
      this.setState({ loading: false });
    });
  }
  
  // 获取所有及以选择数据
  getPublishAllAnnTypesData = () => {
    this.setState({ loading: true });
    /**
     * 获取当前采购单位下选择的关联公告信息，typeId集合（announcementTypes）
    */
    this.props.dispatch({
      type: 'publishConfig/getAnnouncementData',
      payload: { userType: this.state.originatorType, distCode: this.state.changeCode },
    }).then(() => {
      return (
        /**
         * 获取当前采购单位下关联公告信息树形结构，包含是否发布的（allAnnTypes）
        */
        this.props.dispatch({
          type: 'publishConfig/getTreeData',
          payload: { distCode: this.state.changeCode },
        })
      );
    }).then(() => {
      const { announcementData, announcementTypeData } = this.props.publishConfig;
      this.setState({
        isEdit: true,
        loading: false,
        saveData: announcementData.result,
        selectAnnTypeId: announcementData.result.announcementTypes || '',
        allAnnTypes: announcementTypeData,
      });
    });
  }

  // tab切换操作
  onChangeTab = (key) => {
    // eslint-disable-next-line no-underscore-dangle
    const _this = this;
    if (this.state.isEdit) {
      confirm({
        title: '是否保存当前更改信息?',
        content: '',
        okText: '保存',
        cancelText: '取消',
        onOk() {
          _this.onSave();
          _this.setState({
            originatorType: key,
          });
        },
        onCancel() {
          _this.setState({
            originatorType: key,
          }, () => {
            _this.onClose();
          });
        },
      });
    } else {
      this.setState({ originatorType: key }, this.getPublishSelectAnnTypesData);
    }
  }
  // 编辑
  onEdit = () => {
    this.getPublishAllAnnTypesData();
  }
  // 保存
  onSave = () => {
    const getData = this.state.saveData;
    getData.originatorType = this.state.originatorType;
    getData.announcementTypes = this.state.selectAnnTypeId;
    if (userIdentity.isAdmin) {
      getData.distCode = this.state.changeCode;
    }
    this.props.dispatch({
      type: 'publishConfig/save',
      payload: JSON.stringify(getData),
    }).then(() => {
      const { success, error } = this.props.publishConfig.saveData;
      if (success) {
        message.success('保存成功');
        this.setState({ isEdit: false }, this.getPublishSelectAnnTypesData);
      } else {
        message.error(error);
      }
    });
  }
  // 取消编辑
  onClose = () => {
    this.setState({
      isEdit: false,
    }, () => {
      this.getPublishSelectAnnTypesData();
    });
  }
  // 选择公告类型
  onChangeTag = (id, name, isCheck) => {
    const { selectAnnTypeId } = this.state;
    const selectTypeId = (selectAnnTypeId !== '') ? selectAnnTypeId.split(',') : [];

    if (!isCheck) {
      selectTypeId.push(id.toString());
    } else {
      selectTypeId.forEach((item, index) => {
        if (Number(item) === id) {
          selectTypeId.splice(index, 1);
        }
      });
    }
    this.setState({
      selectAnnTypeId: selectTypeId.join(','),
    });
  }

  contentRender = (showAll) => {
    const { getSelectedAnnTypesData } = this.props.publishConfig;
    const { allAnnTypes, isEdit, selectAnnTypeId } = this.state;

    let annTypesData = [];
    if (isEdit) {
      if (showAll) {
        annTypesData = allAnnTypes;
      } else {
        // 非财政部门tab，不展示政府采购监管公告
        // 政府采购监管公告 === 7 
        annTypesData = allAnnTypes.filter(i => i.typeId !== 7);
      }
    } else {
      annTypesData = getSelectedAnnTypesData;
    }

    return (
      <div className="operat-btn">
        <span>{isEdit ? '选择公告类型' : '已选择公告类型'}</span>
        <div className="content-warp">
          {
            (!this.state.isEdit && getSelectedAnnTypesData.length < 1)
              ? <NoContent type="wufachakan" text="暂未添加，请编辑添加公告类型" />
              : null
          }
          <AnnTypeList
            annTypesData={annTypesData}
            isEdit={this.state.isEdit}
            selectAnnTypeId={isEdit ? selectAnnTypeId : null}
            onChangeTag={this.onChangeTag}
          />
        </div>
      </div>
    );
  }

  changeShowModalHandler = () => {
    this.setState({
      visible: true,
    }, () => {
      this.props.dispatch({
        type: 'publishConfig/getTreeData',
        payload: { distCode: this.state.changeCode },
      });
    });
  }

  // 弹框显示
  showModal = (type) => {
    this.setState({
      modalConfig: Object.assign({ type }, configModalConst[type] || {}),
    }, this.changeShowModalHandler);
  }

  // 区划选择
  onChangeCode = (value, selectedOptions) => {
    this.setState({
      changeCode: value[value.length - 1],
      codeName: selectedOptions?.[selectedOptions.length - 1]?.name,
    }, () => {
      this.checkIsRelative();
    });
  }

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (err) {
        message.warning('请完善信息');
        return;
      }
      this.warning(values);
    });
  }

  // 配置同步到其他区划 二次确认逻辑
  batchSynchronizeHandler = ({ changeCode, values }) => {
    this.props.dispatch({
      type: 'publishConfig/getSaveCode',
      payload: JSON.stringify({
        sourceDist: changeCode,
        userTypeList: values.userTypeList,
        targetDist: values.targetDist,
      }),
    }).then(() => {
      const { success, error } = this.props.publishConfig.saveCodeList;
      if (success) {
        message.success('同步成功');
        this.handleCancel();
        this.checkIsRelative();
      } else {
        message.error(error);
      }
    });
  }
  // 批量配置不可发布 二次确认逻辑
  batchDispublishHandler = ({ values }) => {
    batchCancelPublicAnnouncementTypes(values).then(({ success, error }) => {
      if (success) {
        message.success('配置成功');
        this.handleCancel();
        this.checkIsRelative();
      } else {
        error && message.error(error);
      }
    });
  }

  // 批量配置可发布 二次确认逻辑
  batchCanpublishHandler = ({ values }) => {
    batchSavePublicAnnouncementTypes(values).then(({ success, error }) => {
      if (success) {
        message.success('配置成功');
        this.handleCancel();
        this.checkIsRelative();
      } else {
        error && message.error(error);
      }
    });
  }


  // 二次弹框提示
  warning = (values) => {
    const {
      changeCode,
      modalConfig: {
        tip,
        okText,
        okHandler,
      },
    } = this.state;
    // eslint-disable-next-line no-underscore-dangle
    const _this = this;
    confirm({
      content: tip,
      okText,
      cancelText: '取消',
      onOk() {
        _this[okHandler]?.({ values, changeCode });
      },
      onCancel() {
        // This is intentions
      },
    });
  }
  // 关闭弹框
  handleCancel = () => {
    this.setState({
      visible: false,
    }, () => {
      this.props?.form?.resetFields();
    });
  }

  render() {
    const {
      isRelative, visible, codeName, changeCode,
      isEdit, modalConfig,
    } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { originatorTypeList = [] } = this.props;
    const { announcementTypeData } = this.props.publishConfig;

    return (
      <div>
        <Spin spinning={this.state.loading}>
          <ZcyBreadcrumb
            routes={publishRoutes}
            globalBtn={getSortGlobalBtn({
              type: PUBLISHTYPE, // 发布页面
              isEdit,
              onSave: this.onSave,
              onClose: this.onClose,
              showModal: this.showModal,
              onEdit: this.onEdit,
              changeCode: this.state.changeCode,
            })}
            extraContent={
              userIdentity.isAdmin && (
                <DistrictCascader
                  disabled={isEdit}
                  localKey="publish"
                  onChangeCode={this.onChangeCode}
                />
              )
            }
          />
          <Panel >
            {
              (changeCode || !userIdentity.isAdmin) ? (
                isRelative ? (
                  <Tabs
                    activeKey={this.state.originatorType}
                    onChange={this.onChangeTab}
                  >
                    {
                      originatorTypeList.map((org) => {
                        const { code, desc } = org;
                    
                        return (
                          <TabPane tab={desc} key={code}>
                            {/* 06 === 财政部门 */}
                            {this.contentRender(code === '06')}
                          </TabPane>
                        );
                      })
                    }
                  </Tabs>
                ) : (
                  <Empty
                    description="暂无公告类型配置数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )
              ) : (
                <Empty
                  description="请选择目标区划"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              )
            }
            {
              visible && (
                <Modal
                  key={modalConfig.type}
                  title={modalConfig.title}
                  visible
                  onOk={this.handleOk}
                  onCancel={this.handleCancel}
                  destroyOnClose
                >
                  {getModalForm({
                    type: modalConfig.type,
                    getFieldDecorator,
                    codeName,
                    announcementTypeData: formatTreeData(announcementTypeData, { labelKey: 'name', valueKey: 'typeId' }),
                    originatorTypeList,
                  })}
                </Modal>
              )
            }
          </Panel>
        </Spin>
      </div>
    );
  }
}
export default announcementPublishConfig;
