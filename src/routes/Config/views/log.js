import React, { Component } from 'react';
import { Zcy<PERSON><PERSON>, ZcyB<PERSON>crumb } from 'doraemon';
import { getLogCustomItem, getLogTableColumn, logRoutes } from '../config';

import { getLogData, getOperationList, getSourceList } from '../services';
import './index.less';

export default class Log extends Component {
  constructor(props) {
    super(props);
    this.state = {
      searchParams: {
        pageNo: 1,
        pageSize: 10,
      },
      sourceList: [], // 配置菜单枚举值
      operationList: [], // 配置类型枚举值
      listData: {
        data: [],
        total: 0,
      }, // table 数据列表
    };
  }

  componentDidMount() {
    this.getOperationListHandler();
    this.getSourceListHandler();
    this.searchFunc();
  }

  getOperationListHandler = () => {
    getOperationList().then(({ result = [] }) => {
      this.setState({
        operationList: result,
      });
    });
  }

  getSourceListHandler = () => {
    getSourceList().then(({ result = [] }) => {
      this.setState({
        sourceList: result,
      });
    });
  }

  handleDateParams = (params = {}) => {
    const { pageNo, pageSize, source, operation, operatorName, operateTime } = params;
    const [addTimeStart, addTimeEnd] = operateTime || [];
    return {
      addTimeStart,
      addTimeEnd,
      pageNo,
      pageSize,
      source,
      operation,
      operatorName,
    };
  }

  handleSearch = (params = {}) => {
    this.setState({
      searchParams: this.handleDateParams(params),
    }, this.searchFunc);
  }

  searchFunc = async () => {
    const { result: { data, total } } = await getLogData(this.state.searchParams) || { result: {} };
    this.setState({
      listData: {
        data,
        total,
      },
    });
  }


  render() {
    const { searchParams, listData, sourceList, operationList } = this.state;
    const pagination = {
      total: listData.total,
      showSizeChanger: true,
      defaultCurrent: searchParams.pageNo,
      defaultPageSize: searchParams.pageSize,
    };

    return (
      <div className="config-log-container">
        <ZcyBreadcrumb
          routes={logRoutes}
        />
        <ZcyList
          customItem={getLogCustomItem({ sourceList, operationList })}
          table={{
            dataSource: listData.data,
            columns: getLogTableColumn(),
            pagination,
          }}
          onSearch={this.handleSearch}
        />
      </div>
    );
  }
}
