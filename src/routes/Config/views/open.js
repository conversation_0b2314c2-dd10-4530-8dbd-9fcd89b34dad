import React, { Component } from 'react';
import { connect } from 'dva';
import { Empty, ZcyBreadcrumb, Tabs, Panel, Spin, message, Modal, Form, Button, TreeSelect } from 'doraemon';

import ConfigList from '../components/ConfigList/index';
import PublishModal from '../components/PublishModal';
import SetModal from '../components/SetModal';
import ParamList from '../components/ParamList/index';
import { getRelationById } from '../services';
import userIdentity from 'src/utils/roleType';
import { formatTreeData } from 'src/utils/tree';
import DistrictCascader from 'src/components/DistrictCascader';
import DistrictTreeSelect from 'src/components/DistrictTreeSelect';

const { TabPane } = Tabs;
const { Item } = Form;
const SHOW_CHILD = TreeSelect.SHOW_CHILD;
const { confirm } = Modal;

@Form.create()
@connect(({ openConfig }) => ({
  openConfig,
}))
class announcementOpenConfig extends Component {
  constructor(props) {
    super(props);
    this.state = {
      configType: '1', // 公告开放、监管参数配置标记
      configTypeTag: 0, // 监管参数是否为第一次请求
      tabTag: '0', // 公告开放tab标记
      visible: false,
      viewVisible: false, // 显示查看公告弹窗
      loading: false,
      showReason: false,
      typeId: '', // 公告开放配置设置ID
      openDataTemp: {},
      itemId: '', // 编辑公告配置ID
      checkData: [], // 选择公告模板ID
      checkDataName: [], // 选择公告模板名称
      isMore: false, // 是否为查看更多
      paramData: [],
      paramsItemData: {}, // 单条监管参数
      submitType: 1, // 提交类型
      publishModalTitle: '新增监管参数',
      viewHtmlTemp: '',
      viewName: '',
      annItemData: {},
      // eslint-disable-next-line react/no-unused-state
      cgfs: {},
      types: null, // 区分查看与同步区划弹框内容
      changeCode: null, // 默认区划
      codeName: '', // 默认区划
      treeDataAnnouncement: [], // 目标区划
      // eslint-disable-next-line react/no-unused-state
      keys: 0,
      // eslint-disable-next-line react/no-unused-state
      treeNode: [],
    };
  }

  componentDidMount() {
    this.path();
    if (!userIdentity.isAdmin) {
      this.getOpenInitData();
    }
  }

  path = () => {
    const { pathname } = this.props.location;
    if (pathname === '/config/open') {
      this.setState({
        configType: '1',
      }, () => {
        this.onChangeType();
      });
    } else if (pathname === '/config/supervision') {
      this.setState({
        configType: '2',
      }, () => {
        this.onChangeType();
      });
    }
  }

  // 公告开发配置初始化数据
  getOpenInitData = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'openConfig/getListData',
      payload: {
        distCode: this.state.changeCode,
      },
    }).then(() => {
      this.setState({
        loading: false,
        paramData: this.props.openConfig.announcementTypeData,
      });
    });
  }

  // 监管参数配置初始化数据
  getOpenParamsData = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'openConfig/getParamsData',
      payload: {
        distCode: this.state.changeCode,
      },
    }).then(() => {
      this.setState({ loading: false });
    });
  }

  // 配置类型切换
  onChangeType = () => {
    const { configType } = this.state;
    if (configType === '2') {
      this.setState({
        configTypeTag: this.state.configTypeTag + 1,
      });
    }
    this.setState({
      configType,
    }, () => {
      if (this.state.configType === '2' && this.state.configTypeTag === 1) {
        this.getOpenParamsData();
      }
    });
  }

  // tab切换
  changeTabs = (key) => {
    this.setState({ tabTag: key });
  }

  renderTip = () => {
    return (
      // eslint-disable-next-line react/no-danger
      <div dangerouslySetInnerHTML={{ __html: '<span>asdadad</span>' }} />
    );
  }

  set = (relationId) => {
    getRelationById({
      relationId,
    }).then((res) => {
      if (!res.success) return;
      const data = res.result;
      this.setState({
        openDataTemp: data,
        typeId: data.typeId,
        annItemData: data,
      }, () => {
        this.props.dispatch({
          type: 'openConfig/obtainCGLXANDCGFS',
          payload: {
            districtCode: data.distCode,
          },
        });
        this.cgfs = {
          procurementMethodName: data.procurementMethodName,
          procurementMethodCode: data.procurementMethodCode,
        };
        this.showModal();
      });
      //
    });
  }

  // 弹框显示
  showModal = () => {
    this.setState({ visible: true });
  }
  handleCancel = () => {
    const form = this.formRef.props.form;
    this.setState({
      visible: false,
      showReason: false,
      checkData: [],
      typeId: '',
    }, () => {
      form.resetFields();
    });
  }
  // 公告开放设置
  handleCreate = () => {
    const form = this.formRef.props.form;
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      // 公告开放配置表单
      if (this.state.configType === '1') {
        const getParams = {
          isNotify: values.isNotify,
          termType: values.termType || 0,
          expiryPeriod: values.expiryPeriod || -1,
          unOpenReason: values.unopenReason || '',
          typeId: this.state.typeId,
          distCode: this.state.openDataTemp.distCode,
          name: this.state.openDataTemp.name,
          isOut: parseInt(values.isOut, 10),
          isForcedControl: parseInt(values.isForcedControl, 10),
          canRevoke: parseInt(values.canRevoke, 10),
          revokeTime: values.revokeTime,
          objectionKeys: values.objectionKeys ? values.objectionKeys.join() : undefined,
          beObjectionKeys: values.beObjectionKeys ? values.beObjectionKeys.join() : undefined,
          canObject: values.canObject,
          checkAgencyTypes: Array.isArray(values.checkAgencyTypes)
            ? values.checkAgencyTypes.join()
            : undefined,
          secondEdit: values.secondEdit,
          customizedTitle: values.customizedTitle,
          titlePrefix: values.titlePrefix,
          titleSuffix: values.titleSuffix,
          canContentCensor: values.canContentCensor,
          attachmentShow: values.attachmentShow,
          hasAttachment: values.attachmentShow ? values.hasAttachment : 0,
          attachmentExternalDisplayRules: values.attachmentShow
            ? +values.attachmentExternalDisplayRules : 1,
          attachmentUploadDesc: values.attachmentShow ? values.attachmentUploadDesc : '',
          isRevocable: values.isRevocable,
          // 开启取消按钮才传值revocableButtonName
          revocableButtonName: +values.isRevocable === 1 ? values.revocableButtonName : '',
          publishTypes: values.publishTypes ? values.publishTypes.join(',') : undefined,
          whiteListDistrictCode: values.whiteListDistrictCode || [],
          enableSummaryCalculation: values.enableSummaryCalculation,
          ...this.cgfs,
        };
        if (userIdentity.isAdmin) {
          getParams.distCode = this.state.changeCode;
        }
        this.props.dispatch({
          type: 'openConfig/setOpen',
          payload: getParams,
        }).then(() => {
          const { success, error } = this.props.openConfig.setData;
          if (success) {
            this.setState({
              visible: false,
            }, () => {
              message.success('设置成功！');
              this.getOpenInitData();
            });
            form.resetFields();
          } else {
            message.error(error);
          }
        });
      }
      // 监管参数配置表单
      if (this.state.configType === '2') {
        if (this.state.checkData.length <= 0 || (this.state.checkData.length <= 1 && this.state.checkData[0] === '')) {
          message.warning('公告类型至少选择一项，请检查后提交!');
          return;
        }
        // 拼装数据
        const getData = {
          announcementTypes: this.state.checkData.join(','),
          announcementTypeName: this.state.checkDataName.join(','),
          groupName: values.groupName,
          envs: {
            regulatoryOrgName: values.regulatoryOrgName || '',
            regulatoryContactFax: values.regulatoryContactFax || '',
            regulatoryContactPhone: values.regulatoryContactPhone || '',
            regulatoryContactAddr: values.regulatoryContactAddr || '',
            regulatoryContactPerson: values.regulatoryContactPerson || '',
          },
          id: this.state.itemId,
        };
        if (userIdentity.isAdmin) {
          getData.districtCode = this.state.changeCode;
        }
        this.props.dispatch({
          type: 'openConfig/postAddParams',
          payload: JSON.stringify(getData),
        }).then(() => {
          const { success, error } = this.props.openConfig.addRes;
          if (success) {
            message.success((this.state.submitType === 2) ? '编辑成功！' : '添加成功！');
            this.setState({ visible: false }, () => {
              this.getOpenParamsData();
            });
            form.resetFields();
          } else {
            message.error(error);
          }
        });
      }
    });
  }

  isShowReason = (type) => {
    if (type === '0') {
      this.setState({ showReason: true });
    } else {
      this.setState({ showReason: false });
    }
  }

  // 新增、编辑、查看监管参数配置
  addParamConfig = (id, type) => {
    // 如果为编辑或者查看，获取单条列表数据进修编辑、查看
    if (type === 'edit' || type === 'more') {
      const { paramsRes } = this.props.openConfig;
      const paramsItemData = paramsRes.filter(item => (item.id === id));
      let checkData = [];
      if (paramsItemData[0].announcementTypes) {
        checkData = paramsItemData[0].announcementTypes.split(',');
      }
      this.setState({
        checkData,
        checkDataName: (paramsItemData[0].announcementTypeName || '').split(','),
        paramsItemData: paramsItemData[0],
        publishModalTitle: (type === 'edit') ? '编辑监管参数' : '监管参数',
      });
    } else {
      this.setState({
        publishModalTitle: '新增监管参数',
        checkData: [],
        checkDataName: [],
        paramsItemData: [],
      });
    }
    this.setState({
      itemId: (type === 'edit') ? id : '',
      isMore: (type === 'more'),
      visible: true,
      paramData: [],
      submitType: (type === 'edit') ? 2 : 1,
    }, () => {
      this.getOpenInitData();
      this.showModal();
    });
  }

  // 删除监管参数配置列表
  delParam = (id) => {
    this.props.dispatch({
      type: 'openConfig/delParamsItem',
      payload: id,
    }).then(() => {
      message.success('删除成功！');
      this.getOpenParamsData();
    });
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef;
  }


  // tag选择
  changeTag = (id, name, isCheck) => {
    const { checkData, checkDataName } = this.state;
    if (isCheck) {
      this.setState({
        checkData: [...checkData, id],
        checkDataName: [...checkDataName, name],
      });
    } else {
      this.setState({
        checkData: checkData.filter(i => (parseInt(i, 10) !== id)),
        checkDataName: checkDataName.filter(i => i !== name),
      });
    }
  }

  // 同步区划保存／查看关闭
  hideViewModal = () => {
    const { types } = this.state;
    if (types) {
      // 同步区划保存
      this.props.form.validateFields((err, values) => {
        if (err) {
          message.warning('请完善信息');
          return;
        }
        this.warning(values);
      });
    } else {
      this.setState({
        viewVisible: false,
      }, () => {
        this.setState({
          viewHtmlTemp: '',
          types: null,
        });
      });
    }
  }
  warning = (values) => {
    const { changeCode } = this.state;
    // eslint-disable-next-line no-underscore-dangle
    const _this = this;
    confirm({
      content: '选择覆盖目标区划数据，将覆盖原区划下的配置，复制成功后将不可恢复，请确认是否继续。',
      okText: '继续',
      cancelText: '取消',
      onOk() {
        const pamse = {};
        pamse.targetDist = values.certificateType;
        pamse.sourceDist = changeCode;
        pamse.typeIdList = values.typeId;
        _this.props.dispatch({
          type: 'openConfig/getSaveCode',
          payload: JSON.stringify(pamse),
        }).then(() => {
          const { success, error } = _this.props.openConfig.saveCodeList;
          if (success) {
            message.success('同步成功');
            _this.setState({
              viewVisible: false,
              viewHtmlTemp: '',
              types: null,
            });
            _this.props.form.resetFields();
            _this.getOpenInitData();
          } else {
            message.error(error);
          }
        });
      },
      onCancel() {
        // This is intentions
      },
    });
  }

  hideViewCancel = () => {
    this.setState({
      viewVisible: false,
    }, () => {
      this.setState({
        viewHtmlTemp: '',
        types: null,
      });
    });
  }

  // 配置同步其他区划弹框显示
  showCodeModal = (types) => {
    const { announcementTypeData } = this.props.openConfig;
    this.setState({
      viewVisible: true,
      types,
      viewName: '公告开放配置同步到其他区划',
      treeDataAnnouncement: formatTreeData(announcementTypeData, { labelKey: 'name', valueKey: 'typeId' }), // 公告类型
    });
  }

  // 区划选择
  onChangeCode = (value, selectedOptions) => {
    this.setState({
      changeCode: value[value.length - 1],
      codeName: selectedOptions[selectedOptions.length - 1].name,
    }, () => {
      const { pathname } = this.props.location;
      if (pathname === '/config/open') {
        // 开放配置
        this.getOpenInitData();
      } else {
        // 监管配置
        this.getOpenParamsData();
      }
    });
  }

  render() {
    const { announcementTypeData, paramsRes } = this.props.openConfig;
    const { getFieldDecorator } = this.props.form;
    const {
      types, codeName,
      configType, treeDataAnnouncement, typeId,
      changeCode,
    } = this.state;
    const routes = [{
      label: '公告监管配置',
      to: '/openConfig',
    }];
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
    };
    const globalBtn = (userIdentity.isAdmin && configType === '1' && changeCode)
      ?
      [
        {
          label: '配置同步到其他区划',
          type: 'default',
          onClick: () => this.showCodeModal(1),
        },
      ]
      :
      [];

    const modalFooter = types === 1 ? [
      <Button key="back" onClick={this.hideViewCancel}>
        取消
      </Button>,
      <Button key="submit" type="primary" onClick={this.hideViewModal}>
        确认
      </Button>,
    ] : [
      <Button key="back" onClick={this.hideViewCancel}>
        关闭
      </Button>,
    ];

    return (
      <div>
        <Spin spinning={this.state.loading}>
          <ZcyBreadcrumb
            routes={routes}
            globalBtn={globalBtn}
            extraContent={
              userIdentity.isAdmin && (
                <DistrictCascader
                  localKey="open"
                  onChangeCode={this.onChangeCode}
                />
              )
            }
          />
          <Panel>
            {
              // 开发配置
              (this.state.configType === '1') ? (
                Array.isArray(announcementTypeData) ? (
                  announcementTypeData.length ? (
                    <Tabs defaultActiveKey={this.state.tabTag} tabPosition="left" onChange={this.changeTabs}>
                      {
                        announcementTypeData && announcementTypeData.map((item, index) => {
                          return (
                            <TabPane tab={item.name} key={index}>
                              <ConfigList
                                listData={item.children}
                                set={this.set}
                              />
                            </TabPane>
                          );
                        })
                      }
                    </Tabs>
                  ) : (
                    <Empty
                      description="请选择目标区划"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  )
                ) : (
                  <Empty
                    description="暂未配置公告类型，请联系管理员配置"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )
              ) : (
              // 监管配置
                (changeCode || !userIdentity.isAdmin) ? (
                  <div>
                    <ParamList
                      paramsData={paramsRes}
                      addParamConfig={this.addParamConfig}
                      delParam={this.delParam}
                    />
                  </div>
                ) : (
                  <Empty
                    description="请选择目标区划"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )
        
              )
            }
          </Panel>
        </Spin>
        {
          (this.state.configType === '1') ? (
            <SetModal
              typeId={typeId}
              wrappedComponentRef={this.saveFormRef}
              visible={this.state.visible}
              onCancel={this.handleCancel}
              onCreate={this.handleCreate}
              showReason={this.state.showReason}
              isShowReason={this.isShowReason}
              annItemData={this.state.annItemData}
              cgfsChange={(cgfs) => { this.cgfs = cgfs; }}
              distCode={this.state.changeCode}
            />
          ) : (
            <PublishModal
              wrappedComponentRef={this.saveFormRef}
              visible={this.state.visible}
              onCancel={this.handleCancel}
              onCreate={this.handleCreate}
              changeTag={this.changeTag}
              isMore={this.state.isMore}
              announcementTypeData={this.state.paramData}
              paramsItemData={this.state.paramsItemData}
              publishModalTitle={this.state.publishModalTitle}
            />
          )
        }
        <Modal
          title={this.state.viewName}
          visible={this.state.viewVisible}
          onOk={this.hideViewModal}
          onCancel={this.hideViewCancel}
          footer={modalFooter}
          width={640}
        >
          {
            types === 1 ?
              (
                <Form>
                  <Item
                    {...formItemLayout}
                    label="当前区划"
                    style={{ marginTop: 24 }}
                  >
                    {codeName}
                  </Item>
                  <Item
                    {...formItemLayout}
                    label="目标区划"
                    required
                  >
                    {
                      getFieldDecorator('certificateType', {
                        rules: [{
                          required: true, message: '请选择目标区划',
                        }],
                      })(
                        <DistrictTreeSelect />
                      )
                    }
                  </Item>
                  <Item
                    {...formItemLayout}
                    label="请选择公告类型"
                    required
                  >
                    {
                      getFieldDecorator('typeId', {
                        rules: [{
                          required: true, message: '请选择公告类型',
                        }],
                      })(
                        <TreeSelect
                          allowClear
                          style={{ width: 300 }}
                          treeData={treeDataAnnouncement}
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                          treeCheckable
                          showCheckedStrategy={SHOW_CHILD}
                          searchPlaceholder="请选择公告类型"
                        />
                      )
                    }
                  </Item>
                </Form>
              )
              : (
                <div
                  // eslint-disable-next-line react/no-danger
                  dangerouslySetInnerHTML={{ __html: this.state.viewHtmlTemp }}
                  style={{ height: 500, overflow: 'auto', overflowX: 'hidden' }}
                />
              )}
        </Modal>
      </div>
    );
  }
}
export default announcementOpenConfig;
