import React, { useEffect, useState } from 'react';
import {
  <PERSON>cy<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ZcyList,
  Spin,
  Alert,
  Panel,
  Empty,
  message,
  Modal,
  Switch,
} from 'doraemon';
import { fixNull, handleBtnByUiRule } from 'src/utils/utils';
import { getQueryParamObj } from '@zcy/utils';
import DistrictCascader from 'src/components/DistrictCascader';
import { 
  sortSiteDisplayPost,
  listSiteDistrictGet,
  listAnnouncementTypesInfoGet,
  ListAnnouncementTypesInfoGetResponse,
  relationSiteAnnouncementTypePost,
  deleteSitePost,
  ListSiteDistrictGetResponse,
  updateMainSitePost,
} from 'src/api/announcement/config';
import { useAnnSiteDrawer } from '../components/AnnSiteDrawer';
import { useAnnSiteLinkDrawer } from '../components/AnnSiteLinkDrawer';
import { SetAnnPush } from '../components/SetAnnPush';
import { PageType } from '../constants';
import './index.less';

const ROUTES = [
  {
    label: '公告站点配置',
  },
];

// type DeepRequired<T> = {
//   [K in keyof T]-?: NonNullable<T[K]> extends ({} | {}[]) ? DeepRequired<T[K]> : T[K];
// }

// type AnnouncementConfigSiteVoList = DeepRequired<ListSiteDistrictGetResponse>;

// 解出type
type AnnouncementConfigSiteVoList = Required<Required<ListSiteDistrictGetResponse>['result']>['announcementConfigSiteVoList'];

const SiteConfig: React.FC = () => {
  const searchObj = getQueryParamObj(window.location.search) as {
    districtCode: string
  };


  const [loading, setLoading] = useState(false);
  const [annPushLoading, setAnnPushLoading] = useState(false);
  const [districtCode, setDistrictCode] = useState(searchObj?.districtCode || '');
  const [listData, setListData] = useState<AnnouncementConfigSiteVoList>([]);
  const [annPushList, setAnnPushList] = useState<
    ListAnnouncementTypesInfoGetResponse['result']
  >([]);
  const [siteId, setSiteId] = useState(0);
  const [isExtend, setIsExtend] = useState<boolean | null>(false);
  const [extendDistrictName, setExtendDistrictName] = useState('');
  const [parentDistrictCode, setParentDistrictCode] = useState('');
  const [parentDistrictName, setParentDistrictName] = useState('');
  const [sortEnable, setSortEnable] = useState(false);
  const [pushVisible, setPushVisible] = useState(false);
  const [openDrawer] = useAnnSiteDrawer({
    districtCode,
    onSubmit: () => getListData(),
  });

  const [openLinkDrawer] = useAnnSiteLinkDrawer({
    onSubmit: () => getListData(),
  });

  useEffect(() => {
    if (districtCode) getListData();
    setSortEnable(false);
  }, [districtCode]);

  const getListData = () => {
    setLoading(true);
    listSiteDistrictGet({ districtCode: districtCode + '' })
      .then((res) => {
        if (!res.success) return;
        const data = res.result;
        setListData(data?.announcementConfigSiteVoList!);
        setIsExtend(data?.isExtend!);
        setExtendDistrictName(data?.extendDistrictName || '');
        setParentDistrictCode(data?.parentDistrictCode || '');
        setParentDistrictName(data?.parentDistrictName || '');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onChangeCode = (codeArr) => {
    if (codeArr?.length) {
      setDistrictCode(codeArr[codeArr.length - 1]);
    }
  };

  const submitSort = () => {
    setLoading(true);
    return sortSiteDisplayPost(
      {
        districtCode,
        announcementConfigSiteSortParamList: listData!.map((ele, index) => ({
          id: ele!.id,
          sort: index + 1,
        })),
      }
    )
      .then((res) => {
        if (!res.success) return;
        message.success('排序成功');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getGlobalBtn = () => {
    if (!districtCode) return [];
    return handleBtnByUiRule(
      [
        !sortEnable && {
          label: '设置排序',
          disabled: !(listData?.length >= 2),
          onClick: () => {
            setSortEnable(true);
          },
        },
        {
          label: '关联公告站点',
          disabled: sortEnable,
          type: 'primary',
          onClick: () => {
            openLinkDrawer({
              selectedList: listData,
              districtCode,
            });
          },
        },
        sortEnable && {
          label: '取消',
          onClick: () => {
            setSortEnable(false);
            getListData();
          },
        },
        sortEnable && {
          label: '确定',
          type: 'primary',
          onClick: () => {
            setSortEnable(false);
            submitSort().then(getListData);
          },
        },
      ].filter(Boolean)
    );
  };

  const look = (curId) => {
    openDrawer({
      curId,
      pageType: PageType.detail,
    });
  };
  const edit = (curId) => {
    openDrawer({
      curId,
      pageType: PageType.edit,
      onSubmit: () => getListData(),
    });
  };

  const del = (id) => {
    let title = '';
    if (listData.length === 1) {
      title = parentDistrictCode && parentDistrictCode === '0' ? '各省站点为独立配置，如需推送公告请自行关联站点！' : `将自动继承${parentDistrictName}区划配置，请确认！`;
    } else if (listData.length > 1) {
      title = '删除的站点将不再推送公告，请确认！';
    }
    Modal.confirm({
      title,
      closable: true,
      onOk: () => {
        return deleteSitePost({ id, districtCode }).then((res) => {
          if (!res.success) return;
          message.success('删除成功');
          getListData();
        });
      },
    });
  };

  const moveUp = (index) => {
    const curItem = listData[index];
    const prevItem = listData[index - 1];
    setListData((e) => {
      e[index] = prevItem;
      e[index - 1] = curItem;
      return [...e];
    });
  };
  const moveDown = (index) => {
    const curItem = listData[index];
    const nextItem = listData[index + 1];
    setListData((e) => {
      e[index] = nextItem;
      e[index + 1] = curItem;
      return [...e];
    });
  };

  const openPushModal = (announcementSiteId, id) => {
    setSiteId(id);
    listAnnouncementTypesInfoGet({
      districtCode: districtCode + '',
      announcementSiteId,
    }).then((res) => {
      if (!res.success) return;
      setAnnPushList(res.result!);
      setPushVisible(true);
    });
  };

  const pushSwitchChange = (value, index) => {
    setAnnPushList((e) => {
      e![index].isRelation = value;
      return [...e!];
    });
  };

  const closePushModal = () => {
    setPushVisible(false);
  };

  const submitPushModal = () => {
    setAnnPushLoading(true);
    const announcementTypes = annPushList?.filter(item => item.isRelation)
      .map(item => item.announcementTypeCode!) || [];
    relationSiteAnnouncementTypePost({
      relationId: siteId,
      districtCode: districtCode + '',
      announcementTypes: announcementTypes || [],
    }).then((res) => {
      if (res.success) {
        message.success('设置成功');
        setPushVisible(false);
      }
      setAnnPushLoading(false);
    });
  };

  const getTableColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'index',
        render: (_, __, index) => index + 1,
        width: 70,
        align: 'center',
      },
      {
        title: '公告站点名称',
        dataIndex: 'siteName',
        render: (text) => fixNull(text),
      },
      {
        title: '前台展示',
        dataIndex: 'isDisplay',
        render: (text) => {
          if (text === true) return '是';
          if (text === false) return '否';
          return '-';
        },
      },
      {
        title: '网站域名',
        dataIndex: 'siteDomain',
        render: (text) => fixNull(text),
      },
      {
        title: '主站点',
        dataIndex: 'isMainSite',
        render: (text, { id }) => {
          return (
            <Switch 
              disabled={sortEnable}
              checked={text}
              onChange={(val) => {
                setLoading(true);
                updateMainSitePost({
                  districtCode,
                  id,
                  isMainSite: val,
                }).then(res => {
                  if (!res.success) return;
                  return message.success('操作成功', () => {
                    getListData();
                  });
                }).finally(() => {
                  setLoading(false);
                });
              }}
            />
          );
        },
      },
      {
        title: '后台入口',
        dataIndex: 'siteManagerUrl',
        render: (text) => fixNull(text),
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 150,
        render: (_, { id, announcementSiteId }, index) => {
          if (sortEnable) {
            return (
              <React.Fragment>
                {index === 0 ? null : (
                  <a onClick={() => moveUp(index)} className="zcy-mg-r-8">
                    上移
                  </a>
                )}
                {index === listData.length - 1 ? null : (
                  <a onClick={() => moveDown(index)} className="zcy-mg-r-8">
                    下移
                  </a>
                )}
              </React.Fragment>
            );
          }
          return (
            <React.Fragment>
              <a onClick={() => openPushModal(announcementSiteId, id)} className="zcy-mg-r-20">
                设置公告推送
              </a>
              <a onClick={() => look(id)} className="zcy-mg-r-8">
                查看
              </a>
              <a onClick={() => edit(id)} className="zcy-mg-r-8">
                编辑
              </a>
              <a onClick={() => del(id)} className="zcy-mg-r-8">
                删除
              </a>
            </React.Fragment>
          );
        },
      },
    ];
  };

  const renderContent = () => {
    if (!districtCode) {
      return (
        <Empty
          description="请选择目标区划"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ); 
    }
    if (!listData?.length) {
      return (
        <Empty
          description="暂无公告站点配置数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ); 
    }
    return (
      <React.Fragment>
        <Alert
          message="此处配置的公告站点，用于在当前区划发布公告后，以列表的方式展示在各网站中对应的一个或多个公告详情页面链接，并在公告中心的公告发布结果中依次陈列。"
          type="firstinfo"
          closable
          showIcon
          iconType="exclamation-circle-o"
        />
        <ZcyList
          table={{
            rowKey: 'id',
            dataSource: listData,
            columns: getTableColumn(),
            pagination: false,
          }}
        />
      </React.Fragment>
    );
  };

  const setAnnPushProps = {
    pushVisible,
    submitPushModal,
    annPushList,
    pushSwitchChange,
    closePushModal,
    annPushLoading,
  };

  const extendOrNoConfig = () => {
    const styleObj = { color: '#bfbfbf', marginLeft: '8px' };
    if (isExtend === null) {
      return (
        <span style={styleObj}>
          无数据
        </span>
      );
    }
    return (
      isExtend ? (
        <span style={styleObj}>
          配置继承自：{extendDistrictName}
        </span>

      ) : (
        <span style={styleObj}>
          配置位于当前区划
        </span>
      )
    );
  };

  return (
    <div className={`siteConfig ${sortEnable ? 'sortEnable' : ''}`}>
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={getGlobalBtn()}
          extraContent={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DistrictCascader
                localKey="site"
                defaultValue={searchObj.districtCode}
                disabled={false}
                onChangeCode={onChangeCode}
              />
              {
                // 添加区划判断
                districtCode ? extendOrNoConfig() : null
              }
            </div>
          }
        />
        <Panel>{renderContent()}</Panel>
      </Spin>
      <SetAnnPush {...setAnnPushProps} />
    </div>
  );
};

export default SiteConfig;
