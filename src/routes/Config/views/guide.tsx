import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>cyList, Spin, Modal, message, Progress, Table } from 'doraemon';
import EmptyBlock from 'src/components/Empty';
import DistrictCascader from 'src/components/DistrictCascader';
import { getQueryParamObj, appendQueryParams } from '@zcy/utils';
import { listAnnouncementGuideGet, synchronousGet, deleteAnnouncementGuideGet, checkButtonGet, initAnnouncementGuideGet, checkDataExistGet } from 'src/api/announcement/api/config/guide';
import type { ListAnnouncementGuideGetResponse, CheckButtonGetResponse } from 'src/api/announcement/api/config/guide';
import type { FFC } from 'src/types/component';
import { useGuideAddAnnDrawer } from '../components/GuideAddAnnDrawer';
import { PageType } from '../constants';
import { useGuideActionLogDrawer } from '../components/GuideActionLogDrawer';
import './guide.less';

type TTableActionType = 'guide' | 'guideDetail' | 'sync' | 'look' | 'edit' | 'delete'
type TRes = Required<Required<ListAnnouncementGuideGetResponse>>['result']
type TExtendInfo = Omit<TRes, 'guideList'>
type TResData = Required<TRes>['guideList']
type TResDataList = Required<TResData>['data']
type TButtonInfo = Required<CheckButtonGetResponse>['result']


// eslint-disable-next-line no-undef
type TListResItem = PickListItem<TResDataList>

const Actions = Table.Actions;


const ROUTES = [{
  label: '公告接入管理',
}];

const DEFAULT_PARAMS = {
  pageNo: 1,
  pageSize: 10,
};

const GuideConfig: FFC = () => {
  const searchObj = getQueryParamObj(window.location.search) as {
    districtCode: string
  };

  const [loading, setLoading] = useState(false);
  const [districtCode, setDistrictCode] = useState(searchObj?.districtCode || '');
  const [districtName, setDistrictName] = useState('');
  const [listData, setListData] = useState<TListResItem[]>([]);
  const [total, setTotal] = useState(0);
  const [extendInfo, setExtendInfo] = useState<TExtendInfo>({});
  const [listParams, setListParams] = useState(DEFAULT_PARAMS);
  const [buttonInfo, setButtonInfo] = useState<TButtonInfo>({});

  const [openGuideAddDrawer] = useGuideAddAnnDrawer({});
  const [openGuideActionLogDrawer] = useGuideActionLogDrawer({});


  useEffect(() => {
    if (districtCode) init(districtCode, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [districtCode]);

  /**
   * 
   * @param code 区划code
   * @param isInit 是否重置到首页
   */
  const init = (code, isInit = false) => {
    if (code) {
      getListData(isInit ? DEFAULT_PARAMS : {});
      getButtons(code);
    }
  };

  const getGlobalBtn = () => {
    if (!districtCode) return [];
    return [
      {
        label: '操作日志',
        onClick: () => {
          openGuideActionLogDrawer({
            districtCode,
            logType: 'list',
          });
        },
      },
      buttonInfo?.canInit && {
        label: '初始化',
        type: 'primary',
        onClick: () => {
          // 文案待确认
          return Modal.confirm({
            title: '确认同步该配置？',
            closable: true,
            onOk: () => {
              setLoading(true);
              initAnnouncementGuideGet({ districtCode }).then(res => {
                if (!res.success) return;
                return message.success('同步成功', () => init(districtCode, true));
              }).finally(() => {
                setLoading(false);
              });
            },
          });
        },
      },
      buttonInfo?.canCreate && {
        label: '新增公告接入',
        type: 'primary',
        onClick: () => {
          checkExtend() && openGuideAddDrawer({
            onSubmit: () => init(districtCode, true),
            districtCode,
            pageType: PageType.add,
          });
        },
      },
    ].filter(Boolean);
  };

  const getButtons = (code) => {
    return checkButtonGet({
      districtCode: code,
    }).then(res => {
      if (!res.success) return;
      setButtonInfo(res.result ?? {});
    });
  };

  const getListData = (params = {}) => {
    const { pageNo, pageSize } = { ...listParams, ...params };
    setLoading(true);
    listAnnouncementGuideGet({
      districtCode,
      pageNo: pageNo + '',
      pageSize: pageSize + '',
    }).then(res => {
      if (!res.success) return;
      setListData(res.result?.guideList?.data ?? []);
      setTotal(res.result?.guideList?.total ?? 0);
      setExtendInfo({
        isExtend: res.result?.isExtend,
        districtCode: res.result?.districtCode,
        districtName: res.result?.districtName,
      });
    }).finally(() => {
      setLoading(false);
    });
  };

  const onChangeCode = (codeArr, arr) => {
    if (codeArr?.length) {
      setDistrictCode(codeArr[codeArr.length - 1]);
      setDistrictName(arr[arr.length - 1].name);
    } else {
      setDistrictCode('');
      setDistrictName('');
      setListData([]);
    }
  };

  const onChangeTable = ({ current, pageSize }) => {
    getListData({
      pageNo: current,
      pageSize,
    });
    setListParams({
      pageNo: current,
      pageSize,
    });
  };

  const getCurNodeInfo = nodeList => {
    // 当前第一个状态不是已提交的节点，即为当前配置环节
    return nodeList.find((ele:any) => ele.status !== 2);
  };

  const getTableColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'index',
        width: 70,
        render: (_, __, index) => index + 1,
      },
      {
        title: '公告code',
        dataIndex: 'announcementType',
        width: 100,
        render: (_, { announcementType }) => announcementType,
      },
      {
        title: '接入公告类型',
        dataIndex: 'announcementTypeName',
        width: 130,
        render: (_, { announcementTypeName }) => announcementTypeName,
      },
      {
        title: '业务公告',
        dataIndex: 'businessAnnouncement',
        width: 90,
        render: (_, { businessAnnouncement }) => (businessAnnouncement ? '是' : '否'),
      },
      {
        title: '手工公告',
        dataIndex: 'manualAnnouncement',
        width: 90,
        render: (_, { manualAnnouncement }) => (manualAnnouncement ? '是' : '否'),
      },
      {
        title: '配置进度',
        dataIndex: 'progress',
        width: 240,
        render: (_, { nodeList = [] }) => {
          const list:any[] = nodeList ?? [];
          const doneCount = list.filter(ele => ele.status === 2)?.length;
          const progressCount = list.filter(ele => ele.status === 1)?.length;
          const todoCount = list.filter(ele => ele.status === 0)?.length;
          const nodeTotal = doneCount + progressCount + todoCount;
          const percent = nodeTotal > 0 ? (doneCount + progressCount) / nodeTotal * 100 : 0;
          const successPercent = nodeTotal > 0 ? doneCount / nodeTotal * 100 : 0;
          return (
            <div className="zcy-flex">
              <Progress 
                showInfo={false}
                percent={Math.round(percent)} 
                successPercent={Math.round(successPercent)}
              />
              <span style={{
                width: '48px',
                marginLeft: '8px',
                color: 'rgba(0, 0, 0, 0.45)',
              }}
              >
                {Math.round(successPercent)}% 
              </span>
            </div>
          );
        },
      },
      {
        title: '当前配置环节',
        dataIndex: 'curNodeInfo',
        width: 120,
        render: (_, { nodeList = [] }) => {
          const curNodeInfo = getCurNodeInfo(nodeList ?? []);
          return curNodeInfo ? (
            <span>
              {curNodeInfo.nodeOrder} : {curNodeInfo.nodeName}
            </span>
          ) : (
            <span>
              已完成
            </span>
          );
        },
      },
      {
        title: '配置状态',
        dataIndex: 'status',
        width: 100,
        render: (_, { status }) => {
          if (status === 0) {
            return (
              <span style={{ color: '#F59A23' }}>
                未发布
              </span>
            );
          }
          if (status === 1) {
            return (
              <span style={{ color: '#528302' }}>
                已发布
              </span>
            );
          }
          return '-';
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 180,
        render: (_, item) => {
          const { canDetail, canEdit, canDelete, canEnable } = item;
          const actions:any[] = [];
          if (canDetail) {
            actions.push({
              label: '查看配置',
              onRowClick: () => handleAction('guide', { ...item }),
            });
          }
          if (canEdit) {
            actions.push({
              label: '进入配置',
              onRowClick: () => handleAction('guideDetail', { ...item }),
            });
          }
          if (canEnable) {
            actions.push({
              label: '发布',
              onRowClick: () => handleAction('sync', { ...item }),
            });
          }
          if (true) {
            actions.push({
              label: '查看',
              onRowClick: () => handleAction('look', { ...item }),
            });
          }
          if (true) {
            actions.push({
              label: '编辑',
              onRowClick: () => handleAction('edit', { ...item }),
            });
          }
          if (canDelete) {
            actions.push({
              label: '删除',
              onRowClick: () => handleAction('delete', { ...item }),
            });
          }
          return (
            <Actions maxShowSize={4} actions={actions} />
          );
        },
      },
    ];
  };

  const checkExtend = () => {
    if (extendInfo.isExtend) {
      Modal.warning({
        title: '非独立配置，请先进行初始化',
      });
    }
    return (!extendInfo.isExtend);
  };


  const handleAction = (type: TTableActionType, item: TListResItem) => {
    const curNodeInfo = getCurNodeInfo(item.nodeList ?? []);
    switch (type) {
      // 查看配置
      case 'guide':
        return checkDataExistGet({ id: item.id + '' }).then(res => {
          if (!res.success) return;
          if (!res.result) return message.error('数据未同步无法查看！');
          return window.open(appendQueryParams({
            districtCode,
            districtName,
          }, {
            url: `/announcement-front/#/config/guideDetail/${item.id}/${'detail'}`,
          }));
        });
      // 进入配置 
      case 'guideDetail':
        return checkExtend() && window.open(appendQueryParams(Object.assign({
          districtCode,
          districtName,
        }, curNodeInfo ? { nodeCode: curNodeInfo.nodeCode } : {},), {
          url: `/announcement-front/#/config/guideDetail/${item.id}/${'edit'}`,
        }));
      case 'sync':
        return checkExtend() && Modal.confirm({
          title: '确认发布该配置？',
          closable: true,
          onOk: () => {
            synchronousGet({
              relationId: item.id + '',
              districtCode,
            }).then(res => {
              if (!res.success) return;
              return message.success('发布成功！', () => init(districtCode));
            });
          },
        });
      case 'look':
        return openGuideAddDrawer({
          curId: item.id,
          pageType: PageType.detail,
        });
      case 'edit':
        return checkExtend() && openGuideAddDrawer({
          onSubmit: () => init(districtCode),
          curId: item.id,
          districtCode,
          pageType: PageType.edit,
        });
      case 'delete':
        return checkExtend() && Modal.confirm({
          title: '确认删除该配置？',
          content: '注意！删除后，当前区划将无法再创建该公告类型的业务公告及手工公告，确认继续？',
          closable: true,
          onOk: () => {
            deleteAnnouncementGuideGet({
              relationId: item.id + '',
              districtCode,
            }).then(res => {
              if (!res.success) return;
              return message.success('删除成功！', () => init(districtCode, false));
            });
          },
        });
    }
  };

  const renderExtra = () => {
    const styleObj = { color: '#bfbfbf', marginLeft: '8px' };
    if (extendInfo.isExtend === null) {
      return (
        <span style={styleObj}>无数据</span>
      );
    }
    return extendInfo.isExtend ? (
      <div style={{ display: 'inline-block' }}>
        <span style={styleObj}>配置继承自：{extendInfo.districtName}</span>
      </div>
    ) : (
      <div style={{ display: 'inline-block' }}>
        <span style={styleObj}>配置位于当前区划</span>
      </div>
    );
  };

  return (
    <div className="guide">
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={getGlobalBtn()}
          extraContent={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <DistrictCascader
                defaultValue={searchObj.districtCode}
                localKey="guide"
                disabled={false}
                allowClear
                onChangeCode={onChangeCode}
                updateDistrictNames={(names) => {
                  if (names?.length) {
                    setDistrictName(names[names.length - 1]);
                  }
                }}
              />
              {districtCode ? renderExtra() : null}
            </div>
          }
        />
        {
          districtCode ? (
            <ZcyList
              table={{
                rowKey: 'id',
                dataSource: listData,
                columns: getTableColumn(),
                onChange: onChangeTable,
                pagination: {
                  total,
                  showSizeChanger: true,
                },
              }}
            />
          ) : <EmptyBlock />
        }
      </Spin>
    </div>
  );
};


export default GuideConfig;
