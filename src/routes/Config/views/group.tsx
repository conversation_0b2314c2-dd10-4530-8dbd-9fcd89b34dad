import React, { useEffect, useState } from 'react';
import { ZcyB<PERSON>crumb, ZcyList, Spin, Switch, Modal, message, Tag } from 'doraemon';
import DistrictCascader from 'src/components/DistrictCascader';
import EmptyBlock from 'src/components/Empty';
import { checkButtonGet, listAnnouncementGroupGet, openAnnouncementGroupGet, initAnnouncementGroupGet, ListAnnouncementGroupGetResponse, deleteAnnouncementGroupGet } from 'src/api/announcement/api/config/group';
import { getQueryParamObj } from '@zcy/utils';
import './index.less';
import { useAnnGroupDrawer } from '../components/AnnGroupDrawer';
import { PageType } from '../constants';
import { useAnnGroupSyncModal } from '../components/AnnGroupSyncModal';

const ROUTES = [{
  label: '公告分组配置',
}];

const GroupConfig: React.FC = () => {
  const searchObj = getQueryParamObj(window.location.search) as {
    districtCode: string
  };

  const [loading, setLoading] = useState(false);
  const [updateButtonsCount, setUpdateButtonsCount] = useState(0);
  const [breadcrumbBtnInfo, setBreadcrumbBtnInfo] = useState({
    canInit: false,
    canSyn: false,
    canOpen: false,
    currentOpenFlag: false,
  });
  const [districtCode, setDistrictCode] = useState(searchObj?.districtCode || '');
  const [districtName, setDistrictName] = useState('');
  const [listData, setListData] = useState<ListAnnouncementGroupGetResponse['result']>([]);
  const [openAnnGroupDrawer] = useAnnGroupDrawer({});
  const [openGroupSyncModal] = useAnnGroupSyncModal({
    title: '公告分组配置同步到其他区划',
  });

  useEffect(() => {
    if (districtCode) {
      getListData();
      setUpdateButtonsCount(e => e + 1);
    }
  }, [districtCode]);

  useEffect(() => {
    if (updateButtonsCount) updateButtons();
  }, [updateButtonsCount]);

  const getListData = () => {
    setLoading(true);
    listAnnouncementGroupGet({ districtCode }).then(res => {
      if (!res.success) return;
      setListData(res.result ?? []);
    }).finally(() => {
      setLoading(false);
    });
  };

  const onChangeCode = (codeArr, arr) => {
    if (codeArr?.length) {
      setDistrictCode(codeArr[codeArr.length - 1]);
      setDistrictName(arr[arr.length - 1].name);
    } else {
      setDistrictCode('');
      setDistrictName('');
      setListData([]);
    }
  };

  const updateButtons = () => {
    checkButtonGet({ districtCode }).then(res => {
      if (!res.success) return;
      setBreadcrumbBtnInfo(e => ({
        canInit: !!res.result?.canInit,
        canSyn: !!res.result?.canSyn,
        canOpen: !!res.result?.canOpen,
        currentOpenFlag: !!res.result?.currentOpenFlag,
      }));
    });
  };

  const edit = ({
    groupName, id, announcementTypes,
  }) => {
    openAnnGroupDrawer({
      districtCode,
      curInfo: {
        id,
        groupName,
        announcementTypes,
      },
      pageType: PageType.edit,
      onSubmit: () => getListData(),
    });
  };
  const del = (id) => {
    return Modal.confirm({
      title: '确认删除公告分组？',
      closable: true,
      onOk: () => {
        setLoading(true);
        deleteAnnouncementGroupGet({ id }).then(res => {
          if (!res.success) return;
          message.success('删除分组成功');
          getListData();
          setUpdateButtonsCount(e => e + 1);
        }).finally(() => {
          setLoading(false);
        });
      },
    });
  };

  const getTableColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'index',
        width: 70,
        render: (_, __, index) => index + 1,
      }, {
        title: '公告分组',
        width: 120,
        dataIndex: 'groupName',
      }, {
        title: '包含公告类型',
        dataIndex: 'announcementTypeDtoList',
        render: (announcementTypeDtoList = []) => {
          return (announcementTypeDtoList ?? []).map((ele:any) => 
            (<Tag key={ele.typeId} style={{ border: 'none', margin: 4, background: '#f0f1f5', color: '#333', fontSize: '14px' }}>{ele.typeName}</Tag>)
          );
        },
      }, {
        title: '包含类型数量',
        width: 120,
        dataIndex: 'announcementTypeDtoListCount',
        render: (_, { announcementTypeDtoList }) => {
          return (announcementTypeDtoList ?? [])?.length ?? 0;
        },
      }, {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (_, { groupName, id, announcementTypeDtoList }) => {
          return (
            <React.Fragment>
              <a onClick={() => edit({ groupName, id, announcementTypes: announcementTypeDtoList.map(ele => ele.typeCode) })} className="zcy-mg-r-8">编辑</a>
              <a onClick={() => del(id)} >删除</a>
            </React.Fragment>
          );
        },
      },
    ];
  };

  const beforeSwitchEnable = (val) => {
    const title = val ? '启用后，公告类型分组配置将实时生效，确认继续？' : '停用后将关闭所有类型手工公告的前台入口，直到再次启用，确认继续？';
    Modal.confirm({
      title,
      closable: true,
      onOk: () => {
        switchEnable(val);
      },
    });
  }; 

  const renderEnabled = () => {
    return (
      <div style={{ display: 'inline-block', marginLeft: 16 }}>
        <span>是否启用：</span>
        <Switch
          disabled={!breadcrumbBtnInfo.canOpen}
          onChange={beforeSwitchEnable}
          checked={breadcrumbBtnInfo.currentOpenFlag}
          checkedChildren="启用"
          unCheckedChildren="停用"
        />
      </div>
    );
  };

  const switchEnable = (val) => {
    setLoading(true);
    openAnnouncementGroupGet({
      districtCode,
      status: (val ? 1 : 0) + '',
    }).then(res => {
      if (!res.success) return;
      setUpdateButtonsCount(e => e + 1);
    }).finally(() => {
      setLoading(false);
    });
  };

  const handleInit = () => Modal.confirm({
    title: '将自动导入公告大类信息作为分组的初始配置，确认继续？',
    closable: true,
    onOk: () => {
      setLoading(true);
      initAnnouncementGroupGet({
        districtCode,
      }).then(res => {
        if (!res.success) return;
        message.success('初始化成功');
        getListData();
        setUpdateButtonsCount(e => e + 1);
      }).finally(() => {
        setLoading(false);
      });
    },
  });
  const handleSyn = () => openGroupSyncModal({
    districtName,
    districtCode,
    onSubmit: () => {
      setUpdateButtonsCount(e => e + 1);
    },
  });
 
  const handleCreate = () => {
    openAnnGroupDrawer({
      pageType: PageType.add,
      districtCode,
      onSubmit: () => {
        getListData();
        setUpdateButtonsCount(e => e + 1);
      },
    });
  };
  

  const getGlobalBtn = () => {
    if (!districtCode) return [];
    const { 
      canInit,
      canSyn,
    } = breadcrumbBtnInfo;
    const buttons = [
      {
        label: '公告分组配置初始化',
        disabled: !canInit,
        onClick: () => handleInit(),
      },
      {
        label: '配置同步到其他环境',
        disabled: !canSyn,
        onClick: () => handleSyn(),
      }, 
      {
        label: '新增公告分组',
        type: 'primary',
        onClick: () => handleCreate(),
      }];
    
    return buttons;
  };

  return (
    <div>
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={getGlobalBtn()}
          extraContent={
            <React.Fragment>
              <DistrictCascader
                defaultValue={searchObj.districtCode}
                disabled={false}
                allowClear
                localKey="group"
                onChangeCode={onChangeCode}
              />
              {districtCode ? renderEnabled() : null}
            </React.Fragment>
          }
        />
        {
          districtCode ? (
            <ZcyList
              table={{
                rowKey: 'id',
                dataSource: listData,
                columns: getTableColumn(),
                pagination: false,
              }}
            />
          ) : <EmptyBlock />
        }
        
      </Spin>
    </div>
  );
};


export default GroupConfig;
