import React, { useEffect, useState } from 'react';
import { ZcyB<PERSON>crumb, Input, Spin, ZcyList, Modal, Badge } from 'doraemon';
import { useAnnTypeLogDrawer } from '../components/AnnTypeLogDrawer';
import { useAnnTypeDrawer } from '../components/AnnTypeDrawer';
import { useAnnTypeCountDrawer } from '../components/AnnTypeCountDrawer';
import { useAnnTypeSynDrawer, SyncType } from '../components/AnnTypeSynDrawer';
import { PageType, SourceType, DEL_ALERT_MSG } from '../constants';
import { listAnnTypesList, listBigAnnType, getCloudRegion } from '../services/api';
import './index.less';

const ROUTES = [
  {
    label: '公告类型管理',
    to: '/type',
  },
];

const initPageParams = {
  pageNo: 1,
  pageSize: 10,
};

const TypeConfig = () => {
  const [loading, setLoading] = useState(false);
  const [openCountDrawer] = useAnnTypeCountDrawer();
  const [openAnnTypeDrawer] = useAnnTypeDrawer();
  const [openAnnTypeSynDrawer] = useAnnTypeSynDrawer({
    syncType: SyncType.annType,
  });
  const [openLogDrawer] = useAnnTypeLogDrawer({
    source: SourceType.announcementTypeConfig,
  });
  const [listData, setListData] = useState({
    data: [],
    total: 0,
  });
  const [addOfAuth, setAddOfAuth] = useState(false);
  const [searchParams, setSearchParams] = useState(initPageParams);
  const [filteredValue, setFilteredValue] = useState({});
  const [bigAnnType, setBigAnnType] = useState([]);

  const getListBigAnnType = () => listBigAnnType().then((res) => {
    setBigAnnType((res.result ?? []).map(ele => ({
      text: ele.typeName,
      value: ele.typeCode,
    })));
  });

  const getBaseConfig = () => {
    getCloudRegion().then((res) => {
      if (!res.success) return;
      setAddOfAuth(res.result);
    });
  };

  useEffect(() => {
    getListBigAnnType();
    onRefresh();
    getBaseConfig();
  }, []);

  const globalBtn = [{
    label: '公告启停统计',
    onClick: () => openCountDrawer(),
  }, {
    label: '操作日志',
    onClick: () => openLogDrawer(),
  }, 
  addOfAuth && {
    label: '配置同步到其他环境',
    onClick: () => openAnnTypeSynDrawer({
      onSubmit: () => onRefresh(),
    }),
  },
  addOfAuth && {
    label: '新建公告类型',
    type: 'primary',
    onClick: () => openAnnTypeDrawer({
      pageType: PageType.add,
      onSubmit: () => onRefresh(),
    }),
  }].filter(Boolean);

  const getData = (params) => {
    return listAnnTypesList({
      ...params,
    }).then((res) => {
      setListData({
        data: res.result.data ?? [],
        total: res.result.totalPage,
      });
      setLoading(false);
    });
  };
  
  const look = (id) => {
    openAnnTypeDrawer({
      pageType: PageType.detail,
      curId: id,
      onSubmit: () => {},
    });
  };

  const edit = (id) => {
    openAnnTypeDrawer({
      pageType: PageType.edit,
      curId: id,
      onSubmit: () => onRefresh(),
    });
  };

  const del = () => Modal.warning({
    title: '删除公告类型',
    content: DEL_ALERT_MSG,
    closable: true,
  });

  const onRefresh = () => {
    getData({ ...initPageParams, ...searchParams, ...getFilterData(filteredValue) });
  };

  const getFilterData = (filters) => {
    const temp = {};
    if (filters.isEnable && filters.isEnable.length) {
      temp.isEnable = filters.isEnable[0];
    } else {
      temp.isEnable = null;
    }
    if (filters.parentType && filters.parentType.length) {
      temp.parentType = filters.parentType[0];
    } else {
      temp.parentType = null;
    }
    return temp;
  };

  const onSearch = (params, t) => {
    const data = { ...initPageParams, ...params, ...getFilterData(filteredValue) };
    setSearchParams(params);
    if (t === 'btn') {
      getData(data);
    }
  };

  const onChange = ({ current, pageSize }, filters) => {
    setFilteredValue(filters);
    const filterParams = getFilterData(filters);
    setSearchParams(e => ({
      ...e,
      pageNo: current,
      pageSize,
    }));
    getData({
      ...searchParams,
      ...filterParams,
      pageNo: current,
      pageSize,
    });
  };

  const getTableColumn = () => {
    return [
      {
        title: '公告类型名称',
        dataIndex: 'typeName',
      },
      {
        title: '公告Code',
        dataIndex: 'typeCode',
        width: 105,
      },
      {
        title: '公告特性值',
        dataIndex: 'characteristicValues',
      }, {
        title: '启用状态',
        dataIndex: 'isEnable',
        width: 115,
        filters: [
          {
            text: '启用',
            value: 1,
          },
          {
            text: '停用',
            value: 0,
          },
        ],
        filteredValue: filteredValue.isEnable ?? [],
        filterMultiple: false,
        render: (val) => {
          if (val === 1) return <Badge status="success" text="启用" />;
          return <Badge status="default" text="停用" />;
        },
      }, {
        title: '所属公告大类',
        dataIndex: 'parentType',
        width: 140,
        filters: bigAnnType,
        filteredValue: filteredValue.parentType ?? [],
        filterMultiple: false,
        render: (_, { parentTypeName }) => {
          return parentTypeName;
        },
      }, {
        title: '操作',
        dataIndex: 'action',
        width: 140,
        render: (_, { id }) => {
          return (
            <React.Fragment>
              <a onClick={() => look(id)} className="zcy-mg-r-8">查看</a>
              <a onClick={() => edit(id)} className="zcy-mg-r-8">编辑</a>
              <a onClick={() => del()} >删除</a>
            </React.Fragment>
          );
        },
      },
    ];
  };

  const customItem = [{
    label: '公告类型名称',
    id: 'typeName',
    render: () => {
      return <Input />;
    },
  }, {
    label: '公告Code',
    id: 'typeCode',
    render: () => {
      return <Input />;
    },
  }, {
    label: '公告特性值',
    id: 'characteristicValues',
    render: () => {
      return <Input />;
    },
  }];

  return (
    <div className="typeConfig">
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={globalBtn}
        />
        <ZcyList
          customItem={customItem}
          onSearch={onSearch}
          table={{
            rowKey: 'id',
            dataSource: listData.data,
            columns: getTableColumn(),
            onChange,
            pagination: {
              total: listData.total,
              showSizeChanger: true,
            },
          }}
        />
      </Spin>
    </div>
  );
};

export default TypeConfig;
