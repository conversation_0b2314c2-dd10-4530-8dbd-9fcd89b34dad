import React, { useEffect, useState, useRef } from 'react';
import { Zcy<PERSON>read<PERSON>rumb, Spin, Button, message } from 'doraemon';
import FlowNav from 'src/components/FlowNav';
import { FFC } from 'src/types/component';
import { getQueryParamObj, appendQueryParams } from '@zcy/utils';
import {
  listNodeGet,
  announcementDefinitionGet, announcementTitleConfigurationGet,
  businessFieldDefinitionGet, contentSubjectGet, contentSupervisionGet,
  processDefinitionGet, processApplicationGet, announcementOptionsGet,
  announcementPushGet, announcementDisplayGet, announcementEntranceGet,
  saveBusinessFieldDefinitionPost,
  saveAnnouncementTitleConfigurationPost,
  saveContentSubjectPost,
  saveContentSupervisionPost,
  saveProcessDefinitionPost,
  saveProcessApplicationPost,
  saveAnnouncementOptionsPost,
  saveAnnouncementPushPost,
  saveAnnouncementDisplayPost,
  saveAnnouncementEntrancePost,
} from 'src/api/announcement/api/config/guide';
import type { ListNodeGetResponse } from 'src/api/announcement/api/config/guide';
import { useGuideActionLogDrawer } from '../components/GuideActionLogDrawer';
import { ConfigMain, ConfigContent } from '../components/GuideConfig';
import type { TFlowNodeCode, TFormData } from '../components/GuideConfig/types';
import { showAccountModal } from '../components/GuideConfig/utils';

import './guide.less';

type TNodeList = Required<ListNodeGetResponse>['result']
// eslint-disable-next-line no-undef
type TNodeItem = PickListItem<TNodeList>


const ROUTES = [{
  label: '公告配置向导',
}];

const GuideConfig: FFC<{}, { guideId: string, actionType: 'edit' | 'detail' }> = ({ match, history }) => {
  const searchObj = getQueryParamObj(window.location.search) as {
    nodeCode: TFlowNodeCode
    districtCode: string
    districtName: string
  };

  const nodeCode = searchObj.nodeCode || 'AnnouncementDefinition';
  const districtName = searchObj.districtName;
  const districtCode = searchObj.districtCode;

  const guideId = match.params?.guideId;
  const actionType = match.params?.actionType || 'detail';

  const [loading, setLoading] = useState(false);
  const [currentNodeCode, setCurrentNodeCode] = useState(nodeCode);
  const [nodeList, setNodeList] = useState<TNodeList>([]);
  const formRef = useRef<any>(null);
  const [nodeData, setNodeData] = useState<TFormData>({});
  const [openGuideActionLogDrawer] = useGuideActionLogDrawer({
    hasSubmoduleName: true,
  });

  const getActionTypeVal = (t: 'edit' | 'detail') => {
    if (t === 'edit') return '2';
    if (t === 'detail') return '1';
    return '';
  };

  const getCurrentNode = (code: TFlowNodeCode, list: TNodeList) => {
    if (!code) return null;
    const n = list.find(ele => ele.nodeCode === code);
    return n ?? null;
  };

  const initContent = (node: TNodeItem | null) => {
    if (!node) return null;
    const nodeId = node.id;
    const nCode = node.nodeCode;

    const params = {
      nodeId: nodeId + '',
      type: getActionTypeVal(actionType),
    };
    switch (nCode) {
      case 'AnnouncementDefinition':
        return announcementDefinitionGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'BusinessFieldDefinition':
        return businessFieldDefinitionGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'AnnouncementTitleConfiguration':
        return announcementTitleConfigurationGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'ContentSubject':
        return contentSubjectGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'ContentSupervision':
        return contentSupervisionGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'ProcessDefinition':
        return processDefinitionGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'ProcessApplication':
        return processApplicationGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'AnnouncementOptions':
        return announcementOptionsGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'AnnouncementPush':
        return announcementPushGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'AnnouncementDisplay':
        return announcementDisplayGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
      case 'AnnouncementEntrance':
        return announcementEntranceGet(params).then(res => {
          if (!res.success) return;
          setNodeData(res.result);
        });
    }
  };

  const initNode = (code: TFlowNodeCode, id: string) => {
    if (!code || !id) return;
    setLoading(true);
    listNodeGet({ id, type: getActionTypeVal(actionType) }).then(res => {
      if (!res.success) return null;
      const list = res.result ?? [];
      setNodeList(list);
      return initContent(getCurrentNode(code, list));
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    initNode(currentNodeCode, guideId);
  }, [currentNodeCode, guideId]);

  const getGlobalBtn = () => {
    const canSave = nodeData?.canSave;
    const url:any = nodeData?.url || {};

    const savaBtn = {
      label: '保存配置',
      onClick: onSave,
      type: 'primary',
    };
    const openLogBtn = {
      label: '操作日志',
      onClick: openLog,
    };
    switch (currentNodeCode) {
      case 'AnnouncementDefinition':
        return [
          openLogBtn,
        ];
      case 'BusinessFieldDefinition':
        return [
          openLogBtn,
          canSave && savaBtn,
        ].filter(Boolean);
      case 'AnnouncementTitleConfiguration':
        return [
          openLogBtn,
          url.templateConfigurationUrl && {
            label: '公告模板配置(原生)',
            onClick: () => window.open(url.templateConfigurationUrl),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'ContentSubject':
        return [
          openLogBtn,
          url.metadataConfiguration && {
            label: '元数据配置',
            onClick: () => window.open(url.metadataConfiguration),
          },
          url.wuxiangPageConfiguration && {
            label: '公告表单-页面配置',
            onClick: () => {
              if (nodeData?.isFormPage) {
                showAccountModal();
              }
              window.open(url.wuxiangPageConfiguration);
            },
          },
          url.wuxiangTemplateConfigUrl && {
            label: '公告表单-模板配置',
            onClick: () => {
              if (nodeData?.isFormPage) {
                showAccountModal();
              }
              window.open(url.wuxiangTemplateConfigUrl);
            },
          },
          url.templateConfigurationUrl && {
            label: '公告模板配置(原生)',
            onClick: () => window.open(url.templateConfigurationUrl),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'ContentSupervision':
        return [
          openLogBtn,
          url.sensitiveConfiguration && {
            label: '公告敏感词配置',
            onClick: () => window.open(url.sensitiveConfiguration),
          },
          url.supervisionConfiguration && {
            label: '公告监管配置',
            onClick: () => window.open(url.supervisionConfiguration),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'ProcessDefinition':
        return [
          openLogBtn,
          url.processDefinition && {
            label: '公告流程定义',
            onClick: () => window.open(url.processDefinition),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'ProcessApplication':
        return [
          openLogBtn,
          url.processConfigurationPlatform && {
            label: '公告流程配置(平台级)',
            onClick: () => window.open(url.processConfigurationPlatform),
          },
          url.processConfigurationDistrict && {
            label: '公告流程配置(区划级)',
            onClick: () => window.open(url.processConfigurationDistrict),
          },
          url.processConfigurationInstitution && {
            label: '公告流程配置(机构级)',
            onClick: () => window.open(url.processConfigurationInstitution),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'AnnouncementOptions':
        return [
          openLogBtn,
          canSave && savaBtn,
        ].filter(Boolean);
      case 'AnnouncementPush':
        return [
          openLogBtn,
          url.pushScriptConfiguration && {
            label: '推送脚本配置',
            onClick: () => window.open(url.pushScriptConfiguration),
          },
          url.announcementSiteConfiguration && {
            label: '公告站点配置',
            onClick: () => window.open(url.announcementSiteConfiguration),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'AnnouncementDisplay':
        return [
          openLogBtn,
          url.hallBuildingConfiguration && {
            label: '大厅组件配置',
            onClick: () => window.open(url.hallBuildingConfiguration),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      case 'AnnouncementEntrance':
        return [
          openLogBtn,
          url.announcementGroupConfiguration && {
            label: '公告分组配置',
            onClick: () => window.open(url.announcementGroupConfiguration),
          },
          url.orderingApplicationsConfiguration && {
            label: '租户订购应用',
            onClick: () => window.open(url.orderingApplicationsConfiguration),
          },
          url.orderMenuConfiguration && {
            label: '岗位订购菜单',
            onClick: () => window.open(url.orderMenuConfiguration),
          },
          url.orderingFeaturesConfiguration && {
            label: '用户订购功能',
            onClick: () => window.open(url.orderingFeaturesConfiguration),
          },
          canSave && savaBtn,
        ].filter(Boolean);
      default:
        return [];
    }
  };

  const openLog = () => {
    openGuideActionLogDrawer({
      districtCode,
      logType: 'node',
      bizId: guideId,
    });
  };


  /**
   * type 1-保存  2-提交
   */
  const onNodeSave = (nCode: TFlowNodeCode, data: any, type: 1 | 2) => {
    const nodeInfoId = getCurrentNode(nCode, nodeList)!.id!;
    switch (nCode) {
      case 'AnnouncementDefinition':
        return Promise.resolve(false);
      case 'BusinessFieldDefinition':
        return saveBusinessFieldDefinitionPost({
          nodeInfoId,
          type,
          businessFieldDefinition: data.businessFieldDefinition,
        });
      case 'AnnouncementTitleConfiguration':
        return saveAnnouncementTitleConfigurationPost({
          nodeInfoId,
          type,
          customizedTitle: data.customizedTitle,
        });
      case 'ContentSubject':
        return saveContentSubjectPost({
          nodeInfoId,
          type,
          isFormPage: data.isFormPage,
          secondEdit: data.secondEdit,
        });
      case 'ContentSupervision':
        return saveContentSupervisionPost({
          nodeInfoId,
          type,
          canContentCensor: data.canContentCensor,
        });
      case 'ProcessDefinition':
        return saveProcessDefinitionPost({
          nodeInfoId,
          type,
        });
      case 'ProcessApplication':
        return saveProcessApplicationPost({
          nodeInfoId,
          type,
          isCheckAgencyTypes: data.isCheckAgencyTypes,
          checkAgencyTypes: data.checkAgencyTypes,
        });
      case 'AnnouncementOptions':
        return saveAnnouncementOptionsPost({
          nodeInfoId,
          type,
          termType: data.termType || 0,
          expiryPeriod: data.expiryPeriod || -1,
          procurementMethod: data.procurementMethod.map(ele => ({
            code: ele.value,
            codeDesc: ele.label,
          })),
          publishTypes: data.publishTypes ? data.publishTypes.join(',') : undefined,
          attachmentShow: data.attachmentShow,
          hasAttachment: data.hasAttachment,
          canObject: data.canObject,
          objectionKeys: data.objectionKeys ? data.objectionKeys.join() : undefined,
          beObjectionKeys: data.beObjectionKeys ? data.beObjectionKeys.join() : undefined,
          canRevoke: data.canRevoke,
          revokeTime: data.revokeTime,
          isRevocable: data.isRevocable,
          revocableButtonName: data.revocableButtonName,
          isForcedControl: data.isForcedControl,
          whiteListDistrictCode: data.whiteListDistrictCode || [],
        });
      case 'AnnouncementPush':
        return saveAnnouncementPushPost({
          nodeInfoId,
          type,
          isNotify: data.isNotify,
          isOut: data.isOut,
        });
      case 'AnnouncementDisplay':
        return saveAnnouncementDisplayPost({
          nodeInfoId,
          type,
        });
      case 'AnnouncementEntrance':
        return saveAnnouncementEntrancePost({
          nodeInfoId,
          type,
        });
    }
  };


  const onSave = () => {
    formRef.current?.validateFieldsAndScroll((error, values) => {
      if (error) {
        return;
      }
      onNodeSave(currentNodeCode, { ...values }, 1).then(res => {
        if (!res?.success) return;
        return message.success('保存成功', () => initNode(currentNodeCode, guideId));
      });
    });
  };

  const onSubmit = () => {
    formRef.current?.validateFieldsAndScroll((error, values) => {
      if (error) {
        return;
      }
      onNodeSave(currentNodeCode, { ...values }, 2).then(res => {
        if (!res?.success) return;
        return message.success('提交成功', () => initNode(currentNodeCode, guideId));
      });
    });
  };

  const changeFlow = (code) => {
    setCurrentNodeCode(code);
    history.push(appendQueryParams({
      districtCode,
      districtName,
    }, {
      url: `/config/guideDetail/${guideId}/${actionType}?nodeCode=${code}`,
    }));
  };

  const renderExtra = () => {
    return (
      <div style={{ lineHeight: '30px' }}>
        <div style={{ display: 'inline-block' }}>
          <span style={{ color: '#121600' }}>公告区划：</span>
          <span>{districtName}</span>
        </div>
        <div style={{ display: 'inline-block', marginLeft: 16 }}>
          <span style={{ color: '#121600' }}>公告类型：</span>
          <span>{nodeData?.announcementTypeName}</span>
        </div>
      </div>
    );
  };

  const canSave = nodeData?.canSave;
  const canSubmit = nodeData?.canSubmit;
  const showButtonGroup = !!canSave || !!canSubmit;

  const ButtonGroup = showButtonGroup ? (
    <div className="zcy-mg-l-16">
      {
        canSave ? <Button onClick={onSave} className="zcy-mg-r-8" type="primary">保存配置</Button> : null
      }
      {
        canSubmit ? <Button onClick={onSubmit} type="primary">提交配置</Button> : null
      }
    </div>
  ) : null;

  const Content = (
    <div className="guideDetail-content">
      <ConfigMain />
      <ConfigContent
        formData={nodeData}
        pageType={actionType}
        cNodeCode={currentNodeCode}
        districtCode={districtCode}
        ref={formRef}
      />
      {ButtonGroup}
    </div>
  );

  return (
    <div className="guideDetail">
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={getGlobalBtn()}
          extraContent={renderExtra()}
        />
        <div className="zcy-flex">
          <FlowNav nodeList={nodeList} cNodeCode={currentNodeCode} onChange={changeFlow} />
          {Content}
        </div>
      </Spin>
    </div>
  );
};


export default GuideConfig;
