import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>cyList, Spin, Modal } from 'doraemon';
import './index.less';
import { useAnnBigTypeDrawer } from '../components/AnnBigTypeDrawer';
import { useAnnTypeLogDrawer } from '../components/AnnTypeLogDrawer';
import { listBigAnnType, getCloudRegion } from '../services/api';
import { PageType, SourceType, DEL_ALERT_MSG } from '../constants';

const ROUTES = [
  {
    label: '公告大类管理',
    to: '/manage',
  },
];

const ManageConfig = () => {
  const [loading, setLoading] = useState(true);
  const [listData, setListData] = useState([]);
  const [openDrawer] = useAnnBigTypeDrawer({
    onSubmit: () => getData(),
  });
  const [openLogDrawer] = useAnnTypeLogDrawer({
    source: SourceType.announcementBigType,
  });
  const [addOfAuth, setAddOfAuth] = useState(false);


  const globalBtn = [
    {
      label: '操作日志',
      onClick: () => openLogDrawer(),
    },
    addOfAuth && {
      label: '新建公告大类',
      type: 'primary',
      onClick: () => openDrawer({
        pageType: PageType.add,
        curInfo: {},
      }),
    },
  ].filter(Boolean);

  const getData = () => listBigAnnType().then((res) => {
    setListData(res.result ?? []);
    setLoading(false);
  });

  const getBaseConfig = () => {
    getCloudRegion().then((res) => {
      if (!res.success) return;
      setAddOfAuth(res.result);
    });
  };

  useEffect(() => {
    getData();
    getBaseConfig();
  }, []);

  const edit = ({ typeCode, typeName, id }) => {
    openDrawer({
      pageType: PageType.edit,
      curInfo: { typeCode, typeName, id },
    });
  };

  const del = () => Modal.warning({
    title: '删除公告大类',
    content: DEL_ALERT_MSG,
    closable: true,
  });

  const getTableColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'index',
        render: (_, __, index) => index + 1,
      },
      {
        title: '公告大类ID',
        dataIndex: 'typeCode',
      },
      {
        title: '公告大类名称',
        dataIndex: 'typeName',
      }, {
        title: '操作',
        dataIndex: 'action',
        render: (_, { typeCode, typeName, id }) => {
          return (
            <React.Fragment>
              <a onClick={() => edit({ typeCode, typeName, id })} className="zcy-mg-r-8">编辑</a>
              <a onClick={() => del()} >删除</a>
            </React.Fragment>
          );
        },
      },
    ];
  };

  return (
    <div>
      <Spin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
          globalBtn={globalBtn}
        />
        <ZcyList
          table={{
            rowKey: 'id',
            dataSource: listData,
            columns: getTableColumn(),
            pagination: false,
          }}
        />
      </Spin>
    </div>
  );
};

export default ManageConfig;
