.operat-btn {
  >span {
    font-size: 16px;
    line-height: 32px;
  }
  .btn-warp {
    float: right;
    >button {
      margin-left: 10px;
    }
  }
  .operat-code {
    float: right;
    margin-bottom: 20px;
  }
}

.content-warp {
  margin-top: 20px;
  border: 1px dashed #979797;
  min-height: 100px;
  .type-list {
    width: 100%;
    padding: 20px;
    border: none;
    .type-item {
      >span {
        top: 4px;
      }
    }
  }
}

.content-sort {
  margin-top: 20px;
  border-top: 1px solid #e8e8e8;
  min-height: 100px;
  .type-list {
    width: 100%;
    padding: 20px;
    border: none;
    .type-item {
      & > span {
        top: 4px;
      }
    }
  }
}
.sortCls {
  .doraemon-alert-warning {
    width: 635px;
    font-size: 12px;
  }
}

.config-log-container {
  .doraemon-col-8 {
    width: auto;
  }
}

.typeConfig {
  .doraemon-dropdown.doraemon-dropdown-placement-bottomRight, .doraemon-dropdown.doraemon-dropdown-placement-bottomLeft {
    z-index: 10000;
  }
  .doraemon-table-filter-dropdown .doraemon-dropdown-menu-without-submenu {
    max-height: 200px;
  }
}

.siteConfig{
  &.sortEnable{
    tr:hover > td {
      background: inherit !important;
    }
  }
  .isExtend {
    margin-left: 40px;
  }
}