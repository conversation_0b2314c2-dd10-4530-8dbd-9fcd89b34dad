import { treeList, listRelatedAnnTypes, getAnnOriginator, getAllAnnTypeList, saveAnnouncemen, saveCode } from '../services';


export default {
  namespace: 'sortConfig',

  state: {
    getSelectedAnnTypesData: [],
    announcementTypeData: [], // 全部公告类型
    announcementData: {},
    saveData: {},
    treeData: [], // 区划树
    saveCodeList: {}, // 同步区划保存
    getListReTypes: [], // 初始类型数据
  },

  effects: {
    // 区划
    * getTreeList({ payload }, { call, put }) {
      const response = yield call(treeList, payload);
      yield put({
        type: 'treeList',
        payload: response,
      });
    },
    // 基础展示数据
    * getListRelatedAnnTypes({ payload }, { call, put }) {
      const response = yield call(listRelatedAnnTypes, payload);
      yield put({
        type: 'listRelatedAnnTypes',
        payload: response,
      });
    },
    // 已选择公告类型
    * getAnnouncementData({ payload }, { call, put }) {
      const response = yield call(getAnnOriginator, payload);
      yield put({
        type: 'getAnnouncementRes',
        payload: response,
      });
    },
    // 全部公告类型
    * getTreeData({ payload }, { call, put }) {
      const response = yield call(getAllAnnTypeList, payload);
      yield put({
        type: 'getTreeDataRes',
        payload: response,
      });
    },
    // 保存
    * save({ payload }, { call, put }) {
      const response = yield call(saveAnnouncemen, payload);
      yield put({
        type: 'saveRes',
        payload: response,
      });
    },
    // 同步区划保存
    * getSaveCode({ payload }, { call, put }) {
      const response = yield call(saveCode, payload);
      yield put({
        type: 'saveCode',
        payload: response,
      });
    },
  },

  reducers: {
    treeList(state, action) {
      return {
        ...state,
        treeData: action.payload.result || [],
      };
    },
    listRelatedAnnTypes(state, action) {
      return {
        ...state,
        getSelectedAnnTypesData: action.payload.result || [],
      };
    },
    getAnnouncementRes(state, action) {
      return {
        ...state,
        announcementData: action.payload || {},
      };
    },
    getTreeDataRes(state, action) {
      return {
        ...state,
        announcementTypeData: action.payload.result || [],
      };
    },
    saveRes(state, action) {
      return {
        ...state,
        saveData: action.payload || {},
      };
    },
    saveCode(state, action) {
      return {
        ...state,
        saveCodeList: action.payload || {},
      };
    },
  },
};
