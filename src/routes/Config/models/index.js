import {
  queryList,
  setOpenConfig,
  paramsList,
  addParams,
  delParams,
  viewAnnouncement,
  obtainCGLXANDCGFS,
  treeList,
  syncAnnTypeConfig,
  listAgencyTypes,
  reqOriginatorTypeList,
  printFormDataOssApi,
  getAnnTitleTemplate,
  getPublishTypeConfig,
  checkExistRelatedAnnTypes,
} from '../services';

function getNodes(tree) {
  const newTree = [];
  if (!tree) {
    return null;
  }
  tree.forEach((item) => {
    newTree.push({
      title: item.codeName,
      value: item.code,
      key: item.code,
      children: (item.children ? getNodes(item.children) : undefined),
    });
  });
  return newTree;
}

export default {
  namespace: 'openConfig',

  state: {
    announcementTypeData: [],
    setData: {},
    paramsRes: [],
    addRes: {},
    delRes: {},
    viewData: {},
    cgfs: [],
    treeData: [],
    saveCodeList: [],
    originatorTypeList: [],
    annTitleTemplate: {},
    publishTypeOptions: [],
    publishTypeDefaultValue: undefined,
  },

  effects: {
    * checkExistRelatedAnnTypes({ payload }, { call }) {
      const response = yield call(checkExistRelatedAnnTypes, payload);
      return response;
    },
    * listAgencyTypes({ payload }, { call }) {
      const response = yield call(listAgencyTypes, payload);
      return response;
    },
    * getListData({ payload }, { call, put }) {
      const response = yield call(queryList, payload);
      yield put({
        type: 'queryListRes',
        payload: response,
      });
      return response;
    },
    * setOpen({ payload }, { call, put }) {
      const response = yield call(setOpenConfig, payload);
      yield put({
        type: 'setOpenConfigRes',
        payload: response,
      });
    },
    * getParamsData({ payload }, { call, put }) {
      const response = yield call(paramsList, payload);
      yield put({
        type: 'paramsListRes',
        payload: response,
      });
    },
    * postAddParams({ payload }, { call, put }) {
      const response = yield call(addParams, payload);
      yield put({
        type: 'addParamsRes',
        payload: response,
      });
    },
    * delParamsItem({ payload }, { call, put }) {
      const response = yield call(delParams, payload);
      yield put({
        type: 'delParamsRes',
        payload: response,
      });
    },
    * getViewAnnouncement({ payload }, { call, put }) {
      const response = yield call(viewAnnouncement, payload);
      yield put({
        type: 'viewAnnouncementRes',
        payload: response,
      });
    },
    * printFormDataOss({ payload }, { call }) {
      const response = yield call(printFormDataOssApi, payload);
      return response;
    },
    * obtainCGLXANDCGFS({ payload }, { call, put }) {
      const response = yield call(obtainCGLXANDCGFS, payload);
      yield put({
        type: 'saveCgfs',
        payload: getNodes(response.result.cgfs),
      });
    },
    // 区划
    * getTreeList({ payload }, { call, put }) {
      const response = yield call(treeList, payload);
      yield put({
        type: 'treeList',
        payload: response,
      });
    },
    // 同步区划保存
    * getSaveCode({ payload }, { call, put }) {
      const response = yield call(syncAnnTypeConfig, payload);
      yield put({
        type: 'syncAnnTypeConfig',
        payload: response,
      });
    },
    // 获取采购单位类型列表
    * getOriginatorTypeList({ payload }, { call, put }) {
      const response = yield call(reqOriginatorTypeList, payload);
      if (response.success) {
        yield put({
          type: 'update',
          payload: {
            originatorTypeList: response.result || [],
          },
        });
      }
    },

    // 查询公告模板信息
    * getAnnTitleTemplate({ payload }, { call, put }) {
      const response = yield call(getAnnTitleTemplate, payload);
      yield put({
        type: 'update',
        payload: {
          annTitleTemplate: response.result || {},
        },
      });
    },

    * getPublishTypeConfig({ payload }, { call, put }) {
      const response = yield call(getPublishTypeConfig, payload);
      let publishTypeDefaultValue;
      if (response.result && response.result.length) {
        publishTypeDefaultValue = +response.result[0].v;
      }

      const res = {
        publishTypeOptions: response.result || [],
        publishTypeDefaultValue,
      };
      
      yield put({
        type: 'update',
        payload: res,
      });
      
      return res;
    },
    
  },

  reducers: {
    queryListRes(state, action) {
      return {
        ...state,
        announcementTypeData: action.payload.result || [],
      };
    },
    setOpenConfigRes(state, action) {
      return {
        ...state,
        setData: action.payload || {},
      };
    },
    paramsListRes(state, action) {
      return {
        ...state,
        paramsRes: action.payload.result || [],
      };
    },
    addParamsRes(state, action) {
      return {
        ...state,
        addRes: action.payload || {},
      };
    },
    delParamsRes(state, action) {
      return {
        ...state,
        delRes: action.payload || {},
      };
    },
    viewAnnouncementRes(state, action) {
      return {
        ...state,
        viewData: action.payload || {},
      };
    },
    saveCgfs(state, action) {
      return {
        ...state,
        cgfs: action.payload || [],
      };
    },
    treeList(state, action) {
      return {
        ...state,
        treeData: action.payload.result || [],
      };
    },
    syncAnnTypeConfig(state, action) {
      return {
        ...state,
        saveCodeList: action.payload || {},
      };
    },
    update(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
