import { getAnnouncement, queryList, getSelectedAnnTypes, saveAnnouncement, treeList, syncAnnOriginatorConfig } from '../services';


export default {
  namespace: 'publishConfig',

  state: {
    getSelectedAnnTypesData: [],
    announcementTypeData: [],
    announcementData: {},
    saveData: {},
    treeData: [],
    saveCodeList: {},
  },

  effects: {
    // 基础展示数据
    * getSelectedAnnTypesData({ payload }, { call, put }) {
      const response = yield call(getSelectedAnnTypes, payload);
      yield put({
        type: 'getSelectedAnnTypesDataRes',
        payload: response,
      });
    },
    // 已选择公告类型
    * getAnnouncementData({ payload }, { call, put }) {
      const response = yield call(getAnnouncement, payload);
      yield put({
        type: 'getAnnouncementRes',
        payload: response,
      });
    },
    // 公告类型
    * getTreeData({ payload }, { call, put }) {
      const response = yield call(queryList, payload);
      yield put({
        type: 'getTreeDataRes',
        payload: response,
      });
    },
    // 保存
    * save({ payload }, { call, put }) {
      const response = yield call(saveAnnouncement, payload);
      yield put({
        type: 'saveRes',
        payload: response,
      });
    },
    // 区划
    * getTreeList({ payload }, { call, put }) {
      const response = yield call(treeList, payload);
      yield put({
        type: 'treeList',
        payload: response,
      });
    },
    // 同步区划保存
    * getSaveCode({ payload }, { call, put }) {
      const response = yield call(syncAnnOriginatorConfig, payload);
      yield put({
        type: 'syncAnnOriginatorConfig',
        payload: response,
      });
    },
  },

  reducers: {
    getSelectedAnnTypesDataRes(state, action) {
      return {
        ...state,
        getSelectedAnnTypesData: action.payload.result || [],
      };
    },
    getAnnouncementRes(state, action) {
      return {
        ...state,
        announcementData: action.payload || {},
      };
    },
    getTreeDataRes(state, action) {
      return {
        ...state,
        announcementTypeData: action.payload.result || [],
      };
    },
    saveRes(state, action) {
      return {
        ...state,
        saveData: action.payload || {},
      };
    },
    treeList(state, action) {
      return {
        ...state,
        treeData: action.payload.result || [],
      };
    },
    syncAnnOriginatorConfig(state, action) {
      return {
        ...state,
        saveCodeList: action.payload || {},
      };
    },
  },
};
