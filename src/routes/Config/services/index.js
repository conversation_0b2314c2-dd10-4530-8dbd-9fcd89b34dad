import { request } from 'doraemon';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';

export async function listAgencyTypes() {
  return request('/announcement/config/listAgencyTypes', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function queryList(params) {
  return request('/announcement/relation/listRelatedAnnTypes', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}

// 判断对应区划是否关联公告类型
export async function checkExistRelatedAnnTypes(params) {
  return request('/announcement/relation/checkExistRelatedAnnTypes', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}

// 设置开发配置
export async function setOpenConfig(data) {
  return request('/announcement/config/saveAnnTypeConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}


// 监管参数配置列表
export async function paramsList(params) {
  return request('/announcement/config/getRegularList', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}

// 新增、编辑监管参数 setAnnouncementEnvInfo
export async function addParams(data) {
  return request('/announcement/config/saveRegularConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}

// 删除监管参数
export async function delParams(data) {
  return request('/announcement/config/deleteRegular', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}


/** ----手工创建公告--- */
// 基础数据
export async function getSelectedAnnTypes(params) {
  return request('/announcement/relation/getSelectedAnnTypes', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}

// 已选择公告类型
export async function getAnnouncement(params) {
  return request('/announcement/config/getAnnOriginator', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}
// 保存
export async function saveAnnouncement(data) {
  return request('/announcement/config/saveAnnOriginator', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}
// 查看
export async function viewAnnouncement(data) {
  return request('/announcement/config/templatePreview', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params: {
      ...data,
    },
  });
}

// 采购方式和、采购类型
export async function obtainCGLXANDCGFS(params) {
  return request('/announcement/api/obtainCGLXANDCGFS', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 开放配置同步区划保存
export async function syncAnnTypeConfig(data) {
  return request('/announcement/config/syncAnnTypeConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}
// 发布配置同步区划保存
export async function syncAnnOriginatorConfig(data) {
  return request('/announcement/config/syncAnnOriginatorConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}

// 公告模板配置
// 区划
export async function treeList() {
  return getAuthorityDistTreeGet();
}

// 基础数据展示
export async function listRelatedAnnTypes(params) {
  return request('/announcement/relation/listRelatedAnnTypes', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}
// 已选择公告类型
export async function getAnnOriginator(params) {
  return request('/announcement/config/listRelatedAnnTypeIds', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}
// 所有类型
export async function getAllAnnTypeList() {
  return request('/announcement/config/getAllAnnTypeList', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
  });
}
// 保存
export async function saveAnnouncemen(data) {
  return request('/announcement/config/saveRelationConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}
// 同步区划保存
export async function saveCode(data) {
  return request('/announcement/config/syncRelationConfig', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    data,
  });
}


// 获取采购单位列表
export async function reqOriginatorTypeList(params) {
  return request('/announcement/config/listUserTypes', {
    method: 'GET',
    params,
  });
}

// 获取公告模板（对接表单中心后）
export async function printFormDataOssApi(params) {
  return request('/announcement/api/form/common/v3/print', {
    method: 'post',
    data: { ...params },
  });
}


// 获取配置日志列表
export async function getLogData(params) {
  return request('/announcement/audit/log/paging', {
    params,
  });
}


// 公告审计日志列表配置类型下拉选项
export async function getOperationList(params) {
  return request('/announcement/audit/log/operation/list', {
    params,
  });
}


// 公告审计日志列表配置菜单下拉选项
export async function getSourceList(params) {
  return request('/announcement/audit/log/source/list', {
    params,
  });
}


// 批量取消公告发布类型
export async function batchCancelPublicAnnouncementTypes(params) {
  return request('/announcement/public/batchCancelPublicAnnouncementTypes', {
    method: 'post',
    data: { ...params },
  });
}

// 批量新增公告发布类型
export async function batchSavePublicAnnouncementTypes(params) {
  return request('/announcement/public/batchSavePublicAnnouncementTypes', {
    method: 'post',
    data: { ...params },
  });
}

// 批量保存公告关联类型
export async function batchSaveRelationAnnouncementTypes(params) {
  return request('/announcement/relation/batchSaveRelationAnnouncementTypes', {
    method: 'post',
    data: { ...params },
  });
}

// 批量取消公告关联类型
export async function batchCancelRelationAnnouncementTypes(params) {
  return request('/announcement/relation/batchCancelRelationAnnouncementTypes', {
    method: 'post',
    data: { ...params },
  });
}

// 查询公告模板信息
export async function getAnnTitleTemplate(params) {
  return request('/announcement/api/template/getAnnTitleTemplate', {
    params,
  });
}

// 根据公告类型+区划查询公告发布类型
export async function getPublishTypeConfig(params) {
  return request('/announcement/config/getPublishTypeConfig', {
    params,
  });
}


// 查询开放配置具体信息
export async function getRelationById(params) {
  return request('/announcement/relation/getRelationById', {
    params,
  });
}
