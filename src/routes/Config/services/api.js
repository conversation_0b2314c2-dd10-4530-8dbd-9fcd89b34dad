import hecateRequest from 'src/utils/request';

// 获取所有操作类型
export const getOperationType = () => hecateRequest('/announcement/audit/log/getOperationType');

// 操作日志查询
export const listBigAnnLog = params => hecateRequest('/announcement/audit/log/listBigAnnLog', { params });

// 保存公告大类
export const saveBigAnnType = data => hecateRequest('/announcement/config/saveBigAnnType', {
  method: 'POST',
  data,
});

// 编辑公告大类
export const updateBigAnnType = data => hecateRequest('/announcement/config/updateBigAnnType', {
  method: 'POST',
  data,
});

// 查询公告大类列表
export const listBigAnnType = () => hecateRequest('/announcement/config/listBigAnnType');

// 删除公告大类型
export const delBigAnnType = params => hecateRequest('/announcement/config/delBigAnnType', { params });


// 创建公告小类
export const createAnnType = data => hecateRequest('/announcement/config/createAnnType', {
  method: 'POST',
  data,
});

// 查询公告类型配置
export const listAnnTypesList = params => hecateRequest('/announcement/config/listAnnTypesList', { params });

// 查询公告详情
export const getAnnTypeInfo = params => hecateRequest('/announcement/config/getAnnTypeInfo', { params });


// 编辑公告类型
export const updateAnnType = data => hecateRequest('/announcement/config/updateAnnType', {
  method: 'POST',
  data,
});

// 删除公告小类型
export const delLittleAnnType = params => hecateRequest('/announcement/config/delLittleAnnType', { params });

// 获取公告大类启停数量
export const countStartStopSetting = () => hecateRequest('/announcement/config/countStartStopSetting');

// 注册公告id
export const getAnnTypeId = params => hecateRequest('/announcement/config/getAnnTypeId', { params });

// 区分环境，云平台与岛端
export const getCloudRegion = () => hecateRequest('/announcement/config/cloudRegion');
