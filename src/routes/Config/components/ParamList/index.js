import React, { Component } from 'react';
import { Form, Button, Row, Col, Modal } from 'doraemon';
import './index.less';

const { Item } = Form;
const { confirm } = Modal;

class ParamList extends Component {
  constructor(props) {
    super(props);
    this.state = {

    };
  }

  addParamConfig = (id, type) => {
    this.props.addParamConfig(id, type);
  }


  showConfirm = (id) => {
    const { delParam } = this.props;
    confirm({
      title: '您是否确认删除?',
      content: '',
      onOk() {
        delParam(id);
      },
      onCancel() {
        // This is intentions
      },
    });
  }

  renderTypeName = (data) => {
    return data ? data.split(',').slice(0, 2).join(',') : '';
  }

  render() {
    const { paramsData } = this.props;
    const formItemLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const formItemLayoutLast = {
      labelCol: { span: 10 },
      wrapperCol: { span: 12 },
    };
    return (
      <div>
        {
          paramsData && paramsData.map((item, index) => {
            return (
              <div className="top-warp" key={index}>
                <div className="top-title">
                  <span>{item.groupName}</span>
                  <span>
                    <Button onClick={() => this.showConfirm(item.id)}>删除</Button>
                    <Button onClick={() => this.addParamConfig(item.id, 'edit')}>编辑</Button>
                  </span>
                </div>
                <div className="bot-content">
                  <Row gutter={24}>
                    <Col span={12}>
                      <Item
                        {...formItemLayout}
                        label="同级政府采购监督管理部门"
                      >
                        {item.envs.regulatoryOrgName}
                      </Item>
                    </Col>
                    <Col span={12}>
                      <Item
                        {...formItemLayout}
                        label="联系人"
                      >
                        {item.envs.regulatoryContactPerson}
                      </Item>
                    </Col>
                    <Col span={12}>
                      <Item
                        {...formItemLayout}
                        label="监督投诉电话"
                      >
                        {item.envs.regulatoryContactPhone}
                      </Item>
                    </Col>
                    <Col span={12}>
                      <Item
                        {...formItemLayout}
                        label="传真"
                      >
                        {item.envs.regulatoryContactFax}
                      </Item>
                    </Col>
                    <Col span={12}>
                      <Item
                        {...formItemLayout}
                        label="地址"
                      >
                        {item.envs.regulatoryContactAddr}
                      </Item>
                    </Col>
                    <Col span={12}>
                      <Item
                        {...formItemLayoutLast}
                        label="公告类型"
                      >
                        {
                          this.renderTypeName(item.announcementTypeName)
                        }
                      </Item>
                    </Col>
                  </Row>
                  <span className="more-btn" onClick={() => this.addParamConfig(item.id, 'more')}>更多</span>
                </div>
              </div>
            );
          })
        }
        <div className="add-btn" onClick={this.addParamConfig}>+ 新增</div>
      </div>
    );
  }
}
export default ParamList;
