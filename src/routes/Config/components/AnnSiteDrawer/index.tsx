import React, { useEffect, useState } from 'react';
import { Drawer, Form, FormGrid, Panel, Switch, message, Spin } from 'doraemon';
import { saveSitePost } from 'src/api/announcement/config';
import { getSiteDetailGet, GetSiteDetailGetResponse } from 'src/api/announcement/config/getSiteDetail';
import useDrawer from 'src/hooks/useDrawer';
import { PageType } from '../../constants';


const AnnSiteDrawer = ({
  form,
  visible,
  onCancel,
  onSubmit,
  curId,
  districtCode,
  pageType = PageType.add,
}) => {
  const [loading, setLoading] = useState(false);
  const [info, setInfo] = useState<GetSiteDetailGetResponse['result']>({});

  const { validateFields, getFieldDecorator } = form;
  
  useEffect(() => {
    setLoading(true);
    getSiteDetailGet({ id: curId }).then(res => {
      if (!res.success) return;
      setInfo(res.result);
    }).finally(() => {
      setLoading(false);
    });
  }, [curId]);
  
  const onSave = () => {
    if (pageType === PageType.detail) return onCancel();
    validateFields((err, { isDisplay }) => {
      if (err) return;
      setLoading(true);
      saveSitePost({ id: curId, isDisplay, districtCode }).then(res => {
        if (!res.success) return;
        message.success('更新成功', () => {
          onCancel();
          onSubmit();
        });
      }).finally(() => {
        setLoading(false);
      });
    });
  };


  const TitleNode = () => {
    if (pageType === PageType.detail) return <>查看公告站点</>;
    const title = pageType === PageType.add ? '新增公告站点' : '编辑公告站点';
    return (
      <div className="drawer-titleNode-wrapper">
        <span>{title}</span>
      </div>
    );
  };

  const formGridItem = [
    {
      label: '网站群站点ID',
      colSpan: 2,
      render: () => {
        return <span>{info?.siteId || '-'}</span>;
      },
    }, {
      label: '网站群站点名称',
      colSpan: 2,
      render: () => {
        return <span>{info?.siteName || '-'}</span>;
      },
    }, {
      label: '网站群站点域名',
      colSpan: 2,
      render: () => {
        return <span>{info?.siteDomain || '-'}</span>;
      },
    }, {
      label: '后台入口',
      colSpan: 2,
      render: () => {
        return <span>{info?.siteManagerUrl || '-'}</span>;
      },
    }, {
      label: '前台展示',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('isDisplay', {
          initialValue: info?.isDisplay,
          valuePropName: 'checked',
        })(

          pageType === PageType.edit ? (
            <Switch
              checkedChildren="启用"
              unCheckedChildren="停用"
            />
          ) : <span>{info?.isDisplay ? '启用' : '停用'}</span>
        );
      },
    }];

  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="annSite-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <Form>
          <Panel.Sub bordered title="基本信息" >
            <FormGrid bordered formGridItem={formGridItem} />
          </Panel.Sub>
        </Form>
      </Spin>
    </Drawer>
  );
};

const AnnSiteDrawerByForm = Form.create()(AnnSiteDrawer);
const useAnnSiteDrawer = (props) => {
  return useDrawer(AnnSiteDrawerByForm, props);
};

export { useAnnSiteDrawer };
export default AnnSiteDrawerByForm;

