import React, { Component } from 'react';
import TypeItem from './TypeItem';
import './index.less';

class AnnouncementTypesList extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    const { announcementTypeData, isMore, changeTag, announcementTypes } = this.props;
    return (
      <div className="type-list">
        {
          announcementTypeData && announcementTypeData.map((item, index) => {
            return (
              <div className="type-item" key={index}>
                <span>{item.name} : </span>
                <div className="type">
                  {
                    item.children && item.children.map((i) => {
                      return (
                        <TypeItem
                          key={i.id}
                          isMore={isMore}
                          data={i}
                          changeTag={changeTag}
                          announcementTypes={announcementTypes}
                        />
                      );
                    })
                  }
                </div>
              </div>
            );
          })
        }
      </div>
    );
  }
}
export default AnnouncementTypesList;
