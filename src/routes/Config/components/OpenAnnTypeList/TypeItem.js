import React, { Component } from 'react';

class TagItem extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isCheck: false,
    };
  }

  componentDidMount() {
    const { announcementTypes, data } = this.props;
    if (announcementTypes && announcementTypes.indexOf(data.typeId) !== -1) {
      this.setState({
        isCheck: !this.state.isCheck,
      });
    }
  }

  changeTag = (id, name) => {
    this.setState({
      isCheck: !this.state.isCheck,
    }, () => {
      this.props.changeTag(id, name, this.state.isCheck);
    });
  }

  render() {
    const { data, isMore } = this.props;
    return (
      <div
        className={(this.state.isCheck) ? 'change-item addTag' : 'change-item'}
        // eslint-disable-next-line react/jsx-no-bind
        onClick={isMore ? null : this.changeTag.bind(this, data.typeId, data.name)}
      >
        {data.name}
      </div>
    );
  }
}
export default TagItem;
