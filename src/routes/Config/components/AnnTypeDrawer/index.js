import React, { useEffect, useState } from 'react';
import { Drawer, message, Input, Form, FormGrid, Panel, Switch, Select } from 'doraemon';
import './index.less';
import { PageType } from '../../constants';
import { createAnnType, updateAnnType, listBigAnnType, getAnnTypeInfo, getAnnTypeId } from '../../services/api';
import { handleValueTrimByObj } from 'utils/utils';
import useDrawer from 'src/hooks/useDrawer';

const { Option } = Select;

const AnnTypeDrawer = ({
  form,
  visible,
  onCancel,
  onSubmit,
  curId,
  pageType = PageType.add,
}) => {
  const [bigAnnType, setBigAnnType] = useState([]);
  const [info, setInfo] = useState({});
  const { validateFields, getFieldDecorator, getFieldsValue, setFieldsValue } = form;
  const { parentType } = getFieldsValue(['parentType']);
  const showTypeCodeFiled = pageType === PageType.edit || parentType;

  const onBeforeSave = () => {
    if (pageType === PageType.add || pageType === PageType.edit) {
      return onSave();
    }
    onCancel();
    onSubmit();
  };
  
  const onSave = () => {
    validateFields((err, values) => {
      if (err) return;
      values.isEnable = values.isEnable ? 1 : 0;
      const handleValues = handleValueTrimByObj(values);
      if (pageType === PageType.add) {
        return createAnnType({
          ...handleValues,
          id: null,
        }).then((res) => {
          if (!res.success) return;
          message.success('新增成功', () => {
            onCancel();
            onSubmit();
          });
        });
      }
      return updateAnnType({
        ...handleValues,
        id: info.id,
      }).then((res) => {
        if (!res.success) return;
        message.success('更新成功', () => {
          onCancel();
          onSubmit();
        });
      });
    });
  };

  const TitleNode = () => {
    if (pageType === PageType.detail) return '查看公告类型';
    const title = pageType === PageType.add ? '新增公告类型' : '编辑公告类型';
    return (
      <div className="drawer-titleNode-wrapper">
        <span>{title}</span>
      </div>
    );
  };

  const getListBigAnnType = () => listBigAnnType().then((res) => {
    setBigAnnType(res.result ?? []);
  });

  const getAnnTypeInfoDetail = () => {
    getAnnTypeInfo({ id: curId }).then((res) => {
      setInfo(res.result);
    });
  };

  useEffect(() => {
    getListBigAnnType();
  }, []);

  useEffect(() => {
    setInfo({});
    if (visible) {
      if (pageType !== PageType.add && curId) {
        getAnnTypeInfoDetail();
      }
    }
  }, [visible, pageType, curId]);

  const changeParentType = (val) => {
    getAnnTypeId({ parentId: val }).then((res) => {
      const value = res.result;
      setFieldsValue({
        typeCode: value,
      });
      setInfo(e => ({
        ...e,
        typeCode: value,
      }));
    });
  };

  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onBeforeSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="annType-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Form>
        <Panel.Sub bordered title="所属公告大类" >
          <FormGrid bordered
            formGridItem={[{
              label: '公告大类',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('parentType', {
                  initialValue: info?.parentType || undefined,
                  rules: [{
                    required: true,
                    message: '请输入',
                  }],
                })(
                  (pageType === PageType.add) ? (
                    <Select onChange={changeParentType} getPopupContainer={() => document.querySelector('.annType-drawer')} >
                      {bigAnnType.map(ele =>
                        <Option key={ele.typeCode} value={ele.typeCode}>{ele.typeName}</Option>)}
                    </Select>
                  ) : <span>{info.parentTypeName}</span>
                );
              },
            }]}
          />
        </Panel.Sub>
        <Panel.Sub bordered title="基本信息" >
          <FormGrid bordered
            formGridItem={[{
              label: '公告名称',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('typeName', {
                  initialValue: info?.typeName || undefined,
                  rules: [{
                    required: true,
                    message: '请输入',
                  }],
                })(
                  (pageType === PageType.detail) ?
                    <span>{info.typeName}</span> :
                    <Input maxLength={20} placeholder="请输入" />
                );
              },
            },
            showTypeCodeFiled && {
              label: '公告Code',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('typeCode', {
                  initialValue: info?.typeCode || undefined,
                })(
                  <span>{info?.typeCode ?? '-'}</span>
                );
              },
            }].filter(e => e)}
          />
        </Panel.Sub>
        <Panel.Sub bordered title="配置信息" >
          <FormGrid bordered
            formGridItem={[{
              label: '公告特性值',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('characteristicValues', {
                  initialValue: info?.characteristicValues || undefined,
                  rules: [{
                    required: true,
                    message: '请输入',
                  }],
                })(
                  (pageType === PageType.detail) ?
                    (
                      <React.Fragment>
                        <span>{info.characteristicValues}</span>
                        <a className="zcy-mg-l-12"
                          href={info.characteristicValuesUrl}
                          target="_blank"
                        >设置
                        </a>
                      </React.Fragment>
                    ) : 
                    (
                      pageType === PageType.add ? (
                        <Input maxLength={100}
                          placeholder="请输入"
                        />
                      ) : <span>{info.characteristicValues}</span>
                    )
                );
              },
            }, {
              label: '公告标题特性值',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('titleCharacteristicValues', {
                  initialValue: info?.titleCharacteristicValues || undefined,
                })(
                  pageType === PageType.detail ? (
                    <React.Fragment>
                      <span>{info.titleCharacteristicValues || '-'}</span>
                      { info.titleCharacteristicValuesUrl ? (
                        <a className="zcy-mg-l-12"
                          href={info.titleCharacteristicValuesUrl}
                          target="_blank"
                        >设置
                        </a>
                      ) : null}
                    </React.Fragment>                   
                  ) : (
                    pageType === PageType.add ? (
                      <Input maxLength={100}
                        placeholder="请输入"
                      />
                    ) : (info?.titleCharacteristicValues) ? (
                      <span>{info?.titleCharacteristicValues || '-'}</span>
                    ) : (
                      <Input maxLength={100}
                        placeholder="请输入"
                      />
                    )
                  )
                );
              },
            }, {
              label: '表单页面code',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('formPageCode', {
                  initialValue: info?.formPageCode || undefined,
                })(
                  (pageType === PageType.detail) ?
                    <span>{info?.formPageCode ?? '-'}</span> : 
                    <Input maxLength={200} placeholder="请输入" />
                );
              },
            }, {
              label: '流程key',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('processDefineKey', {
                  initialValue: info?.processDefineKey || undefined,
                })(
                  (pageType === PageType.detail) ?
                    <span>{info?.processDefineKey ?? '-'}</span> : 
                    <Input maxLength={200} placeholder="请输入" />
                );
              },
            }, {
              label: '细分权限码',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('addPrivilegeCode', {
                  initialValue: info?.addPrivilegeCode || undefined,
                })(
                  (pageType === PageType.detail) ?
                    <span>{info?.addPrivilegeCode ?? '-'}</span> : 
                    <Input maxLength={200} placeholder="请输入" />
                );
              },
            }].filter(Boolean)}
          />
        </Panel.Sub>
        <Panel.Sub bordered title="启用状态" >
          <FormGrid bordered
            className="annTypeDrawer-switch"
            formGridItem={[{
              label: '启用状态',
              colSpan: 2,
              render: () => {
                return getFieldDecorator('isEnable', {
                  initialValue: pageType === PageType.add ? true : info?.isEnable === 1,
                  rules: [{
                    required: true,
                    message: '请输入',
                  }],
                  valuePropName: 'checked',
                })(
                  (pageType === PageType.detail) ?
                    <span>{info?.isEnable === 1 ? '启用' : '停用'}</span> : (
                      <Switch
                        checkedChildren="启用"
                        unCheckedChildren="停用"
                      />
                    )
                );
              },
            }]}
          />
        </Panel.Sub>
      </Form>
    </Drawer>
  );
};

const AnnTypeDrawerByForm = Form.create()(AnnTypeDrawer);
const useAnnTypeDrawer = (props) => {
  return useDrawer(AnnTypeDrawerByForm, props);
};

export { useAnnTypeDrawer };
export default AnnTypeDrawerByForm;
