import React, { Component } from 'react';
import { Input, Modal, Form, Row, Col, Button, ZcyValidate, Popover, Icon } from 'doraemon';
import OpenAnnTypeList from './OpenAnnTypeList';
import './PublishModal.less';

const { Item } = Form;


@Form.create()
class ReleaseModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    const { 
      visible, onCancel, onCreate, form, announcementTypeData, 
      changeTag, isMore, paramsItemData, publishModalTitle,
    } = this.props;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const formItemLayout2 = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };

    const configModal = {
      height: 520,
      overflow: 'auto',
      overflowX: 'hidden',
    };

    const label = (
      <Popover
        getPopupContainer={triggerNode => triggerNode.parentNode}
        placement="bottomRight"
        content={<div style={{ width: 320 }}>请输入监管参数分组名称，例：采购公告监管参数</div>}
      >
        <Icon type="question-circle-o" /> 配置组名称
      </Popover>
    );

    return (
      <Modal
        wrapClassName="jgcs-modal"
        width="980px"
        visible={visible}
        title={publishModalTitle}
        okText="保存"
        onCancel={onCancel}
        onOk={onCreate}
        footer={
          isMore ? [
            <Button key="submit" type="primary" onClick={onCancel}>确认</Button>,
          ] : [
            <Button key="back" onClick={onCancel}>取消</Button>,
            <Button key="submit" type="primary" onClick={onCreate}>保存</Button>,
          ]
        }
      >
        <Form style={configModal}>
          <Row gutter={24}>
            <Col span={12}>
              <Item
                className="groupName"
                {...formItemLayout}
                label={label}
              >
                {
                  isMore ? (
                    <span>{paramsItemData.groupName}</span>
                  ) : (
                    getFieldDecorator('groupName', {
                      initialValue: paramsItemData.groupName,
                      rules: [
                        ZcyValidate.maxLen(200),
                        { required: true, message: '请输入配置组名称!' },
                      ],
                    })(
                      <Input placeholder="请输入配置组名称" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={12}>
              <Item
                style={{ overflow: 'init' }}
                {...formItemLayout}
                label="同级政府采购监督管理部门"
              >
                {
                  isMore ? (
                    <span>{paramsItemData.envs.regulatoryOrgName}</span>
                  ) : (
                    getFieldDecorator('regulatoryOrgName', {
                      initialValue: paramsItemData.envs && paramsItemData.envs.regulatoryOrgName,
                      rules: [
                        ZcyValidate.maxLen(200),
                        { required: true, message: '请输入同级政府采购监督管理部门!' },
                      ],
                    })(
                      <Input placeholder="同级政府采购监督管理部门" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={12}>
              <Item
                {...formItemLayout}
                label="联系人"
              >
                {
                  isMore ? (
                    <span>{paramsItemData.envs.regulatoryContactPerson}</span>
                  ) : (
                    getFieldDecorator('regulatoryContactPerson', {
                      initialValue: paramsItemData?.envs?.regulatoryContactPerson,
                      rules: [ZcyValidate.maxLen(200)],
                    })(
                      <Input placeholder="请输入联系人" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={12}>
              <Item
                {...formItemLayout}
                label="监督投诉电话"
              >
                {
                  isMore ? (
                    <span>{paramsItemData.envs.regulatoryContactPhone}</span>
                  ) : (
                    getFieldDecorator('regulatoryContactPhone', {
                      initialValue: paramsItemData?.envs?.regulatoryContactPhone,
                      rules: [
                        { required: true, message: '请输入手机号码' },
                        { max: 30, message: '长度不能操作30字符' },
                      ],
                    })(
                      <Input placeholder="请输入监督投诉电话" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={12}>
              <Item
                {...formItemLayout}
                label="传真"
              >
                {
                  isMore ? (
                    <span>{paramsItemData.envs.regulatoryContactFax}</span>
                  ) : (
                    getFieldDecorator('regulatoryContactFax', {
                      initialValue: paramsItemData.envs && paramsItemData.envs.regulatoryContactFax,
                      rules: [
                        { pattern: /^\d{3}-\d{8}$|^\d{4}-\d{7,8}$/, message: '格式不正确！' },
                      ],
                    })(
                      <Input placeholder="请输入传真" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={12}>
              <Item
                {...formItemLayout}
                label="地址"
              >
                {
                  isMore ? (
                    <span>{paramsItemData.envs.regulatoryContactAddr}</span>
                  ) : (
                    getFieldDecorator('regulatoryContactAddr', {
                      initialValue: paramsItemData?.envs?.regulatoryContactAddr,
                      rules: [ZcyValidate.maxLen(200)],
                    })(
                      <Input placeholder="请输入地址" />
                    )
                  )
                }
              </Item>
            </Col>
            <Col span={24}>
              <Item
                {...formItemLayout2}
                label="使用公告类型"
              >
                <OpenAnnTypeList
                  announcementTypeData={announcementTypeData}
                  isMore={isMore}
                  announcementTypes={paramsItemData.announcementTypes}
                  changeTag={changeTag}
                />
              </Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
}

export default ReleaseModal;
