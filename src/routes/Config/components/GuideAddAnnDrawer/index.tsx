import React, { useEffect, useState } from 'react';
import { Drawer, Form, FormGrid, Panel, Select, message, Switch, Spin } from 'doraemon';
import useDrawer from 'src/hooks/useDrawer';
import { FFC } from 'src/types/component';
import { announcementBusinessTypeDetailGet, createAnnouncementGuidePost, selectAnnouncementTypeGet, updateAnnouncementGuidePost } from 'src/api/announcement/api/config/guide';
import { listUserTypesGet } from 'src/api/announcement/config';
import type { SelectAnnouncementTypeGetResponse, AnnouncementBusinessTypeDetailGetResponse } from 'src/api/announcement/api/config/guide';

import { PageType } from '../../constants';

type TAnnTypeList = Required<SelectAnnouncementTypeGetResponse>['result']
type TDetailInfo = Required<AnnouncementBusinessTypeDetailGetResponse>['result']

enum EPageType {
  add = PageType.add,
  edit = PageType.edit,
  detail = PageType.detail
}

const GuideAddAnnDrawer: FFC<{
  visible: boolean,
  onCancel: () => void,
  onSubmit: () => void,
  districtCode: string,
  curId?: string
  pageType: EPageType
}> = ({
  form,
  visible,
  onCancel,
  onSubmit,
  districtCode,
  curId,
  pageType = PageType.add,
}) => {
  const { validateFields, getFieldDecorator } = form;
  const [list, setList] = useState<TAnnTypeList>([]);
  const [loading, setLoading] = useState(false);
  const [userTypes, setUserTypes] = useState<any[]>([]);
  const [curInfo, setCurInfo] = useState<TDetailInfo>();


  useEffect(() => {
    if (pageType === PageType.add) getList();
    if (pageType === PageType.edit || pageType === PageType.detail) getDetail();
  }, [pageType]);

  useEffect(() => {
    listUserTypesGet().then(res => {
      if (!res.success) return;
      setUserTypes((res.result as any[]) ?? []);
    });
    return () => setUserTypes([]);
  }, []);


  const getDetail = () => {
    setLoading(true);
    return announcementBusinessTypeDetailGet({
      relationId: curId!,
    }).then(res => {
      if (!res.success) return;
      setCurInfo(res.result);
    }).finally(() => {
      setLoading(false);
    });
  };

  const getList = () => {
    setLoading(true);
    return selectAnnouncementTypeGet({
      districtCode,
    }).then(res => {
      if (!res.success) return;
      setList(res.result ?? []);
    }).finally(() => {
      setLoading(false);
    });
  };


  const onSave = () => {
    validateFields((err, {
      announcementType,
      manualAnnouncement, businessAnnouncement, userType,
    }) => {
      if (err) return;
      setLoading(true);
      if (pageType === PageType.add) {
        createAnnouncementGuidePost({
          districtCode,
          announcementType,
          manualAnnouncement,
          businessAnnouncement,
          userType,
        }).then(res => {
          if (!res.success) return;
          return message.success('新增成功', () => {
            onCancel();
            onSubmit();
          });
        }).finally(() => {
          setLoading(false);
        });
        return;
      }
      updateAnnouncementGuidePost({
        id: +curId!,
        manualAnnouncement,
        businessAnnouncement,
        userType,
      }).then(res => {
        if (!res.success) return;
        return message.success('编辑成功', () => {
          onCancel();
          onSubmit();
        });
      }).finally(() => {
        setLoading(false);
      });
    });
  };

  const renderUserTypes = (userValues: string[]) => {
    const userDescArr = userValues.map(code => {
      const item = userTypes.find(user => user.code === code);
      if (item) return item.desc;
      return code;
    });
    return (
      <span>{ userDescArr.join('、') || '-' }</span>
    );
  };


  const title = pageType === PageType.add ? '新增公告接入' : '编辑公告接入';
  const TitleNode = pageType === PageType.detail ? (
    <>查看公告接入</>
  ) : (
    <div className="drawer-titleNode-wrapper">
      <span>{title}</span>
    </div>
  );

  const formGridItem = [
    {
      label: '公告类型',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('announcementType', {
          initialValue: curInfo?.announcementType || undefined,
          rules: [{
            required: true,
            message: '请选择',
          }],
        })(
          (pageType === PageType.add) ?
              (
                <Select
                  placeholder="请选择"
                  getPopupContainer={() => document.querySelector('.guideAddAnn-drawer')}
                  showSearch
                  allowClear
                  optionFilterProp="name"
                >
                  {
                    list.map(({ typeName, typeCode }) => (
                      <Select.Option name={typeName} key={typeCode} value={typeCode}>
                        {typeName}
                      </Select.Option>
                    ))
                  }
                </Select>
              ) :
              (
                <span>{curInfo?.announcementTypeName || '-'}</span>
              )
        );
      },
    },
    {
      label: '开启业务公告',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('businessAnnouncement', {
          initialValue: curInfo?.businessAnnouncement ?? true,
          valuePropName: 'checked',
        })(
          (pageType === PageType.add || pageType === PageType.edit) ? (
            <Switch
              checkedChildren="开启"
              unCheckedChildren="关闭"
            />
          ) : (
            <span>
              {
                curInfo?.businessAnnouncement ? '开启' : '关闭'
              }
            </span>
          )
        );
      },
    },
    {
      label: '开启手工公告',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('manualAnnouncement', {
          initialValue: curInfo?.manualAnnouncement ?? false,
          valuePropName: 'checked',
        })(
          (pageType === PageType.add || pageType === PageType.edit) ? (
            <Switch
              checkedChildren="开启"
              unCheckedChildren="关闭"
            />
          ) : (
            (
              <span>
                {
                  curInfo?.manualAnnouncement ? '开启' : '关闭'
                }
              </span>
            )
          )
        );
      },
    },
    {
      label: '手工公告关联的用户类别',
      colSpan: 2,
      help: curInfo?.isExtend ? <span className="zcy-mg-t-4 zcy-dp-ib">配置继承自：{curInfo.districtName}</span> : '',
      render: () => {
        return getFieldDecorator('userType', {
          initialValue: curInfo?.userType || undefined,
        })(
          pageType === PageType.detail ? renderUserTypes(curInfo?.userType ?? []) : (
            <Select
              mode="multiple"
              placeholder="请选择"
              getPopupContainer={() => document.querySelector('.guideAddAnn-drawer')}
            >
              {
                userTypes?.map(
                  ({ code, desc }) =>
                    <Select.Option key={code} value={code}>{desc}</Select.Option>
                )
              }
            </Select>
          )
        );
      },
    },
  ];

  return (
    <Drawer
      title={TitleNode}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="guideAddAnn-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <Form>
          <Panel.Sub bordered title="基本信息" >
            <FormGrid bordered formGridItem={formGridItem} />
          </Panel.Sub>
        </Form>
      </Spin>
    </Drawer>
  );
};


const GuideAddAnnDrawerByForm = Form.create()(GuideAddAnnDrawer);

const useGuideAddAnnDrawer = (props) => {
  return useDrawer(GuideAddAnnDrawerByForm, props);
};

export { useGuideAddAnnDrawer };
export default GuideAddAnnDrawerByForm;
