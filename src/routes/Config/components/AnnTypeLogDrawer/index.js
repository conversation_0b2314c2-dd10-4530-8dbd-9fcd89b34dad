import React, { useEffect, useState } from 'react';
import { Z<PERSON><PERSON>ist, Drawer, Input, Spin, DatePicker, Select } from 'doraemon';
import moment from 'moment';
import { listBigAnnLog } from '../../services/api';
import { SourceType, OperationType } from '../../constants';
import useDrawer from 'src/hooks/useDrawer';

const { RangePicker } = DatePicker;
const { Option } = Select;

const getTitle = (type) => {
  if (type === SourceType.announcementBigType) return '查看公告大类操作日志';
  return '查看公告类型操作日志';
};

const initSearchParams = {
  pageNo: 1,
  pageSize: 10,
  operationType: null,
};

const AnnTypeLogDrawer = ({ visible, onCancel, source }) => {
  const [loading, setLoading] = useState(false);
  const [listData, setListData] = useState({
    data: [],
    total: 0,
  });
  const getCustomItem = () => {
    return [{
      label: '操作类型',
      id: 'operationType',
      decoratorOptions: {
        initialValue: OperationType[0].code,
      },
      render: () => {
        return (
          <Select getPopupContainer={e => e?.parentNode}>
            {OperationType.map(ele =>
              <Option key={ele.code} value={ele.code}>{ele.name}</Option>)}
          </Select>
        );
      },
    },
    {
      label: source === SourceType.announcementBigType ? '公告大类名称' : '公告类型名称',
      id: 'typeName',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    },
    {
      label: source === SourceType.announcementBigType ? '公告大类ID' : '公告Code',
      id: 'typeCode',
      render: () => {
        return <Input maxLength={18} placeholder="请输入" />;
      },
    },
    {
      label: '操作人',
      id: 'operatorName',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    }, {
      label: '操作时间',
      id: 'addTimeRange',
      render: () => {
        return (
          <RangePicker
            getCalendarContainer={e => e?.parentNode}
            format="YYYY/MM/DD"
            placeholder={['开始时间', '结束时间']}
          />
        );
      },
    }, {
      label: '备注',
      id: 'remark',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    }];
  };

  const getTableColumn = () => {
    const insertColumn = [];
    if (source === SourceType.announcementBigType) {
      insertColumn.push(...[{
        title: '公告大类ID',
        dataIndex: 'typeCode',
      },
      {
        title: '公告大类名称',
        dataIndex: 'typeName',
      }]);
    } else {
      insertColumn.push(...[{
        title: '公告Code',
        dataIndex: 'typeCode',
      },
      {
        title: '公告类型名称',
        dataIndex: 'typeName',
      }]);
    }

    return [
      {
        title: '操作类型',
        dataIndex: 'operationType',
      },
      ...insertColumn,
      {
        title: '操作人',
        dataIndex: 'operatorName',
      },
      {
        title: '操作时间',
        dataIndex: 'addTime',
        render: (val) => {
          if (!val) return '-';
          return moment(val).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '备注',
        dataIndex: 'remark',
        render: (val) => {
          if (!val) return '-';
          return val;
        },
      },
    ];
  };

  const getLogsData = (params) => {
    let addTimeStart;
    let addTimeEnd;
    if (params.operationType === OperationType[0].code) {
      params.operationType = null;
    }
    if (params.addTimeRange && params.addTimeRange.length) {
      [addTimeStart, addTimeEnd] = params.addTimeRange;
    }

    return listBigAnnLog({
      ...params,
      addTimeRange: undefined,
      addTimeStart,
      addTimeEnd,
      source,
    }).then((res) => {
      setListData({
        data: (res.result?.data ?? []).map((ele, index) => ({ ...ele, localId: index })),
        total: res.result?.total ?? 0,
      });
      setLoading(false);
    });
  };

  const onSearch = (params) => {
    getLogsData({ ...initSearchParams, ...params });
  };

  useEffect(() => {
    if (visible) {
      getLogsData({ ...initSearchParams });
    }
  }, [visible]);


  return (
    <Drawer
      title={getTitle(source)}
      visible={visible}
      width={1100}
      className="annTypeLog-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <ZcyList
          customItem={getCustomItem()}
          onSearch={onSearch}
          table={{
            rowKey: 'localId',
            dataSource: listData.data,
            columns: getTableColumn(),
            pagination: {
              total: listData.total,
              showSizeChanger: true,
              defaultCurrent: initSearchParams.pageNo,
              defaultPageSize: initSearchParams.pageSize,
            },
          }}
        />
      </Spin>
    </Drawer>
  );
};

const useAnnTypeLogDrawer = (props) => {
  return useDrawer(AnnTypeLogDrawer, props);
};

export { useAnnTypeLogDrawer };
export default AnnTypeLogDrawer;
