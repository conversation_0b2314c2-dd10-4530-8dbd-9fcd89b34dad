import { Modal } from 'doraemon';
import type { TPageType } from './types';

// 请切换账户角色 提示
const showAccountModal = () => {
  Modal.warning({
    title: '请切换账户角色',
    content: '如页面报错404或提示无权限，请在右上方切换账号当前角色至【无相管理机构】后，再次刷新页面',
  });
};

const getConfigBtnText = (pageType: TPageType) => {
  if (pageType === 'detail') return '查看配置';
  return '进入配置';
};

export {
  showAccountModal,
  getConfigBtnText,
};

// 请切换账户角色 提示
// const showAccountModal = () => {
//   Modal.confirm({
//     title: '请切换账户角色',
//     content: '如页面报错404或提示无权限，请在右上方切换账号当前角色至【无相管理机构】后，再次刷新页面',
//     cancelButtonProps: {
//       type: 'primary',
//     },
//     okButtonProps: {
//       type: 'primary',
//     },
//     okText: '进入模板管理(只读)',
//     cancelText: '进入表单模板中心',
//     onOk: () => { console.log('onOk'); },
//     onCancel: () => { console.log('onCancel'); },
//   });
// };
