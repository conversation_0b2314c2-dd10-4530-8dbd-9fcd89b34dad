import React from 'react';
import { Panel, FormGrid, Switch, Alert } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';
import { getConfigBtnText } from '../utils';

const { Sub } = Panel;

const AnnouncementDisplay: PureFFC<IFormGridProps> = ({ form, pageType, formData }) => {
  const { getFieldDecorator } = form;
  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告展现渠道"
      >
        <Alert
          message="说明：目前支持以下两种方式进行公告信息的展现，请区划PM注意核实当前区划站点的实际搭建方式，根据实际情况进行选择。"
          type="firstinfo"
          showIcon
          iconType="exclamation-circle-o"
        />
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '搭建大厅展现',
              render: () => {
                // return getFieldDecorator('customizedTitle', {
                //   valuePropName: 'checked',
                // })(
                //   <Switch
                //     checkedChildren="使用"
                //     unCheckedChildren="不使用"
                //   />
                // );
                return (
                  <React.Fragment>
                    <a
                      target="_blank"
                      href={linkUrl?.hallShowConfiguration || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '搭建网站展现',
              render: () => {
                // return getFieldDecorator('customizedTitle', {
                //   valuePropName: 'checked',
                // })(
                //   <Switch
                //     checkedChildren="使用"
                //     unCheckedChildren="不使用"
                //   />
                // );
                return (
                  <React.Fragment>
                    <a
                      target="_blank"
                      href={linkUrl?.announcementSiteShowConfiguration || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default AnnouncementDisplay;
