import React from 'react';
import { Panel, FormGrid, Switch } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import { useAnnPreviewModal } from '../../AnnPreviewModal';
import { ANN_PREVIEW_KEY } from '../../../constants';
import AnnBaseInfo from './AnnBaseInfo';
import { showAccountModal, getConfigBtnText } from '../utils';

const { Sub } = Panel;

const AnnouncementTitleConfiguration: PureFFC<IFormGridProps> = ({ 
  form, 
  pageType, formData, districtCode,
}) => {
  const [openAnnPreview] = useAnnPreviewModal({
    annPreviewKey: ANN_PREVIEW_KEY[1],
    announcementType: formData?.announcementTypeCode,
    districtCode,
    visible: true,
  });
  const { getFieldDecorator } = form;
  const canEdit = formData?.canSave || formData?.canSubmit;

  const handlePreview = () => {
    openAnnPreview({});
  };

  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告标题配置"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '公告标题自定义配置',
              render: () => {
                return getFieldDecorator('customizedTitle', {
                  initialValue: !!formData?.customizedTitle,
                  valuePropName: 'checked',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Switch
                      checkedChildren="启用"
                      unCheckedChildren="停用"
                    />
                  ) : (
                    <span>
                      {formData?.customizedTitle ? '启用' : '停用'}
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告标题特性值',
              render: () => {
                return (
                  <React.Fragment>
                    <span>{formData?.characteristicValues || '-'}</span>
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.templateTitleCharacteristicValuesUrl || '-'}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告标题模板名称',
              render: () => {
                return (
                  <React.Fragment>
                    <span>{formData?.templateName || '-'}</span>
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.templateTitleNameUrl || '-'}
                      onClick={() => {
                        if (formData?.isFormPage) {
                          showAccountModal();
                        }
                      }}
                    >{getConfigBtnText(pageType)}
                    </a>
                    {
                      formData?.templateId ? (
                        <React.Fragment>
                          <a className="zcy-mg-l-12"
                            onClick={() => handlePreview()}
                          >预览
                          </a>
                          {
                            formData?.isTemplateExtend ? (
                              <p style={{ color: '#bfbfbf' }}>注：继承自{formData?.templateDistrictName}
                                （{formData?.templateDistrictCode}）
                              </p>
                            ) : (
                              <p style={{ color: '#bfbfbf' }}>注：区划为{formData?.templateDistrictName}
                                （{formData?.templateDistrictCode}）
                              </p>
                            )
                          }
                        </React.Fragment>
                      ) : null
                    }

                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default AnnouncementTitleConfiguration;
