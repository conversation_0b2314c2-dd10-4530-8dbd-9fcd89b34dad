import React from 'react';
import { Panel, FormGrid, Switch, Divider } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';
import { getConfigBtnText } from '../utils';

const { Sub } = Panel;

const SupervisionFormGrid = ({ data }) => {
  const groupInfo = data?.announcementSupervisorEnvDTO ?? {};
  return (
    <FormGrid
      className="config-grid"
      formGridItem={[
        {
          label: '配置组名称',
          render: () => {
            return data?.groupName || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        }, {
          label: '同级政府采购监督管理部门',
          render: () => {
            return groupInfo?.regulatoryOrgName || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        }, {
          label: '联系人',
          render: () => {
            return groupInfo?.regulatoryContactPerson || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        }, {
          label: '传真',
          render: () => {
            return groupInfo?.regulatoryContactFax || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        }, {
          label: '监督投诉电话',
          render: () => {
            return groupInfo?.regulatoryContactPhone || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        }, {
          label: '地址',
          render: () => {
            return groupInfo?.regulatoryContactAddr || '-';
          },
          colSpan: 2,
          ...formItemLayout,
        },
      ]}
    />
  );
};


const ContentSupervision: PureFFC<IFormGridProps> = ({ form, pageType, formData }) => {
  const { getFieldDecorator, getFieldValue } = form;
  const canEdit = formData?.canSave ?? formData?.canSubmit;
  const list = formData?.announcementSupervisorDTOS ?? [];
  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="敏感词规则配置"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '是否开启敏感词校验',
              render: () => {
                return getFieldDecorator('canContentCensor', {
                  initialValue: !!formData?.canContentCensor,
                  valuePropName: 'checked',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Switch
                      checkedChildren="启用"
                      unCheckedChildren="停用"
                    />
                  ) : (
                    <span>
                      {
                        formData?.canContentCensor ? '启用' : '停用'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('canContentCensor') && {
              label: '公告敏感词校验规则',
              render: () => {
                return (
                  <React.Fragment>
                    <a
                      target="_blank"
                      href={linkUrl?.supervisionConfigurationRule || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('canContentCensor') && {
              label: '规则配置说明',
              render: () => {
                // const text = `
                // 1、查询条件：业务场景选择“采购公告”，该业务场景适用于公告中心的所有公告。
                // 2、请留意“配置级别”、“状态”两项参数，只有适配当前区划且开启的敏感词，才会校验。
                // 3、开启校验后，公告中心将对“提示级、禁止级”两类敏感词全部进行拦截，请知悉。
                // `;
                const text = formData?.contentCensorRuleExplain || '-';
                return (
                  <React.Fragment>
                    <span style={{ whiteSpace: 'pre-line' }}>
                      {text.trim()}
                    </span>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ].filter(Boolean)}
        />
      </Sub>
      {
        list.length ? (
          <Sub
            title="公告监管配置"
          >
            {
              list.map((ele, index) => {
                if (index === 0) return <SupervisionFormGrid key={ele.id} data={ele} />;
                return (
                  <React.Fragment key={ele.id}>
                    <Divider />
                    <SupervisionFormGrid data={ele} />
                  </React.Fragment>
                );
              })
            }
          </Sub>
        ) : null
      }

    </React.Fragment>
  );
};

export default ContentSupervision;
