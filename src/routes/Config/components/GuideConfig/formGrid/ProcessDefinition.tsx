import React from 'react';
import { Panel, FormGrid } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';
import { getConfigBtnText } from '../utils';

const { Sub } = Panel;

const ProcessDefinition: PureFFC<IFormGridProps> = ({ formData, pageType }) => {
  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="流程定义配置（天行）"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '独立配置流程',
              render: () => {
                return (
                  <React.Fragment>
                    {
                      formData?.isIndependenceProcess === null ? (
                        <span>-</span>
                      ) : (
                        <span>{ formData?.isIndependenceProcess ? '是' : '否'}</span>
                      )
                    }
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.processDefinitionConfiguration}
                    >{getConfigBtnText(pageType)}
                    </a>
                    <p style={{ color: '#bfbfbf' }}>
                      注：未独立配置流程的，均使用公告新工作流通用流程（announcement_check_flow_base）
                    </p>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '关联流程名称',
              render: () => {
                return formData?.processDefineKeyName || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '关联流程key',
              render: () => {
                return formData?.processDefineKey || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default ProcessDefinition;
