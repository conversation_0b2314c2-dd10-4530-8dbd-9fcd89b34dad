import React, { useEffect, useState } from 'react';
import { Panel, FormGrid, Input, Radio, Select, TreeSelect, SelectItem, Popover, Icon } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { PUBLISH_TYPES } from 'src/constants';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';
import { obtainCglxandcgfsGet } from 'src/api/announcement/api';
import { formItemLayout, termTypeOptions } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';

const { Sub } = Panel;
const { Group } = Radio;
const { Option } = Select;

const IS_ALL_CHINESE = /^[\u4e00-\u9fa5]+$/;

const getNodes = (tree: any[]) => {
  const newTree: any[] = [];
  if (!tree) {
    return [];
  }
  tree.forEach((item) => {
    newTree.push({
      title: item.codeName,
      value: item.code,
      key: item.code,
      children: (item.children ? getNodes(item.children) : undefined),
    });
  });
  return newTree;
};

const transTreeList = data =>
  data.map((i) => {
    i.value = i.code + '';
    i.label = i.name;
    i.title = i.name;
    if (i.children) transTreeList(i.children);
    return i;
  });

const AnnouncementOptions: PureFFC<IFormGridProps> = ({
  form, pageType,
  formData, districtCode }) => {
  const { getFieldDecorator, getFieldValue } = form;
  const [cgfs, setCgfs] = useState<any[]>([]);
  const [districtTree, setDistrictTree] = useState<any[]>([]);

  const canEdit = formData?.canSave || formData?.canSubmit;

  useEffect(() => {
    if (!districtCode) return;
    obtainCglxandcgfsGet({
      districtCode,
    }).then(res => {
      if (!res.success) return;
      setCgfs(getNodes((res.result as any)?.cgfs ?? []));
    });
  }, [districtCode]);

  useEffect(() => {
    getDistTree();
  }, []);

  const renderPublishTypes = (publishTypes) => {
    if (!publishTypes) return '-';
    return publishTypes.split(',').map(ele => {
      return PUBLISH_TYPES.find(e => e.v === +ele)?.k;
    }).join('、') || '-';
  };

  const getDistTree = () => {
    getAuthorityDistTreeGet().then((res) => {
      if (!res.success) return;
      setDistrictTree(transTreeList(res.result));
    });
  };

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="基本属性"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '公示期计算方式',
              render: () => {
                return getFieldDecorator('termType', {
                  initialValue: formData?.termType,
                  rules: [{
                    required: true,
                    message: '请选择',
                  }],
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Select
                      style={{ width: 220 }}
                      placeholder="请选择"
                      getPopupContainer={el => el.parentNode}
                    >
                      {
                        termTypeOptions.map(({ value, label }) => (
                          <Option key={value} value={value}>
                            {label}
                          </Option>
                        ))
                      }
                    </Select>
                  ) : (
                    <span>
                      {
                        termTypeOptions.find(ele => ele.value === formData?.termType)?.label || '-'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公示天数',
              render: () => {
                return (
                  <React.Fragment>
                    {
                      getFieldDecorator('expiryPeriod', {
                        initialValue: formData?.expiryPeriod ?? 999,
                        rules: [
                          { required: true, message: '请输入' },
                          { pattern: /^((0|[1-9])\d{0,2}|-1)$/, message: '仅支持-1到999整数' },
                        ],
                      })(
                        (pageType === 'edit' && canEdit) ? (
                          <Input placeholder="请输入" />
                        ) : (
                          <React.Fragment>
                            {formData?.expiryPeriod}
                          </React.Fragment>
                        )
                      )
                    }
                    <p style={{ color: '#bfbfbf' }}>注：-1为无限期</p>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '对应采购方式',
              render: () => {
                return getFieldDecorator('procurementMethod', {
                  rules: [{ required: true, message: '请选择' }],
                  initialValue: (formData?.procurementMethod || []).map(ele => ({
                    label: ele.codeDesc,
                    value: ele.code,
                  })),
                })(
                  <TreeSelect
                    disabled={pageType === 'detail'}
                    style={{ width: 220, outline: 'none' }}
                    treeCheckable
                    labelInValue
                    dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
                    treeData={cgfs}
                    placeholder="请选择"
                    getPopupContainer={el => el.parentNode}
                  />
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {

            },
            {
              label: '发布时间方案设置',
              render: () => {
                return getFieldDecorator('publishTypes', {
                  initialValue: formData?.publishTypes ? formData?.publishTypes.split(',').map(ele => +ele) : undefined,
                  rules: [
                    { required: true, message: '请选择' },
                  ],
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Select
                      mode="multiple"
                      optionFilterProp="label"
                      getPopupContainer={node => node}
                    >
                      {PUBLISH_TYPES.map(ele =>
                        <Option key={ele.v} label={ele.k} value={+ele.v}>{ele.k}</Option>)}
                    </Select>
                  ) : (
                    <span>
                      {renderPublishTypes(formData?.publishTypes)}
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
      <Sub
        title="附件控制"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '需要上传附件',
              render: () => {
                return getFieldDecorator('attachmentShow', {
                  rules: [{ required: true }],
                  initialValue: !!formData?.attachmentShow,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.attachmentShow ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('attachmentShow') && {
              label: '附件是否必填',
              render: () => {
                return getFieldDecorator('hasAttachment', {
                  rules: [{ required: true }],
                  initialValue: !!formData?.hasAttachment,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.hasAttachment ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ].filter(Boolean)}
        />
      </Sub>
      <Sub
        title="操作控制"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '是否开通异议',
              render: () => {
                return getFieldDecorator('canObject', {
                  rules: [{ required: true }],
                  initialValue: !!formData?.canObject,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.canObject ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('canObject') && {
              label: '被质疑对象信息设置',
              render: () => {
                return getFieldDecorator('objectionKeys', {
                  rules: [
                    { required: true, message: '请选择' },
                  ],
                  initialValue: formData?.objectionKeys ? formData?.objectionKeys.split(',') : undefined,
                })(
                  <SelectItem
                    disabled={pageType === 'detail'}
                    mode="multiple"
                    getPopupContainer={el => el.parentNode}
                    key={`Type=${formData?.announcementTypeCode}districtCode=${districtCode}`}
                    url={`/announcement/api/obtainMetaDataList?announcementType=${formData?.announcementTypeCode}&districtCode=${districtCode}`}
                    type="objectionKeys"
                    placeholder="请选择"
                  />
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('canObject') && {
              label: '质疑对象信息设置',
              render: () => {
                return getFieldDecorator('beObjectionKeys', {
                  initialValue: formData?.beObjectionKeys ? formData?.beObjectionKeys.split(',') : undefined,
                })(
                  <SelectItem
                    disabled={pageType === 'detail'}
                    mode="multiple"
                    getPopupContainer={el => el.parentNode}
                    key={`Type=${formData?.announcementTypeCode}districtCode=${districtCode}`}
                    url={`/announcement/api/obtainMetaDataList?announcementType=${formData?.announcementTypeCode}&districtCode=${districtCode}`}
                    type="objectionKeys"
                    placeholder="请选择"
                  />
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告发布后是否可撤回',
              render: () => {
                return getFieldDecorator('canRevoke', {
                  initialValue: !!formData?.canRevoke,
                  rules: [{ required: true }],
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.canRevoke ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('canRevoke') && {
              label: '公告发布后可撤回时长',
              render: () => {
                return getFieldDecorator('revokeTime', {
                  rules: [{ required: true }],
                  initialValue: formData?.revokeTime || 600, // 默认 10 分钟
                })(
                  <Select
                    disabled={pageType === 'detail'}
                    getPopupContainer={node => node}
                  >
                    <Option value={600}>10分钟</Option>
                    <Option value={1800}>30分钟</Option>
                    <Option value={7200}>2小时</Option>
                    <Option value={21600}>6小时</Option>
                  </Select>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '是否允许取消公告',
              render: () => {
                return getFieldDecorator('isRevocable', {
                  rules: [{ required: true }],
                  initialValue: !!formData?.isRevocable,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.isRevocable ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('isRevocable') && {
              label: '[取消公告]按钮名称',
              render: () => {
                return getFieldDecorator('revocableButtonName', {
                  rules: [
                    { required: true, message: '必填项' },
                    { pattern: IS_ALL_CHINESE, message: '仅允许输入汉字' },
                  ],
                  initialValue: formData?.revocableButtonName || '取消公告',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Input maxLength={10} placeholder="请输入" />
                  ) : (
                    <React.Fragment>
                      {formData?.revocableButtonName}
                    </React.Fragment>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: (
                <span>
                  发布区划白名单设置
                  <Popover
                    placement="top"
                    getPopupContainer={() => document.getElementsByClassName('config-grid')[0]}
                    content={(
                      <div>
                        以白名单方式指定手工公告发布的特定区划范围。<br />
                        默认范围：可发布到当前操作人所在的本级及下级区划
                      </div>
                    )}
                  >
                    <Icon type="question-circle-o" />
                  </Popover>
                </span>
              ),
              extra: formData?.isFormPage ? <span style={{ width: 220, display: 'inline-block' }}> 注意：当前公告类型已接入表单，上述区划依赖后台表单组件中相应参数配置后生效。如未立即生效，请联系产品运营处理！</span> : null,
              render: () => {
                return getFieldDecorator('whiteListDistrictCode', {
                  rules: [{
                    validator: (_, value, callback) => {
                      if (value?.length > 999) {
                        callback(new Error('最大发布区划白名单数量为999！'));
                      }
                      callback();
                    },
                  }],
                  initialValue: formData?.whiteListDistrict?.map(ele => ele.code)
                   || undefined,
                })(
                  <TreeSelect
                    disabled={pageType === 'detail'}
                    allowClear
                    maxTagCount={20}
                    treeData={districtTree}
                    getPopupContainer={el => el.parentNode}
                    treeCheckable
                    showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    searchPlaceholder="请选择"
                    treeNodeFilterProp="label"
                  />
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ].filter(Boolean)}
        />


      </Sub>
      <Sub
        title="关联控制"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '手工公告关联采购计划',
              render: () => {
                return getFieldDecorator('isForcedControl', {
                  initialValue: !!formData?.isForcedControl,
                  rules: [{ required: true }],
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>强控</Radio>
                      <Radio value={false}>不强控</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.isForcedControl ? '强控' : '不强控'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};


export default AnnouncementOptions;
