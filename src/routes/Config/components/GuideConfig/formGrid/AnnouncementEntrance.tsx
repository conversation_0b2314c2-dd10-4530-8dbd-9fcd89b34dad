import React from 'react';
import { Panel, FormGrid, Switch } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';
import { getConfigBtnText } from '../utils';

const { Sub } = Panel;

const AnnouncementEntrance: PureFFC<IFormGridProps> = ({ form, pageType, formData }) => {
  const { getFieldDecorator } = form;
  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="功能订购开通"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '租户订购应用',
              render: () => {
                // return getFieldDecorator('customizedTitle', {
                //   valuePropName: 'checked',
                // })(
                //   <Switch
                //     checkedChildren="配置"
                //     unCheckedChildren="未配置"
                //   />
                // );
                return (
                  <React.Fragment>
                    <a
                      target="_blank"
                      href={linkUrl?.orderingApplicationsConfiguration || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '岗位订购菜单',
              render: () => {
                // return getFieldDecorator('customizedTitle', {
                //   valuePropName: 'checked',
                // })(
                //   <Switch
                //     checkedChildren="配置"
                //     unCheckedChildren="未配置"
                //   />
                // );
                return (
                  <React.Fragment>
                    <a
                      target="_blank"
                      href={linkUrl?.orderMenuConfiguration || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default AnnouncementEntrance;
