import React from 'react';
import { Panel, FormGrid } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';

const { Sub } = Panel;

const AnnouncementDefinition: PureFFC<IFormGridProps> = ({ formData }) => {
  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告基础映射"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '公告特性值',
              render: () => {
                return formData?.characteristicValues || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告标题特性值',
              render: () => {
                return formData?.titleCharacteristicValues || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '表单页面code',
              render: () => {
                return formData?.formPageCode || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
      <Sub
        title="流程控制映射"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '关联流程key',
              render: () => {
                return formData?.processDefineKey || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
      <Sub
        title="权限控制映射"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '细分权限码',
              render: () => {
                return formData?.addPrivilegeCode || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default AnnouncementDefinition;
