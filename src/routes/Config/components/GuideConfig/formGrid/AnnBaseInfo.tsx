import React from 'react';
import { Panel, FormGrid } from 'doraemon';
import { formItemLayout } from '../config';
import { TFormData } from '../types';

const { Sub } = Panel;

const AnnBaseInfo:React.FC<{formData: TFormData}> = ({ formData }) => {
  return (
    <Sub
      title="公告基本信息"
    >
      <FormGrid
        className="config-grid"
        formGridItem={[
          {
            label: '公告大类',
            render: () => {
              return formData?.bigAnnouncementTypeName || '-';
            },
            colSpan: 2,
            ...formItemLayout,
          }, {
            label: '公告类型名称',
            render: () => {
              return formData?.announcementTypeName || '-';
            },
            colSpan: 2,
            ...formItemLayout,
          }, {
            label: '公告Code',
            render: () => {
              return formData?.announcementTypeCode || '-';
            },
            colSpan: 2,
            ...formItemLayout,
          },
        ]}
      />
    </Sub>
  );
};

export default AnnBaseInfo;
