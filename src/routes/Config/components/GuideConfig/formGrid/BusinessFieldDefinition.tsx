import React from 'react';
import { Panel, FormGrid, Input } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';

const { Sub } = Panel;

const BusinessFieldDefinition: PureFFC<IFormGridProps> = ({ form, pageType, formData }) => {
  const { getFieldDecorator } = form;
  const canEdit = formData?.canSave || formData?.canSubmit;

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告页面配置"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '业务字段定义',
              render: () => {
                return getFieldDecorator('businessFieldDefinition', {
                  initialValue: formData?.businessFieldDefinition,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Input.TextArea maxLength={1000} />
                  ) : (
                    <span >
                      {formData?.businessFieldDefinition || '-'.trim()}
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default BusinessFieldDefinition;
