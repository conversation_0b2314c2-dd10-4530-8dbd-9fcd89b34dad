import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FormGrid, Switch } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { fixNull } from 'src/utils/utils';
import { IFormGridProps } from '../types';
import { formItemLayout } from '../config';
import AnnBaseInfo from './AnnBaseInfo';

const { Sub } = Panel;

const AnnouncementPush: PureFFC<IFormGridProps> = ({ formData, pageType, form }) => {
  const { getFieldDecorator } = form;
  const canEdit = formData?.canSave || formData?.canSubmit;
  const list = formData?.configSiteRelationDtoList ?? [];
  const getTableColumn = () => {
    return [
      {
        title: '序号',
        dataIndex: 'index',
        render: (_, __, index) => index + 1,
        width: 70,
        align: 'center',
      }, {
        title: '公告站点名称',
        dataIndex: 'siteName',
        render: (text) => fixNull(text),
      }, {
        title: '站点域名',
        width: 200,
        dataIndex: 'siteDomain',
        render: (text) => fixNull(text),
      }, 
      {
        title: '所在栏目',
        width: 90,
        dataIndex: 'categoryName',
        render: (text) => fixNull(text),
      }, 
      {
        title: '操作',
        dataIndex: 'action',
        width: 90,
        render: (_, { siteManagerUrl }) => {
          return (
            <React.Fragment>
              {
                siteManagerUrl ? (
                  <a onClick={() => window.open(siteManagerUrl)}>进入后台</a>
                ) : (
                  <span>-</span>
                )
              }
            </React.Fragment>
          );
        },
      },
    ];
  };
  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告展现渠道"
      >
        <Alert
          message="说明：此处只展现已启用的，且已关联当前公告类型相关展现栏目的公告站点清单。第三方站点无法获取公告所在栏目。"
          type="firstinfo"
          showIcon
          iconType="exclamation-circle-o"
        />
        <ZcyList
          table={{
            rowKey: 'id',
            dataSource: list,
            columns: getTableColumn(),
            pagination: false,
          }}
        />
      </Sub>
      <Sub
        title="推送控制"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '是否开启短信通知',
              render: () => {
                return getFieldDecorator('isNotify', {
                  initialValue: !!formData?.isNotify,
                  valuePropName: 'checked',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Switch
                      checkedChildren="启用"
                      unCheckedChildren="停用"
                    />
                  ) : (
                    <span>
                      {formData?.isNotify ? '启用' : '停用'}
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '是否开放到外网',
              render: () => {
                return getFieldDecorator('isOut', {
                  initialValue: !!formData?.isOut,
                  valuePropName: 'checked',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Switch
                      checkedChildren="启用"
                      unCheckedChildren="停用"
                    />
                  ) : (
                    <span>
                      {formData?.isOut ? '启用' : '停用'}
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>
    </React.Fragment>
  );
};

export default AnnouncementPush;
