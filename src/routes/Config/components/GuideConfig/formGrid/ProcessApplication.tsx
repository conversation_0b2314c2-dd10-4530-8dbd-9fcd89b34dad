import React, { useEffect, useState } from 'react';
import { Panel, FormGrid, Radio, Checkbox, Row } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { listAgencyTypesGet } from 'src/api/announcement/config';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import AnnBaseInfo from './AnnBaseInfo';
import { getConfigBtnText } from '../utils';

const { Sub } = Panel;
const { Group } = Radio;
const CheckboxGroup = Checkbox.Group;

const ProcessApplication: PureFFC<IFormGridProps> = ({ formData, pageType, form }) => {
  const { getFieldDecorator, getFieldValue } = form;
  const linkUrl: any = formData?.url ?? {};
  const [agencyTypes, setAgencyTypes] = useState<any[]>([]);
  const checkAgencyTypes = Array.isArray(formData?.checkAgencyTypes)
   && !!formData?.checkAgencyTypes.length;
  const canEdit = formData?.canSave || formData?.canSubmit;

  useEffect(() => {
    listAgencyTypesGet().then(res => {
      if (!res.success) return;
      setAgencyTypes((res.result as any[]) ?? []);
    });
    return () => setAgencyTypes([]);
  }, []);

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="流程套用配置"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '流程层级',
              render: () => {
                return (
                  <React.Fragment>
                    {
                      formData?.isPlatform === null ? (
                        <span>-</span>
                      ) : (
                        <span>{formData?.isPlatform ? '平台级' : '区划级' }</span>
                      )
                    }
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.processApplicationConfiguration || '-'}
                    >{getConfigBtnText(pageType)}
                    </a>
                    <p style={{ color: '#bfbfbf' }}>
                      注：未配置机构级配置则自动套用区划级配置，未配置区划级配置则自动套用平台级配置。
                    </p>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '流程名称',
              render: () => {
                return formData?.processDefineKeyName || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '流程key',
              render: () => {
                return formData?.processDefineKey || '-';
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ]}
        />
      </Sub>

      <Sub
        title="审批控制选项"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '校验代理机构公告发布',
              render: () => {
                return getFieldDecorator('isCheckAgencyTypes', {
                  initialValue: checkAgencyTypes,
                  rules: [{ required: true }],
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                      <p style={{ color: '#bfbfbf' }}>注：选择是否校验公告发布区划</p>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.isCheckAgencyTypes ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('isCheckAgencyTypes') && {
              label: '校验代理机构类别',
              render: () => {
                return getFieldDecorator('checkAgencyTypes', {
                  rules: [{ required: true, message: '请选择校验代理机构类别' }],
                  initialValue: formData?.checkAgencyTypes,
                })(
                  <CheckboxGroup disabled={pageType !== 'edit'}>
                    {agencyTypes.map(item => (
                      <Row key={item.code}>
                        <Checkbox value={item.code}>{item.desc}</Checkbox>
                      </Row>
                    ))}
                  </CheckboxGroup>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ].filter(Boolean)}
        />
      </Sub>
    </React.Fragment>
  );
};

export default ProcessApplication;
