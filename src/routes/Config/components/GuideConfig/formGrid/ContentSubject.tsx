import React from 'react';
import { Panel, FormGrid, Radio, Switch } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { formItemLayout } from '../config';
import { IFormGridProps } from '../types';
import { useAnnPreviewModal } from '../../AnnPreviewModal';
import { ANN_PREVIEW_KEY } from '../../../constants';
import AnnBaseInfo from './AnnBaseInfo';
import { showAccountModal, getConfigBtnText } from '../utils';

const { Sub } = Panel;
const { Group } = Radio;

const ContentSubject: PureFFC<IFormGridProps> = ({ form, pageType, formData, districtCode }) => {
  const [openAnnPreview] = useAnnPreviewModal({
    annPreviewKey: ANN_PREVIEW_KEY[0],
    announcementType: formData?.announcementTypeCode,
    districtCode,
    visible: true,
  });
  const { getFieldDecorator, getFieldValue } = form;
  const canEdit = formData?.canSave || formData?.canSubmit;

  const handlePreview = () => {
    openAnnPreview({});
  };


  const linkUrl: any = formData?.url ?? {};

  return (
    <React.Fragment>
      <AnnBaseInfo formData={formData} />
      <Sub
        title="公告内容配置"
      >
        <FormGrid
          className="config-grid"
          formGridItem={[
            {
              label: '公告特性值',
              render: () => {
                return (
                  <React.Fragment>
                    <span>{formData?.characteristicValues || '-'}</span>
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.templateCharacteristicValuesUrl || ''}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告内容模板名称',
              render: () => {
                return (
                  <React.Fragment>
                    <span>{formData?.templateId ? formData?.templateName || '-' : '-'}</span>
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.templateNameUrl || ''}
                      onClick={() => {
                        if (formData?.isFormPage) {
                          showAccountModal();
                        }
                      }}
                    >{getConfigBtnText(pageType)}
                    </a>
                    {
                      formData?.templateId ? (
                        <React.Fragment>
                          <a className="zcy-mg-l-12"
                            onClick={() => handlePreview()}
                          >预览
                          </a>
                          {
                            formData?.isTemplateExtend ? (
                              <p style={{ color: '#bfbfbf' }}>注：继承自{formData?.templateDistrictName}
                                （{formData?.templateDistrictCode}）
                              </p>
                            ) : (
                              <p style={{ color: '#bfbfbf' }}>注：区划为{formData?.templateDistrictName}
                                （{formData?.templateDistrictCode}）
                              </p>
                            )
                          }
                        </React.Fragment>
                      ) : null
                    }
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '公告接入表单',
              render: () => {
                return getFieldDecorator('isFormPage', {
                  initialValue: !!formData?.isFormPage,
                  valuePropName: 'checked',
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Switch
                      checkedChildren="是"
                      unCheckedChildren="否"
                    />
                  ) : (
                    <span>
                      {
                        formData?.isFormPage ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            getFieldValue('isFormPage') && {
              label: '表单页面code',
              render: () => {
                return (
                  <React.Fragment>
                    <span>{formData?.formPageCode || '-'}</span>
                    <a className="zcy-mg-l-12"
                      target="_blank"
                      href={linkUrl?.wuxiangPageCodeConfiguration || ''}
                      onClick={() => {
                        if (formData?.isFormPage) {
                          showAccountModal();
                        }
                      }}
                    >{getConfigBtnText(pageType)}
                    </a>
                  </React.Fragment>
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
            {
              label: '正文开放自定义编辑',
              render: () => {
                return getFieldDecorator('secondEdit', {
                  initialValue: !!formData?.secondEdit,
                })(
                  (pageType === 'edit' && canEdit) ? (
                    <Group>
                      <Radio value>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Group>
                  ) : (
                    <span>
                      {
                        formData?.secondEdit ? '是' : '否'
                      }
                    </span>
                  )
                );
              },
              colSpan: 2,
              ...formItemLayout,
            },
          ].filter(Boolean)}
        />
      </Sub>
    </React.Fragment>
  );
};

export default ContentSubject;
