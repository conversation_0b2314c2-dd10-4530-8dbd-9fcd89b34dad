import React from 'react';
import { Panel, FormGrid } from 'doraemon';
import { formItemLayout } from './config';

const { Sub } = Panel;

const formGridItem = [{
  label: '采购监管-公告中心',
  render: () => {
    return '产品（@林风）';
  }, 
  colSpan: 2,
  ...formItemLayout,
}];

const ConfigMain:React.FC = () => {
  const desc = `
  1、XXXXXXXXXXXXXXX；
  2、XXXXXXXXXXXXXXXXXXX；
  3、XXXXXXXXXXXXXXXXXXXXXXXXXXXXX。
`;

  return (
    <div className="config-wrapper">
      <Panel title="配置要点" >
        <Sub
          title="配置负责人"
        >
          <FormGrid
            className="config-grid"
            formGridItem={formGridItem}
          />
        </Sub>
        <Sub
          title="配置要点说明"
        >
          <div className="ml-40" >
            <span className="config-desc_line">
              {desc.trim()}
            </span>
          </div>
        </Sub>
      </Panel>
    </div>
  ); 
};


export default ConfigMain;
