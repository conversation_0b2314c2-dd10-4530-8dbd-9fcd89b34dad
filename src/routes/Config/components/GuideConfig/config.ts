export const formItemLayout = {
  labelCol: {
    xs: {
      span: 6,
    },
    sm: {
      span: 6,
    },
  },
};

export const termTypeOptions = [
  { label: '工作日', value: 0 },
  { label: '自然日', value: 1 },
];

/**
 * 公告定义
*/
const AnnouncementDefinition = [];

/**
 * 业务字段定义
*/
const BusinessFieldDefinition = [];

/**
 * 公告标题配置
*/
const AnnouncementTitleConfiguration = [];

/**
 * 内容主体
*/
const ContentSubject = [];

/**
 * 内容监管
*/
const ContentSupervision = [];

/**
 * 流程定义
*/
const ProcessDefinition = [];

/**
 * 流程套用
*/
const ProcessApplication = [];

/**
 * 公告选项
*/
const AnnouncementOptions = [];

/**
 * 公告推送
*/
const AnnouncementPush = [];

/**
 * 公告展现
*/
const AnnouncementDisplay = [];

/**
 * 公告入口
*/
const AnnouncementEntrance = [];

export const guideNode = {
  AnnouncementDefinition,
  BusinessFieldDefinition,
  AnnouncementTitleConfiguration,
  ContentSubject,
  ContentSupervision,
  ProcessDefinition,
  ProcessApplication,
  AnnouncementOptions,
  AnnouncementPush,
  AnnouncementDisplay,
  AnnouncementEntrance,
};
