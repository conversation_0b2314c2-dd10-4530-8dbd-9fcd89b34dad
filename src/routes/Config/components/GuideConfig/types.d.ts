import type {
  AnnouncementDefinitionGetResponse, 
  AnnouncementTitleConfigurationGetResponse,
  BusinessFieldDefinitionGetResponse,
  ContentSubjectGetResponse,
  ContentSupervisionGetResponse,
  ProcessDefinitionGetResponse,
  ProcessApplicationGetResponse,
  AnnouncementOptionsGetResponse,
  AnnouncementPushGetResponse,
  AnnouncementDisplayGetResponse,
  AnnouncementEntranceGetResponse, 
} from 'src/api/announcement/api/config/guide';


export type TFlowNodeCode = 'AnnouncementDefinition'
  | 'BusinessFieldDefinition'
  | 'AnnouncementTitleConfiguration'
  | 'ContentSubject'
  | 'ContentSupervision'
  | 'ProcessDefinition'
  | 'ProcessApplication'
  | 'AnnouncementOptions'
  | 'AnnouncementPush'
  | 'AnnouncementDisplay'
  | 'AnnouncementEntrance'


export type TPageType = 'add' | 'edit' | 'detail'

export interface IFormGridProps {
  pageType: TPageType, 
  formData: TFormData
  districtCode: string
}


export type TFormData = AnnouncementDefinitionGetResponse['result'] 
& AnnouncementTitleConfigurationGetResponse['result']
& BusinessFieldDefinitionGetResponse['result']
& ContentSubjectGetResponse['result']
& ContentSupervisionGetResponse['result']
& ProcessDefinitionGetResponse['result']
& ProcessApplicationGetResponse['result']
& AnnouncementOptionsGetResponse['result']
& AnnouncementPushGetResponse['result']
& AnnouncementDisplayGetResponse['result']
& AnnouncementEntranceGetResponse['result']
