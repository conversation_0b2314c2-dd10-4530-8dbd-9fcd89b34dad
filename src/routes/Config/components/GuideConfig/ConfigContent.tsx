import React from 'react';
import { Panel, Form } from 'doraemon';
import { PureFFC } from 'src/types/component';
import { TFlowNodeCode, TPageType, TFormData } from './types';

import AnnouncementDefinition from './formGrid/AnnouncementDefinition';
import BusinessFieldDefinition from './formGrid/BusinessFieldDefinition';
import ContentSubject from './formGrid/ContentSubject';
import AnnouncementTitleConfiguration from './formGrid/AnnouncementTitleConfiguration';
import ContentSupervision from './formGrid/ContentSupervision';
import ProcessDefinition from './formGrid/ProcessDefinition';
import ProcessApplication from './formGrid/ProcessApplication';
import AnnouncementOptions from './formGrid/AnnouncementOptions';
import AnnouncementPush from './formGrid/AnnouncementPush';
import AnnouncementDisplay from './formGrid/AnnouncementDisplay';
import AnnouncementEntrance from './formGrid/AnnouncementEntrance';

const ConfigContent: PureFFC<{
  cNodeCode: TFlowNodeCode
  pageType: TPageType
  formData: TFormData
  districtCode: string
}> = ({
  cNodeCode,
  pageType,
  formData,
  districtCode,
  form,
}) => {
  const renderContent = () => {
    const props = {
      formData,
      form,
      pageType,
      districtCode,
    };
    switch (cNodeCode) {
      case 'AnnouncementDefinition':
        return <AnnouncementDefinition {...props} />;
      case 'BusinessFieldDefinition':
        return <BusinessFieldDefinition {...props} />;
      case 'AnnouncementTitleConfiguration':
        return <AnnouncementTitleConfiguration {...props} />;
      case 'ContentSubject':
        return <ContentSubject {...props} />;
      case 'ContentSupervision':
        return <ContentSupervision {...props} />;
      case 'ProcessDefinition':
        return <ProcessDefinition {...props} />;
      case 'ProcessApplication':
        return <ProcessApplication {...props} />;
      case 'AnnouncementOptions':
        return <AnnouncementOptions {...props} />;
      case 'AnnouncementPush':
        return <AnnouncementPush {...props} />;
      case 'AnnouncementDisplay':
        return <AnnouncementDisplay {...props} />;
      case 'AnnouncementEntrance':
        return <AnnouncementEntrance {...props} />;
      default:
        return null;
    }
  };

  return (
    <Form>
      <div className="config-wrapper">
        <Panel title="配置内容" >
          {renderContent()}
        </Panel>
      </div>
    </Form>
  );
};

export default Form.create()(ConfigContent);
