import React, { useState, useEffect } from 'react';
import { Drawer, ZcyList, Tag, Input, Alert, message, Spin } from 'doraemon';
import { fixNull } from 'src/utils/utils';
import useDrawer from 'src/hooks/useDrawer';
import { pageSiteInfoGet, PageSiteInfoGetResponse } from 'src/api/announcement/config/pageSiteInfo';
import { relationSitePost } from 'src/api/announcement/config/relationSite';
import './index.less';
import { PageType } from '../../constants';


type TList = Required<PageSiteInfoGetResponse>['result']['data']

const pageSizeOptions = ['10', '20', '50', '100'];

const AnnSiteLinkDrawer = ({
  visible,
  onCancel,
  onSubmit,
  districtCode,
  selectedList,
  pageType = PageType.add,
}) => {
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [list, setList] = useState<TList>([]);
  const [selectRows, setSelectRows] = useState<TList>([]);

  useEffect(() => {
    if (visible) {
      init();
      getList();
    }
  }, [visible]);

  const init = () => {
    setTotal(0);
    setList([]);
    setSelectRows([]);
  };
  
  const onSave = () => {
    if (!selectRows!.length) {
      return onCancel();
    }
    setLoading(true);
    return relationSitePost({
      announcementSiteIds: selectRows!.map(ele => ele.announcementSiteId!),
      districtCode,
    }).then(res => {
      if (!res.success) return;
      message.success('关联成功')!;
      onCancel();
      onSubmit();
    }).finally(() => {
      setLoading(false);
    });
  };

  const TitleNode = () => {
    const title = '关联站点';
    return (
      <div className="drawer-titleNode-wrapper">
        <span>{title}</span>
      </div>
    );
  };

  const del = (id) => {
    const index = selectRows!.findIndex(ele => ele.announcementSiteId === id);
    selectRows!.splice(index, 1);
    setSelectRows([...selectRows!]);
  };

  const onSelectChange = (selectKeys, selectedRows = []) => {
    const tempRows = [...selectRows!, ...selectedRows];
    setSelectRows(selectKeys.map(id => tempRows.find(ele => ele.announcementSiteId === id)));
  };

  const getList = (params:any = {}) => {
    const {
      siteName = '',
      pageNo = 1,
      pageSize = pageSizeOptions[0],
    } = params;
    setLoading(true);
    return pageSiteInfoGet({
      siteName,
      pageNo,
      pageSize,
      districtCode,
    }).then((res) => {
      if (!res.success) return;
      setTotal(res.result!.total!);
      setList(res.result!.data);
    }).finally(() => {
      setLoading(false);
    });
  };

  const getTableColumn = () => {
    return [
      {
        title: '公告站点名称',
        dataIndex: 'siteName',
        render: (text) => fixNull(text),
      }, {
        title: '网站域名',
        dataIndex: 'siteDomain',
        render: (text) => fixNull(text),
      },
    ];
  };

  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="annSiteLink-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      {
        selectedList?.length ? (
          <Alert
            style={{ marginTop: 8 }}
            message={`已关联站点：${selectedList.map(ele => ele.siteName).join('、')}`}
            type="firstinfo"
            showIcon
            iconType="exclamation-circle-o"
          />
        ) : null
      }
      <Spin spinning={loading}>
        <ZcyList
          customItem={[
            {
              label: '公告站点名称',
              id: 'siteName',
              render: () => <Input placeholder="请输入" />,
            },
          ]}
          preTabelContent={(
            <div style={{
              marginTop: -24,
              paddingBottom: 12,
            }}
            >
              {selectRows!.map(item => (
                <Tag
                  closable
                  color="orange"
                  key={item.announcementSiteId}
                  onClose={() => del(item.announcementSiteId)}
                  style={{ marginBottom: 4 }}
                >
                  {item.siteName}
                </Tag>
              ))}
            </div>
          )}
          table={{
            rowKey: 'announcementSiteId',
            dataSource: list,
            columns: getTableColumn(),
            pagination: {
              showQuickJumper: {
                goButton: true,
              },
              showSizeChanger: true,
              total,
              pageSizeOptions,
            },
            rowSelection: {
              selectedRowKeys: selectRows!.map(ele => ele.announcementSiteId),
              onChange: onSelectChange,
            },
          }}
          onSearch={getList}
        />
      </Spin>

    </Drawer>
  );
};


const useAnnSiteLinkDrawer = (props) => {
  return useDrawer(AnnSiteLinkDrawer, props);
};

export { useAnnSiteLinkDrawer };

export default AnnSiteLinkDrawer;
