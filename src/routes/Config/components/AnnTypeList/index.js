import React, { Component } from 'react';
import TypeItem from './TypeItem';
import './index.less';

class AnnouncementTypesList extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  render() {
    const { annTypesData, isEdit, selectAnnTypeId, onChangeTag } = this.props;
    const selectAnnTypeIdArr = selectAnnTypeId ? selectAnnTypeId.split(',') : [];
    return (
      <div className="type-list">
        {
          annTypesData && annTypesData.map((item, index) => {
            return (
              <div className="type-item" key={index}>
                <span>{item.name} : </span>
                <div className="type-item-content">
                  {
                    item.children && item.children.map((i) => {
                      return (
                        <TypeItem
                          key={i.typeId}
                          data={i}
                          isEdit={isEdit}
                          isMore={isEdit ? selectAnnTypeIdArr.indexOf(`${i.typeId}`) : null}
                          onChangeTag={onChangeTag}
                        />
                      );
                    })
                  }
                </div>
              </div>
            );
          })
        }
      </div>
    );
  }
}
export default AnnouncementTypesList;
