import React, { Component } from 'react';
import './index.less';

/**
 * @params isMore          是否为以选择标记
 * @params data            渲染数据
 * @params isEdit          编辑or预览
 * @params onChangeTag     选择回调
 */

class TypeItem extends Component {
  onChangeTag = (id, name) => {
    const { onChangeTag, isMore } = this.props;
    onChangeTag(id, name, isMore !== -1);
  }

  render() {
    const { data, isEdit, isMore } = this.props;
    return (
      <div
        className={isEdit ? (isMore !== -1 ? 'change-item addTag' : 'change-item') : 'change-item display'}
        // eslint-disable-next-line react/jsx-no-bind
        onClick={isEdit ? this.onChangeTag.bind(this, data.typeId, data.name, isMore !== -1) : null}
      >
        {data.name}
      </div>
    );
  }
}
export default TypeItem;
