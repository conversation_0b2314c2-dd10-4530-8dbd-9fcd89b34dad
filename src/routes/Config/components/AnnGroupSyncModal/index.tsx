import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { Modal, Form, TreeSelect, message } from 'doraemon';
import { announcementGroupSynPost } from 'src/api/announcement/api/config/group';
import { formatTreeData } from 'src/utils/tree';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';

const { Item } = Form;
const SHOW_CHILD = TreeSelect.SHOW_CHILD;


export const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const AnnGroupSyncModal = ({
  form,
  districtName,
  districtCode,
  onCancel,
  onSubmit,
  ...rest
}) => {
  const { getFieldDecorator, validateFields } = form;
  const [treeSelectData, setTreeSelectData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getAuthorityDistTreeGet().then(res => {
      if (!res.success) return;
      setTreeSelectData(formatTreeData(res.result));
    });
  }, []);

  const onOk = () => {
    validateFields((err, { targetDistrictCode }) => {
      if (err) return;
      Modal.confirm({
        title: '公告分组同步',
        content: '选择覆盖目标区划数据，将覆盖原区划下的配置，复制成功后将不可恢复，请确认是否继续。注意！执行同步时，目标区划未关联的公告类型将自动跳过',
        onOk: () => {
          setLoading(true);
          return announcementGroupSynPost({
            targetDistrictCode,
            districtCode,
          }).then((res) => {
            if (!res.success) return;
            message.success('同步成功', () => {
              onCancel();
              onSubmit();
            });
          }).finally(() => {
            setLoading(false);
          });
        },
      });
    });
  };

  return (
    <Modal
      {...rest}
      visible
      onOk={onOk}
      onCancel={onCancel}
      okButtonProps={
        { loading }
      }
    >
      <Form>
        <Item
          {...formItemLayout}
          label="当前区划"
          style={{ marginTop: 19 }}
        >
          {districtName}
        </Item>
        <Item
          {...formItemLayout}
          label="目标区划"
          required
        >
          {
            getFieldDecorator('targetDistrictCode', {
              rules: [{
                required: true, message: '请选择目标区划',
              }],
            })(
              <TreeSelect
                allowClear
                style={{ width: 300 }}
                treeData={treeSelectData}
                getPopupContainer={triggerNode => triggerNode.parentNode}
                treeCheckable
                showCheckedStrategy={SHOW_CHILD}
                searchPlaceholder="请选择目标区划"
                treeNodeFilterProp="label"
              />
            )
          }
        </Item>
      </Form>
    </Modal>
  );
};

const useAnnGroupSyncModal = (args) => {
  const DIV = document.createElement('div');
  const openModal = (props) => {
    setTimeout(() => {
      ReactDOM.render(
        <AnnGroupSyncModalByForm {...args} {...props} onCancel={closeModal} />,
        DIV,
      );
    }, 0);
  };

  const closeModal = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(DIV);
    if (unmountResult && DIV?.parentNode) {
      DIV.parentNode.removeChild(DIV);
    }
  };
  return [openModal, closeModal];
};
const AnnGroupSyncModalByForm = Form.create()(AnnGroupSyncModal);

export default AnnGroupSyncModalByForm;
export { useAnnGroupSyncModal };

