import type { SelectGuideLogPostResponse } from 'src/api/announcement/api/config/guide';
import type { ListAnnTypesListGetResponse } from 'src/api/announcement/config';

export type TLogsList = Required<SelectGuideLogPostResponse>['result']['data']
export type TAnnTypeList = Required<ListAnnTypesListGetResponse>['result']['data']
export type TLogType = 'list' | 'node'
export interface TLogDrawerProps {
  onCancel: ()=>{}
  visible: boolean
  logType: TLogType
  districtCode?: string
  bizId?: number
  hasSubmoduleName?: boolean
}
