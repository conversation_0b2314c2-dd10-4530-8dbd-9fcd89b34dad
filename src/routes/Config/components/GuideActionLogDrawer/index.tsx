
import React, { useEffect, useState } from 'react';
import { ZcyList, Drawer, Input, Spin, Select, Tooltip } from 'doraemon';
import moment from 'moment';
import useDrawer from 'src/hooks/useDrawer';
import { selectGuideLogPost } from 'src/api/announcement/api/config/guide';
import { listAnnTypesListGet } from 'src/api/announcement/config';
import { fixNull } from 'src/utils/utils';
import type { TLogsList, TAnnTypeList, TLogDrawerProps } from './type';

const { Option } = Select;

const OperationType = [
  { name: '全部', code: 'all' },
  { name: '新增', code: 'create' },
  { name: '编辑', code: 'edit' },
  { name: '删除', code: 'delete' },
  { name: '初始化', code: 'init' },
  { name: '发布', code: 'synchronous' },
];

const initSearchParams = {
  pageNo: 1,
  pageSize: 10,
  operationType: OperationType[0].code,
};


const GuideActionLogDrawer: React.FC<TLogDrawerProps> = ({
  visible, onCancel, districtCode, logType, bizId, hasSubmoduleName,
}) => {
  const [loading, setLoading] = useState(false);
  const [annTypeList, setAnnTypeList] = useState<TAnnTypeList>([]);
  const [listData, setListData] = useState<{
    data: TLogsList,
    total: number
  }>({
    data: [],
    total: 0,
  });

  const getCustomItem = () => {
    if (logType === 'node') {
      return [
        {
          label: '操作人',
          id: 'operatorName',
          render: () => {
            return <Input placeholder="请输入" />;
          },
        },
      ];
    }
    return [
      {
        label: '操作类型',
        id: 'operationType',
        decoratorOptions: {
          initialValue: OperationType[0].code,
        },
        render: () => {
          return (
            <Select getPopupContainer={e => e?.parentNode}>
              {OperationType.map(ele =>
                <Option key={ele.code} value={ele.code}>{ele.name}</Option>)}
            </Select>
          );
        },
      },
      {
        label: '操作人',
        id: 'operatorName',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '公告类型',
        id: 'announcementType',
        render: () => {
          return (
            <Select
              showSearch
              allowClear
              optionFilterProp="name"
              getPopupContainer={e => e?.parentNode}
            >
              {
                annTypeList!.map(ele => (
                  <Option key={ele.typeCode} name={ele.typeName} value={ele.typeCode}>
                    {ele.typeName}
                  </Option>
                ))
              }
            </Select>
          );
        },
      },
    ];
  };


  const getTableColumn = () => {
    return [
      {
        title: '操作类型',
        dataIndex: 'operationTypeName',
        width: 90,
        render: (text) => fixNull(text),
      },
      hasSubmoduleName && {
        title: '节点名称',
        dataIndex: 'submoduleName',
        width: 120,
        render: (text) => fixNull(text),
      },
      {
        title: '操作人',
        dataIndex: 'operatorName',
        width: 170,
        render: (text) => fixNull(text),
      },
      {
        title: '操作时间',
        dataIndex: 'addTime',
        width: 170,
        render: (val) => {
          if (!val) return '-';
          return moment(val).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '日志信息',
        dataIndex: 'message',
        render: (text) => {
          if (!text) return '-';
          return (
            <Tooltip title={text} getPopupContainer={n => n}>
              <span style={{ whiteSpace: 'pre-line' }} className="zcy-line-2">{text}</span>
            </Tooltip>
          );
        },
      },
    ].filter(Boolean);
  };

  const getLogsData = (params) => {
    return selectGuideLogPost({
      ...params,
      districtCode,
      bizId,
    }).then((res) => {
      setListData({
        data: (res.result?.data ?? []),
        total: res.result?.total ?? 0,
      });
      setLoading(false);
    });
  };

  const onSearch = (params) => {
    getLogsData({ ...initSearchParams, ...params });
  };

  const fetchAnnTypeList = (params) => {
    listAnnTypesListGet(params).then(res => {
      if (!res.success) return;
      setAnnTypeList(res.result?.data ?? []);
    });
  };

  useEffect(() => {
    if (visible) {
      getLogsData({ ...initSearchParams });
      fetchAnnTypeList({
        pageNo: 1,
        pageSize: 999,
      });
    }
  }, [visible]);


  return (
    <Drawer
      title="查看公告接入管理操作日志"
      visible={visible}
      width={1100}
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <ZcyList
          customItem={getCustomItem()}
          onSearch={onSearch}
          table={{
            rowKey: 'id',
            dataSource: listData.data,
            columns: getTableColumn(),
            pagination: {
              total: listData.total,
              showSizeChanger: true,
              defaultCurrent: initSearchParams.pageNo,
              defaultPageSize: initSearchParams.pageSize,
            },
          }}
        />
      </Spin>
    </Drawer>
  );
};

const useGuideActionLogDrawer = (props) => {
  return useDrawer(GuideActionLogDrawer, props);
};

export { useGuideActionLogDrawer };
export default GuideActionLogDrawer;
