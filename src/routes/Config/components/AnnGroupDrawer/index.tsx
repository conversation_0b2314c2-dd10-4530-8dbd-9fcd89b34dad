import React, { useEffect, useState } from 'react';
import { Drawer, Input, Form, FormGrid, Panel, message, Spin, Select } from 'doraemon';
import { selectAnnouncementTypeGet, createAnnouncementGroupPost, updateAnnouncementGroupPost, SelectAnnouncementTypeGetResponse } from 'src/api/announcement/api/config/group';
import useDrawer from 'src/hooks/useDrawer';
import { PageType } from '../../constants';

const Option = Select.Option;

const AnnGroupDrawer = ({
  form,
  visible,
  onCancel,
  onSubmit,
  districtCode,
  curInfo,
  pageType = PageType.add,
}) => {
  const [loading, setLoading] = useState(false);
  const [info, setInfo] = useState({
    groupName: '',
    announcementTypes: [],
  });
  const [typeOptions, setTypeOptions] = useState<SelectAnnouncementTypeGetResponse['result']>([]);

  const { validateFields, getFieldDecorator } = form;

  useEffect(() => {
    setLoading(true);

    selectAnnouncementTypeGet({ districtCode, id: curInfo?.id }).then(res => {
      if (!res.success) return;
      setTypeOptions(res.result);
    }).finally(() => {
      setLoading(false);
    });
  }, [districtCode, curInfo]);


  useEffect(() => {
    setInfo({
      groupName: '',
      announcementTypes: [],
    });
    if (visible) {
      if (pageType !== PageType.add && curInfo) {
        setInfo({ ...curInfo });
      }
    }
  }, [visible, pageType, curInfo]);


  const onSave = () => {
    validateFields((err, { groupName, announcementTypes }) => {
      if (err) return;
      setLoading(true);
      if (pageType === PageType.add) {
        return createAnnouncementGroupPost({
          groupName,
          announcementTypes,
          districtCode,
        }).then((res) => {
          if (!res.success) return;
          message.success('新增成功', () => {
            onCancel();
            onSubmit();
          });
        }).finally(() => {
          setLoading(false);
        });
      }
      if (pageType === PageType.edit) {
        return updateAnnouncementGroupPost({
          groupName,
          announcementTypes,
          id: curInfo.id!,
          districtCode,
        }).then((res) => {
          if (!res.success) return;
          message.success('更新成功', () => {
            onCancel();
            onSubmit();
          });
        }).finally(() => {
          setLoading(false);
        });
      }
      setLoading(false);
    });
  };

  const TitleNode = () => {
    if (pageType === PageType.detail) return <>查看公告分组</>;
    const title = pageType === PageType.add ? '新增公告分组' : '编辑公告分组';
    return (
      <div className="drawer-titleNode-wrapper">
        <span>{title}</span>
      </div>
    );
  };

  const formGridItem = [
    {
      label: '公告分组名称',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('groupName', {
          initialValue: info.groupName || undefined,
          rules: [{
            required: true,
            message: '请输入',
          }],
        })(
          <Input placeholder="请输入" maxLength={20} />
        );
      },
    }, {
      label: '包含公告类型',
      colSpan: 2,
      render: () => {
        return getFieldDecorator('announcementTypes', {
          initialValue: info.announcementTypes || undefined,
          rules: [{
            required: true,
            message: '请选择',
          }],
        })(
          <Select
            mode="multiple"
            optionFilterProp="label"
            getPopupContainer={() => document.querySelector('.annGroup-drawer')}
          >
            {typeOptions!.map(ele =>
              (<Option key={ele.typeId} label={ele.name} value={ele.typeId}>{ele.name}</Option>)
            )}
          </Select>
        );
      },
    }];

  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={onSave}
      width={640}
      className="annGroup-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <Form>
          <Panel.Sub bordered title="基本信息" >
            <FormGrid bordered formGridItem={formGridItem} />
          </Panel.Sub>
        </Form>
      </Spin>
    </Drawer>
  );
};


const AnnGroupDrawerByForm = Form.create()(AnnGroupDrawer);

const useAnnGroupDrawer = (props) => {
  return useDrawer(AnnGroupDrawerByForm, props);
};

export { useAnnGroupDrawer };
export default AnnGroupDrawerByForm;

