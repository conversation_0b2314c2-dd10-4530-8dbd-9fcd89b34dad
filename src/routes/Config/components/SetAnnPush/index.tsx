/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-05-15 17:43:22
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-06-02 14:19:32
 * @FilePath: /zcy-announcement-v2-front/src/routes/Config/components/SetAnnPush/index.tsx
 * @Description:
 */
import React from 'react';
import {
  Modal,
  // Alert,
  Button,
  Table,
  Switch,
} from 'doraemon';
import { ListAnnouncementTypesInfoGetResponse } from 'src/api/announcement/config';
import './index.less';

interface SetAnnPushProps {
  annPushList: ListAnnouncementTypesInfoGetResponse['result'];
  pushVisible: boolean;
  pushSwitchChange: (value: boolean, index: number) => void;
  submitPushModal: () => void;
  closePushModal: () => void;
  annPushLoading: boolean;
}

// const pointOut = () => {
//   return (
//     <>
//       注意：此处仅控制公告推送是否开启，如启用后“公告展现栏目”显示{' '}
//       <span className="no-config">&lt;未配置&gt;</span>{' '}
//       则公告尚未展现，请继续配置当前站点接收公告后需展现的栏目位置:
//       <Button size="small" type="link">
//         点击前往配置
//       </Button>
//     </>
//   );
// };

export const SetAnnPush = (props: SetAnnPushProps) => {
  const {
    pushVisible,
    submitPushModal,
    closePushModal,
    pushSwitchChange,
    annPushLoading,
    annPushList,
  } = props;

  const columns = [
    {
      title: '序号',
      width: 70,
      key: 'index',
      render: (_, row, index) => {
        return index + 1;
      },
    },
    {
      title: '公告类型分组',
      width: 140,
      dataIndex: 'announcementTypeGroup',
      key: 'announcementTypeGroup',
    },
    {
      title: '公告类型',
      width: 160,
      dataIndex: 'announcementTypeName',
      key: 'announcementTypeName',
    },
    {
      title: '公告类型Code',
      width: 70,
      dataIndex: 'announcementTypeCode',
      key: 'announcementTypeCode',
    },
    {
      title: '开启推送',
      width: 100,
      render: (_, { isRelation }, index) => {
        return (
          <Switch
            checked={isRelation}
            onChange={(value) =>
              pushSwitchChange(value, index)
            }
          />
        );
      },
    },
  ];

  return (
    <>
      <Modal
        width="60%"
        bodyStyle={{
          minHeight: '500px',
        }}
        title="设置公告推送"
        visible={pushVisible}
        onCancel={closePushModal}
        footer={[
          <Button
            key="submit"
            type="primary"
            loading={annPushLoading}
            onClick={submitPushModal}
          >
            确认
          </Button>,
        ]}
      >
        {/* <Alert
          className="push-alert"
          message={pointOut()}
          type="firstinfo"
          closable
          showIcon
          iconType="exclamation-circle-o"
        /> */}
        <Table
          pagination={false}
          className="push-table"
          columns={columns}
          dataSource={annPushList}
          scroll={{ y: 540 }}
        />
      </Modal>
    </>
  );
};
