import React, { useEffect, useState } from 'react';
import ReactD<PERSON> from 'react-dom';
import { Modal, ZcyList, Input, message, Tabs, Form, Tooltip } from 'doraemon';
import { pageAnnDataGet, PageAnnDataGetResponse, previewAnnouncementContentByIdGet, 
  previewAnnouncementTitleByIdGet, previewAnnouncementTitleByMetaDataPost, previewAnnouncementContentByMetaDataPost } from 'src/api/announcement/api/template';
import moment from 'moment';
import { useAnnouncementContentPreviewDrawer } from 'src/components/AnnouncementContentBlock';
import './index.less';
import { ACTIVE_KEY_LIST, ANN_PREVIEW_KEY } from '../../constants';

type TPageAnnDataGetResponseRes = Required<Required<PageAnnDataGetResponse>['result']>

const FormItem = Form.Item;
const TabPane = Tabs.TabPane;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};


const AnnPreviewModal = ({
  onCancel,
  visible,
  annPreviewKey = ANN_PREVIEW_KEY[0],
  announcementType,
  districtCode,
  ...rest
}) => {
  const [loading, setLoading] = useState(false);
  const [metaDataInputText, setMetaDataInputText] = useState('');
  const [activeKey, setActiveKey] = useState(ACTIVE_KEY_LIST[0]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [list, setList] = useState<TPageAnnDataGetResponseRes['data']>([]);
  const [open] = useAnnouncementContentPreviewDrawer({});
  
  useEffect(() => {
    if (visible) {
      init();
      handleSearch({
        pageNo: 1,
        pageSize: 5,
      }).then(arr => {
        if (arr?.length) {
          setSelectedRows([arr[0]]);
        }
      });
    }
  }, [visible]);

  const init = () => {
    setTotal(0);
    setList([]);
    setMetaDataInputText('');
    setSelectedRows([]);
    setActiveKey(ACTIVE_KEY_LIST[0]);
  };

  const handleSearch = ({
    announcementId = '',
    title = '',
    pageNo = 1,
    pageSize = 5,
  } = {}) => {
    setLoading(true);
    return pageAnnDataGet({
      announcementId,
      title,
      pageNo: pageNo + '',
      pageSize: pageSize + '',
      announcementType,
      districtCode, 
    }).then((res) => {
      if (!res.success) return;
      setTotal(res?.result?.total ?? 0);
      setList(res?.result?.data ?? []);
      return res?.result?.data ?? [];
    }).finally(() => {
      setLoading(false);
    });
  };

  const onSelectChange = (_, selectRows = []) => {
    setSelectedRows([...selectRows]);
  };

  const goDetail = ({ formPageCode, id, district, annBigType }) => {
    let url = '';
    if (formPageCode) {
      url = `/announcement-front/#/dynamic/detail?formPageCode=${formPageCode}&annId=${id}&districtId=${district}&annBigType=${annBigType}`;
    } else {
      url = `/announcement-front/#/detail/${annBigType}/${id}`;
    }

    window.open(url);
  };

  const goMetadata = (metaData = '') => {
    Modal.info({
      width: 980,
      title: 'metaData数据',
      content: metaData,
    });
  };

  const getTableColumn = () => {
    return [
      {
        title: '公告ID',
        dataIndex: 'announcementId',
        width: 170,
        textAlign: 'center',
      },
      {
        title: '公告标题',
        dataIndex: 'title',
        render: (text) => {
          const title = text || '';
          return (
            <Tooltip getPopupContainer={n => n} title={title} placement="topLeft" >
              <span className="short-for-ellipsis">{title}</span>
            </Tooltip>
          );
        },
      }, {
        title: '发布时间',
        dataIndex: 'releasedAt',
        width: 170,
        render: (text) => {
          if (!text) return '-';
          return moment(text).format('YYYY-MM-DD HH:mm:ss');
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 190,
        render: (_, { announcementId, metaData,
          formPageCode, district, annBigType,
        }) => {
          return (
            <React.Fragment>
              <a
                onClick={() => goDetail({
                  formPageCode, id: announcementId, district, annBigType,
                })}
                className="zcy-mg-r-8"
              >查看详情
              </a>
              <a onClick={() => goMetadata(metaData)} >查看metadata</a>
            </React.Fragment>
          );
        },
      },

    ];
  };

  const handleMetadataPreview = (metaData = '') => {
    setLoading(true);
    // template
    if (annPreviewKey === ANN_PREVIEW_KEY[0]) {
      return previewAnnouncementContentByMetaDataPost({
        metaData: metaData.trim(),
        announcementType,
        districtCode,   
      }).then(res => {
        if (!res.success) return;
        return open({
          annContent: res.result?.content,
          annTitle: res.result?.title,
        });
      }).finally(() => {
        setLoading(false);
      });
    }
    // title
    if (annPreviewKey === ANN_PREVIEW_KEY[1]) {
      setLoading(true);
      return previewAnnouncementTitleByMetaDataPost({
        metaData: metaData.trim(),
        announcementType,
        districtCode,
      }).then(res => {
        if (!res.success) return;
        return open({
          annTitle: res.result,
        });
      }).finally(() => {
        setLoading(false);
      });
    }
    setLoading(false);
  };

  const onOk = () => {
    if (activeKey === ACTIVE_KEY_LIST[0]) {
      if (selectedRows.length < 1) {
        return message.error('请选择一条公告数据再进行预览');
      }
      setLoading(true);
      // template
      if (annPreviewKey === ANN_PREVIEW_KEY[0]) {
        return previewAnnouncementContentByIdGet({
          announcementId: selectedRows[0].announcementId,
        }).then(res => {
          if (!res.success) return;
          return open({
            annContent: res.result?.content,
            annTitle: res.result?.title, 
          });
        }).finally(() => {
          setLoading(false);
        });
      }
      // title
      if (annPreviewKey === ANN_PREVIEW_KEY[1]) {
        return previewAnnouncementTitleByIdGet({
          announcementId: selectedRows[0].announcementId,
        }).then(res => {
          if (!res.success) return;
          return open({
            annTitle: res.result, 
          });
        }).finally(() => {
          setLoading(false);
        });
      }
    }
    if (activeKey === ACTIVE_KEY_LIST[1]) {
      if (metaDataInputText.trim() === '') {
        return message.error('请输入metadata数据');
      }
      return handleMetadataPreview(metaDataInputText);
    }
  };

  return (
    <Modal
      title="设置公告预览数据源"
      visible={visible}
      width={980}
      {...rest}
      onOk={onOk}
      onCancel={onCancel}
      okText="预览"
      okButtonProps={
        { loading }
      }
    >
      <Tabs defaultActiveKey={ACTIVE_KEY_LIST[0]} onChange={setActiveKey}>
        <TabPane tab="基于公告样本预览" key={ACTIVE_KEY_LIST[0]}>
          <ZcyList
            customItem={[
              {
                label: '公告ID',
                id: 'announcementId',
                render: () => <Input placeholder="请输入" />,
              },
              {
                label: '公告标题',
                id: 'title',
                render: () => <Input placeholder="请输入" />,
              },
            ]}
            table={{
              rowKey: 'announcementId',
              dataSource: list,
              columns: getTableColumn(),
              pagination: {
                showQuickJumper: {
                  goButton: true,
                },
                showSizeChanger: true,
                total,
                pageSizeOptions: ['5', '10', '20'],
                defaultPageSize: 5,
              },
              rowSelection: {
                type: 'radio',
                selectedRowKeys: selectedRows.map(ele => ele.announcementId),
                onChange: onSelectChange,
              },
            }}
            onSearch={handleSearch}
          />
        </TabPane>
        <TabPane tab="基于metadata预览" key={ACTIVE_KEY_LIST[1]}>
          <FormItem
            {...formItemLayout}
            label="操作说明"
          >
            <div style={{
              fontSize: 14,
              lineHeight: '22px',
              paddingTop: 8,
              marginBottom: 8,
            }}
            >
              <p>1、复制metadata数据粘贴到下方输入框后，点击【确定】即可查看公告预览;</p>
              <p>2、本功能不对json串进行合规性校验，请自行检查metadata数据格式正确性;</p>
              <p>3、适用场景：接入方后端在与公告后端达成一致后，生成metadata数据串;</p>
            </div>
          </FormItem>
          
          <FormItem
            {...formItemLayout}
            label="metadata"
          >
            <Input.TextArea
              style={{ width: 680 }}
              maxLength={999999}
              autosize={{ minRows: 12, maxRows: 24 }}
              onChange={e => {
                setMetaDataInputText(e.target.value);
              }}
              value={metaDataInputText}
            />
          </FormItem>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

const useAnnPreviewModal = (args) => {
  const DIV = document.createElement('div');
  const openModal = (props) => {
    setTimeout(() => {
      ReactDOM.render(
        <AnnPreviewModal {...args} {...props} onCancel={closeModal} />,
        DIV,
      );
    }, 0);
  };

  const closeModal = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(DIV);
    if (unmountResult && DIV?.parentNode) {
      DIV.parentNode.removeChild(DIV);
    }
  };
  return [openModal, closeModal];
};

export default AnnPreviewModal;
export { useAnnPreviewModal };
