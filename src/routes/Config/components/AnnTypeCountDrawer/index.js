import React, { useEffect, useState } from 'react';
import { Drawer, Alert, Table } from 'doraemon';
import { countStartStopSetting } from '../../services/api';
import useDrawer from 'src/hooks/useDrawer';

const localStorageKey = `annTypeCountDrawerAlert__${window.currentUserIdentity.operatorId}`;


const AnnTypeCountDrawer = ({
  visible, 
  onCancel, 
}) => {
  const [tableData, setTableData] = useState([]);
  const [showMsg, setShowMsg] = useState(false);

  const handleAlert = () => {
    const value = localStorage.getItem(localStorageKey);
    if (value !== 'hide') {
      setShowMsg(true);
    } else {
      setShowMsg(false);
    }
  };

  useEffect(() => {
    if (visible) {
      countStartStopSetting().then((res) => {
        setTableData(res?.result ?? []);
      });
      handleAlert();
    }
  }, [visible]);

  const onCancelAlert = () => {
    localStorage.setItem(localStorageKey, 'hide');
  };

  return (
    <Drawer
      title="公告启停统计"
      visible={visible}
      width={640}
      className="annTypeCount-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <React.Fragment>
        {showMsg ? (
          <Alert
            message="此处仅展示公告大类下的启停数据，启停操作请返回列表页，点击编辑"
            type="firstinfo"
            closable
            showIcon
            iconType="exclamation-circle-o"
            className="zcy-mg-b-16"
            onClose={onCancelAlert}
          />
        ) : null }
       
        <Table
          rowKey="typeCode"
          dataSource={tableData}
          columns={[
            {
              title: '公告大类名称',
              dataIndex: 'typeName',
            }, {
              title: '启用公告数量(个)',
              dataIndex: 'enableNum',
            }, {
              title: '停用公告数量(个)',
              dataIndex: 'disableNum',
            },
          ]}
          pagination={false}
        />
      </React.Fragment>
    </Drawer>
  );
};

const useAnnTypeCountDrawer = (props) => {
  return useDrawer(AnnTypeCountDrawer, props);
};

export { useAnnTypeCountDrawer };
export default AnnTypeCountDrawer;
