import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Input, Form, TreeSelect, Radio, SelectItem, Checkbox,
  Row, Popover, Icon, Select, Drawer, Panel, Modal,
} from 'doraemon';
import './SetModal.less';
import { PUBLISH_TYPES } from 'src/constants';
import AnnPreviewModal from './AnnPreviewModal';
import { ANN_PREVIEW_KEY } from '../constants';
import { getDistTreePost } from 'src/api/announcement/api/district';
import { formatTreeData } from 'src/utils/tree';

const IS_ALL_CHINESE = /^[\u4e00-\u9fa5]+$/;

const { Item } = Form;
const { Group } = Radio;
const { TextArea } = Input;
const CheckboxGroup = Checkbox.Group;
const Option = Select.Option;
const { Sub } = Panel;

const termTypeOptions = [
  { label: '工作日', value: 0 },
  { label: '自然日', value: 1 },
];

@Form.create()
@connect(({ openConfig }) => ({
  openConfig,
}))
class SetModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      agencyTypes: [],
      annPreviewModalVisible: false,
      annPreviewKey: '',
      treeSelectData: [],
    };
  }

  componentDidMount() {
    this.loadListAgencyTypes();
    this.getDistTree();
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.visible !== this.props.visible && nextProps.visible) {
      this.getAnnTitleTemplate();
    }
  }

  getAnnTitleTemplate = () => {
    const { distCode, typeId } = this.props;
    this.props.dispatch({
      type: 'openConfig/getAnnTitleTemplate',
      payload: {
        districtCode: distCode ?? window?.currentUserDistrict?.code,
        announcementType: typeId,
      },
    });
  }

  getDistTree = () => {
    getDistTreePost().then((res) => {
      if (!res.success) return;
      this.setState({
        treeSelectData: formatTreeData(res.result),
      });
    });
  }

  loadListAgencyTypes = () => {
    this.props.dispatch({
      type: 'openConfig/listAgencyTypes',
    }).then((res) => {
      this.setState({
        agencyTypes: res.result,
      });
    });
  }
  onChange = (e) => {
    this.props.isShowReason(e.target.value);
  }

  onCgfsChange = (value, label) => {
    this.props.cgfsChange({ procurementMethodCode: value.join(','), procurementMethodName: label.join(',') });
  }

  initCgfsCode = (code) => {
    if (code) {
      return code.split(',');
    }
    return [];
  }

  handlePreview = ({ annPreviewKey }) => {
    this.setState({
      annPreviewModalVisible: true,
      annPreviewKey,
    });
  }

  handleTemplateCenterUrl = (url) => {
    if (!url) return;
    const {
      annItemData,
    } = this.props;
    if (annItemData.isFormPage) {
      return Modal.warning({
        title: '该模板位于无相管理机构，请切换账号角色后查看',
        content: '如“表单模板中心”页面报错404或提示无权限，请在右上方切换账号当前角色至【无相管理机构】后，再次刷新页面',
        onOk() {
          window.open(url);
        },
        okText: '进入表单模板中心',
        closable: true,
      });
    }
    return window.open(url);
  }

  get canObject() {
    const { getFieldValue } = this.props.form;
    return getFieldValue('canObject');
  }
  get doValidate() {
    const { getFieldValue } = this.props.form;
    return getFieldValue('doValidate');
  }

  render() {
    const {
      visible,
      onCancel,
      onCreate,
      form,
      showReason,
      annItemData,
      openConfig,
      typeId,
    } = this.props;
    let { distCode } = this.props;
    if (!distCode) {
      distCode = window.currentUserDistrict.code;
    }
    const { agencyTypes, annPreviewModalVisible, annPreviewKey, treeSelectData } = this.state;
    const { getFieldDecorator, getFieldValue } = form;
    const formItemLayout = {
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
    };
    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 0,
        },
        sm: {
          span: 16,
          offset: 10,
        },
      },
    };
    let customizedTitle = getFieldValue('customizedTitle');
    if (customizedTitle === undefined) {
      customizedTitle = !!annItemData.customizedTitle;
    }
    let { checkAgencyTypes } = annItemData;
    if (checkAgencyTypes) {
      checkAgencyTypes = checkAgencyTypes.split(',');
    }

    const { annTitleTemplate = {} } = openConfig;
    const { annTemplateUrl, annTitleTemplateUrl,
      annTemplateName, annTitleTemplateName, annTemplateId, annTitleTemplateId,
      isAnnTemplateExtend,
      annTemplateDistrictName,
      annTemplateDistrictCode,
      isAnnTitleTemplateExtend,
      annTitleTemplateDistrictName,
      annTitleTemplateDistrictCode,
    } = annTitleTemplate;

    return (
      <Drawer
        className="configSetDrawer"
        visible={visible}
        title={`设置:${annItemData.name}`}
        openConfirmBtn
        onOk={onCreate}
        onCancel={onCancel}
        width={600}
        onClose={onCancel}
      >
        <div id="configOpenSetModal">
          <Form>
            <Sub
              title="基本属性"
            >
              <Item label="公告期计算方式：" {...formItemLayout}>
                {getFieldDecorator('termType', {
                  initialValue: annItemData.termType ?? 0,
                })(
                  <Select
                    style={{ width: 220 }}
                    placeholder="请选择"
                    getPopupContainer={el => el.parentNode}
                  >
                    {
                      termTypeOptions.map(({ value, label }) => (
                        <Select.Option key={value} value={value}>
                          {label}
                        </Select.Option>
                      ))
                    }
                  </Select>
                )}
              </Item>
              <Item
                {...formItemLayout}
                label="公告天数"
              >
                {getFieldDecorator('expiryPeriod', {
                  rules: [
                    { pattern: /^((0|[1-9])\d{0,2}|-1)$/, message: '仅支持-1到999整数' },
                  ],
                  initialValue: (annItemData.expiryPeriod === null || typeof (annItemData.expiryPeriod) === 'undefined') ? 999 : annItemData.expiryPeriod,
                })(
                  <Input placeholder="请输入有效的公告日期" />
                )}
                <span>注：-1为无限期</span>

              </Item>
              <Item
                {...formItemLayout}
                label="对应采购方式"
              >
                {getFieldDecorator('procurementMethodCode', {
                  rules: [{ required: true, message: '请选择' }],
                  initialValue: this.initCgfsCode(annItemData.procurementMethodCode),
                })(
                  <TreeSelect
                    style={{ width: 220, outline: 'none' }}
                    treeCheckable
                    dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
                    treeData={openConfig.cgfs}
                    placeholder="请选择"
                    getPopupContainer={triggerNode => triggerNode.parentNode}
                    onChange={this.onCgfsChange}
                  />
                )}
              </Item>

              <Item
                {...formItemLayout}
                label="发布时间方案设置"
              >
                {typeId && distCode && getFieldDecorator('publishTypes', {
                  rules: [
                    { required: true, message: '请选择' },
                  ],
                  initialValue: annItemData.publishTypes ? annItemData.publishTypes.split(',').map(ele => +ele) : undefined,
                })(

                  <Select
                    mode="multiple"
                    optionFilterProp="label"
                    getPopupContainer={node => node}
                  >
                    {PUBLISH_TYPES.map(ele =>
                      <Option label={ele.k} value={+ele.v}>{ele.k}</Option>)}
                  </Select>
                )}
              </Item>

              {
                annItemData.enableSummaryCalculationShow && (
                  <Item
                    {...formItemLayout}
                    label="允许修改预留总金额&占比"
                  >
                    {getFieldDecorator('enableSummaryCalculation', {
                      rules: [{ required: true }],
                      initialValue: !!annItemData.enableSummaryCalculation,
                    })(
                      <Group>
                        <Radio value>允许</Radio>
                        <Radio value={false}>禁止</Radio>
                      </Group>
                    )}
                  </Item>
                )}
              
            </Sub>


            <Sub title="内容控制">
              <Item
                {...formItemLayout}
                label="公告模板名称"
              >
                <React.Fragment>
                  {annTemplateId ? annTemplateName : '-'}
                  <a className="zcy-mg-l-12"
                    onClick={() => this.handleTemplateCenterUrl(annTemplateUrl)}
                  >设置
                  </a>
                  {
                    annTemplateId ? (
                      <React.Fragment>
                        <a className="zcy-mg-l-12"
                          onClick={() => this.handlePreview({
                            annPreviewKey: ANN_PREVIEW_KEY[0],
                          })}
                        >预览
                        </a>
                        {
                          isAnnTemplateExtend ?
                            <p style={{ color: '#bfbfbf' }}>注：继承自{annTemplateDistrictName}（{annTemplateDistrictCode}）</p> :
                            <p style={{ color: '#bfbfbf' }}>注：区划为{annTemplateDistrictName}（{annTemplateDistrictCode}）</p>
                        }
                      </React.Fragment>
                    ) : null
                  }
                </React.Fragment>
              </Item>
              <Item
                {...formItemLayout}
                label="公告标题自定义配置"
              >
                {getFieldDecorator('customizedTitle', {
                  rules: [{ required: true }],
                  initialValue: !!annItemData.customizedTitle,
                })(
                  <Group>
                    <Radio value>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Group>
                )}
              </Item>

              {customizedTitle && (
                <React.Fragment>
                  <Item
                    {...formItemLayout}
                    label="公告标题模板名称"
                  >
                    <React.Fragment>
                      {annTitleTemplateId ? annTitleTemplateName : '-'}
                      <a className="zcy-mg-l-12"
                        href={annTitleTemplateUrl}
                        target="_blank"
                      >设置
                      </a>
                      {
                        annTitleTemplateId ? (
                          <React.Fragment>
                            <a className="zcy-mg-l-12"
                              onClick={() => this.handlePreview({
                                annPreviewKey: ANN_PREVIEW_KEY[1],
                              })}
                            >预览
                            </a>
                            {
                              isAnnTitleTemplateExtend ?
                                (
                                  <p style={{ color: '#bfbfbf' }}>注：继承自{annTitleTemplateDistrictName}
                                    （{annTitleTemplateDistrictCode}）
                                  </p> 
                                ) : (
                                  <p style={{ color: '#bfbfbf' }}>注：区划为{annTitleTemplateDistrictName}
                                    （{annTitleTemplateDistrictCode}）
                                  </p>
                                )
                            }
                          </React.Fragment>
                        ) : null
                      }
                    </React.Fragment>
                  </Item>
                </React.Fragment>
              )}

              <Item
                {...formItemLayout}
                label="正文开放自定义编辑"
              >
                {getFieldDecorator('secondEdit', {
                  rules: [{ required: true }],
                  initialValue: !!annItemData.secondEdit,
                })(
                  <Group>
                    <Radio value>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Group>
                )}
              </Item>
            </Sub>

            <Sub title="附件控制">
              <Item
                {...formItemLayout}
                label="附件是否展示"
              >
                {getFieldDecorator('attachmentShow', {
                  rules: [{ required: true }],
                  initialValue: annItemData.attachmentShow,
                })(
                  <Group>
                    <Radio value={1}>是</Radio>
                    <Radio value={0}>否</Radio>
                  </Group>
                )}
              </Item>
              {
                Number(getFieldValue('attachmentShow')) === 1 && (
                  <Item
                    {...formItemLayout}
                    label="附件是否必填"
                  >
                    {getFieldDecorator('hasAttachment', {
                      rules: [{ required: true }],
                      initialValue: String(annItemData.hasAttachment) || '1',
                    })(
                      <Group>
                        <Radio value="1">是</Radio>
                        <Radio value="0">否</Radio>
                      </Group>
                    )}
                  </Item>
                )
              }

              {
                Number(getFieldValue('attachmentShow')) === 1 && (
                  <Item
                    {...formItemLayout}
                    label={
                      <span>
                        附件对外展示规则
                        <Popover
                          placement="top"
                          getPopupContainer={() => (document.getElementById('configOpenSetModal'))}
                          content={(
                            <div>
                              说明：选择前者即所有附件均会强制公开<br />无法个别附件内部流转，请按需选择                    
                            </div>
                          )}
                        >
                          <Icon type="question-circle-o" />
                        </Popover>
                      </span>
                    }
                  >
                    {getFieldDecorator('attachmentExternalDisplayRules', {
                      rules: [{ required: true }],
                      initialValue: String(annItemData.attachmentExternalDisplayRules) || '1',
                    })(
                      <Group>
                        <Radio value="1">用户按需自行选择</Radio>
                        <Radio value="2">所有附件均发至外网</Radio>
                      </Group>
                    )}
                  </Item>
                )
              }

              {
                Number(getFieldValue('attachmentShow')) === 1 && (
                  <Item
                    label={
                      <span>
                        附件上传提示文案
                        <Popover
                          placement="top"
                          getPopupContainer={() => (document.getElementById('configOpenSetModal'))}
                          content={(
                            <div>
                              说明：该文案将作为附件上传要求提示<br />信息，提供经办人查看，留空时不显示                        
                            </div>
                          )}
                        >
                          <Icon type="question-circle-o" />
                        </Popover>
                      </span>
                    }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('attachmentUploadDesc', {
                      initialValue: annItemData.attachmentUploadDesc,
                    })(
                      <TextArea size="small" maxLength="200" placeholder="请输入" />
                    )}
                  </Item>
                ) 
              }

            </Sub>

            <Sub title="审批控制">
              <Item
                {...formItemLayout}
                label={
                  <span>
                    校验代理机构公告发布
                    <Popover
                      placement="top"
                      getPopupContainer={() => (document.getElementById('configOpenSetModal'))}
                      content={(
                        <div>
                          选择校验区划发布时，校验代理机构在公告发布<br />区划是否被审核，若未被审核通过则不能发布
                        </div>
                      )}
                    >
                      <Icon type="question-circle-o" />
                    </Popover>
                  </span>
                }
              >
                {getFieldDecorator('doValidate', {
                  rules: [{ required: true }],
                  initialValue: Array.isArray(checkAgencyTypes) && checkAgencyTypes.length ? '1' : '0',
                })(
                  <Group>
                    <Radio value="1">校验公告发布区划</Radio>
                    <Radio value="0">不校验</Radio>
                  </Group>
                )}
              </Item>

              {this.doValidate === '1' && (
                <Item
                  {...formItemLayout}
                  label="请选择校验代理机构类别"
                >
                  {getFieldDecorator('checkAgencyTypes', {
                    rules: [{ required: true, message: '请选择校验代理机构类别' }],
                    initialValue: checkAgencyTypes,
                  })(
                    <CheckboxGroup>
                      {agencyTypes.map(item => (
                        <Row key={item.code}>
                          <Checkbox value={item.code}>{item.desc}</Checkbox>
                        </Row>
                      ))}
                    </CheckboxGroup>
                  )}
                </Item>
              )}

              <Item
                {...formItemLayout}
                label="是否开启敏感词校验"
              >
                {getFieldDecorator('canContentCensor', {
                  rules: [{ required: true }],
                  initialValue: annItemData.canContentCensor,
                })(
                  <Group>
                    <Radio value={1}>校验</Radio>
                    <Radio value={0}>不校验</Radio>
                  </Group>
                )}
              </Item>
            </Sub>

            <Sub title="操作控制">
              <Item
                {...formItemLayout}
                label="是否开通异议"
              >
                {getFieldDecorator('canObject', {
                  rules: [{ required: true }],
                  initialValue: !!annItemData.canObject,
                })(
                  <Group>
                    <Radio value>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Group>
                )}
              </Item>

              {this.canObject && (
                <span>
                  <Item
                    {...formItemLayout}
                    label="被质疑对象信息设置"
                  >
                    {typeId && distCode && getFieldDecorator('objectionKeys', {
                      rules: [
                        { required: true, message: '请选择被质疑对象信息' },
                      ],
                      initialValue: annItemData.objectionKeys ? annItemData.objectionKeys.split(',') : undefined,
                    })(
                      <SelectItem
                        mode="multiple"
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                        key={`Type=${typeId}districtCode=${distCode}`}
                        url={`/announcement/api/obtainMetaDataList?announcementType=${typeId}&districtCode=${distCode}`}
                        type="objectionKeys"
                        placeholder="请选择"
                      />
                    )}
                  </Item>
                  <Item
                    {...formItemLayout}
                    label="质疑对象信息设置"
                  >
                    {typeId && distCode && getFieldDecorator('beObjectionKeys', {
                      initialValue: annItemData.beObjectionKeys ? annItemData.beObjectionKeys.split(',') : undefined,
                    })(
                      <SelectItem
                        mode="multiple"
                        getPopupContainer={triggerNode => triggerNode.parentNode}
                        key={`Type=${typeId}districtCode=${distCode}`}
                        url={`/announcement/api/obtainMetaDataList?announcementType=${typeId}&districtCode=${distCode}`}
                        type="objectionKeys"
                        placeholder="请选择"
                      />
                    )}
                  </Item>
                </span>
              )}
              <Item
                {...formItemLayout}
                label="公告发布后是否可撤回"
              >
                {getFieldDecorator('canRevoke', {
                  rules: [{ required: true }],
                  initialValue: String(annItemData.canRevoke) || '1',
                })(
                  <Group>
                    <Radio value="1">是</Radio>
                    <Radio value="0">否</Radio>
                  </Group>
                )}
              </Item>
              {
                String(getFieldValue('canRevoke')) === '1' && (
                  <Item
                    {...formItemLayout}
                    label="公告发布后可撤回时长"
                  >
                    {getFieldDecorator('revokeTime', {
                      rules: [{ required: true }],
                      initialValue: annItemData.revokeTime || 600, // 默认 10 分钟
                    })(
                      <Select getPopupContainer={node => node}>
                        <Option value={600}>10分钟</Option>
                        <Option value={1800}>30分钟</Option>
                        <Option value={7200}>2小时</Option>
                        <Option value={21600}>6小时</Option>
                      </Select>
                    )}
                  </Item>
                )
              }

              {
                annItemData.isRevocableShow && (
                  <Item
                    {...formItemLayout}
                    label="是否允许取消公告"
                  >
                    {getFieldDecorator('isRevocable', {
                      rules: [{ required: true }],
                      initialValue: String(annItemData.isRevocable || '0'),
                    })(
                      <Group>
                        <Radio value="1">是</Radio>
                        <Radio value="0">否</Radio>
                      </Group>
                    )}
                  </Item>
                )
              }

              {
                Number(getFieldValue('isRevocable')) === 1 && (
                  <Item
                    {...formItemLayout}
                    label="[取消公告]按钮名称"
                  >
                    {getFieldDecorator('revocableButtonName', {
                      rules: [
                        { required: true, message: '必填项' },
                        { pattern: IS_ALL_CHINESE, message: '仅允许输入汉字' },
                      ],
                      initialValue: annItemData.revocableButtonName || '取消公告',
                    })(
                      <Input maxLength={10} placeholder="请输入" />
                    )}
                  </Item>
                )
              }

              <Item
                {...formItemLayout}
                label={
                  <span>
                    发布区划白名单设置
                    <Popover
                      placement="top"
                      getPopupContainer={() => (document.getElementById('configOpenSetModal'))}
                      content={(
                        <div>
                          以白名单方式指定手工公告发布的特定区划范围。<br />
                          默认范围：可发布到当前操作人所在的本级及下级区划
                        </div>
                      )}
                    >
                      <Icon type="question-circle-o" />
                    </Popover>
                  </span>
                }
                extra={
                  annItemData.isFormPage ? 
                    <span style={{ width: 220, display: 'inline-block' }}>注意：当前公告类型已接入表单，上述区划依赖后台表单组件中相应参数配置后生效。如未立即生效，请联系产品运营处理！</span> : null}
              >
                {
                  getFieldDecorator('whiteListDistrictCode', {
                    rules: [{
                      validator: (_, value, callback) => {
                        if (value?.length > 999) {
                          callback(new Error('最大发布区划白名单数量为999！'));
                        }
                        callback();
                      },
                    }],
                    initialValue: annItemData.whiteListDistrictList?.map(ele => ele.code)
                     || undefined,
                  })(
                    <TreeSelect
                      allowClear
                      maxTagCount={20}
                      treeData={treeSelectData}
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                      treeCheckable
                      showCheckedStrategy={TreeSelect.SHOW_CHILD}
                      searchPlaceholder="请选择"
                      treeNodeFilterProp="label"
                    />
                  )
                }
              </Item>


            </Sub>

            <Sub title="关联控制">
              <Item
                {...formItemLayout}
                label="手工公告关联采购计划"
              >
                {getFieldDecorator('isForcedControl', {
                  rules: [{ required: true }],
                  initialValue: String(annItemData.isForcedControl) || '1',
                })(
                  <Group>
                    <Radio value="1">强控</Radio>
                    <Radio value="0">不强控</Radio>
                  </Group>
                )}
              </Item>
            </Sub>

            <Sub title="推送控制">
              <Item
                {...formItemLayout}
                label="是否开启短信通知"
              >
                {getFieldDecorator('isNotify', {
                  rules: [{ required: true }],
                  initialValue: !!annItemData.isNotify,
                })(
                  <Group>
                    <Radio value>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Group>
                )}
              </Item>
              <Item
                {...formItemLayout}
                label="是否开放到外网"
              >
                {getFieldDecorator('isOut', {
                  rules: [{ required: true }],
                  initialValue: String(annItemData.isOut) || '1',
                })(
                  <Group onChange={this.onChange}>
                    <Radio value="1">是</Radio>
                    <Radio value="0">否</Radio>
                  </Group>
                )}
              </Item>
            </Sub>


            {
              showReason ? (
                <Item
                  {...tailFormItemLayout}
                >
                  {getFieldDecorator('unopenReason', {
                    rules: [{ required: true, message: '请输入不开放理由!' }],
                    initialValue: annItemData.unOpenReason,
                  })(
                    <TextArea size="small" maxLength="250" placeholder="请输入不开放理由" />
                  )}
                </Item>
              ) : null
            }


          </Form>

          {annPreviewModalVisible ? (
            <AnnPreviewModal 
              visible={annPreviewModalVisible}
              annPreviewKey={annPreviewKey}
              announcementType={typeId}
              districtCode={distCode}
              onCancel={() => this.setState({ annPreviewModalVisible: false })}
            />
          ) : null}
         
        </div>
      </Drawer>
    );
  }
}

export default SetModal;
