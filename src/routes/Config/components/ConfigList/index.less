.config-list {
  overflow: hidden;
  zoom: 1;
  padding: 10px;
  min-height: 400px;
  .config-item {
    position: relative;
    width: 160px;
    height: 160px;
    float: left;
    background-color: #f6f7fa;
    border-radius: 6px;
    box-shadow: 0 2px 0 0 #edeff5;
    margin: 0 20px 20px 0;
    .item-content {
      position: relative;
      padding: 48px 10px 0;
      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        display: block;
        border-bottom-left-radius: 6px;
        width: 0;
        height: 0;
        border-color: #fff #fff #d1d7e8 #d1d7e8;
        border-width: 25px;
        border-style: solid;
      }
      .short-2 {
        font-size: 16px;
        color: #001830;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      >span {
        display: inline-block;
        width: 100%;
        text-align: center;
        margin-top: 10px;
        span {
          font-size: 12px;
          color: #84888e;
        }
      }
    }
    .operate-btn {
      position: absolute;
      width: 100%;
      bottom: 10px;
      text-align: center;
      >span {
        display: inline-block;
        color: #3177fd;
        width: 60px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 6px;
        cursor: pointer;
        &:hover {
          background-color: #edeff5;
        }
      }
    }
  }
}
