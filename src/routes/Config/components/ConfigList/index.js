import React, { Component } from 'react';
import { Tooltip } from 'doraemon';
import './index.less';

class ConfigItem extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  set = (id) => {
    this.props.set(id);
  }

  render() {
    const { listData } = this.props;

    return (
      <div className="config-list">
        {
          listData && listData.map((item) => {
            return (
              <div className="config-item" key={item.id}>
                <div className="item-content">
                  <Tooltip title={item.name} placement="right">
                    <span className="short-2">
                      {item.name}
                    </span>
                  </Tooltip>
                </div>
                <div className="operate-btn">
                  <span onClick={() => this.set(item.id)}>设置</span>
                </div>
              </div>
            );
          })
        }
      </div>
    );
  }
}
export default ConfigItem;
