import React from 'react';
import { Cascader, Form } from 'doraemon';
import './index.less';

export default ({ 
  localKey,
  disabled, 
  treeData = [],
  defaultValue = [], 
  allowClear = false,
  onChangeCode = () => {},
}) => {
  const localDefaultValue = defaultValue;
  if (localKey) {
    const item = localStorage.getItem(`DistrictCascader_${localKey}`);
    if (item) {
      //
    }
  }
  

  return (
    <Form className="header-select-container">
      <Cascader
        allowClear={allowClear}
        options={treeData}
        disabled={disabled}
        defaultValue={localDefaultValue}
        onChange={onChangeCode}
        placeholder="请选择地区"
        getPopupContainer={node => node}
      />
    </Form>
  );
};
