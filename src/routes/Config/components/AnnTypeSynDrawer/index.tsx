import React, { useEffect, useState } from 'react';
import { Drawer, message, Form, Select, FormGrid, Panel, Modal } from 'doraemon';
import useDrawer from 'src/hooks/useDrawer';
import { syncAnnouncementTypePost, syncAnnouncementTypeCheckPost, listAllEnvironmentsGet, ListAllEnvironmentsGetResponse } from 'src/api/announcement/api/config/announcementType';
import { listAnnTypesList, listBigAnnType } from '../../services/api';

export enum SyncType {
  annType,
  annBigType
}

const AnnTypeSynDrawer = ({
  form,
  visible,
  onCancel,
  onSubmit,
  syncType = SyncType.annType,
}) => {
  const [synEnvList, setSynEnvList] = useState<ListAllEnvironmentsGetResponse['result']>([]);
  const [list, setList] = useState([]);
  const { validateFields, getFieldDecorator } = form;

  useEffect(() => {
    getList();
    getEnvList();
  }, []);

  const getList = () => {
    if (syncType === SyncType.annType) {
      return listAnnTypesList({
        pageNo: 1,
        pageSize: 999,
      }).then(res => {
        if (!res.success) return;
        setList(res.result?.data ?? []);
      });
    }
    if (syncType === SyncType.annBigType) {
      return listBigAnnType().then(res => {
        if (!res.success) return;
        setList(res.result ?? []);
      });
    }
    return null;
  };

  const getEnvList = () => {
    listAllEnvironmentsGet().then(res => {
      if (!res.success) return;
      setSynEnvList(res.result ?? []);
    });
  };
  const onSave = () => {
    validateFields((err, { environments, announcementTypeCodes }) => {
      if (err) return;
      announcementTypeCodes = announcementTypeCodes.map(ele => +ele);
      return Modal.confirm({
        title: '所选公告类型将同步到目标区划，是否继续？',
        closable: true,
        onOk: () => {
          return syncAnnouncementTypeCheckPost({
            environments,
            announcementTypeCodes,
          }).then(res => {
            if (!res.success) return;
            if (res.result?.length) {
              return Modal.error({
                title: '无法同步！目前环境已存在公告类型：',
                content: (
                  <React.Fragment>
                    { res.result.map(((ele, index) => (
                      <div key={index}>
                        <span>{ele.codeDesc}，</span>
                        <span>{ele.code}</span>
                      </div>
                    )))}
                    <div style={{ marginTop: 5 }}>
                      请前往目标环境进行修改！
                    </div>
                  </React.Fragment>
                ),
              });
            }
            return syncAnnouncementTypePost({
              environments,
              announcementTypeCodes,
            }).then(res1 => {
              if (!res1.success) return;
              message.success('同步成功', () => {
                onCancel();
                onSubmit();
              });
            });
          });
        },
      });
    });
  };

  const TitleNode = () => {
    const title = '配置同步到其他环境';
    return (
      <div className="drawer-titleNode-wrapper">
        <span>{title}</span>
      </div>
    );
  };

  const formGridItem = [{
    label: '当前环境',
    colSpan: 2,
    render: () => {
      return '云平台';
    },
  }, {
    label: '目标环境',
    colSpan: 2,
    render: () => {
      return getFieldDecorator('environments', {
        rules: [{
          required: true,
          message: '请输入',
        },
        ],
      })(
        <Select
          placeholder="请选择"
          getPopupContainer={() => document.querySelector('.annTypeSyn-drawer')}
          showSearch 
          optionFilterProp="name"
        >
          {
            synEnvList?.map(({ env, envName }) => (
              <Select.Option name={envName} key={env} value={env}>
                {envName}
              </Select.Option>
            ))
          }
        </Select>
      );
    },
  }, {
    label: '公告类型',
    colSpan: 2,
    render: () => {
      return getFieldDecorator('announcementTypeCodes', {
        rules: [{
          required: true,
          message: '请输入',
        }],
      })(
        <Select
          mode="multiple"
          placeholder="请选择"
          getPopupContainer={() => document.querySelector('.annTypeSyn-drawer')}
          showSearch
          optionFilterProp="name"
        >
          {
            list.map(({ typeName, typeCode }) => (
              <Select.Option name={typeName} key={typeCode} value={typeCode}>
                {typeName}
              </Select.Option>
            ))
          }
        </Select>
      );
    },
  }];

  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="annTypeSyn-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Form>
        <Panel.Sub bordered title="基本信息" >
          <FormGrid bordered formGridItem={formGridItem} />
        </Panel.Sub>
      </Form>
    </Drawer>
  );
};

const AnnTypeSynDrawerByForm = Form.create()(AnnTypeSynDrawer);

const useAnnTypeSynDrawer = (props) => {
  return useDrawer(AnnTypeSynDrawerByForm, props);
};

export { useAnnTypeSynDrawer };
export default AnnTypeSynDrawerByForm;

