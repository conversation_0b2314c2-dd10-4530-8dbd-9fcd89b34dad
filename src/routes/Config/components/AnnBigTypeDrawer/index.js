import React from 'react';
import { Drawer, message, Input, Form, FormGrid, Panel } from 'doraemon';
import { PageType } from '../../constants';
import { saveBigAnnType, updateBigAnnType } from '../../services/api';
import useDrawer from 'src/hooks/useDrawer';

const AnnBigTypeDrawer = ({
  form,
  visible,
  onCancel,
  onSubmit,
  curInfo = {},
  pageType = PageType.add,
}) => {
  const { validateFields, getFieldDecorator } = form;
  const onSave = () => {
    validateFields((err, { typeCode, typeName }) => {
      if (err) return;
      if (pageType === PageType.add) {
        return saveBigAnnType({
          typeCode,
          id: null,
          typeName: typeName.trim(),

        }).then((res) => {
          if (!res.success) return;
          message.success('新增成功', () => {
            onCancel();
            onSubmit();
          });
        });
      }
      return updateBigAnnType({ 
        typeCode, 
        typeName: typeName.trim(),
        id: curInfo.id, 
      }).then((res) => {
        if (!res.success) return;
        message.success('更新成功', () => {
          onCancel();
          onSubmit();
        });
      });
    });
  };

  const formGridItem = [{
    label: '公告大类配置层级',
    colSpan: 2,
    render: () => {
      return '平台级';
    },
  }, {
    label: '公告大类ID',
    colSpan: 2,
    extra: pageType === PageType.add ? '格式固定，例如1001、2001' : null,
    render: () => {
      return getFieldDecorator('typeCode', {
        initialValue: curInfo.typeCode || undefined,
        rules: [{
          required: true,
          message: '请输入',
        },
        { pattern: /^(([1-9])\d+|[1-9])$/, message: '请输入正整数' },
        ],
      })(
        (pageType === PageType.add) ?
          <Input placeholder="请输入" maxLength={10} />
          : <span>{curInfo.typeCode}</span>
      );
    },
  }, {
    label: '公告大类名称',
    colSpan: 2,
    render: () => {
      return getFieldDecorator('typeName', {
        initialValue: curInfo.typeName || undefined,
        rules: [{
          required: true,
          message: '请输入',
        }],
      })(
        <Input placeholder="请输入" maxLength={20} />
      );
    },
  }];

  return (
    <Drawer
      title={pageType === PageType.add ? '新增公告大类' : '编辑公告大类'}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={640}
      className="annBigType-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Form>
        <Panel.Sub bordered title="基本信息" >
          <FormGrid bordered formGridItem={formGridItem} />
        </Panel.Sub>
      </Form>
    </Drawer>
  );
};

const AnnBigTypeDrawerByForm = Form.create()(AnnBigTypeDrawer);

const useAnnBigTypeDrawer = (props) => {
  return useDrawer(AnnBigTypeDrawerByForm, props);
};

export { useAnnBigTypeDrawer };
export default AnnBigTypeDrawerByForm;

