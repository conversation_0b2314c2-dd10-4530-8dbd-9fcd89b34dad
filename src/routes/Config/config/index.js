import React from 'react';
import { Form, TreeSelect, Select, Alert, DatePicker, Input } from 'doraemon';
import moment from 'moment';
import userIdentity from 'src/utils/roleType';
import DistrictTreeSelect from 'src/components/DistrictTreeSelect';

const { Item } = Form;

const { RangePicker } = DatePicker;

const SHOW_CHILD = TreeSelect.SHOW_CHILD;

export const SORTTYPE = 'sortType';

export const PUBLISHTYPE = 'publishType';

export const BATCHADD = 'BATCHADD';

export const BATCHDLET = 'BATCHDLET';

export const SINGLESYNCHRONIZE = 'SINGLESYNCHRONIZE';

export const BATCHCANPUBLISH = 'BATCHCANPUBLISH';

export const BATCHDISPUBLISH = 'BATCHDISPUBLISH';

export const BATCHSYNCHRONIZE = 'BATCHSYNCHRONIZE';

export const getSortGlobalBtn = ({
  isEdit,
  onSave,
  onClose,
  showModal,
  onEdit,
  type,
  changeCode,
}) => {
  if (!changeCode && userIdentity.isAdmin) {
    return [];
  }
  if (type === SORTTYPE) {
    return isEdit
      ?
      [
        {
          label: '保存',
          type: 'primary',
          onClick: onSave,
        },
        {
          label: '取消',
          type: 'default',
          onClick: onClose,
        },
      ]
      :
      [
        {
          label: '批量添加关联',
          type: 'default',
          onClick: () => showModal(BATCHADD),
        },
        {
          label: '批量删除关联',
          type: 'default',
          onClick: () => showModal(BATCHDLET),
        },
        {
          label: '同步到其他区划',
          type: 'default',
          onClick: () => showModal(SINGLESYNCHRONIZE),
        },
        {
          label: '编辑',
          type: 'primary',
          onClick: onEdit,
        },
      ];
  }
  if (type === PUBLISHTYPE) {
    return isEdit
      ?
      [
        {
          label: '保存',
          type: 'primary',
          onClick: onSave,
        },
        {
          label: '取消',
          type: 'default',
          onClick: onClose,
        },
      ]
      :
      [
        {
          label: '批量配置可发布',
          type: 'default',
          onClick: () => showModal(BATCHCANPUBLISH),
        },
        {
          label: '批量配置不可发布',
          type: 'default',
          onClick: () => showModal(BATCHDISPUBLISH),
        },
        userIdentity.isAdmin // 为监管才展示
          &&
        {
          label: '配置同步到其他区划',
          type: 'default',
          onClick: () => showModal(BATCHSYNCHRONIZE),
        },
        {
          label: '编辑',
          type: 'primary',
          onClick: onEdit,
        },
      ].filter(Boolean);
  }
};

export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

export const sortRoutes = [
  {
    label: '公告关联配置',
    to: '/sort',
  },
];

export const publishRoutes = [
  {
    label: '公告发布配置',
    to: '/open',
  },
];

export const getModalForm = ({
  type, getFieldDecorator, codeName,
  announcementTypeData,
  originatorTypeList = [],
}) => {
  switch (type) {
  case SINGLESYNCHRONIZE:
    return (
      <>
        <Alert closable style={{ width: 590 }} message="覆盖目标区划数据将覆盖原区划下的配置，复制成功后将不可恢复，请谨慎同步！" type="warning" showIcon />
        <Form>
          <Item
            {...formItemLayout}
            label="当前区划"
            style={{ marginTop: 19 }}
          >
            {codeName}
          </Item>
          <Item
            {...formItemLayout}
            label="复制方式选择"
            required
          >
            覆盖目标区划数据
          </Item>
          <Item
            {...formItemLayout}
            label="目标区划"
            required
          >
            {
              getFieldDecorator('targetDist', {
                rules: [{
                  required: true, message: '请选择目标区划',
                }],
              })(
                <DistrictTreeSelect />
              )
            }
          </Item>
        </Form>
      </>
    );
  case BATCHADD:
  case BATCHDLET:
    return (
      <Form>
        <Item
          {...formItemLayout}
          label="目标区划"
          required
        >
          {
            getFieldDecorator('targetDist', {
              rules: [{
                required: true, message: '请选择目标区划',
              }],
            })(
              <DistrictTreeSelect />
            )
          }
        </Item>
        <Item
          {...formItemLayout}
          label="公告类型"
          required
        >
          {
            getFieldDecorator('typeIdList', {
              rules: [{
                required: true, message: '请选择公告类型',
              }],
            })(
              <TreeSelect
                allowClear
                style={{ width: 300 }}
                treeData={announcementTypeData}
                getPopupContainer={triggerNode => triggerNode.parentNode}
                treeCheckable
                showCheckedStrategy={SHOW_CHILD}
                searchPlaceholder="请选择公告类型"
              />
            )
          }
        </Item>
      </Form>
    );
  case BATCHCANPUBLISH:
  case BATCHDISPUBLISH:
    return (
      <Form>
        <Item
          {...formItemLayout}
          label="目标区划"
          required
        >
          {
            getFieldDecorator('targetDist', {
              rules: [{
                required: true, message: '请选择目标区划',
              }],
            })(
              <DistrictTreeSelect />
            )
          }
        </Item>
        <Item
          {...formItemLayout}
          label="选择用户类别"
          required
        >
          {
            getFieldDecorator('userTypes', {
              rules: [{
                required: true, message: '请选择用户类别',
              }],
            })(
              <Select
                mode="multiple"
                style={{ width: 300 }}
                placeholder="请选择用户类别"
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {
                  originatorTypeList?.map(
                    ({ code, desc }) =>
                      <Select.Option key={code} value={code}>{desc}</Select.Option>
                  )
                }
              </Select>
            )
          }
        </Item>
        <Item
          {...formItemLayout}
          label="公告类型"
          required
        >
          {
            getFieldDecorator('typeIdList', {
              rules: [{
                required: true, message: '请选择公告类型',
              }],
            })(
              <TreeSelect
                allowClear
                style={{ width: 300 }}
                treeData={announcementTypeData}
                getPopupContainer={triggerNode => triggerNode.parentNode}
                treeCheckable
                showCheckedStrategy={SHOW_CHILD}
                searchPlaceholder="请选择公告类型"
              />
            )
          }
        </Item>
      </Form>
    );
  case BATCHSYNCHRONIZE:
    return (
      <Form>
        <Item
          {...formItemLayout}
          label="当前区划"
          style={{ marginTop: 24 }}
        >
          {codeName}
        </Item>
        <Item
          {...formItemLayout}
          label="目标区划"
          required
        >
          {
            getFieldDecorator('targetDist', {
              rules: [{
                required: true, message: '请选择目标区划',
              }],
            })(
              <DistrictTreeSelect />
            )
          }
        </Item>
        <Item
          {...formItemLayout}
          label="选择用户类别"
          required
        >
          {
            getFieldDecorator('userTypeList', {
              rules: [{
                required: true, message: '请选择用户类别',
              }],
            })(
              <Select
                mode="multiple"
                style={{ width: 300 }}
                placeholder="请选择用户类别"
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {
                  originatorTypeList?.map(
                    ({ code, desc }) =>
                      <Select.Option key={code} value={code}>{desc}</Select.Option>
                  )
                }
              </Select>
            )
          }
        </Item>
      </Form>
    );
  default:
    break;
  }
};

export const configModalConst = {
  [SINGLESYNCHRONIZE]: {
    title: '同步到其他区划',
    tip: '选择覆盖目标区划数据，将覆盖原区划下的配置，复制成功后将不可恢复，请确认是否继续。',
    okText: '继续',
    okHandler: 'singleSynchronizeHandler',
  },
  [BATCHADD]: {
    title: '批量添加关联',
    tip: '添加后立即生效，确定批量添加关联吗？',
    okHandler: 'batchAddHandler',
    okText: '确定添加',
  },
  [BATCHDLET]: {
    title: '批量删除关联',
    tip: '删除后立即生效，确定批量删除关联吗？',
    okHandler: 'batchDletHandler',
    okText: '确定删除',
  },
  [BATCHCANPUBLISH]: {
    title: '批量配置可发布',
    tip: '配置后立即生效，确定批量配置可发布吗？',
    okHandler: 'batchCanpublishHandler',
    okText: '确定配置',
  },
  [BATCHDISPUBLISH]: {
    title: '批量配置不可发布',
    tip: '配置后立即生效，确定批量配置不可发布吗？',
    okHandler: 'batchDispublishHandler',
    okText: '确定配置',
  },
  [BATCHSYNCHRONIZE]: {
    title: '配置同步到其他区划',
    tip: '选择覆盖目标区划数据，将覆盖原区划下的配置，复制成功后将不可恢复，请确认是否继续。',
    okHandler: 'batchSynchronizeHandler',
    okText: '继续',
  },
};

export const logRoutes = [
  {
    label: '公告配置日志',
    to: '/log',
  },
];

export const getLogCustomItem = ({ sourceList = [], operationList = [] }) => (
  [
    {
      label: '操作时间',
      id: 'operateTime', // 分为 addTimeStart - addTimeEnd
      render: () => {
        return (
          <RangePicker
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            placeholder={['开始时间', '结束时间']}
          />
        );
      },
    },
    {
      label: '配置菜单',
      id: 'source',
      render: () => {
        return (
          <Select allowClear placeholder="请选择">
            {
              sourceList?.map(
                item => <Select.Option value={item.key} key={item.key}>{item.value}</Select.Option>
              )
            }
          </Select>
        );
      },
    },
    {
      label: '配置类型',
      id: 'operation',
      render: () => {
        return (
          <Select allowClear placeholder="请选择">
            {
              operationList?.map(
                item => <Select.Option value={item.key} key={item.key}>{item.value}</Select.Option>
              )
            }
          </Select>
        );
      },
    },
    {
      label: '操作人',
      id: 'operatorName',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    }]
);

export const getLogTableColumn = () => {
  return (
    [
      {
        title: '操作时间',
        dataIndex: 'addTime',
        render: time => moment(time).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '配置菜单',
        dataIndex: 'source',
      },
      {
        title: '配置类型',
        dataIndex: 'operation',
      },
      {
        title: '操作人',
        dataIndex: 'operatorName',
      },
    ]
  );
};
