module.exports = [
  {
    url: '/config/open',
    view: 'open',
  },
  {
    url: '/config/supervision',
    view: 'open',
  },
  {
    url: '/config/publish',
    view: 'publish',
    models: ['publish'],
  },
  {
    url: '/config/sort',
    view: 'sort',
    models: ['sort'],
  },
  {
    url: '/config/log',
    view: 'log',
  },
  // 公告大类管理
  {
    url: '/config/manage',
    view: 'manage',
  },
  // 公告类型管理
  {
    url: '/config/type',
    view: 'type',
  },
  // 公告分组管理
  {
    url: '/config/group',
    view: 'group',
  },
  // 公告站点配置
  {
    url: '/config/site',
    view: 'site',
  },
  // 公告接入管理
  {
    url: '/config/guide',
    view: 'guide',
  },
  // 公告接入管理节点详情
  {
    url: '/config/guideDetail/:guideId/:actionType(edit|detail)',
    view: 'guideDetail',
  },
];
