export const PageType = {
  add: 1,
  edit: 2,
  detail: 3,
};

export const SourceType = {
  // 公告大类管理
  announcementBigType: 'announcementBigType',
  // 公告类型管理
  announcementTypeConfig: 'announcementTypeConfig',
};

export const OperationType = [
  { name: '全部', code: 'all' },
  { name: '保存', code: 'save' },
  { name: '编辑', code: 'update' },
  { name: '删除', code: 'delete' },
  { name: '同步', code: 'synchronization' },
];

export const DEL_ALERT_MSG = '暂不支持在配置环节删除公告大类、公告类型，请联系公告中心产品同学进行处理！';

export const ACTIVE_KEY_LIST = ['list', 'metadata'];

export const ANN_PREVIEW_KEY = ['template', 'title'];
