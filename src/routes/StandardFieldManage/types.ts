/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 15:17:19
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-22 20:31:45
 * @FilePath: /zcy-announcement-v2-front/src/routes/StandardFieldManage/types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface Template {
    id: string;
    bookmarkType: string;
    bookmarkCode: string;
    bookmarkName: string;
    scope: string;
    fieldType: string;
    status: 'active' | 'inactive';
    updatedBy: string;
    updatedAt: string;
  }
  
export type FormMode = 'create' | 'edit' | 'view' | 'usage';
