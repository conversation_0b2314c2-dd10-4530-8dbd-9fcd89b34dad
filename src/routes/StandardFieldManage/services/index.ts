/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 16:41:56
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 17:57:05
 * @FilePath: /zcy-announcement-v2-front/src/routes/StandardFieldManage/services/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@zcy/zcy-request';

// 获取书签列表
export async function fetchBookmarkList(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/bookmark/list', {
      method: 'GET',
      params,
    }
  );
}

// 查询书签
export async function fetchModalBookmarkListApi(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/bookmark/queryBookmark', {
      method: 'GET',
      params,
    }
  );
}

// 保存书签
export async function updateTemplateBookmarkApi(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/bookmark/save', {
      method: 'POST',
      data: params,
    }
  );
}

// 显示使用书签和模板
export async function fetchTemplateUseList(params) {
  return request(
    '/api/opPlatform/prodOperation/announcement/stdlib/showUsedInfo', {
      method: 'GET',
      params,
    }
  );
}
