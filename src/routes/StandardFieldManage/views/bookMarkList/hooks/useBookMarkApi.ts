/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 16:40:18
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 18:00:13
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchBookmarkList,
  fetchTemplateUseList,
} from '../../../services';

export default function useBookMarkApi() {
  const bookmarkListRes = useRequest(fetchBookmarkList, {
    manual: true,
  });
  const bookmarkListResult = bookmarkListRes?.data?.result;

  // 显示使用书签和模板
  const templateUseListRes = useRequest(
    (params) => {
      return fetchTemplateUseList(params);
    },
    {
      manual: true,
    }
  );
  const templateUseListResult = templateUseListRes?.data?.result;
  return {
    bookmarkList: bookmarkListResult?.data || [],
    bookmarkListPagination: {
      showSizeChanger: true,
      total: bookmarkListResult?.total,
      pageSize: bookmarkListResult?.pageSize,
      current: bookmarkListResult?.pageNum,
    },
    bookmarkListLoading: bookmarkListRes.loading,
    fetchBookmarkList: bookmarkListRes.runAsync,
    templateUseList: templateUseListResult || [],
    fetchTemplateUseList: templateUseListRes.runAsync,
    // deleteTemplateBookmark: deleteTemplateBookmarkRes.runAsync,
    // deleteTemplateBookmarkLoading: deleteTemplateBookmarkRes.loading,
  };
}
