import { useSetState } from '@zcy/react-hooks';

export default function useBookmarkState() {
  const [state, setState] = useSetState({
    addOrEditBookmarkVisible: false,
    selectBookMarkVisible: false,
    selectedData: [],
    mode: 'create',
  });
    /** 控制新增编辑框是否显示 */
  const changeAddOrEditBookmarkVisible = (show: boolean) => {
    setState({
      addOrEditBookmarkVisible: show,
    });
  };
  /** 控制框是否显示 */
  const changeSelectBookMarkVisible = (show: boolean) => {
    setState({
      selectBookMarkVisible: show,
    });
  };

  const getSelectedData = (data: []) => {
    setState({
      selectedData: data,
    });
  };

  const changeMode = (mode: string) => {
    setState({ mode });
  };
  
  return {
    ...state,
    changeAddOrEditBookmarkVisible,
    changeSelectBookMarkVisible,
    getSelectedData,
    changeMode,
  };
}
