/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-20 18:01:30
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 18:16:33
 * @FilePath: /zcy-announcement-v2-front/src/routes/StanaedFieldManage/views/list.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useCallback } from 'react';
import PaaSLayout from '@/layouts/PaaSLayout';
import { ColumnProps } from '@zcy/doraemon/lib/table/interface';
import { Spin, ZcyList, Input, Select, Table } from '@zcy/doraemon';
import useBookMarkApi from './hooks/useBookMarkApi';
import useBookmarkState from './hooks/useBookMarkState';
import AddOrEditBookmark from '../../components/AddOrEditBookmark';
import SelectBookMarkModal from '../../components/SelectBookMarkModal';

const { Actions } = Table;
const { Option } = Select;
export default function StandardList() {
  const {
    bookmarkList, // 书签列表时数据
    fetchBookmarkList, //
    bookmarkListLoading,
    bookmarkListPagination,
    templateUseList,
    fetchTemplateUseList,
  } = useBookMarkApi();

  const {
    addOrEditBookmarkVisible,
    changeAddOrEditBookmarkVisible,
    selectBookMarkVisible,
    changeSelectBookMarkVisible,
    selectedData,
    getSelectedData,
    mode,
    changeMode,
  } = useBookmarkState();

  const bookmarkInitSearchParams = {
    pageNo: 1,
    pageSize: 20,
    bizType: 1, // 政采业务
  };

  useEffect(() => {
    onSearch(bookmarkInitSearchParams);
  }, []);
  const onSearch = useCallback((params) => {
    const searchParams = {
      ...params,
    };
    fetchBookmarkList(searchParams);
  }, [fetchBookmarkList]);
  const onSelectBookMark = (selectedItem) => {
    getSelectedData(selectedItem?.[0]);
    changeSelectBookMarkVisible(false);
    // 打开新增书签弹窗
    changeAddOrEditBookmarkVisible(true);
  };

  // 新增书签
  const addBookMark = () => {
    changeMode('create');
    changeSelectBookMarkVisible(true);
  };

  // 编辑
  const handleEdit = (record) => {
    changeMode('edit');
    getSelectedData(record);
    changeAddOrEditBookmarkVisible(true);
  };
  
  // 模板引用情况
  const handleTemplateUse = (record) => {
    fetchTemplateUseList(record.id);
    changeMode('templateUse');
    getSelectedData(record);
    changeAddOrEditBookmarkVisible(true);
  };
  const searchItem = [
    {
      label: '书签编码',
      id: 'code',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '书签名称',
      id: 'name',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '标准字段编码',
      id: 'stdlibCode',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '标准字段名称',
      id: 'stdlibName',
      render: () => {
        return (<Input placeholder="请输入" />);
      },
    }, 
    {
      label: '业务类型',
      id: 'bizType',
      render: () => {
        return (
          <Select>
            <Option value="1">1</Option>
            <Option value="2">2</Option>
            <Option value="3">3</Option>
          </Select>
        );
      },
    },
  ];
  const generateActions = (buttonList, record) => {
    return buttonList?.map(button => ({
      label: button.name,
      onRowClick: () => handleAction(button.code, record),
    })) || [];
  };
  const handleAction = (actionCode, record) => {
    switch (actionCode) {
      case 'edit':
        handleEdit(record);
        break;
      case 'disable':
        // handleDisable(record.id);
        break;
      case 'view':
        // handleViewDetail(record);
        break;
      case 'addChild':
        // handleAddChild(record.id);
        break;
      case 'template':
        handleTemplateUse(record);
        break;
      default:
        console.warn('未知操作类型:', actionCode);
    }
  };
  const tableColumns: ColumnProps<any>[] = [
    {
      title: '书签编码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: '书签名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '书签类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: '标准字段名称',
      dataIndex: 'stdlibName',
      key: 'stdlibName',
      width: 150,
    },
    {
      title: '标准字段编码',
      dataIndex: 'stdlibCode',
      key: 'stdlibCode',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      fixed: 'right',
      render: (text, record) => {
        return (
          <div className="template-bookmark-list-actions">
            <Actions
              record={record}
              maxShowSize={3}
              actions={generateActions(record.buttonList, record)}
            />
          </div>
        );
      },
    },
  ];

  
  return (
    <PaaSLayout
      globalBtn={[   
        {
          label: '新增书签',
          type: 'primary',
          onClick: () => addBookMark(),
        }]}
    >
      <Spin spinning={bookmarkListLoading}>
        <ZcyList
          expandForm
          initSearchParams={bookmarkInitSearchParams}
          onSearch={onSearch}
          customItem={searchItem}
          table={{
            columns: tableColumns,
            dataSource: bookmarkList,
            rowKey: 'id',
            pagination: bookmarkListPagination,
            rowSelection: undefined,
            scroll: {
              x: 1200,
              y: 1140,
            },
          }}
          openSearchCache
        />
        {
          selectBookMarkVisible && (
            <SelectBookMarkModal
              visible={selectBookMarkVisible}
              onCancel={() => changeSelectBookMarkVisible(false)}
              onModalOk={(selectedItem) => {
                onSelectBookMark(selectedItem);
              }}
            />
          )
        }
        {
          addOrEditBookmarkVisible && (
            <AddOrEditBookmark
              mode={mode}
              visible={addOrEditBookmarkVisible}
              onCancel={() => changeAddOrEditBookmarkVisible(false)}
              onModalOk={() => {
                onSearch(bookmarkInitSearchParams);
                changeAddOrEditBookmarkVisible(false);
              }}
              formDetail={selectedData}
              templateUseList={templateUseList}
            />
          )
        }
      </Spin>
    </PaaSLayout>
  );
}
