/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-24 21:04:23
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 17:17:43
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useSetState } from '@zcy/react-hooks';

export default function useSelectedItemState() {
  const [state, setState] = useSetState({
    selectedItem: [],
  });
  // 选择数据
  const changeSelectedItem = (value) => {
    setState({
      selectedItem: value,
    });
  };
  
  return {
    ...state,
    changeSelectedItem,
  };
}
