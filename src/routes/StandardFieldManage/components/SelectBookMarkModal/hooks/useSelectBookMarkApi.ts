/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-23 15:48:10
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 17:16:25
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useRequest } from '@zcy/react-hooks';
import {
  fetchModalBookmarkListApi,
} from '../../../services';

export default function useSelectBookMarkApi() {
  // 新增书签
  const modalBookmarkListRes = useRequest(
    (params) => {
      return fetchModalBookmarkListApi({ ...params });
    },
    {
      manual: true,
    }
  );
  const modalBookmarkListResult = modalBookmarkListRes?.data?.result;
  return {
    modalBookmarkList: modalBookmarkListResult?.data || [],
    fetchModalBookmarkList: modalBookmarkListRes.runAsync,
    modalBookmarkListLoading: modalBookmarkListRes.loading,
    modalBookmarkListPagination: {
      showSizeChanger: true,
      total: modalBookmarkListResult?.total,
      pageSize: modalBookmarkListResult?.pageSize,
      current: modalBookmarkListResult?.pageNum,
    },
  };
}
