import React from 'react';
import { Input, Select, Radio, Table } from '@zcy/doraemon';

const { Option } = Select;
const RadioGroup = Radio.Group;
export const handleFormFields = (fields, record, form) => {
  const { getFieldDecorator, getFieldsValue } = form;
  const formValues = getFieldsValue(); // 获取当前表单值
  return fields
    .filter((field) => {
      // 基础可见性过滤
      if (!field.visible) return false;
      // 处理字段联动显示逻辑
      if (field.relateTo) {
        const { name, value } = field.relateTo;
        const relatedValue = formValues?.[name];
        return relatedValue === value;
      }
      return true;
    }) // 过滤掉不需要显示的字段
    .map(field => ({
      label: field.label,
      required: field.required,
      render: () => {
        // 表单控件配置
        const decoratorOptions = {
          rules: [field.rules],
          initialValue: record?.[field.name],
        };
        // 根据不同类型渲染不同控件
        let formControl;
        if (field.isText) {
        //   formControl = <span>{field?.name}</span>;
          formControl = <span>{record?.[field.name]}</span>;
        } else if (field.type === 'table') {
          formControl = (
            <Table 
              columns={field.columns}
            />
          );
        } else {
          switch (field.type) {
            case 'input':
              formControl = (
                <Input 
                  placeholder={field?.placeholder} 
                  disabled={field?.disabled}
                />
              );
              break;
            case 'select':
              formControl = (
                <Select 
                  placeholder={field?.placeholder}
                  disabled={field?.disabled}
                >
                  {field.options?.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              );
              break;
            case 'radio':
              formControl = (
                <RadioGroup 
                  options={field.options} 
                />
              );
              break;
              // 可以继续添加其他类型的控件
            default:
              formControl = <Input placeholder={field.placeholder} />;
          }
        }
    
        return getFieldDecorator(field.name, decoratorOptions)(formControl);
      },
    }));
};
