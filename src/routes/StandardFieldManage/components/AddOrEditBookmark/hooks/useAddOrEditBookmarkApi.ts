/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-23 13:44:23
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-23 15:02:05
 */
import { useRequest } from '@zcy/react-hooks';
import {
  updateTemplateBookmarkApi,
} from '../../../services';

export default function useAddOrEditBookmarkApi() {
  // 新增书签
  const updateTemplateBookmarkRes = useRequest(
    (params) => {
      return updateTemplateBookmarkApi({ ...params });
    },
    {
      manual: true,
    }
  );
  
  return {
    updateTemplateBookmark: updateTemplateBookmarkRes.runAsync,
    updateTemplateBookmarkLoading: updateTemplateBookmarkRes.loading,
  };
}
