/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:17:08
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 18:08:19
 */
import React from 'react';
import { Form, Modal, FormGrid, message } from '@zcy/doraemon';
import { ColumnType } from '@zcy/doraemon/lib/form-grid/interface';
import { FormMode, Template } from '../../types';
import useAddOrEditBookmarkApi from './hooks/useAddOrEditBookmarkApi';
import { bookMarkFieldsConfig } from '../../config/bookMarkFieldsConfig';
import { handleFormFields } from '../../components/handleFormFields';
import { BOOKMARKMODALTITLE } from '../../constants';

interface AddOrEditBookmarkProps {
  mode: FormMode;
  visible: boolean;
  formDetail: [];
  onCancel: () => void;
  onModalOk: (values: Template) => void;
}

const featureFormItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 11 },
  column: 1 as ColumnType,
  bordered: false,
};

const AddOrEditBookmark: React.FC<AddOrEditBookmarkProps> = (props) => {
  const { mode, visible, onCancel, onModalOk, formDetail, form, templateUseList } = props;
  const { validateFields } = form;
  const {
    updateTemplateBookmark,
    updateTemplateBookmarkLoading,
  } = useAddOrEditBookmarkApi();

  const confirmLoading = updateTemplateBookmarkLoading;

  const onSubmit = () => {
    validateFields((error, values) => {
      if (error) {
        return;
      }

      const params = {
        ...values,
      };
      updateTemplateBookmark(params).then((res) => { 
        if (res?.success) {
          if (mode === 'create') {
            message.success('新增成功');
          } else {
            message.success('修改成功');
          }
          onModalOk?.();
        }
      });
    });
  };
  
  
  const getFormItem = (record?: any) => {
    const fields = bookMarkFieldsConfig(mode);
    return handleFormFields(fields, record, form, templateUseList);
  };

  return (
    <Modal
      title={BOOKMARKMODALTITLE[mode]}
      visible={visible}
      width={mode === 'templateUse' ? 800 : 500}
      onOk={() => onSubmit()}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
    >
      <Form>
        <FormGrid 
          {...featureFormItemLayout} 
          formGridItem={getFormItem(formDetail) || []} 
        />
      </Form>
    </Modal>
  );
};
export default Form.create()<AddOrEditBookmarkProps>(AddOrEditBookmark);
