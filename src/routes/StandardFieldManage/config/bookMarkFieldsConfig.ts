/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-22 20:19:50
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-26 16:07:14
 */
// 表单字段配置，根据mode控制显隐
export const bookMarkFieldsConfig = (mode) => [
  {
    name: 'code',
    label: '书签编码',
    type: 'input',
    placeholder: '请输入书签编码',
    visible: true,
    required: mode === 'create' || mode === 'edit',
    rules: [
      {
        required: true,
        message: '请输入书签编码',
      },
    ],
    disabled: true,
    isText: mode === 'templateUse',
  },
  {
    name: 'name',
    label: '书签名称',
    type: 'input',
    placeholder: '请输入书签名称',
    visible: true,
    required: mode === 'create' || mode === 'edit',
    rules: [
      {
        required: true,
        message: '请输入书签编码',
      },
    ],
    disabled: true,
    isText: mode === 'templateUse',
  },
  {
    name: 'type',
    label: '书签类型',
    type: 'select',
    placeholder: '请输入书签编码',
    visible: true,
    required: mode === 'create' || mode === 'edit',
    disabled: true,
    options: [
      {
        label: '书签类型1',
        value: '1',
      },
      {
        label: '书签类型2',
        value: '2',
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择书签类型',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'stdlibName',
    label: '标准字段名称',
    type: 'select',
    placeholder: '请选择标准字段名称',
    visible: true,
    required: mode === 'create' || mode === 'edit',
    options: [
      {
        label: '标准字段名称1',
        value: '1',
      },
      {
        label: '标准字段名称2',
        value: '2',
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择标准字段名称',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'componentType',
    label: '控件类型',
    type: 'select',
    placeholder: '请选择控件类型',
    visible: true,
    required: mode === 'create' || mode === 'edit',
    options: [
      {
        label: '控件类型1',
        value: '1',
      },
      {
        label: '控件类型2',
        value: '2',
      },
    ],
    rules: [
      {
        required: true,
        message: '请选择控件类型',
      },
    ],
    isText: mode === 'templateUse',
  },
  {
    name: 'templateUse',
    label: '模板引用情况',
    type: 'table',
    required: false,
    visible: mode === 'templateUse',
  },
  
  // 其他字段配置...
];
