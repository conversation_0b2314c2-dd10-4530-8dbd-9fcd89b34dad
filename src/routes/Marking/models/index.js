import {
  pagingIdentification, listAnnouncementTypes, getDistTree,
  switchIdentification, addIdentification, deleteIdentification, modifyIdentification,
} from '../services';

export default {
  namespace: 'marking',

  state: {},

  effects: {
    * fetchTableData({ payload }, { call }) {
      return yield call(pagingIdentification, payload);
    },
    * listAnnouncementTypes({ payload }, { call }) {
      return yield call(listAnnouncementTypes, payload);
    },
    * getDistTree({ payload }, { call }) {
      return yield call(getDistTree, payload);
    },
    * switchIdentification({ payload }, { call }) {
      return yield call(switchIdentification, payload);
    },
    * addIdentification({ payload }, { call }) {
      return yield call(addIdentification, payload);
    },
    * deleteIdentification({ payload }, { call }) {
      return yield call(deleteIdentification, payload);
    },
    * modifyIdentification({ payload }, { call }) {
      return yield call(modifyIdentification, payload);
    },
  },
};
