import React, { Component } from 'react';
import { connect } from 'dva';
import {
  ZcyList, Input, ZcyBreadcrumb, Form, Modal, Switch, Tooltip, message, Popconfirm,
} from 'doraemon';
import moment from 'moment';
import AddMarkingModal from './components/addMarking';
import './index.less';

@connect(({ marking, loading }) => ({
  marking,
  tableLoading: loading.models.marking,
}))
@Form.create()
export default class List extends Component {
  state = {
    visible: false,
    tableData: [],
    pageNo: 1,
    total: 0,
    columnOp: {},
  }

  componentDidMount() {
    this.fetchTableData({
      pageNo: 1,
      pageSize: 10,
    });
  }

  fetchTableData = (params) => {
    this.setState({
      pageNo: params.pageNo,
    });
    this.props.dispatch({
      type: 'marking/fetchTableData',
      payload: params,
    }).then((res) => {
      if (res.success) {
        if (res.result) {
          this.setState({
            tableData: res.result.data,
            total: res.result.total,
          });
        } else {
          this.setState({
            tableData: [],
            total: 0,
          });
        }
      } else {
        Modal.error({
          title: '获取列表失败！',
          content: res.error,
        });
      }
    });
  }

  // 新增标识
  add = () => {
    this.setState({
      columnOp: {},
      visible: true,
    });
  }

  // 修改标识
  edit = (columnOp) => {
    this.setState({
      columnOp,
      visible: true,
    });
  }

  onOk = (payload) => {
    const { id } = payload;
    let type = 'marking/addIdentification';
    let successMsg = '新增标识成功！';
    if (id) {
      type = 'marking/modifyIdentification';
      successMsg = '修改标识成功';
    }
    this.props.dispatch({
      type,
      payload,
    }).then((res) => {
      if (res.success) {
        message.success(successMsg);
        this.fetchTableData({
          pageNo: 1,
          pageSize: 10,
        });
        this.setState({
          visible: false,
        });
      } else {
        message.error(res.error);
      }
    });
  }

  onCancel = () => {
    this.setState({
      visible: false,
    });
  }

  onDel = (column) => {
    this.props.dispatch({
      type: 'marking/deleteIdentification',
      payload: {
        name: column.name,
        type: column.type,
      },
    }).then((res) => {
      if (res.success) {
        message.success('删除标识成功！');
        this.fetchTableData({
          pageNo: 1,
          pageSize: 10,
        });
      } else {
        message.error(res.error);
      }
    });
  }

  onStatusChange = (column, enabled) => {
    this.props.dispatch({
      type: 'marking/switchIdentification',
      payload: {
        enabled,
        identificationName: column.name,
        type: column.type,
      },
    }).then((res) => {
      if (res.success) {
        const { tableData } = this.state;
        tableData.find(item => item.id === column.id).enable = enabled;
        this.setState({
          tableData,
        });
        if (enabled) {
          message.success('开启成功！');
        } else {
          message.success('关闭成功！');
        }
      } else {
        Modal.error({
          title: '操作失败！',
          content: res.error,
        });
      }
    });
  }

  handleSearch = (params) => {
    this.fetchTableData(params);
  }

  render() {
    const { visible, tableData, total, columnOp, pageNo } = this.state;
    const { tableLoading } = this.props;
    const customItem = [{
      label: '标识名称',
      id: 'name',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    }];
    const tabs = {
      tabList: [{
        label: '公告标识列表',
        isAll: true,
      }],
    };
    const columns = [
      {
        title: '标识类型',
        dataIndex: 'type',
        width: 116,
        render(type) {
          if (type === 1) {
            return '自动生成标识';
          }
          return '手动选择标识';
        },
      }, {
        title: '标识名称',
        dataIndex: 'name',
        width: 116,
      }, {
        title: '应用公告类型',
        dataIndex: 'announcementTypeNames',
        width: 156,
        render(announcementTypeNames) {
          const str = Array.isArray(announcementTypeNames) ? announcementTypeNames.join('、') : '';
          return (
            <div className="short-str">
              <Tooltip title={str}>
                <span>{str}</span>
              </Tooltip>
            </div>
          );
        },
      }, {
        title: '应用区划',
        dataIndex: 'distNames',
        width: 156,
        render(distNames) {
          const distNameString = Array.isArray(distNames) ? distNames.join('、') : '';
          return (
            <div className="short-str">
              <Tooltip title={distNameString}>
                <span>{distNameString}</span>
              </Tooltip>
            </div>
          );
        },
      }, {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 120,
        render(createTime) {
          return moment(createTime).format('YYYY-MM-DD');
        },
      }, {
        title: '状态',
        dataIndex: 'enable',
        width: 100,
        render: (enable, column) => {
          return (
            <Switch
              checkedChildren="开启"
              unCheckedChildren="关闭"
              checked={enable}
              onChange={(e) => {
                this.onStatusChange(column, e);
              }}
            />
          );
        },
      }, {
        title: '操作',
        width: 100,
        render: (column) => {
          if (!column.enable) {
            return (
              <div className="opearation">
                <a onClick={() => {
                  this.edit(column);
                }}
                >编辑
                </a>
                <Popconfirm title="确定要删除吗?"
                  onConfirm={() => {
                    this.onDel(column);
                  }}
                >
                  <a>删除</a>
                </Popconfirm>
              </div>
            );
          }
        },
      }];
    return (
      <div className="marking">
        <ZcyBreadcrumb
          routes={[{
            label: '公告管理',
          }, {
            label: '公告打标工具',
          }]}
          globalBtn={[{
            label: '新增',
            type: 'primary',
            onClick: this.add,
          }]}
        />
        <ZcyList
          key={this.state.zcylistKey}
          customItem={customItem}
          tabs={tabs}
          table={{
            rowKey: 'id',
            loading: tableLoading,
            columns,
            dataSource: tableData,
            pagination: {
              current: pageNo,
              total,
            },
          }}
          onSearch={this.handleSearch}
        />
        <AddMarkingModal
          visible={visible}
          onOk={this.onOk}
          onCancel={this.onCancel}
          data={columnOp}
        />
      </div>
    );
  }
}
