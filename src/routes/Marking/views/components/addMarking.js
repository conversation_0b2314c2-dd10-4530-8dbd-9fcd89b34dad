import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Spin, Input, Modal, Form, Radio, message, TreeSelect,
} from 'doraemon';
import './addMarking.less';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const SHOW_CHILD = TreeSelect.SHOW_CHILD;

@connect(({ marking, loading }) => ({
  marking,
  loading: loading.models.marking,
  confirmLoading: loading.effects['marking/addIdentification'] || loading.effects['marking/modifyIdentification'],
}))
@Form.create()
class addMarking extends Component {
  state = {
    districtData: [],
    annTypes: [],
  }

  componentDidMount() {
    this.fetchDistTree();
    this.fetchAnnTypes();
  }

  componentWillReceiveProps(nextProps) {
    if (!this.props.visible && nextProps.visible) {
      const { data } = nextProps;
      if (data.id) {
        data.announcementTypes = data.announcementTypes.map(item => item.toString());
      }
      this.props.form.resetFields();
    }
  }

  // 获取所有公告类型
  fetchAnnTypes = () => {
    this.props.dispatch({
      type: 'marking/listAnnouncementTypes',
    }).then((res) => {
      if (res.success) {
        const annTypes = this.resolveAnnTypes(res.result);
        this.setState({
          annTypes,
        });
      } else {
        message.error(res.error);
      }
    });
  }

  // 获取区划
  fetchDistTree = () => {
    this.props.dispatch({
      type: 'marking/getDistTree',
    }).then((res) => {
      if (res.success) {
        const districtData = this.resolveTree(res.result[0].children);
        this.setState({
          districtData,
        });
      } else {
        message.error(res.error);
      }
    });
  }

  // 解析区划
  resolveTree = (tree) => {
    const newTree = [];
    tree.forEach((item) => {
      newTree.push({
        title: item.text,
        value: item.code,
        children: (item.children && Array.isArray(item.children) && item.children.length > 0) ?
          this.resolveTree(item.children) : undefined,
      });
    });
    return newTree;
  }

  // 解析区划
  resolveAnnTypes = (tree) => {
    const newTree = [];
    tree.forEach((item) => {
      newTree.push({
        title: item.name,
        value: item.typeId.toString(),
        children: item.children.map((children) => {
          return {
            title: children.name,
            value: children.typeId.toString(),
          };
        }),
      });
    });
    return newTree;
  }

  onOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { id } = this.props.data;
        values.id = id;
        this.props.onOk(values);
      } else {
        message.warning('请完善信息！');
      }
    });
  }

  render() {
    const { districtData, annTypes } = this.state;
    const { loading, confirmLoading, data } = this.props;
    const { getFieldDecorator } = this.props.form;

    return (
      <Modal
        {...this.props}
        title={data.id ? '编辑标识' : '新增标识'}
        onOk={this.onOk}
        confirmLoading={confirmLoading}
        wrapClassName="marking-add-modal"
      >
        <Spin spinning={loading}>
          <Form onSubmit={this.handleSubmit}>
            <FormItem
              {...formItemLayout}
              label="标识类型"
            >
              {getFieldDecorator('type', {
                rules: [{
                  required: true, message: '不能为空',
                }],
                initialValue: data.type || 1,
              })(
                <RadioGroup disabled={!!data.id}>
                  <Radio value={1}>自动生成标识</Radio>
                  <Radio value={2}>手动选择标识</Radio>
                </RadioGroup>
              )}
            </FormItem>
            <FormItem
              {...formItemLayout}
              label="标识名称"
            >
              {getFieldDecorator('name', {
                rules: [{
                  required: true, message: '不能为空',
                }, {
                  max: 4, message: '不能超过4个字符',
                }],
                initialValue: data.name,
              })(
                <Input
                  placeholder="请输入"
                  style={{ width: 250 }}
                  disabled={!!data.id}
                />
              )}
            </FormItem>
            <FormItem
              {...formItemLayout}
              label="应用公告类型"
            >
              {getFieldDecorator('announcementTypes', {
                rules: [{
                  required: true, message: '不能为空',
                }],
                initialValue: data.announcementTypes,
              })(
                <TreeSelect
                  className="tree"
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  showCheckedStrategy={SHOW_CHILD}
                  treeData={annTypes}
                  treeCheckable
                  searchPlaceholder="请选择"
                  treeNodeFilterProp="title"
                  style={{ width: 250 }}
                  dropdownStyle={{ maxHeight: 300 }}
                />
              )}
            </FormItem>
            <FormItem
              {...formItemLayout}
              label="应用区划"
            >
              {getFieldDecorator('distCodes', {
                rules: [{
                  required: true, message: '不能为空',
                }],
                initialValue: data.distCodes,
              })(
                <TreeSelect
                  className="tree"
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  showCheckedStrategy={SHOW_CHILD}
                  treeData={districtData}
                  treeCheckable
                  searchPlaceholder="请选择"
                  treeNodeFilterProp="title"
                  style={{ width: 250 }}
                  dropdownStyle={{ maxHeight: 300 }}
                />
              )}
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}

export default addMarking;
