import { request } from 'do<PERSON>mon';

// 公告标识列表
export async function pagingIdentification(data) {
  return request('/announcement/api/pagingIdentification', {
    method: 'POST',
    data,
  });
}

// 所有公告类型
export async function listAnnouncementTypes() {
  return request('/announcement/relation/listRelatedAnnTypes', {
    method: 'GET',
  });
}

// 区划
export async function getDistTree(params) {
  return request('/announcement/api/getDistTree', {
    method: 'GET',
    params,
  });
}

// 开启/关闭公告标识
export async function switchIdentification(data) {
  return request('/announcement/api/switchIdentification', {
    method: 'POST',
    data,
  });
}

// 新增公告标识
export async function addIdentification(data) {
  return request('/announcement/api/addIdentification', {
    method: 'POST',
    data,
  });
}

// 删除公告标识
export async function deleteIdentification(data) {
  return request('/announcement/api/deleteIdentification', {
    method: 'POST',
    data,
  });
}

// 编辑公告标识
export async function modifyIdentification(data) {
  return request('/announcement/api/modifyIdentification', {
    method: 'POST',
    data,
  });
}
