import { request } from 'doraemon';
import moment from 'moment';

export async function reqList(params) {
  const urlMap = {
    base: '/announcement/pushStatistics/pushStatisticsBase',
    district: '/announcement/pushStatistics/pushStatisticsByProvince',
    type: '/announcement/pushStatistics/pushStatisticsByAnnType',
  };
  const { date, time, tab, ...rest } = params;
  const t = tab === 'base' ? date : [time, time];
  return request(urlMap[tab], {
    params: {
      ...rest,
      startDate: moment(t[0]).startOf('day').format('YYYY-MM-DD'),
      endDate: moment(t[1]).endOf('day').format('YYYY-MM-DD'),
    },
  });
}

