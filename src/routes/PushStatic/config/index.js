import React from 'react';
import {
  Select,
  DatePicker,
} from 'doraemon';
import {
  bizDateFormat,
  bizStringFormat,
} from '@zcy/utils';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 获得搜索项配置
export const getCustomItem = (webSites, tab) => {
  return [
    {
      label: '推送网站',
      id: 'targetKey',
      render: () => (
        <Select>
          {webSites.map(site => <Option key={site.code} value={site.code}>{site.name}</Option>)}
        </Select>
      ),
    },
    ...(tab === 'base' ? [{
      label: '时间',
      id: 'date',
      render: () => <RangePicker />,
    }] : []),
    ...(tab !== 'base' ? [{
      label: '时间',
      id: 'time',
      render: () => <DatePicker />,
    }] : []),
  ];
};

// 获得表格配置项
export const getTableConfig = (component, tab) => {
  const { props } = component;
  const { list = [], tableQueryParams = {} } = props;

  const getColumns = () => {
    return [
      {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
        width: 300,
        render: text => (
          bizDateFormat(text)
        ),
      },
      ...(tab === 'district' ? [{
        title: '区划',
        dataIndex: 'provinceName',
        key: 'provinceName',
        width: 300,
        render: text => (
          bizStringFormat(text)
        ),
      }] : []),
      ...(tab === 'type' ? [{
        title: '公告类型',
        dataIndex: 'announcementTypeName',
        key: 'announcementTypeName',
        width: 300,
        render: text => (
          bizStringFormat(text)
        ),
      }] : []),
      {
        title: '推送公告数量',
        dataIndex: 'totalNum',
        key: 'totalNum',
        width: 300,
        render: text => (
          bizStringFormat(text)
        ),
      },
      {
        title: '推送成功数量',
        dataIndex: 'successNum',
        key: 'successNum',
        width: 300,
        render: text => (
          bizStringFormat(text)
        ),
      },
      {
        title: '推送失败率',
        dataIndex: 'failureRate',
        key: 'failureRate',
        width: 300,
        render: text => (
          bizStringFormat(text)
        ),
      },
      {
        title: '操作',
        dataIndex: 'handle',
        key: 'handle',
        width: 100,
        render: (text, record) => {
          const search = [
            `targetKey=${tableQueryParams.targetKey}`,
            `date=${record.date}`,
          ];
          if (record.provinceCode) {
            search.push(`districtPrefix=${record.provinceCode}`);
          }
          if (record.announcementType) {
            search.push(`announcementType=${record.announcementType}`);
          }
          return <a href={`#/push-error/list?${search.join('&')}`}>失败详情</a>;
        },
      },
    ];
  };

  const columns = getColumns();

  // const { data = [], total } = list;

  return {
    columns,
    dataSource: list,
    pagination: false,
    rowKey: 'id',
  };
};
