import React, { Component } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal,
  message,
  request,
} from 'do<PERSON>mon';
import { connect } from 'dva';
import { getTableConfig, getCustomItem } from '../config';

import './index.less';

const { confirm } = Modal;

const ROUTES = [
  {
    label: '业务查询',
  },
  {
    label: '公告推送查询',
  },
];
const tabList = [
  {
    label: '基础数据',
    key: 'base',
  },
  {
    label: '区划统计',
    key: 'district',
  },
  {
    label: '公告类型统计',
    key: 'type',
  },
];

@connect(({ loading, pushStatic }) => ({
  tableQueryParams: pushStatic.tableQueryParams,
  list: pushStatic.list,
  listLoading: loading.effects['pushStatic/fetchList'],
}))


export default class PushStatic extends Component {
  state = {
    webSites: [],
  }
  // 载入页面时,请求数据
  componentDidMount() {
    this.getWebSites();
    const { tableQueryParams } = this.props;
    const { date, time, targetKey, tab } = tableQueryParams;
    if (targetKey && ((tab === 'base' && Array.isArray(date)) || (tab !== 'base' && time))) {
      this.loadList();
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'pushStatic/update',
      payload: {
        list: [],
      },
    });
  }
  getWebSites = async () => {
    const res = await request('/announcement/pushStatistics/listTarget');
    this.setState({
      webSites: res.result,
    });
  }

  // 加载列表数据
  loadList = () => {
    const { tableQueryParams } = this.props;
    this.fetchList(tableQueryParams);
  }

  // 请求列表数据
  fetchList = (params) => {
    const { date, time, targetKey, tab } = params;
    if (!targetKey) {
      message.destroy();
      message.error('请选择推送网站');
      return;
    }
    if (!(Array.isArray(date) && date.length) && tab === 'base') {
      message.destroy();
      message.error('请选择时间');
      this.props.dispatch({
        type: 'pushStatic/update',
        payload: {
          list: [],
        },
      });
      return;
    }
    if (tab !== 'base' && !time) {
      message.destroy();
      message.error('请选择时间');
      this.props.dispatch({
        type: 'pushStatic/update',
        payload: {
          list: [],
        },
      });
      return;
    }
    this.props.dispatch({
      type: 'pushStatic/fetchList',
      payload: {
        ...params,
        tab: tab || tabList[0].key,
      },
    });
  }

  // 搜索
  onSearch = (params) => {
    const { tableQueryParams } = this.props;
    delete params.pageNo;
    delete params.pageSize;
    if (tableQueryParams.tab !== params.tab &&
      tableQueryParams.tab &&
      [tableQueryParams.tab, params.tab].includes('base')
    ) {
      delete params.date;
      delete params.time;
    }
    this.props.dispatch({
      type: 'pushStatic/update',
      payload: {
        tableQueryParams: {
          ...params,
        },
      },
    });
    this.fetchList(params);
  }


  // 删除
  handleDelete = () => {
    message.destroy();
    confirm({
      title: '正在操作删除,是否继续操作？',
      onOk: () => {
        message.success('删除成功');
        this.loadList();
      },
    });
  }
  render() {
    const { webSites } = this.state;
    const { listLoading, tableQueryParams } = this.props;
    // initSearchParams 搜索项默认回填参数
    const { tab, ...initSearchParams } = tableQueryParams;
    const activeKey = tab || tabList[0].key;
    const customItem = getCustomItem(webSites, activeKey);
    const tabs = {
      defaultActiveKey: activeKey,
      activeKey,
      tabList,
    };
    const table = getTableConfig(this, activeKey);
    return (
      <ZcySpin spinning={!!listLoading}>
        <ZcyBreadcrumb
          routes={ROUTES}
        />
        <ZcyList
          tabs={tabs}
          tabKey="tab"
          table={table}
          customItem={customItem}
          onSearch={this.onSearch}
          initSearchParams={initSearchParams}
        />
      </ZcySpin>
    );
  }
}
