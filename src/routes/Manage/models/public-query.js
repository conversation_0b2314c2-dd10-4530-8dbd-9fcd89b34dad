import { handleReqError } from 'src/utils/utils';
import _ from 'lodash';
import {
  fetchPublicAnnListApi,
  getDistTree,
  fetchAnnTypesApi,
  listValidTagConfig,
  fetchPublishOrgListApi,
  newFetchList,
  myAnnDataConfig,
} from '../services';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';
import { formatTreeData } from 'src/utils/tree';

// 处理树形数据，使其符合组件要求
function resolveTree(tree) {
  const newTree = [];
  tree.forEach((item) => {
    newTree.push({
      title: item.text,
      value: item.code,
      children: (item.children && Array.isArray(item.children) && item.children.length > 0) ?
        resolveTree(item.children) : undefined,
      disabled: !item.leaf,
    });
  });
  return newTree;
}

export default {
  namespace: 'publicQuery',
  state: {
    annListInfo: {},
    PublishOrg: [],
    districtTree: [],
    districtTreeByAuth: [],
    total: 0,
    listParams: {
      pageNo: 1,
      pageSize: 10,
    },
    annTypes: [],
    tagTypes: [],
    publishOrgList: [],
  },

  effects: {
    *fetchList({ payload }, { call, put }) {
      const response = yield call(fetchPublicAnnListApi, payload);
      if (response && response.success) {
        // 设置列表信息
        yield put({
          type: 'setAnnList',
          payload: response,
        });
      } else {
        handleReqError({
          type: 'warning',
          message: response.error,
        });
      }
      return response;
    },
    *newFetchList({ payload }, { call, put }) {
      const response = yield call(newFetchList, payload);
      if (response && response.success) {
        // 设置列表信息
        yield put({
          type: 'setAnnList',
          payload: response,
        });
      } else {
        handleReqError({
          type: 'warning',
          message: response.error,
        });
      }
      return response;
    },

    // 获取区划列表
    *fetchDistrictTree({ payload }, { call, put }) {
      const response = yield call(getDistTree, payload);
      if (response && response.success) {
        const districtTree = resolveTree(_.get(response, 'result', []) || []);
        yield put({
          type: 'setDistrictTree',
          payload: { districtTree },
        });
      }
      return response;
    },

    // 获取区划列表
    *fetchDistrictTreeByAuth({ payload }, { call, put }) {
      const response = yield call(getAuthorityDistTreeGet, payload);
      if (response && response.success) {
        const districtTreeByAuth = formatTreeData(_.get(response, 'result', []) || []);
        yield put({
          type: 'setDistrictTreeByAuth',
          payload: { districtTreeByAuth },
        });
      }
      return response;
    },

    // 获取公告类型
    *fetchAnnTypes({ payload }, { call, put }) {
      const response = yield call(fetchAnnTypesApi, payload);
      yield put({
        type: 'setAnnTypes',
        payload: response,
      });
      return response;
    },

    *fetchTagTypes({ payload }, { call, put }) {
      const response = yield call(listValidTagConfig, payload);
      yield put({
        type: 'setTagTypes',
        payload: response,
      });
      return response;
    },

    *myAnnDataConfig({ payload }, { call }) {
      const response = yield call(myAnnDataConfig, payload);
      return response;
    },

    // 获取发布单位
    *fetchPublishOrgList({ payload }, { call, put }) {
      const response = yield call(fetchPublishOrgListApi, payload);
      yield put({
        type: 'setPublishOrgList',
        payload: response,
      });
      return response;
    },
  },

  reducers: {
    setAnnList(state, { payload }) {
      const { result = {} } = payload || {};
      return {
        ...state,
        annListInfo: result || {},
        // listParams: {
        //   pageNo: result.pageNo,
        //   pageSize: result.pageSize,
        // },
      };
    },
    setDistrictTree(state, { payload }) {
      return {
        ...state,
        districtTree: payload.districtTree || [],
      };
    },
    setDistrictTreeByAuth(state, { payload }) {
      return {
        ...state,
        districtTreeByAuth: payload.districtTreeByAuth || [],
      };
    },
    setAnnTypes(state, { payload }) {
      const { result = [] } = payload || [];
      return {
        ...state,
        annTypes: result || [],
      };
    },
    setTagTypes(state, { payload }) {
      const { result = [] } = payload || [];
      return {
        ...state,
        tagTypes: result || [],
      };
    },
    setPublishOrgList(state, { payload }) {
      const { result = [] } = payload || [];
      return {
        ...state,
        publishOrgList: result || [],
      };
    },
    saveSearchParams(state, { payload }) {
      return {
        ...state,
        listParams: payload,
      };
    },

  },
};
