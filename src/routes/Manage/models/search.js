import { projectAnnouncementQuery } from '../services';

export default {
  namespace: 'search',
  state: {
    projectAnnList: {
      total: 0,
      data: [],
    },
  },

  effects: {
    // 公告查询接口(按项目查询)
    * projectAnnouncementQuery({ payload }, { call, put }) {
      const res = yield call(projectAnnouncementQuery, payload);
      yield put({
        type: 'setProjectAnnsList',
        payload: res,
      });
      return res;
    },
  },

  reducers: {
    setProjectAnnsList(state, action) {
      return {
        ...state,
        projectAnnList: action.payload.result,
      };
    },
  },
};
