import { queryAnnTypeByAnnBigType } from '../services';

export default {
  namespace: 'add',
  state: {
    AnnType: [],
    richTextModalVisible: false,
  },
  effects: {
    * getAnnTypeByAnnBigType({ payload }, { call, put }) {
      const res = yield call(queryAnnTypeByAnnBigType, payload);
      yield put({
        type: 'setAnnTypeByAnnBigType',
        payload: res,
      });
      return res;
    },
    // modal 显示隐藏
    * setModalVisible({ payload }, { put }) {
      yield put({
        type: 'saveModalVisible',
        payload,
      });
    },
  },
  reducers: {
    setAnnTypeByAnnBigType(state, action) {
      return {
        ...state,
        AnnType: action.payload.result,
      };
    },
    saveModalVisible(state, action) {
      return {
        ...state,
        [action.payload.type]: action.payload.visible,
      };
    },
  },
};
