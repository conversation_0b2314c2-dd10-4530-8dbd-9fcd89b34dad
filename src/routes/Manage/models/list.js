import {
  queryList,
  queryAnnTypeByAnnBigType,
  getAnnouncementList,
  projectList,
  getIdentifications,
  syncMetadata,
  getCanRevokeList,
  listSensitiveCensor,
  printAnnouncementContent,
  raiseObjection,
  fetchAnnounceBasicInfoApi,
  operObjection,
  rePushApi,
  getUnitApi,
  getUserApi,
  getAnnounceMentListDistTree,
} from '../services';
import { changeSourceTree } from '../../../utils/tree';
import _ from 'lodash';

export default {
  namespace: 'list',

  state: {
    districtsTree: [],
    units: [],
    data: [],
    categoryModalVisible: false, // visible：新建公告-选择公告类别modal
    projectModalVisibel: false, // visible：新建公告-选择已有项目modal
    selectedProject: null, // 已选项目
    annType: [], // 公告类型
    announcementList: {
      total: 0,
      data: [],
    }, // 公告列表数据
    params: {
      pageNo: 1,
      pageSize: 10,
      type: '1',
    }, // 暂存列表页搜索条件，用于返回时恢复
    projectData: {
      data: [],
      total: 0,
    }, // 项目列表
    annBasicInfo: {},
  },

  effects: {
    * getCollect({ payload }, { call, put }) {
      const response = yield call(queryList, payload);
      yield put({
        type: 'queryList',
        payload: response,
      });
    },
    // modal 显示隐藏
    * setModalVisible({ payload }, { put }) {
      yield put({
        type: 'saveModalVisible',
        payload,
      });
    },
    // 设置已选项目
    * setSelectedProject({ payload }, { put }) {
      yield put({
        type: 'saveSelectedProject',
        payload,
      });
    },
    // 根据大分类获取公告类型
    * getAnnTypeByAnnBigType({ payload }, { call, put }) {
      const res = yield call(queryAnnTypeByAnnBigType, payload);
      yield put({
        type: 'setAnnTypeByAnnBigType',
        payload: res,
      });
      return res;
    },
    // 获取我的待办
    * getAnnouncementList({ payload }, { call, put }) {
      const res = yield call(getAnnouncementList, payload);
      if (res.success) {
        yield put({
          type: 'setAnnouncementList',
          payload: res,
        });
      }
      return res;
    },
    * setParams({ payload }, { put }) {
      yield put({
        type: 'saveParams',
        payload,
      });
    },
    * projectList({ payload }, { call, put }) {
      const res = yield call(projectList, payload);
      if (!res.success) return;
      const data = res.result;
      yield put({
        type: 'saveProjectList',
        payload: data,
      });
      return data;
    },
    * getIdentifications({ payload }, action) {
      const response = yield action.call(getIdentifications, payload);
      return response;
    },
    * syncMetadata({ payload }, action) {
      const response = yield action.call(syncMetadata, payload);
      return response;
    },
    // 用户信息
    * getUser({ payload }, { call }) {
      const res = yield call(getUserApi, payload) || {};
      return res;
    },
    // 发布单位数据
    * getUnit({ payload }, { put, call }) {
      const { result = [] } = yield call(getUnitApi, payload) || {};
      // 去重
      const units = _.uniqBy(result, 'id').slice(0, 30);
      yield put({
        type: 'setUnit',
        payload: { units },
      });
      return units;
    },
    * getCanRevokeList({ payload }, action) {
      const response = yield action.call(getCanRevokeList, payload);
      return response;
    },
    * operObjection({ payload }, action) {
      const response = yield action.call(operObjection, payload);
      return response;
    },
    /**
     * 获取文案内的敏感词
     */
    * listSensitiveCensor({ payload }, action) {
      const response = yield action.call(listSensitiveCensor, payload);
      return response;
    },
    * printAnnouncementContent({ payload }, action) {
      const response = yield action.call(printAnnouncementContent, payload);
      return response;
    },
    // 行政区划数据
    * getDistricts({ payload }, { put, call }) {
      const response = yield call(getAnnounceMentListDistTree, payload) || {};
      const sourceTree = response.result || [];
      const districtsTree = changeSourceTree(sourceTree);
      yield put({
        type: 'setDistricts',
        payload: { districtsTree },
      });
      return districtsTree;
    },
    * raiseObjection({ payload }, action) {
      const response = yield action.call(raiseObjection, payload);
      return response;
    },
    * rePush({ payload }, action) {
      const response = yield action.call(rePushApi, payload);
      return response;
    },
    // 获取含推送情况的公告信息
    *fetchAnnBasicInfo({ payload }, { call, put }) {
      const response = yield call(fetchAnnounceBasicInfoApi, payload);
      yield put({
        type: 'setAnnBasicInfo',
        payload: response,
      });
      return response;
    },
  },

  reducers: {
    update(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    queryList(state, action) {
      return {
        ...state,
        data: action.payload || [],
      };
    },
    saveModalVisible(state, action) {
      return {
        ...state,
        [action.payload.type]: action.payload.visible,
      };
    },
    saveSelectedProject(state, action) {
      return {
        ...state,
        selectedProject: action.payload.selectedProject,
      };
    },
    setAnnTypeByAnnBigType(state, action) {
      return {
        ...state,
        annType: action.payload.result || [],
      };
    },
    setAnnouncementList(state, action) {
      return {
        ...state,
        announcementList: action.payload.result || {
          total: 0,
          data: [],
        },
      };
    },
    saveParams(state, action) {
      return {
        ...state,
        params: action.payload,
      };
    },
    setDistricts(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveProjectList(state, action) {
      return {
        ...state,
        projectData: action.payload || {
          data: [],
          total: 0,
        },
      };
    },

    setAnnBasicInfo(state, { payload }) {
      const { result = {} } = payload;
      return {
        ...state,
        annBasicInfo: result,
      };
    },

    setUnit(state, { payload: { units = [] } }) {
      // 去重
      const newUnits = _.uniqBy(units, 'id');
      return {
        ...state,
        units: newUnits,
      };
    },
    
  },
};
