import React from 'react';
import { Link } from 'dva/router';
import { fixNull } from 'src/utils/utils';
import _isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import { renderTags, TAG_SOURCE_TYPE_MAP } from '../config/custom-tag';
import OutUrlContent from 'components/OutUrlContent';
import { getPubType } from 'utils/utils';
import { Badge, Tag } from 'doraemon';


/**
 * @param start 起始时间
 * @param end 结束时间
 * @param type 类型(1:title, 2:文案)
 */
const getAnnTime = (start, end, type) => {
  if (!start && !end) {
    return type === 2 ? '-' : '';
  }
  const annStart = start ? moment(start).format('YYYY-MM-DD') : '*';
  const annEnd = end ? moment(end).format('YYYY-MM-DD') : '*';
  return `${annStart} ~ ${annEnd}`;
};

export const columnConfig = [{
  title: '公告标题',
  dataIndex: 'title',
  key: 'title',
  width: 200,
  render: (text, record) => {
    const { formPageCode, id, district, annBigType } = record || {};
    if (formPageCode) {
      return (
        <div>
          <Link to={`/dynamic/detail?formPageCode=${formPageCode}&annId=${id}&districtId=${district}&annBigType=${annBigType}`} className="public-query-table-ann-title" >{text}</Link>
          <br />
          {
            renderTags(TAG_SOURCE_TYPE_MAP.business,
              record.announcementTagDtoList, { maxWidth: 180 })
          }
        </div>
      );
    }
    return (
      <div>
        <Link to={`/detail/${annBigType}/${id}`} className="public-query-table-ann-title" >{text}</Link>
        <br />
        {
          renderTags(TAG_SOURCE_TYPE_MAP.business,
            record.announcementTagDtoList, { maxWidth: 180 })
        }
      </div>
    );
  },
}, {
  title: '公告信息',
  width: 280,
  dataIndex: 'info',
  key: 'info',
  render: (text, record) => {
    return (
      <React.Fragment>
        <p className="public-query-table-announcement-info" title={record.projectName}>
          <span>项目名称：{fixNull(record.projectName)}</span>
        </p>
        <p className="public-query-table-announcement-info" title={record.projectCode}>
          <span>项目编号：{fixNull(record.projectCode)}</span>
        </p>
        {record.isReformation ? (
            <Tag color="#CCA97D"
              style={{
                maxWidth: 180,
                verticalAlign: 'bottom',
                cursor: 'default',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
              }}
            >{record.isReformationDesc}
            </Tag>
          ) : null}
        <p className="public-query-table-announcement-info" title={record.amount}>
          <span>项目总额：{fixNull(record.amount)}</span>
        </p>
        <p className="public-query-table-announcement-info" title={record.creatorOrgName}>
          <span>发布单位：{fixNull(record.creatorOrgName)}</span>
        </p>
        <p className="public-query-table-announcement-info" title={getAnnTime(record.releasedAt, record.expiredAt, 1)}>
          <span>公示时间：{getAnnTime(record.releasedAt, record.expiredAt, 2)}</span>
        </p>
      </React.Fragment>
    );
  },
}, {
  title: '公告类型',
  width: 100,
  dataIndex: 'announcementTypeName',
  key: 'announcementTypeName',
}, {
  title: '发布方式',
  dataIndex: 'pubType',
  width: 120,
  render: (text) => {
    return getPubType(text);
  },
}, {
  title: '发布时间',
  width: 140,
  dataIndex: 'releasedAt',
  key: 'releasedAt',
  render: (text) => {
    return moment(text).format('YYYY-MM-DD HH:mm');
  },
}, {
  title: '发布单位',
  width: 120,
  dataIndex: 'creatorOrgName',
  key: 'creatorOrgName',
}, {
  title: '发布人',
  width: 96,
  dataIndex: 'creatorName',
  key: 'creatorName',
}, {
  title: '状态',
  width: 96,
  dataIndex: 'statusName',
  key: 'statusName',
  render: (text, record) => {
    let badgeStatus = 'default';
    if ([0, 1, 2].indexOf(record.status) !== -1) {
      badgeStatus = 'processing';
    }
    if ([3].indexOf(record.status) !== -1) {
      badgeStatus = 'warning';
    }
    if ([4, 5].indexOf(record.status) !== -1) {
      badgeStatus = 'success';
    }
    if ([9].indexOf(record.status) !== -1) {
      badgeStatus = 'default';
    }
    return (
      <div>
        <Badge status={badgeStatus} text={text} />
        <br />
        {renderTags(TAG_SOURCE_TYPE_MAP.system, record.announcementTagDtoList)}
      </div>
    );
  },
}, {
  title: '来源',
  width: 96,
  dataIndex: 'annSourceDesc',
  key: 'annSourceDesc',
}, {
  title: '外网推送情况',
  width: 120,
  dataIndex: 'pushDetails',
  key: 'pushDetails',
  render: (text, record) => {
    return !_isEmpty(record.pushDetails) ? 
      <OutUrlContent hideDivider placement="left" list={record.pushDetails.map(ele => ({ ...ele, outName: ele.targetName }))} />
      : '-';
  },
}];
