import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'doraemon';
import { Link } from 'dva/router';
import { fixNull } from 'src/utils/utils';
import _isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import { renderTags, TAG_SOURCE_TYPE_MAP } from '../config/custom-tag';
import OutUrlContent from '../../../components/OutUrlContent';
import { getPubType } from '../../../utils/utils';

const getAnnTime = (start, end, type) => {
  if (!start && !end) {
    return type === 2 ? '-' : '';
  }
  const annStart = start ? moment(start).format('YYYY-MM-DD') : '*';
  const annEnd = end ? moment(end).format('YYYY-MM-DD') : '*';
  return `${annStart} ~ ${annEnd}`;
};

export const columnConfig = ({ openPullAnn, rePush<PERSON>nn, reEditAnn }) => [
  {
    title: '公告标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    render: (text, record) => {
      const { formPageCode, id, district, annBigType } = record || {};
      if (formPageCode) {
        return (
          <div>
            <Link
              to={`/dynamic/detail?formPageCode=${formPageCode}&annId=${id}&districtId=${district}&annBigType=${annBigType}`}
              className="public-query-table-ann-title"
            >
              {text}
            </Link>
            <br />
            {renderTags(
              TAG_SOURCE_TYPE_MAP.business,
              record.announcementTagDtoList,
              { maxWidth: 180 }
            )}
          </div>
        );
      }
      return (
        <div>
          <Link
            to={`/detail/${annBigType}/${id}`}
            className="public-query-table-ann-title"
          >
            {text}
          </Link>
          <br />
          {renderTags(
            TAG_SOURCE_TYPE_MAP.business,
            record.announcementTagDtoList,
            { maxWidth: 180 }
          )}
        </div>
      );
    },
  },
  {
    title: '公告信息',
    width: 280,
    dataIndex: 'info',
    key: 'info',
    render: (text, record) => {
      return (
        <React.Fragment>
          <p
            className="public-query-table-announcement-info"
            title={record.projectName}
          >
            <span>项目名称：{fixNull(record.projectName)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.projectCode}
          >
            <span>项目编号：{fixNull(record.projectCode)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.amount}
          >
            <span>项目金额：{fixNull(record.amount)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.announcementTypeName}
          >
            <span>公告类型：{fixNull(record.announcementTypeName)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.annSourceDesc}
          >
            <span>公告来源：{fixNull(record.annSourceDesc)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.creatorName}
          >
            <span>创建人：{fixNull(record.creatorName)}</span>
          </p>
        </React.Fragment>
      );
    },
  },
  {
    title: '发布信息',
    dataIndex: 'pubType',
    width: 160,
    render: (text, record) => {
      return (
        <>
          <p
            className="public-query-table-announcement-info"
            title={record.releasedAt ? moment(record.releasedAt).format('YYYY-MM-DD') : '-'}
          >
            <span>发布时间：{record.releasedAt ? moment(record.releasedAt).format('YYYY-MM-DD') : '-'}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={getPubType(record.pubType)}
          >
            <span>发布方式：{getPubType(record.pubType)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.creatorOrgDistrictName || '-'}
          >
            <span>发布区划：{record.creatorOrgDistrictName || '-'}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.creatorOrgName}
          >
            <span>发布单位：{record.creatorOrgName}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={getAnnTime(record.releasedAt, record.expiredAt, 2)}
          >
            <span>公示期：{getAnnTime(record.releasedAt, record.expiredAt, 2)}</span>
          </p>
          <p
            className="public-query-table-announcement-info"
            title={record.creatorName}
          >
            <span>发布人：{record.creatorName}</span>
          </p>
        </>
      );
    },
  },
  {
    title: '推送时间',
    width: 96,
    dataIndex: 'publishTime',
    key: 'publishTime',
    render: (text, record) => {
      return (
        <span>
          {record.publishTime ? moment(record.publishTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </span>
      );
    },
  },
  {
    title: '公告状态',
    width: 96,
    dataIndex: 'statusName',
    key: 'statusName',
    render: (text, record) => {
      let badgeStatus;
      badgeStatus = 'processing';
      if ([3].indexOf(record.status) !== -1) {
        badgeStatus = 'warning';
      }
      if ([4, 5].indexOf(record.status) !== -1) {
        badgeStatus = 'success';
      }
      return (
        <div>
          <Badge status={badgeStatus} text={text} />
          <br />
          {renderTags(
            TAG_SOURCE_TYPE_MAP.system,
            record.announcementTagDtoList
          )}
        </div>
      );
    },
  },
  {
    title: '操作',
    width: 120,
    dataIndex: 'pushDetails',
    key: 'pushDetails',
    render: (text, record) => {
      return (
        <>
          {
            !_isEmpty(record.pushDetails) ? (
              <OutUrlContent
                hideDivider
                placement="left"
                list={record.pushDetails.map((ele) => ({
                  ...ele,
                  outName: ele.targetName,
                }))}
              />
            ) : ''
          }
          <Button type="text" onClick={() => openPullAnn(record.id)}>下架</Button>
          {
            record?.canDataCorrection ? <Button type="text" onClick={() => reEditAnn(record.id)}>数据订正</Button> : null
          }
          <Button type="text" onClick={() => rePushAnn(record.id)}>重新推送</Button>
        </>
      );
    },
  },
];
