import React from 'react';
import { Tag, Tooltip } from 'doraemon';

export const TAG_SOURCE_TYPE_MAP = {
  // 标签来源类型 0-系统标签(A类)(状态) 1-业务标签(B类)(标题)
  system: 0,
  business: 1,
};


export const renderTags = (type, tags = [], style = {}) => {
  if (!tags) return;
  const filterTags = tags.filter(ele => ele.tagSourceType === type);
  if (!filterTags.length) return null;
  const DEFAULT_CSS = {
    verticalAlign: 'baseline',
    marginRight: 4,
    cursor: 'default',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    maxWidth: 100,
    ...style,
  };
  switch (type) {
  case TAG_SOURCE_TYPE_MAP.business:
    return filterTags.map(ele => (
      <Tooltip title={ele.tagDisplay} placement="topLeft">
        {
          ele.isValid ?
            <Tag style={DEFAULT_CSS} color="#2db7f5">{ele.tagDisplay}</Tag> :
            <Tag style={DEFAULT_CSS}>{ele.tagDisplay}</Tag>
        }
      </Tooltip>
    ));
  case TAG_SOURCE_TYPE_MAP.system:
    return filterTags.map(ele => (
      <Tooltip title={ele.tagDisplay} placement="topLeft">
        {
          ele.isValid ?
            <Tag style={{ ...DEFAULT_CSS, background: '#fff', color: '#EA7002', borderColor: '#FCE8D6' }}>{ele.tagDisplay}</Tag> :
            <Tag style={DEFAULT_CSS}>{ele.tagDisplay}</Tag>
        }
      </Tooltip>
    )
    );
  default:
    break;
  }
  return null;
};

