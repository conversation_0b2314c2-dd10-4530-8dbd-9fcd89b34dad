import $ from 'jquery';
import { OPERATOR_STATUS } from 'src/constants';
import Wordexport from '../../../utils/wordexport';
import pushLog from 'utils/pushLog';
import { handleBtnByUiRule } from 'utils/utils';
import tracer from 'src/utils/tracer';
import { downLoadAuditReport } from 'src/components/AuditReport';


export const getGlobalBtn = (_this, annBasicInfo) => {
  const { announcement, dylygsObjection, fileLink,
    printLoading,
    purchaseIntentionAuditReport,
    downLoadAuditReportLoading,
  } = _this.state;
  const { getFileLink } = _this;
  let globalBtn = [];
  const match = OPERATOR_STATUS.find(item => item.key === announcement?.showStatus);
  let downBt = [];

  if (match && match.key === 4) {
    downBt = [{
      label: '下载',
      type: 'primary',
      onClick: () => {
        try {
          tracer({
            utmCD: ['cBreadcrumb', 'dDownload'],
          });
        } catch (err) {
          pushLog(JSON.stringify(err), 'warning');
        }

        try {
          const wordexport = new Wordexport();
          const titleTpl = `<p style="text-align: center;"><span style="font-family: 宋体; font-size: 24px;">${announcement?.title}</span></p>`;
          let content = $(`<div>${announcement?.content}<div>`);
          const blockquotes = content.find('blockquote');
          blockquotes.each((i, bq) => {
            if ($(bq).css('display') === 'none') {
              $(bq).remove();
            }
          });
          content = content.html();
          wordexport.save({
            fileName: announcement?.title,
            content: titleTpl + content,
            href: location.href,
          });
        } catch (error) {
          pushLog(JSON.stringify(error, 'info'));
        }
      },
    }];
  }

  if (_this.props.history.length > 1) {
    globalBtn = [{
      label: '返回',
      onClick: () => {
        _this.props.history.goBack();
      },
    }, ...downBt];
  }

  if ([4, 5].includes(announcement?.status)) {
    globalBtn = [
      ...globalBtn,
      {
        label: '打印',
        type: 'primary',
        loading: printLoading,
        onClick: () => {
          if (fileLink) return window.open(fileLink);
          getFileLink().then((resUrl) => {
            if (resUrl) window.open(resUrl);
          });
        },
      },
    ];
  }

  if (purchaseIntentionAuditReport) {
    globalBtn.push({
      label: '下载审批表',
      type: 'primary',
      loading: downLoadAuditReportLoading,
      onClick: () => {
        _this.setState({
          downLoadAuditReportLoading: true,
        });
        downLoadAuditReport(announcement?.status, _this.announcementId).then((url) => {
          if (url) return window.open(url);
        }).finally(() => {
          _this.setState({
            downLoadAuditReportLoading: false,
          });
        });
      },
    });
  }


  if (annBasicInfo?.isRevocable) {
    globalBtn = [
      ...globalBtn,
      {
        label: annBasicInfo?.revocableButtonName || '取消公告',
        type: 'primary',
        onClick: _this.onCancelAnno,
      },
    ];
  }

  if (dylygsObjection === '1' && announcement?.status === 41) {
    globalBtn.push({
      label: '不通过',
      onClick: _this.noPass,
    });
    globalBtn.push({
      label: '通过公示',
      type: 'primary',
      onClick: _this.passAnn,

    });
  }
  return handleBtnByUiRule(globalBtn);
};
