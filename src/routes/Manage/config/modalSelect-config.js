/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-08-20 20:23:42
 * @FilePath: /zcy-announcement-v2-front/src/routes/Manage/config/modalSelect-config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';

export const ICONS = ['yijianzhengxun1', 'gengzhenggonggao1', 'caigoujieguogonggao1',
  'caigouhetonggongshi', 'lvyueyanshougonggao', 'zhengfucaigoujianguangonggao',
  'caigougonggao', 'caigouhetonggonggao', 'caigoujieguogonggao', 'chakan',
  'zhengfucaigoujianguangonggao1', 'hetong', 'caigoufangshi', 'caigouzuzhixingshi',
  'hetongtiaokuan', 'mobanshichang'];


/**
 *  7003, 投诉处理结果公告
 *  7004, 行政处罚信息公告
 *  7005, 行政处理信息公告
 *  7006, 监督检查处理结果公告
 *  7007, 集中采购机构考核结果公告
 *  7008, 供应商、采购代理机构和评审专家的违法失信行为记录公告
 *  7009, 其他监管公告
 * */  
export const ANNOUNMENTS = [7003, 7004, 7005, 7006, 7007, 7008, 7009, 6003, 3016];


export const tableColumns = (
  [{
    title: '项目编号',
    dataIndex: 'projectNo',
    width: 140,
    render: (text) => {
      return (
        <div className="short-str">
          <span title={text}>{text}</span>
        </div>
      );
    },
  }, {
    title: '项目名称',
    dataIndex: 'projectName',
    render: (text) => {
      return (
        <div className="short-str">
          <span title={text}>{text}</span>
        </div>
      );
    },
  }, {
    title: '采购方式',
    dataIndex: 'purchaseWayName',
    width: 140,
  }, {
    title: '预算金额（元）',
    dataIndex: 'budgetTotal',
    width: 140,
    render(text) {
      return <span className="price">{(text / 100).toFixed(2)}</span>;
    },
  }]
);

export const getRowSelection = dispatch => ({
  type: 'radio',
  onSelect(record, selected, selectedRows) {
    dispatch({
      type: 'list/setSelectedProject',
      payload: {
        selectedProject: selectedRows,
      },
    });
  },
});

