/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-05-31 11:32:29
 * @FilePath: /zcy-announcement-v2-front/src/routes/Manage/config/publicQuery-selectConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import {
  Spin,
} from 'doraemon';
import { debounce } from '@zcy/utils';

const debounceFnMap = {

}; 

export const selectConfig = ({ searchFun, loading }, searchType) => {
  if (!debounceFnMap[searchType]) {
    debounceFnMap[searchType] = debounce(searchFun, 500);
  }
  return {
    allowClear: true, // 捷捷测试
    showSearch: true,
    filterOption: (input, option) =>
      option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    placeholder: '请输入关键字搜索',
    onSearch: debounceFnMap[searchType],
    notFoundContent: loading ? (<Spin style={{ width: '100%' }} size="small" />) : '无匹配的结果',
  };
};
