import React from 'react';
import { Button, Table, message, TreeSelect, Select, DatePicker, Input, Tag, Tooltip, Badge, Modal } from 'doraemon';
import { routerRedux } from 'dva/router';
import TimeCountDown from 'components/TimeCountDown';
import moment from 'moment';
import debounce from 'lodash/debounce';
import { ANN_SOURCE_TYPE, OPERATOR_STATUS } from 'src/constants';
import {
  deleteAnnouncement,
  releaseAnnouncement,
  deleteFormAnnouncement,
} from '../services';
import { selectConfig } from './publicQuery-selectConfig';
import { renderTags, TAG_SOURCE_TYPE_MAP } from './custom-tag'
import OutUrlContent from 'components/OutUrlContent'
import { getPubType } from 'utils/utils';
import './custom-item.less';

const Option = Select.Option;

const confirm = Modal.confirm;

const Actions = Table.Actions;

const RangePicker = DatePicker.RangePicker;

const getProjectInfoSearch = showProjectSearch => (
  showProjectSearch
    ?
    [
      {
        label: '项目名称',
        id: 'projectName',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      }, {
        label: '项目编号',
        id: 'projectCode',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
    ]
    :
    []
);

const getDistrictSearchItem = ({
  units = [], districtsTree, handleFetch, annBigType, isRegulatoryRole, loadingFetch,
}) => {
  // 是否意见征询公告
  const isOptions = Number(annBigType) === 1;
  const items = [];

  if (isRegulatoryRole) {
    items.push(
      {
        label: '发布区划',
        id: 'district',
        render: () => {
          return (
            <TreeSelect
              showSearch
              placeholder="请选择"
              allowClear
              treeNodeFilterProp="title"
              treeData={districtsTree}
            />
          );
        },
      }
    )
  }

  if (isOptions && isRegulatoryRole) {
    items.push(
      {
        label: '发布单位',
        id: 'orgIds',
        render: () => {
          return (
            <Select
              {
              ...selectConfig({
                searchFun: debounce(handleFetch, 200),
                loading: loadingFetch,
              }, 'orgIds')
              }
            >
              {
                units?.map(vNode => (
                  <Option key={vNode.code} value={vNode.code}>{vNode.name}</Option>
                ))
              }
            </Select>
          );
        },
      }
    );
  }
  items.push({
    label: '关键字',
    id: 'contentKeyWord',
    render: () => <Input placeholder="请输入" />,
  });

  items.push({
    label: '创建人',
    id: 'creatorName',
    render: () => <Input placeholder="请输入" />,
  });
  
  return items;
};


export const getCustomItem = ({
  units, districtsTree,
  handleFetch, annBigType,
  isRegulatoryRole, annType,
  loadingFetch, showProjectSearch,
}) => ([{
  label: '公告类型',
  id: 'announcementType',
  decoratorOptions: {
    initialValue: '',
  },
  render: () => {
    return (
      <Select placeholder="请选择" showSearch optionFilterProp="name" allowClear={false}>
        <Select.Option value="">全部</Select.Option>
        {
          annType.map((item) => {
            return (
              <Select.Option name={item.name} value={item.typeId} key={item.typeId}>{item.name}</Select.Option>
            );
          })
        }
      </Select>
    );
  },
}, {
  label: '公告标题',
  id: 'title',
  render: () => {
    return <Input placeholder="请输入" />;
  },
}, ...getProjectInfoSearch(showProjectSearch), {
  label: '发布时间',
  id: 'date',
  render: () => {
    return (
      <DatePicker.RangePicker
        showTime={{
          defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
        }}
      />
    );
  },
}, {
  label: '状态',
  id: 'queryStatus',
  decoratorOptions: {
    initialValue: '',
  },
  render: () => {
    return (
      <Select placeholder="请选择">
        <Select.Option value="">全部</Select.Option>
        {OPERATOR_STATUS.map(item => (
          <Select.Option value={item.key} key={item.key}>{item.value}</Select.Option>
        ))}
      </Select>
    );
  },
}, ...getDistrictSearchItem({
  units, districtsTree, handleFetch, annBigType, isRegulatoryRole, loadingFetch,
})]);

export const getTableCustom = _this => ([{
  title: '公告标题',
  dataIndex: 'title',
  width: 200,
  render: (text, item) => {
    const { objectionState } = item;
    let objStr;
    if (objectionState === 0) {
      objStr = '无异议';
    }
    if (objectionState === 1) {
      objStr = '有异议';
    }
    let identiNames = [];
    if (item.metaData) {
      const metaData = JSON.parse(item.metaData);
      identiNames = metaData.identiNames || [];
    }
    identiNames = identiNames.join('，');
    return (
      <div className="ann-title">
        <span className="short-str">
          {
            identiNames.length > 0 && <Tag color="green" title={identiNames}>{identiNames}</Tag>
          }
          {
            objStr ? <span>[{objStr}]&nbsp;</span> : ''
          }
          <Tooltip title={text} placement="topLeft">
            <a
              className='wrap-text'
              onClick={() => {
                _this.detail(item);
              }}
            >
              {text}
            </a>
          </Tooltip>
        </span>
        {renderTags(TAG_SOURCE_TYPE_MAP.business, item.announcementTagDtoList, { maxWidth: 180 })}
      </div>
    );
  },
}, {
  title: '公告类型',
  dataIndex: 'announcementTypeName',
  width: 145,
  render: (text) => {
    return (
      <div className="short-str">
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      </div>
    );
  },
}, _this?.state?.showProjectSearch && {
  title: '项目信息',
  dataIndex: 'projectName',
  width: 200,
  render: (text, record) => {
    const { projectCode } = record;
    const projectInfo = (
      <div>
        <p className="line-clamp-3">
          <span className='config-content-text'> 项目编号：{projectCode || '-'}</span>    
          {record.isReformation ? (
            <Tag color="#CCA97D"
              style={{
                maxWidth: 180,
                verticalAlign: 'bottom',
                cursor: 'default',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
              }}
            >{record.isReformationDesc}
            </Tag>
          ) : null}
        </p>
        <p className="line-clamp-3">
          项目名称：{text || '-'}
        </p>
      </div>
    );
    return (
      <div>
        <Tooltip title={projectInfo}>
          {projectInfo}
        </Tooltip>
      </div>
    );
  },
}, {
  title: '发布方式',
  dataIndex: 'pubType',
  width: 120,
  render: (text) => {
    return getPubType(text)
  }
}, {
  title: '时间',
  dataIndex: 'releasedAt',
  width: 140,
  render: (text, record) => {
    // 去掉采购意向公告创建时间展示
    const { expiredAt, announcementType = '' } = record;
    const timeInfo = (
      <div>
        <p>
          <span>公告发布时间：</span><br />
          {text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'}
        </p>
        {announcementType === 10016 ? '' : (
          <p>
            <span>公告截止时间：</span><br />
            {expiredAt ? moment(expiredAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </p>
        )}
      </div>
    );
    return (
      <div>
        <Tooltip title={timeInfo}>
          {timeInfo}
        </Tooltip>
      </div>
    );
  },
}, 
{
  title: '创建人',
  dataIndex: 'creatorName',
  width: 120,
  render: (text) => {
    return (
      <span>{text || '-'}</span>
    )
  }
},
{
  title: '状态',
  dataIndex: 'showStatus',
  width: 90,
  render: (key, item, i) => {
    let badgeStatus;
    if ([0, 1, 2, 41, 8].indexOf(key) !== -1) {
      badgeStatus = 'processing';
    }
    if ([3].indexOf(key) !== -1) {
      badgeStatus = 'warning';
    }
    if ([4, 5].indexOf(key) !== -1) {
      badgeStatus = 'success';
    }
    if ([7, 9].indexOf(key) !== -1) {
      badgeStatus = 'default';
    }
    if (key !== null) {
      let text = OPERATOR_STATUS.find(s => s.key === key).value;
      // 审批
      if (key === 1 && item?.isTransmit) {
        text = '转发中'
      }
      return (
        <div>
          <Badge status={badgeStatus} text={text} />
          {(item.canRevokePublishSeconds > 0 && !_this.state.hideRevertBtn[`revertBtn${i}`]) && (
            <TimeCountDown
              time={(item.canRevokePublishSeconds || 0) * 1000}
              cb={() => {
                _this.setState((state) => {
                  const { hideRevertBtn: btns } = state;
                  btns[`revertBtn${i}`] = true;
                  return {
                    hideRevertBtn: btns,
                  };
                });
              }}
            />
          )}
          <div>
            {/** 当公告状态为已发布时（code=4），展示外网推送结果 */}
            {key === 4 && !!item.rePushStatusName ? `(${item.rePushStatusName})` : ''}
          </div>
          {renderTags(TAG_SOURCE_TYPE_MAP.system, item.announcementTagDtoList)}
        </div>
      );
    }
    return '';
  },
}, {
  title: '操作',
  width: 120,
  render: (item, record, i) => {
    const maxShowSize = 3
    /**
     * formPageCode: 对接表单后的 pageCode，如果 formPageCode 有值，则跳转对接处理后的页面和接口，无值走老逻辑
     * secondEdit：公告正文是否允许二次编辑，对接表单后使用。允许：编辑跳转富文本编辑器，不允许，跳转表单页
     */
    const {
      objectionState,
      // canNotify,
      formPageCode,
      // secondEdit,
      options = {},
      isTransmit,
      canRevokePublishSeconds = 0, // 剩余可撤回时间
    } = record;
    const {
      canNotify,
      canRevoke, // 是否可撤回
      canRePush, // 是否需要重新推送
    } = options;
    const operation = [{
      key: 1,
      value: '发布',
    }, {
      key: 2,
      value: '审核',
    }, {
      key: 4,
      value: '编辑',
    }, {
      key: 8,
      value: '删除',
    }];
    const itemList = [];
    operation.forEach((op) => {
      if ((op.key & item.dealCode) === op.key) {
        if (op.key === 1) {
          itemList.push({
            label: op.value,
            onRowClick: () => {
              confirm({
                title: '提示',
                content: '确认发布此公告？',
                onOk: () => {
                  releaseAnnouncement({
                    announcementId: item.id,
                  }).then((res) => {
                    if (res && res.success) {
                      message.success('公告发布中');
                      _this.props.dispatch({
                        type: 'list/getAnnouncementList',
                        payload: {
                          annBigTypes: _this.state.annBigType,
                          ..._this.props.list.params,
                        },
                      });
                    }
                  });
                },
              });
            },
          });
        }
        if (op.key === 2) {
          itemList.push({
            label: isTransmit ? '转发': op.value,
            onRowClick: () => {
              if (formPageCode) {
                _this.detail(record);
              } else {
                _this.props.dispatch(routerRedux.push(`/review/${_this.state.annBigType}/${item.id}`));
              }
            },
          });
        }
        if (op.key === 4) {
          itemList.push({
            label: op.value,
            onRowClick: () => {
              if (formPageCode) {
                _this.props.dispatch(
                  routerRedux.push(
                    `/dynamic/edit?announcementTypeName=${escape(record.announcementTypeName)}&announcementType=${record.announcementType}&formPageCode=${formPageCode}&annId=${record.id}&districtId=${record.district}&annBigType=${record.annBigType}`
                  )
                );
              } else {
                _this.props.dispatch(routerRedux.push(`/edit/${_this.state.annBigType}/${item.id}`));
              }
            },
          });
        }
        if (op.key === 8) {
          itemList.push({
            label: op.value,
            onRowClick: () => {
              confirm({
                title: '提示',
                content: '确认删除此公告？',
                onOk: () => {
                  if (formPageCode) {
                    deleteFormAnnouncement({
                      id: record.id,
                      pageCode: formPageCode,
                      processDefineKey: record.processDefineKey,
                    }).then((res = {}) => {
                      if (res.success) {
                        message.success('删除成功！');
                        _this.reload();
                      }
                    });
                  } else {
                    deleteAnnouncement({
                      id: item.id,
                    }).then((res = {}) => {
                      if (res.success) {
                        message.success('删除成功！');
                        _this.reload();
                      }
                    });
                  }
                },
              });
            },
          });
        }
      }
    });

    // 收否能重新推送
    if (canRePush) {
      // todo 11月份优化掉 后端字段控制 暂时前端控制50开头（重庆不展示此）
      !item?.district?.startsWith('50') && itemList.push({
        label: '填写链接',
        onRowClick: _this.handleRePushVisible,
      });
      // 原有的重新推送按钮改名为“填写链接”，交互不变
      // 新增按钮：重新推送，点击后公告数据重推，用户无需填写链接
      itemList.push({
        label: '重新推送',
        onRowClick: _this.handelPushNotice,
      });
    }
    if (canRevoke) {
      itemList.push({
        label: '撤回',
        onRowClick: () => {
          if (item.revokedLogicDelete) {
            confirm({
              title: '是否继续撤回?',
              content: `撤回后公告列表将不再展示该公告，编辑操作请至${item.appName}公告发起模块`,
              closable: true,
              onOk: () => {
                _this.announcementIdForCancel = item.id;
                _this.props.form.resetFields(['revokeRes']);
                _this.setState({
                  cancelVisible: true,
                  currentRecord: record,
                });
              },
            });
          } else {
            _this.announcementIdForCancel = item.id;
            _this.props.form.resetFields(['revokeRes']);
            _this.setState({
              cancelVisible: true,
              currentRecord: record,
            });
          }
        },
      });
    }
    if (canRevokePublishSeconds > 0 && !_this.state.hideRevertBtn[`revertBtn${i}`]) {
      itemList.push({
        label: '撤回发布',
        onRowClick: _this.handleRevokePublish,
      });
    }
    if (objectionState === -1) {
      itemList.push({
        label: '异议确认',
        onRowClick: _this.objectAction,
      });
    }
    if (canNotify) {
      itemList.push({
        label: '短信通知',
        onRowClick: _this.handleSMSVisible,
      });
    }


    const hasOurUrlList = !!item.announcementOutUrlDtoList?.length
    if (itemList.length === 0 && !hasOurUrlList) {
      return (
        <a className="customLinkBlue"
          onClick={() => {
            _this.detail(item);
          }}
        >查看
        </a>
      );
    }
    const actions = itemList

    return (
      <div>
        {hasOurUrlList ?
          <OutUrlContent hideDivider={!actions.length} list={item.announcementOutUrlDtoList} /> : null}
          {
            actions?.length > 0
              ? actions?.map((i, index) => {
                return (
                  <div key={index} style={{ marginBottom: '8px' }}>
                    <Button
                      text
                      type="primary"
                      onClick={() => i?.onRowClick(record)}
                    >
                      {i?.label}
                    </Button>
                  </div>
                );
              })
              : null
          }
        {/* <Actions actions={actions} record={record} maxShowSize={getMaxShowSize()} /> */}
      </div>
    );
  },
}]?.filter(Boolean));

export const annDataHandleCustom = (annType = [], orgList = [], annStatuses = [], tagConfig = [], handleSelect) => {
  return (
    [
      {
        label: '公告标题',
        id: 'title',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '公告类型',
        id: 'type',
        render: () => handleSelect(annType, 'type'),
      },
      {
        label: '项目名称',
        id: 'projectName',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '项目编号',
        id: 'projectCode',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '发布时间',
        id: 'publishTime',
        render: () => {
          return (
            <RangePicker
              showTime={{
                defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
              }}
            />
          )
        }
      },
      {
        label: '发布单位',
        id: 'createOrgId',
        render: () => handleSelect(orgList, 'createOrgId')
      },
      {
        label: '状态',
        id: 'status',
        decoratorOptions: {
          initialValue: '-1',
        },
        render: () => {
          return (
            <Select>
              <Option key={'-1'} value={'-1'}>全部</Option>
              {
                annStatuses.map((item) =>
                  <Option key={item.code} value={item.code}>{item.name}</Option>
                )
              }
            </Select>
          );
        }
      },
      {
        label: '关键字',
        id: 'contentKeyWord',
        render: () => {
          return <Input placeholder="请输入" />
        }
      },
      {
        label: '公告标签',
        id: 'tagCodes',
        render: () => handleSelect(tagConfig, 'tagCodes')
      },
      {
        label: '公告来源',
        id: 'annSource',
        decoratorOptions: {
          initialValue: -1,
        },
        render: () => {
          return (
            <Select>
              {
                ANN_SOURCE_TYPE.map((item) =>
                  <Option key={item.value} value={item.value}>{item.key}</Option>
                )
              }
            </Select>
          );
        }
      },
    ]
  )
}