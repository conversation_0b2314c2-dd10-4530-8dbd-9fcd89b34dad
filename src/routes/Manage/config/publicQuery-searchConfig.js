import React from 'react';
import { Input, DatePicker, Select, Spin, TreeSelect } from 'doraemon';
import moment from 'moment';
import _get from 'lodash/get';
import './index.less';
import userIdentity from 'src/utils/roleType';
import { ANN_SOURCE_TYPE } from 'src/constants';

const { RangePicker } = DatePicker;
const Option = Select.Option;
// 每加一个搜索条件都要在搜索组件里添加key，否则无法记录
const customItem = (component) => {
  const allItemResult = [{
    label: '区划',
    id: 'district',
    render: () => {
      const districtTree = userIdentity.isAdmin ? (_get(component.props, 'publicQuery.districtTreeByAuth', []) || []) : _get(component.props, 'publicQuery.districtTree', []) || [];
      return (
        <TreeSelect
          treeData={districtTree}
          allowClear
          treeNodeFilterProp="title"
          showSearch
          placeholder="请选择"
          searchPlaceholder="输入关键字搜索"
          dropdownClassName="public-query-tree-select"
          dropdownStyle={{ maxHeight: '300px' }}
          notFoundContent={component.props.treeLoading ? (<Spin style={{ width: '100%' }} size="small" />) : '暂无数据'}
        />
      );
    },
  }, {
    label: '公告类型',
    id: 'type',
    render: () => component.getSelectOptions('annTypes'),
  }, {
    label: '公告标题',
    id: 'title',
    render: () => {
      return <Input placeholder="请输入" maxLength="30" />;
    },
  }, {
    label: '项目名称',
    id: 'projectName',
    render: () => {
      return <Input placeholder="请输入" maxLength="30" />;
    },
  }, {
    label: '项目编号',
    id: 'projectCode',
    render: () => {
      return <Input placeholder="请输入" maxLength="30" />;
    },
  }, {
    label: '发布时间',
    id: 'deliveryDate',
    render: () => {
      return (
        <RangePicker
          showTime={{
            defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
          }}
        />
      );
    },
  }, {
    label: '发布单位',
    id: 'createOrgId',
    render: () => component.getSelectOptions('publishOrgList'),
  }, {
    label: '状态',
    id: 'status',
    render: () => {
      return (
        <Select placeholder="请选择" allowClear>
          <Option value={4}>已发布</Option>
          <Option value={5}>公告结束</Option>
          <Option value={9}>已取消</Option>
        </Select>
      );
    },
  }, {
    label: '关键字',
    id: 'contentKeyWord',
    render: () => <Input placeholder="请输入" />,
  }, {
    label: '公告标签',
    id: 'tagCodes',
    render: () => {
      return (
        <Select mode="multiple" placeholder="请选择" allowClear>
          {
            component?.props?.publicQuery?.tagTypes.map(ele =>
              <Option value={ele.tagCode}>{ele.tagDisplay}</Option>
            )
          }
        </Select>
      );
    },
  },
  {
    label: '公告来源',
    id: 'annSource',
    decoratorOptions: {
      initialValue: -1,
    },
    render: () => {
      return (
        <Select>
          {
            ANN_SOURCE_TYPE.map(item =>
              <Option key={item.value} value={item.value}>{item.key}</Option>
            )
          }
        </Select>
      );
    },
  },
  ];

  return allItemResult;
};

export default function searchConfig(component, filterItems = []) {
  let items = [...customItem(component)];
  items = items.filter(item => !filterItems.includes(item.id));
  return {
    customItem: items,
    onSearch: component.onSearch,
  };
}
