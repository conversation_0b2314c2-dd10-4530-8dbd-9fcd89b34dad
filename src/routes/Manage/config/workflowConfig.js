
// 处理工作流节点展示数据
export const handWorkFlowDotsData = (oldAnnouncementFlowLogDtos = []) => {
  const newAnnouncementFlowLogDtos = [...oldAnnouncementFlowLogDtos.reverse()];
  newAnnouncementFlowLogDtos.forEach((item = {}) => {
    item.userName = item.operatorName;
    item.orgName = item.operatorOrgName;
    item.userAction = item.option;
    item.remark = item.mark;
  });
  return newAnnouncementFlowLogDtos;
};
