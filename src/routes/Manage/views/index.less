@title_color: #333;
@content_color: #666;

.ann-detail {
  margin-top: 10px;
  .announcement-detail-panel {
    .doraemon-panel-header {
      text-align: center;
    }
  }
}

.ztt {
  position: relative;
  float: right;
  padding-right: 20px;
  height: 45px;
  line-height: 45px;
  background: rgba(255, 120, 15, 0.2);
  font-size: 16px;
  color: #ff780f;
  border-right: 1px dashed #ff780f;
  margin-right: -20px;

  &:before {
    display: inline-block;
    vertical-align: middle;
    content: '';
    border-left: 10px solid #fff;
    border-top: 22.5px solid transparent;
    border-bottom: 22.5px solid transparent;
    border-right: 10px solid transparent;
  }
}

.announcement-list {
  .short-str {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .colLabel {
    word-break: break-all;
    text-overflow: ellipsis;
    width: 200px;
    overflow: hidden;
  }

  .line-clamp-3 {
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .ann-title {
    position: relative;
    // width: 200px;
    .wrap-text {
      word-break: break-all;
      text-overflow: ellipsis;
    }
    .doraemon-tag {
      // margin-bottom: 4px;
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      vertical-align: bottom;
    }
    .customLink {
      line-height: 22px;
    }
  }
}

.announcement-search {
  .short-str {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}

.announcement-flow {

  .doraemon-pro-timeline {
    margin-top: 20px;
  }

  .ann-header {
    display: inline-block;

    .ann-title {
      font-size: 16px;
      margin-bottom: 10px;
      color: #202020;

      .doraemon-tag {
        float: right;
      }
    }

    .ann-des {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      font-weight: 400;

      .zcyicon {
        margin-right: 10px;
      }

      span {
        margin-right: 20px;
      }

      > i.anticon {
        margin-right: 5px;
      }
    }
  }

  .flowHead {
    .doraemon-panel-header {
      border-bottom: 1px solid #eee;
    }

    .doraemon-panel-content {
      padding-top: 20px;
    }

    .doraemon-status-bar {
      position: relative;
      top: 7px;
    }
    .doraemon-status-bar-progressing {
      z-index: 1;
    }
  }
}

ul.file-list {
  margin-top: 10px;

  li {
    .inline {
      display: inline-block;
      padding: 10px;
      border-radius: 5px;
      transition: background-color .3s ease-in-out;

      &:hover {
        background-color: #f5f6fa;

        .file-del {
          opacity: 1;
        }
      }

      .file-name {
        margin-left: 10px;
        margin-right: 20px;
      }

      .file-del {
        opacity: 0;
        margin-left: 20px;
        transition: opacity .3s ease-in-out;
      }
    }
  }
}

.file-down {
  margin-top: 20px;

  .doraemon-upload-download-item {
    margin-right: 20px;
  }
  .doraemon-upload-download {
    a {
      margin-right: 0;
    }
  }
}

.checks {
  font-weight: bold;
  margin-right: 10px;
}

.large .doraemon-steps-item-description {
  max-width: 300px !important;
}

.revoke-note {
  i {
    color: orange;
  }
}

// 平台级公告查询页面
.public-query {
  &-list-panel {
    margin-top: 10px;
  }
  &-pagination {
    float: right;
    margin-top: 10px;
  }
  &-table {
    &-announcement-info {
      color: @title_color;
      span {
        color: @content_color;

        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }
    }
    &-ann-title:hover {
      text-decoration: underline;
    }
  }
}
