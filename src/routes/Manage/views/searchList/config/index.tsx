import React from 'react';
import { TAG_SOURCE_TYPE_MAP, renderTags } from 'src/routes/Manage/config/custom-tag';
import { fixNull } from 'src/utils/utils';
import moment from 'moment';

import type { PageListDataItem } from '../types';

const getAnnTime = (start, end, type) => {
  if (!start && !end) {
    return type === 2 ? '-' : '';
  }
  const annStart = start ? moment(start).format('YYYY-MM-DD') : '*';
  const annEnd = end ? moment(end).format('YYYY-MM-DD') : '*';
  return `${annStart} ~ ${annEnd}`;
};


export const renderTitle = (text:string, record: PageListDataItem) => {
  return (
    <React.Fragment>
      <span onClick={() => {}}>{text}</span>
      {
        renderTags(TAG_SOURCE_TYPE_MAP.business,
          record.announcementTagDtoList, { maxWidth: 180 })
      }
    </React.Fragment>
  );
};

export const renderInfo = (text:string, record: PageListDataItem) => {
  return (
    <React.Fragment>
      <p title={record.projectName}>
        <span>项目名称：{fixNull(record.projectName)}</span>
      </p>
      <p title={record.projectCode}>
        <span>项目编号：{fixNull(record.projectCode)}</span>
      </p>
      {/* <p title={record.amount}>
        <span>项目总额：{fixNull(record.amount)}</span>
      </p> */}
      <p title={record.creatorOrgName}>
        <span>发布单位：{fixNull(record.creatorOrgName)}</span>
      </p>
      <p title={getAnnTime(record.releasedAt, record.expiredAt, 1)}>
        <span>公示时间：{getAnnTime(record.releasedAt, record.expiredAt, 2)}</span>
      </p>
    </React.Fragment>
  );
};
