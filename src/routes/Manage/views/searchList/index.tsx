import React, { useEffect, useState } from 'react';
import { ZcyBreadcrumb, Zcy<PERSON>pin, ZcyList } from 'doraemon';

const ROUTES = [{
  label: '公告管理',
}, {
  label: '公告查询',
}];

const SearchList = () => {
  const [loading, setLoading] = useState(false);
  const [dataInfo, setDataInfo] = useState({
    total: 0,
    dataSource: [],
  });

  useEffect(() => {
    setLoading(false);
    setDataInfo({
      total: 0,
      dataSource: [],
    });
  }, []);


  return (
    <div className="search-list-wrapper">
      <ZcySpin spinning={loading}>
        <ZcyBreadcrumb
          routes={ROUTES}
        />
        <ZcyList
          openSearchCache
          table={{
            columns: [],
            dataSource: dataInfo.dataSource,
            rowKey: 'id',
            bordered: true,
            pagination: {
              showSizeChanger: true,
              total: dataInfo.total,
            },
          }}
        />
      </ZcySpin>
    </div>
  );
};

export default SearchList;
