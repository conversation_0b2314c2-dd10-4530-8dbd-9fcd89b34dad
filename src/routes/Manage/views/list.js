import React, { Component } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import {
  ZcyList, Input, ZcyBreadcrumb, message, Modal, Icon, Form,
} from 'doraemon';
import { getCustomItem, getTableCustom } from '../config/custom-item';
import { ZCYANNBIGTYPENAME, SXANNBIGTYPENAME } from 'src/constants';
import _ from 'lodash';
import SMSModal from 'components/SMS/SMS-Modal';
import ModalSelect from './components/modal-select';
import ObjectModal from './components/ObjectModal';
import DistrictModal from './components/DistrictModal';
import { revokeAnnouncement, canCreateAnnouncement, recallFormAnnouncement, fetchPublishOrgListApi } from '../services';
import './index.less';
import RePushModal from './components/re-push-modal';
import { fetchCurrentEnvApi } from 'src/common/services/app';
import pushLog from 'utils/pushLog';
import { formateOrgIds } from 'utils/utils';
import tracer from 'src/utils/tracer';
import { getBackSky } from '@zcy/back-sky-sdk';
import LcyPopModal from '@/components/LcyPopModal';
import { getUrlParam } from '@/utils/utils.js';


export const ISPROXYUNIT = window?.currentUserIdentity?.categoryName?.startsWith('0302');

const localStorageKey = `lcyDrainageModal__${window.currentUserIdentity.operatorId}`;

const ENTERPRISECODE = 14;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

@connect(({ list, loading, flow }) => ({
  list,
  tableLoading: loading.models.list,
  flow,
}))
@Form.create()
export default class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isRegulatoryRole: false, // 是否为监管身份
      permission: false,
      annBigType: null,
      loadAnnType: true,
      cancelVisible: false,
      objectModal: false,
      districtModalVisible: false,
      objectAnnId: null,
      districtId: undefined,
      SMSVisible: false, // 短信通知弹窗
      // announcementId: undefined, // 当前选中行的公告 Id
      currentRecord: {}, // 当前选中行
      // eslint-disable-next-line react/no-unused-state
      hideRevertBtn: {}, // custom-item 组件内使用
      rePushVisible: false, // 重新推送公告弹窗
      env: '', // 当前环境
      unitList: [], // 发布单位数据
      loadingFetch: false, // 获取发布单位状态
      showProjectSearch: null, // 是否为中小企业公告
      defaultActiveKey: '1', // 默认为 我的待办
      navMenuLabel: '',
      isShowLcyModal: false,
    };
  }

  // 获取环境信息（政采云平台 or 上海环境）
  getEnv = () => {
    fetchCurrentEnvApi().then((envRes = {}) => {
      if (envRes?.success) {
        const tenant = _.get(envRes, 'result.tenant', '') || '';
        this.setState({
          env: tenant,
        });
      }
    });
  }

  /**
   * 获取左侧导航栏内容
   * 
  */
  getBackSkyData = () => {
    getBackSky().then(({ menuInfo = {} } = {}) => {
      const { path = [] } = menuInfo;
      if (path && path.length) {
        const lastNode = path[path.length - 1];
        this.setState({
          navMenuLabel: lastNode,
        });
      }
    });
  }

  handleSearch = (params) => {
    const { annBigType } = this.state;
    if (!annBigType) return;
    if (params.date && params.date.length === 2) {
      params.releaseStartAt = params.date[0];
      params.releaseEndAt = params.date[1];
    }
    delete params.date;
    const getListParams = { 
      annBigTypes: annBigType,
      ...params,
      orgIds: formateOrgIds(params.orgIds), 
    };
    this.props.dispatch({
      type: 'list/getAnnouncementList',
      payload: getListParams,
    }).then((res) => {
      if (!res.success) {
        Modal.error({
          title: '提示',
          content: '公告不存在或无权查看此类型公告！',
          onOk: () => {
            this.props.dispatch(
              routerRedux.replace(
                '/overview'
              )
            );
          },
        });
      }
    });

    try {
      tracer({
        utmCD: ['cZcyList', 'dSearchBtn'],
        business: {
          keyWord: this.state.annBigType,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }

    try {
      const paramsKeys = Object.keys({ ...getListParams }).filter(key => !['type', 'pageSize', 'pageNo', 'annBigTypes'].includes(key)).filter(key => params[key]).join('_');
      tracer({
        utmCD: ['cZcyList', 'dCollectSearchParams'],
        business: {
          keyWord: paramsKeys,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
  }

  componentWillReceiveProps(nextProps) {
    /**
     *  区分分组列表
    */ 
    if (nextProps.mode === 'group') {
      return;
    }
    const match = nextProps.location.pathname.split('/');
    if (this.state.annBigType !== match[3] && this.state.annBigType !== null) {
      const annBigType = match[3];
      this.setState({
        annBigType,
        showProjectSearch: Number(annBigType) !== ENTERPRISECODE,
      }, () => {
        this.init();
      });
    }
  }

  getDistrictsSelectForm() {
    this.getDistricts();
    this.getUnit();
  }

  getPermissionData() {
    canCreateAnnouncement({
      announcementType: this.state.annBigType,
    }).then((res = {}) => {
      this.setState({
        permission: res.result,
      });
    });
  }

   init = () => {
     const { annBigType } = this.state;
     if (!annBigType) return;
     this.getEnv();
     this.getBackSkyData();
     this.getAnnType();
     this.getUser();
     this.getPermissionData();
     this.setLocalStorage();
   }

   setLocalStorage = () => {
     const value = localStorage.getItem(localStorageKey);
     const pathParam = getUrlParam('lcyModalTag');
     if (value !== 'hide' && pathParam) {
       this.setState({ isShowLcyModal: true });
     } else {
       this.setState({ isShowLcyModal: false });
     }
   }
  
  componentDidMount = async () => {
    let annBigType = '';
    if (this.props?.mode === 'group') {
      annBigType = this.props.annBigType;
    } else {
      const { params = {} } = this.props?.match ?? {};
      annBigType = params.annBigType || '';
    }
    this.setState({
      annBigType,
      showProjectSearch: Number(annBigType) !== ENTERPRISECODE,
    }, () => {
      this.init();
    });
  }
  // 用户信息
  getUser = () => {
    this.props.dispatch({
      type: 'list/getUser',
    }).then((res = {}) => {
      const { success, result } = res || {};
      if (success) {
        try {
          const isRegulatoryRole = _.get(result, 'creatorBasicUserType', '').indexOf('06') !== -1;
          this.setState({
            isRegulatoryRole,
          });
          this.getDistrictsSelectForm();
        } catch (error) {
          pushLog(JSON.stringify(error), 'info');
        }
      }
    });
  }
  // 发布区划数据
  getDistricts = () => {
    const { dispatch } = this.props;
    dispatch({ type: 'list/getDistricts' });
  }

  // 获取发布单位数据
  getUnit = (params = {}) => {
    this.setState({
      loadingFetch: true,
    }, () => {
      fetchPublishOrgListApi({
        ...params,
        filterByPrivilege: false,
      }).then((res = {}) => {
        this.setState({
          unitList: res?.result || [],
          loadingFetch: false,
        });
      }).catch(() => {
        this.setState({
          unitList: [],
          loadingFetch: false,
        });
      });
    });
  }

  // 发布单位模糊查询
  handleFetch = (value) => {
    this.getUnit({ orgName: value });
  }

  getAnnType = () => {
    const { annBigType } = this.state;
    this.props.dispatch({
      type: 'list/getAnnTypeByAnnBigType',
      payload: {
        annBigType,
      },
    }).then(() => {
      this.setState({
        loadAnnType: false,
      });
    }).catch((error) => {
      pushLog(JSON.stringify(error), 'info');
    });
  }

  handelAddAnnouncement = () => {
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'categoryModalVisible',
        visible: true,
      },
    });
  }
  showDistrictModalVisible = () => {
    this.setState({
      districtModalVisible: true,
    });
  }
  detail = (item) => {
    const {
      formPageCode = '',
      id = '',
      district = '',
      annBigType = '',
    } = item;
    const { annBigType: noticetype = '' } = this.state;
    if (formPageCode) {
      this.props.dispatch(
        routerRedux.push(
          `/dynamic/detail?formPageCode=${formPageCode}&annId=${id}&districtId=${district}&annBigType=${annBigType}`
        )
      );
    } else {
      this.props.dispatch(routerRedux.push(`/detail/${noticetype}/${id}`));
    }
  }
  reload = () => {
    this.listRef.handleReset();
  }
  onCancelOk = () => {
    const {
      currentRecord = {},
    } = this.state;
    const {
      id = '',
      formPageCode = '',
      processDefineKey = '',
      taskModelId = '',
    } = currentRecord;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        if (formPageCode) {
          recallFormAnnouncement({
            id,
            pageCode: formPageCode,
            processDefineKey,
            taskId: taskModelId,
            reason: values.revokeRes || '',
          }).then((res) => {
            if (res && res.success) {
              message.success('撤回成功！');
              this.setState({
                cancelVisible: false,
              });
              this.reload();
            }
          });
        } else {
          revokeAnnouncement({
            ...values,
            announcementId: this.announcementIdForCancel,
          }).then((res) => {
            if (res.success) {
              message.success('撤回成功！');
              this.setState({
                cancelVisible: false,
              });
              this.reload();
            }
          });
        }
      }
    });
  }
  objectAction = (record = {}) => {
    this.setState({
      objectModal: true,
      objectAnnId: record.id,
    });
  }
  hideModal = () => {
    this.setState({
      objectModal: false,
      districtModalVisible: false,
    });
  }
  onDistrictChange = (values = {}) => {
    const { annBigType = '' } = this.state;
    this.setState(values);
    this.props.dispatch({
      type: 'list/getAnnTypeByAnnBigType',
      payload: {
        annBigType,
        districtCode: values.districtId,
      },
    });
    this.hideModal();
    this.handelAddAnnouncement();
    this.getPermissionData();
  }

  // 短信弹窗
  handleSMSVisible = (record) => {
    const { SMSVisible = false } = this.state;
    if (!_.isEmpty(record)) {
      this.setState({
        // announcementId: record.id,
        currentRecord: record,
      });
    }
    this.setState({
      SMSVisible: !SMSVisible,
    });
  }

  handleRePushVisible = (record) => {
    if (!_.isEmpty(record)) {
      this.setState({
        currentRecord: record,
      });
    }
    this.setState({
      rePushVisible: !this.state.rePushVisible,
    });
  }
  handelPushNotice = (record = {}) => {
    const { dispatch } = this.props;
    const { id: announcementId = '' } = record;
    dispatch({
      type: 'list/rePush',
      payload: {
        announcementId,
      },
    }).then((res) => {
      if (res.success) {
        this.reload();
        message.info('公告已经重新推送成功!');
      } else {
        message.error(res.error);
      }
    });
  };

  // 撤回发布操作
  handleRevokePublish = (record = {}) => {
    Modal.confirm({
      title: '是否确认撤回发布该公告？',
      onOk: () => {
        revokeAnnouncement({
          announcementId: record.id,
        }).then((res) => {
          // es查询延迟需要延迟一秒进行查询状态才会变更
          setTimeout(() => {
            this.reload();
          }, 1000);
          if (res && res.success) {
            message.success('撤回发布成功！');
          }
        });
      },
    });
  }

  getBreadcrumb = () => {
    const { env, annBigType, navMenuLabel } = this.state;
    // 左侧导航栏文案
    if (navMenuLabel) {
      return [{
        label: navMenuLabel,
      }]; 
    }
    // 兜底逻辑、待移除
    const annBigTypeName = env === 'sx' ? SXANNBIGTYPENAME : ZCYANNBIGTYPENAME;
    return [{
      label: annBigType === null ?
        '' : annBigTypeName[parseInt(annBigType, 10) - 1],
    }];
  }

  render() {
    const { list, tableLoading } = this.props;
    const {
      objectModal = false,
      objectAnnId = '',
      districtModalVisible = false,
      districtId = '',
      SMSVisible = false,
      currentRecord = {},
      rePushVisible = false,
      annBigType,
      isRegulatoryRole,
      unitList,
      loadingFetch,
      showProjectSearch,
      defaultActiveKey,
      isShowLcyModal,
    } = this.state;
    const { getFieldDecorator } = this.props.form;
    const breadcrumb = this.getBreadcrumb();

    const tabList = [{
      label: '我的待办',
      key: '1',
    }, {
      label: '全部',
      key: '0',
    }];
    const pagination = {
      total: list.announcementList.total,
      showSizeChanger: true,
      defaultCurrent: list.params.pageNo,
      defaultPageSize: list.params.pageSize,
    };
    const table = {
      columns: getTableCustom(this),
      pagination,
      rowKey: 'id',
    };
    const globalBtn = [{
      label: '新增',
      type: 'primary',
      loading: this.state.loadAnnType,
      onClick: () => {
        if (ISPROXYUNIT) {
          this.showDistrictModalVisible(); 
        } else {
          this.handelAddAnnouncement();   
        }
        try {
          tracer({
            utmCD: ['cZcyList', 'dCreate'],
            business: {
              keyWord: this.state.annBigType,
            },
          });
        } catch (err) {
          pushLog(JSON.stringify(err), 'warning');
        }
      },
    }];
    return (
      <div className="announcement-list" key={this.state.annBigType}>
        <ZcyBreadcrumb
          routes={breadcrumb}
          globalBtn={this.state.permission ? globalBtn : []}
        />
        <ZcyList
          openSearchCache
          customItem={getCustomItem({
            units: unitList,
            districtsTree: list?.districtsTree,
            annType: list?.annType,
            handleFetch: this.handleFetch,
            annBigType,
            isRegulatoryRole,
            loadingFetch,
            showProjectSearch,
          })}
          initSearchParams={{ type: '1' }}
          tabs={{
            tabList,
            defaultActiveKey,
          }}
          tabKey="type"
          table={{
            ...table,
            loading: tableLoading,
            dataSource: list.announcementList.data,
            rowClassName: 'ann-row',
          }}
          onSearch={this.handleSearch}
          ref={(node) => { this.listRef = node; }}
        />
        <ModalSelect annBigType={this.state.annBigType} districtId={districtId} />
        <Modal
          title="公告撤回"
          visible={this.state.cancelVisible}
          onOk={this.onCancelOk}
          width={540}
          onCancel={
            () => {
              this.setState({
                cancelVisible: false,
              });
            }
          }
        >
          <Form layout="horizontal">
            <FormItem label="撤回理由" {...formItemLayout}>
              {getFieldDecorator('revokeRes', {
                rules: [{ required: true, message: '请输入' }],
              })(
                <Input.TextArea maxLength={255} placeholder="请输入" />
              )}
            </FormItem>
          </Form>
          <div className="revoke-note">
            <Icon type="exclamation-circle" /> 提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作。
          </div>
        </Modal>
        {objectModal && (
          <ObjectModal
            show={objectModal}
            hideMe={this.hideModal}
            annId={objectAnnId}
            reload={this.reload}
          />
        )}
        {
          districtModalVisible && (
            <DistrictModal
              show={districtModalVisible}
              hideMe={this.hideModal}
              onDistrictChange={this.onDistrictChange}
            />
          )
        }
        {
          SMSVisible ? (
            <SMSModal
              visible={SMSVisible}
              onCancel={this.handleSMSVisible}
              announcementId={currentRecord.id}
              distCode={currentRecord.district}
            />
          ) : null
        }
        {
          rePushVisible ? (
            <RePushModal
              visible={rePushVisible}
              onCancel={this.handleRePushVisible}
              announcementId={currentRecord.id}
              reload={this.reload}
            />
          ) : null
        }
        {isShowLcyModal && (
          <LcyPopModal 
            visible={isShowLcyModal} 
            cancelModal={() => {              
              this.setState({
                isShowLcyModal: false,
              });
              localStorage.setItem(localStorageKey, 'hide');
            }}
          />
        )}
      </div>
    );
  }
}
