import React, { Component } from 'react';
import { ZcyBreadcrumb } from 'doraemon';
import AnnounceList from './announceList';


export default class DemoList extends Component {
  render() {
    const breadcrumb = [{
      label: '公告管理',
    }, {
      label: '公告查询',
    }];

    return (
      <div className="announcement-search">
        <ZcyBreadcrumb
          routes={breadcrumb}
        />
        <AnnounceList {...this.props} />
      </div>
    );
  }
}
