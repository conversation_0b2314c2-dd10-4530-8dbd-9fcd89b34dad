import React, { Component } from 'react';
import { connect } from 'dva';
import _get from 'lodash/get';
import {
  Panel, ZcyBreadcrumb, Modal,
  message, Form,
  Upload, request,
} from 'doraemon';
import { getGlobalBtn } from '../config/detail-config';
import { obtainAnnouncementDetail, getBusinessWorkFlowConfigAPI } from '../services';
import Marking from '../../Flow/views/components/marking';
import './components/modal-review.less';
import './index.less';
import AnnouncementInfo from './components/announcement-info';
import SMSInfo from 'components/SMS/SMS-info';
import DetailSteps from './components/detail-steps';
import DetailObjections from './components/detail-objections';
import CancelAnnoModal from 'src/routes/DynamicAnnouncementEdit/components/cancelAnnoModal';
import { handWorkFlowDotsData } from '../config/workflowConfig';
import { checkDoJumpOutUrl } from 'src/utils/annDetailPermissionAction';
import AnnouncementContentBlock from 'src/components/AnnouncementContentBlock';
import AnnPushInfo from 'src/components/AnnPushInfo';
import { goCASign } from 'src/utils/ca';
import ReformationTag from 'src/components/ReformationTag';


@connect(({ list }) => ({
  list,
  SMSRecordList: list.SMSRecordList,
}))
@Form.create()
export default class Detail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fileList: [],
      announcement: {
        showStatus: null,
        announcementFlowLogDtos: [],
      },
      markingList: [],
      // eslint-disable-next-line react/no-unused-state
      fileLink: '', // detail-config 中使用
      // 获取打印链接loading
      // eslint-disable-next-line react/no-unused-state
      printLoading: false, // detail-config 中使用
      objections: [],
      // eslint-disable-next-line react/no-unused-state
      dylygsObjection: '0', // 单一来源公示是否有异议 // detail-config 中使用
      showCancelAnno: false, // 展示取消公告弹窗 
      isBusinessAnnouncement: true, // 判断是否为业务公告
      businessAnnCurrentStep: 0, // 当前的业务公告的流程节点
      /**
       *  是否展示下载审批表按钮
       */
      // eslint-disable-next-line react/no-unused-state
      purchaseIntentionAuditReport: false, 
      // eslint-disable-next-line react/no-unused-state
      downLoadAuditReportLoading: false,
    };
    this.announcementId = props?.match?.params?.announcementId; // 公告id
    this.annBigType = props?.match?.params?.annBigType; // 公告大类
  }
  componentWillUnmount() {
    this.setState = () => {
      // This is intentional
    };
  }
  async componentDidMount() {
    const init = async () => {
      await this.getBusinessWorkFlowConfig();
      this.fetchAnnBasicDetail();
      this.getAnnouncementEditorDetail();
    };

    checkDoJumpOutUrl({
      annId: this.announcementId,
    }).then((res) => {
      if (!res) init();
    }, () => {
      init();
    });
  }

  getBusinessWorkFlowConfig = () => {
    return new Promise((resolve) => {
      getBusinessWorkFlowConfigAPI({
        announcementId: this.announcementId,
      }).then(({ result }) => {
        this.setState({
          isBusinessAnnouncement: result?.isBusiness,
          businessAnnCurrentStep: result?.currentStep,
        });
        resolve();
      });
    });
  }

  getConfig = async (districtCode, announcementId) => {
    const { result, success } = await request('/announcement/config/allConfig', {
      params: {
        districtCode,
        announcementId,
      },
    });
    if (success) {
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        dylygsObjection: result.dylygsObjection,
        // eslint-disable-next-line react/no-unused-state
        purchaseIntentionAuditReport: result.purchaseIntentionAuditReport,
      });
    }
    if (result?.dylygsObjection === '1') {
      this.getObjections();
    }
  }
  // 获取异议相关人员table数据
  getObjections = async () => {
    const res = await request('/announcement/objection/objectionDto', {
      params: {
        id: this.announcementId,
      },
    });
    if (res.success) {
      this.setState({
        objections: res.result || [],
      });
    }
  }
  // 获取富文本下面的附件信息
  getFileLink = () => {
    this.setState({
      // eslint-disable-next-line react/no-unused-state
      printLoading: true,
    });
    return this.props.dispatch({
      type: 'list/printAnnouncementContent',
      payload: {
        announcementId: +this.announcementId,
      },
    }).then((res) => {
      if (!res.success) return;
      const fileLink = res.result;
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        fileLink,
      });
      return fileLink;
    }).finally(() => {
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        printLoading: false,
      });
    });
  }

  // 获取上半部分 公告基本信息
  fetchAnnBasicDetail = () => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'list/fetchAnnBasicInfo',
      payload: {
        annId: this.announcementId,
      },
    });
  }

  // 获取下半部分 公告富文本信息及顶部的公告审核人
  getAnnouncementEditorDetail = () => {
    obtainAnnouncementDetail({
      announcementId: this.announcementId,
    }).then(({ result, success, error }) => {
      if (!success) {
        return message.error(error);
      }
      const newAnnouncementFlowLogDtos = handWorkFlowDotsData(result?.announcementFlowLogDtos);
      result.announcementFlowLogDtos = newAnnouncementFlowLogDtos;

      this.getConfig(result.district, this.announcementId);
      this.setState({
        announcement: result,
        fileList: result.attachments || [],
      });
      this.initMarking({
        distCode: result.district,
        annType: result.announcementType,
        annId: result.id,
      });
    });
  }

  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (payload) => {
    this.props.dispatch({
      type: 'list/getIdentifications',
      payload,
    }).then((res = {}) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }

  handleCancel = () => {
    this.setState({
      showCancelAnno: false, // 展示取消公告弹窗 
    });
  }

  passAnn = () => {
    const { digest: signContent, id } = this.state.announcement || {};
    goCASign({
      signContent,
      doneCallback: ({ token }) => {
        this.operObjection({
          id,
          paas: true,
          digest: token,
        });
      },
    });
  }

  operObjection = (payload) => {
    this.props.dispatch({
      type: 'list/operObjection',
      payload,
    }).then((res = {}) => {
      if (res.success) {
        if (payload.paas) {
          message.success('通过成功！');
        } else {
          message.success('不通过！');
        }
        this.goPageBack();
      } else {
        message.error(res ? res.error : '接口错误！');
      }
    });
  }

  goPageBack = () => {
    const { history } = this.props;
    const { announcement } = this.state;
    const { annBigType, bizId } = announcement;
    if (history.length <= 1 && bizId) {
      history.replace(`/detail/${annBigType}/${bizId}`);
      return;
    }
    history.goBack();
  }

  noPass = () => {
    const { announcement } = this.state;
    const { id } = announcement || {};
    Modal.confirm({
      content: '公告不通过公示',
      onOk: () => {
        this.operObjection({
          id,
          paas: false,
        });
      },
    });
  }
  onCancelAnno = () => {
    this.setState({
      showCancelAnno: true,
    });
  };
  onCancelSuccess = () => {
    // 因ES更新状态有延迟，所以延迟1秒后再请求
    setTimeout(() => {
      window.location.reload();
    }, 500);
    this.setState({
      showCancelAnno: false,
    });
  }
  render() {
    const {
      announcement,
      markingList,
      objections,
      showCancelAnno,
      fileList,
      isBusinessAnnouncement,
      businessAnnCurrentStep,
    } = this.state;
    const breadcrumb = [{
      label: '公告管理',
    }, {
      label: '公告详情',
    }];

    const annBasicInfo = _get(this.props, 'list.annBasicInfo', {}) || {};
    return (
      <div className="announcement-flow">
        <ZcyBreadcrumb
          routes={breadcrumb}
          globalBtn={getGlobalBtn(this, annBasicInfo)}
        />
        { annBasicInfo.isReformation ? (
          <ReformationTag 
            tagTitle={annBasicInfo.isReformationDesc}
          />
        ) : null }
        <DetailSteps
          announcementDetail={announcement}
          isBusinessAnnouncement={isBusinessAnnouncement}
          businessAnnCurrentStep={businessAnnCurrentStep}
        />
        <div className="ann-detail">
          {/* 公告信息，包含外网推送情况等 */}
          <AnnouncementInfo annBasicInfo={annBasicInfo || {}} />
          <AnnPushInfo
            showRePushBtn={annBasicInfo.isOperateRePushByAnnouncement} 
            showRevokeBtn={annBasicInfo.isOperateRevokeByAnnouncement} 
            dataSource={annBasicInfo.pushDetails} 
            announcementId={this.announcementId}
            callback={() => this.fetchAnnBasicDetail()}
          />
          <Panel >
            <AnnouncementContentBlock 
              title={announcement.title} 
              englishTitle={announcement.englishTitle} 
              content={announcement.content}
            />
          </Panel>
        </div>

        {
          announcement?.attachmentShow ? (
            <div className="file-down" style={{ marginBottom: 20 }}>
              <Panel
                title="附件"
              >
                <Upload.Download bizCode="1014" fileList={fileList} />
              </Panel>
            </div>
          ) : null
        }
        
        {
          // 短信通知结果。开启了短信通知的才展示此模块
          announcement.isNotify ? <SMSInfo announcementId={this.announcementId} /> : null
        }

        <DetailObjections objections={objections} />

        <Marking
          markingList={markingList}
          disabled
          showSelected
          autoShowALl={false}
        />
        {/* 取消公告弹窗 */}
        {showCancelAnno && (
          <CancelAnnoModal 
            visible={showCancelAnno} 
            handleCancel={this.handleCancel} 
            annId={this.announcementId}
            onSuccess={this.onCancelSuccess}
          />
        )}
      </div>
    );
  }
}
