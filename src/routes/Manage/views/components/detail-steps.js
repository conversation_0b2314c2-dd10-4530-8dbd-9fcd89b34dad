import React, { Component } from 'react';
import { Panel, Icon, Steps, ZcyTimeline, ZcyStatusBar, Tooltip } from 'doraemon';
import WorkflowSteps from '@zcy/workflow-steps';
import moment from 'moment';
import { connect } from 'dva';
import { OPERATOR_STATUS } from 'src/constants';
import { getPubType, showTextByCount } from 'utils/utils';
import './detail-step.less';

const getStatus = (showStatus) => {
  const match = OPERATOR_STATUS.find(item => item.key === showStatus);
  let text = '';
  if (match) {
    text = match.value;
  }
  return (
    <ZcyStatusBar
      value={text}
      status="progressing"
    />
  );
};

const showStatusDesc = (statusDesc) => {
  return (
    <ZcyStatusBar
      value={statusDesc}
      status="progressing"
    />
  );
};
@connect()
export default class DetailSteps extends Component {
  constructor(props) {
    super(props);
    this.state = {
      workflowInfo: {},
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.announcementDetail.id !== this.props.announcementDetail.id) {
      if (this.props.isBusinessAnnouncement) return;
      this.fetchWorkflowInfo(nextProps.announcementDetail);
    }
  }

  // 获取工作流节点展示条件参数
  fetchWorkflowInfo = (announcementDetail) => {
    const { dispatch } = this.props;
    const { id, announcementType, isSmall = false } = announcementDetail;
    dispatch({
      type: 'dynamicCreate/fetchWorkflowInfo',
      payload: {
        id,
        announcementType,
        isSmall,
      },
    }).then((res = {}) => {
      if (res.success) {
        const workflowInfo = res?.result || {};
        this.setState({
          workflowInfo,
        });
      }
    });
  }

  render() {
    const { workflowInfo } = this.state;
    const { announcementDetail = {}, businessAnnCurrentStep, isBusinessAnnouncement } = this.props;
    const {
      title,
      creatorName,
      createdAt,
      showStatus,
      district,
      bizId,
      releasedAt,
      pubType,
      announcementFlowLogDtos,
      statusDesc,
    } = announcementDetail;
    const handleCreatedAt = createdAt ? moment(createdAt).format('YYYY-MM-DD HH:mm:ss') : '-';
    const handleReleasedAt = releasedAt ? moment(releasedAt).format('YYYY-MM-DD HH:mm:ss') : '-';
    const handlePubType = getPubType(pubType);

    return (
      <Panel
        className="flowHead"
        title={(
          <div className="ann-header">
            <div className="ann-title">{title}</div>
            <div className="ann-des">
              <Icon type="user" />
              <Tooltip placement="top" title={creatorName}>
                <span>创建人：{showTextByCount(creatorName)}</span>
              </Tooltip>
              <Icon type="clock-circle-o" />
              <span>创建时间：{handleCreatedAt}</span>
              {
                pubType && (
                  <span>
                    <Icon type="fabu" zcy />
                    <span>发布方式：{handlePubType}</span>
                  </span>
                )
              }
              {
                releasedAt && (
                  <span>
                    <Icon type="clock-circle-o" />
                    <span>发布时间：{handleReleasedAt}</span>

                  </span>
                )
              }
            </div>
          </div>
        )}
        // 上海中小企业执行预留会返回statusDesc
        extra={statusDesc ? showStatusDesc(statusDesc) : getStatus(showStatus)} 
      >
        {/* 若为业务公告则展示写死的流程节点 */}
        {
          isBusinessAnnouncement ? (
            <Steps
              current={businessAnnCurrentStep}
            >
              <Steps.Step
                title="创建公告"
              />
              <Steps.Step
                title="公告审核"
              />
              <Steps.Step
                title="公告发布"
              />
            </Steps>
          ) : (
            <WorkflowSteps
              processDefineKey={workflowInfo.processDefineKey}
              districtCode={district}
              bizId={String(bizId)}
              config={{
                conditions: workflowInfo,
              }}
            />
          )
        }
        <ZcyTimeline data={announcementFlowLogDtos || []} />
      </Panel>
    );
  }
}

