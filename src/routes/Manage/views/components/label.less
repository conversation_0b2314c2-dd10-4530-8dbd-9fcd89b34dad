.label {
  display: inline-block;
  margin: 8px 0;
  cursor: pointer;
  transition: color 0.3s ease-in-out;
  &:not(:last-of-type) {
    margin-right: 10px;
  }
  &:hover {
    color: #3177fd;
    .doraemon-badge-count {
      color: #fff;
      background-color: #3177fd;
    }
  }
  .doraemon-badge-count {
    transition: all 0.3s ease-in-out;
    color: #3177fd;
    background-color: #fff;
    border: 1px solid #3177fd;
    transform: scale(0.8);
    &:hover {
      color: #fff;
      background-color: #3177fd;
    }
  }
  .doraemon-badge {
    margin-top: 2px;
  }
}

.label-popover {
  max-width: 400px;
  .short-str {
    margin: 5px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
