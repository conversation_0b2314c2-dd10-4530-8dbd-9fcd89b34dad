.category-modal {
  .doraemon-divider-horizontal {
    width: 250px;
    margin: 0 auto;
    margin-bottom: 10px;
  }
  .doraemon-divider-inner-text {
    color: #ccc;
    font-size: 14px;
    font-weight: normal;
  }
  .category-list {
    display: flex;
    flex-wrap: wrap;
    .item {
      display: inline-block;
      width: 147px;
      height: 135px;
      overflow: hidden;
      border-radius: 6px;
      margin: 5px;
      transition: all .3s ease;
      cursor: default;
      &:hover {
        &:not(.null) {
          background: #ecf2ff;
          .icon {
            opacity: 0;
          }
          .title {
            font-weight: bold;
            transform: translateY(-60px);
          }
          .btn-container {
            transform: translateY(-70px);
          }
        }
      }
      .icon {
        margin: 0 auto;
        text-align: center;
        width: 60px;
        height: 60px;
        line-height: 60px;
        background: #ecf2ff;
        border-radius: 6px;
        transition: opacity .5s ease;
        opacity: 1;
        .anticon {
          color: #b3ccfd;
          font-size: 30px;
          line-height: 60px;
        }
      }
      .title {
        height: 65px;
        padding: 10px;
        color: #606060;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        overflow: hidden;
        transition: all .3s ease-in-out;
      }
      .btn-container {
        margin-top: 10px;
        text-align: center;
        transition: all .3s ease;
        transform: translateY(0);
        button {
          width: 120px;
          margin-left: 0;
          &:last-of-type {
            margin-top: 5px;
          }
        }
      }
    }
  }
}

.project-modal {
  .doraemon-modal-body {
    padding: 0;
    height: 431px;
    .doraemon-table-pagination {
      margin-right: 20px;
    }
    .search-content {
      padding: 10px 20px;
      margin-bottom: 10px;
      input {
        width: 260px;
      }
      .doraemon-input-suffix {
        right: -40px;
      }
    }
  }
  .price {
    color: #f60;
  }
  .short-str {
    margin: 5px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .doraemon-table-placeholder .empty-wrap {
    margin: 119px auto 120px;
  }
}
.search-content {
  width: 200px;
  margin-bottom: 20px;
}
