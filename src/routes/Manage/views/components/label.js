/* eslint-disable no-extra-boolean-cast */
import React, { Component } from 'react';
import { Popover, Badge } from 'doraemon';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import './label.less';

@connect(({ index }) => ({
  index,
}))
class Label extends Component {
  detail = (item) => {
    if (!!item.formPageCode) {
      this.props.dispatch(
        routerRedux.push(
          `/dynamic/detail?formPageCode=${item.formPageCode}&annId=${item.id}&districtId=${item.district}&annBigType=${item.annBigType}`
        )
      );
    } else {
      this.props.dispatch(routerRedux.push(`/detail/${item.annBigType}/${item.id}`));
    }
  }
  render() {
    const { title, num, list } = this.props;
    if (list.length === 0) {
      return (
        <div className="label">
          {title}<Badge count={num} offset={[-5, 5]} />
        </div>
      );
    }
    return (
      <div className="label">
        <Popover content={
          <div className="label-popover">
            {
              list.map((item) => {
                return (
                  <div className="short-str" key={item.id}>
                    <a
                      onClick={() => { this.detail(item); }}
                      title={item.title}
                    >
                      {`【${item.announcementTypeName}】${item.title}`}
                    </a>
                  </div>
                );
              })
            }
          </div>
        }
        >
          {title}<Badge count={num} offset={[-5, 5]} />
        </Popover>
      </div>
    );
  }
}

export default Label;
