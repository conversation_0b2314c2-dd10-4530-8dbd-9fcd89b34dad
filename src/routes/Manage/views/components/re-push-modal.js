import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Modal,
  Alert,
  Form,
  message,
  Input,
  Icon,
  Tooltip,
  Button,
} from 'doraemon';
// import _ from 'lodash';
import './rePushModal.less';

const FormItem = Form.Item;

@connect(({ list, loading }) => ({
  list,
  rePushLoading: loading.effects['list/rePush'],
}))
@Form.create()
export default class RePushModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  // 重新推送
  onOk = () => {
    const {
      dispatch,
      announcementId,
      onCancel,
      reload,
      form: { validateFieldsAndScroll },
    } = this.props;
    validateFieldsAndScroll((error, values) => {
      if (error) {
        return;
      }
      dispatch({
        type: 'list/rePush',
        payload: {
          announcementId,
          ...values,
        },
      }).then((res) => {
        if (res && res.success) {
          message.success('已重新发起推送');
        } else {
          message.error(res.error);
        }
        onCancel();
        reload();
      });
    });
  }

  render() {
    const {
      visible,
      onCancel,
      form: { getFieldDecorator },
      rePushLoading,
    } = this.props;
    return (
      <Modal
        title="重新推送"
        visible={visible}
        onCancel={onCancel}
        width={780}
        destroyOnClose
        className="rePush-modal"
        footer={[
          <Button onClick={this.onOk} type="primary" loading={!!rePushLoading}>重新推送</Button>,
        ]}
      >
        <Alert
          message={
            <React.Fragment>
              <div>公告推送至四川省政府采购网失败，请自行前往四川省网或中国政府采购网发布公告或进行重新推送</div>
              <div>请 5 分钟后刷新列表查看重新推送结果</div>
            </React.Fragment>
          }
          type="firstinfo"
          closable
          showIcon
          iconType="exclamation-circle-o"
          style={{
            marginBottom: '10px',
          }}
        />
        <Form>
          <FormItem
            label={
              <span>
                公告链接
                <Tooltip
                  placement="top"
                  title="请输入中采网或四川省网公告链接"
                  getPopupContainer={() => document.querySelector('.rePush-modal')}
                >
                  <Icon type="tishi3" zcy className="rePush-modal-info-icon" />
                </Tooltip>
              </span>
            }
            labelCol={{
              xs: { span: 12 },
              sm: { span: 8 },
            }}
            wrapperCol={{
              xs: { span: 24 },
              sm: { span: 16 },
            }}
          >
            {getFieldDecorator('outUrl', {
            })(
              <Input placeholder="请输入" className="rePush-modal-input" />
            )}
          </FormItem>
        </Form>
      </Modal>
    );
  }
}
