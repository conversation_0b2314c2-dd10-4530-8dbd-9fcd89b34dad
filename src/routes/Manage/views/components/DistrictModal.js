import React, { Component } from 'react';
import {
  Modal,
  Button,
  Form,
  FormGrid,
  Cascader,
} from 'doraemon';
import { connect } from 'dva';
import _ from 'lodash';
import './DistrictModal.less';

@Form.create()
@connect(({ appCommon }) => ({
  appCommon,
}))
export default class DistrictModal extends Component {
  state = {
    treeData: [],
    defaultDistrict: undefined,
  };
  componentDidMount() {
    this.loadData();
  }
  componentWillUnmount() {
    this.props.form.setFieldsValue({
      districtId: undefined,
    });
  }
  loadData = async () => {
    const { appCommon, dispatch } = this.props;
    const url = `/announcement/api/queryDistrictByOrgId?orgId=${window.currentUserIdentity.orgId}`;
    let res;
    if (appCommon.cache[url]) {
      res = appCommon.cache[url];
    } else {
      res = await dispatch({
        type: 'appCommon/fetchData',
        payload: {
          url: '/announcement/api/queryDistrictByOrgId',
          orgId: window.currentUserIdentity.orgId,
        },
      });
      dispatch({
        type: 'appCommon/setCache',
        payload: {
          [url]: res,
        },
      });
    }
    const treeData = this.dealData(_.get(res, 'distTrees', []));
    const districtId = this.getDist(_.get(res, 'regDist'), treeData);
    this.props.form.setFieldsValue({
      districtId,
    });
    this.setState({
      treeData,
      defaultDistrict: districtId,
    });
  }
  getDist = (districtId, treeData) => {
    if (!Array.isArray(treeData)) {
      return undefined;
    }
    let result;
    treeData.forEach((item) => {
      const subArr = this.getDist(districtId, item.children);
      if (subArr) {
        result = [
          item.code,
          ...subArr,
        ];
      }
      if (item.code === districtId) {
        result = [item.code];
      }
    });
    return result;
  }
  dealData = (data) => {
    if (Array.isArray(data)) {
      data.forEach((item) => {
        item.value = item.code;
        item.label = item.name;
        this.dealData(item.children);
      });
    }
    return data;
  }
  onOk = () => {
    const { form, onDistrictChange } = this.props;
    form.validateFieldsAndScroll((err, values) => {
      if (err) { return; }
      const { districtId } = values;
      onDistrictChange({
        districtId: districtId[districtId.length - 1],
      });
    });
  }
  getFormGridItem = () => {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const { treeData, defaultDistrict } = this.state;
    return [
      {
        label: '区划',
        colSpan: 2,
        render: () => (
          getFieldDecorator('districtId', {
            initialValue: defaultDistrict,
            rules: [{
              required: true,
              message: '请输入',
            }],
          })(
            <Cascader
              placeholder="请选择"
              options={treeData}
              getPopupContainer={() => this.ref}
            />
          )
        ),
      },
    ];
  }
  ref = null;
  render() {
    const { show, hideMe } = this.props;
    return (
      <Modal
        title="项目区划"
        visible={show}
        onCancel={hideMe}
        onOk={this.onOk}
        width={700}
        destroyOnClose
        footer={[
          <Button key="back" onClick={hideMe}>取消</Button>,
          <Button
            key="submit"
            type="primary"
            onClick={this.onOk}
          >
            确定
          </Button>,
        ]}
      >
        <div className="districtModal" ref={(ref) => { this.ref = ref; }}>
          <Form>
            <FormGrid
              bordered
              formGridItem={this.getFormGridItem()}
            />
          </Form>
        </div>
      </Modal>
    );
  }
}
