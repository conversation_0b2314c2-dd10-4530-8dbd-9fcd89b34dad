import React from 'react';
import { Panel, Table } from 'doraemon';


export default function DetailObjections(objections = []) {
  return objections?.length 
    ? 
    (
      <Panel title="异议信息">
        <Table
          columns={[
            {
              title: '异议人名称',
              dataIndex: 'objectOrgName',
              key: 'objectOrgName',
            },
            {
              title: '异议内容',
              dataIndex: 'objectContent',
              key: 'objectContent',
              render: (text, record) => {
                return `${text || ''}${record.objectFactDescribe ? `(${record.objectFactDescribe})` : ''}`;
              },
            },
            {
              title: '异议答复',
              dataIndex: 'replyConclusion',
              key: 'replyConclusion',
              render: (text, record) => {
                return `${text || ''}${record.replyDescribe ? `(${record.replyDescribe})` : ''}`;
              },
            },
          ]}
          dataSource={objections}
        />
      </Panel>
    )
    : 
    null;
}
