import React from 'react';
import { fixNull } from 'src/utils/utils';
import {
  Panel,
  FormGrid,
} from 'doraemon';
import TagLine from 'src/components/TagLine';
import './announcement-info.less';

export default function AnnouncementInfo({
  annBasicInfo = {},
}) {
  const announcementConfig = [{
    label: '发布单位区划',
    render: () => {
      return fixNull(annBasicInfo.creatorOrgDistrictName);
    },
    key: 'creatorOrgDistrictName',
    show: true,
  }, {
    label: '公告区划',
    render: () => {
      return fixNull(annBasicInfo.districtName);
    },
    show: true,
    key: 'districtName',
  }, {
    label: '项目名称',
    render: () => {
      return fixNull(annBasicInfo.projectName);
    },
    show: true,
    key: 'projectName',
  }, {
    label: '项目编号',
    render: () => {
      return fixNull(annBasicInfo.projectCode);
    },
    show: true,
    key: 'projectCode',
  }, {
    label: '公告类型',
    render: () => {
      return fixNull(annBasicInfo.announcementTypeName);
    },
    show: true,
    key: 'announcementTypeName',
  }, {
    label: '采购方式',
    render: () => {
      return fixNull(annBasicInfo.procurementMethodName);
    },
    show: true,
    key: 'procurementMethodName',
  }, {
    label: '采购目录',
    render: () => {
      // 待花花发布完后可去除 gpCatalog
      return fixNull(annBasicInfo.gpCatalogName || annBasicInfo.gpCatalog);
    },
    show: true,
    key: 'gpCatalogName',
  }, {
    label: '项目总额',
    render: () => {
      return fixNull(annBasicInfo.amount);
    },
    show: true,
    key: 'amount',
  }, {
    label: '发布单位',
    render: () => {
      return fixNull(annBasicInfo.creatorOrgName);
    },
    show: true,
    key: 'creatorOrgName',
  }, {
    label: '数据来源',
    render: () => {
      return fixNull(annBasicInfo.annSourceDesc);
    },
    show: true,
    key: 'annSourceDesc',
  }, {
    label: '单一来源编号',
    render: () => {
      return fixNull(annBasicInfo?.singleSourceCode);
    },
    show: annBasicInfo?.announcementType === 3012, // 仅单一来源的展示
    key: 'singleSourceCode',
  },
  ]?.filter(({ show }) => show);

  return (
    <Panel
      title="公告信息"
      className="announcement-info-panel"
    >
      <TagLine tags={(annBasicInfo?.announcementTagDtoList ?? [])} />
      <FormGrid
        bordered
        formGridItem={announcementConfig}
      />
    </Panel>
  );
}
