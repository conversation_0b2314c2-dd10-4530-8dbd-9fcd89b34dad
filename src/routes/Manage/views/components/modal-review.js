import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import { Modal, message, Form, Input, Radio, Select } from 'doraemon';
import './modal-review.less';

const FormItem = Form.Item;
const Option = Select.Option;

@connect(({ flow }) => ({
  reviewModalVisible: flow.reviewModalVisible,
}))
@Form.create()
class ReviewModal extends Component {
  handleCancel = () => {
    this.props.dispatch({
      type: 'flow/setModalVisible',
      payload: {
        type: 'reviewModalVisible',
        visible: false,
      },
    });
  }
  onOk = () => {
    this.props.dispatch({
      type: 'flow/setModalVisible',
      payload: {
        type: 'reviewModalVisible',
        visible: false,
      },
    });
    message.success('已审批');
    this.props.dispatch(routerRedux.push('/flow/publish'));
  }
  render() {
    const { reviewModalVisible, form } = this.props;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
    };
    return (
      <Modal
        destroyOnClose
        visible={reviewModalVisible}
        title="审批"
        onOk={this.onOk}
        onCancel={this.handleCancel}
        wrapClassName="review-modal"
      // width={700}
      >
        <Form layout="horizontal">
          <FormItem label="审批结果：" {...formItemLayout}>
            {getFieldDecorator('file1', {
              rules: [{ required: true, message: '请选择' }],
            })(
              <Radio.Group>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>
            )}
          </FormItem>
          <div className="next-step">
            下一环节进入：已审核
          </div>
          <FormItem label="执行人：" {...formItemLayout}>
            {getFieldDecorator('file2', {
              rules: [{ required: true, message: '请选择' }],
            })(
              <Select
                placeholder="请选择"
                onChange={this.handleSelectChange}
              >
                <Option value="male">male</Option>
                <Option value="female">female</Option>
              </Select>
            )}
          </FormItem>
          <FormItem label="审批意见：" {...formItemLayout}>
            {getFieldDecorator('file3', {
              rules: [{ required: true, message: '请输入' }],
            })(
              <Input.TextArea placeholder="请输入" />
            )}
          </FormItem>
        </Form>
      </Modal>
    );
  }
}

export default ReviewModal;
