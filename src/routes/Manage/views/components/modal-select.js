/* eslint-disable no-extra-boolean-cast */
import React, { Component } from 'react';
import { Modal, Divider, Icon, Button, Table, Input, Spin, request } from 'doraemon';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import _get from 'lodash/get';
import { getUrlWithAppCode } from '@zcy/basic-sdk';
import { fetchCurrentEnvApi } from '../../services';
import PlanModal from './PlanModal';
import { ICONS, ANNOUNMENTS, tableColumns, getRowSelection } from '../../config/modalSelect-config';
import './modal-select.less';
import { getAppCode } from 'src/utils/roleType';
import { message } from 'doraemon';


const confirm = Modal.confirm;

@connect(({ list, loading }) => ({
  list,
  tableLoading: loading.effects['list/projectList'],
  syncLoading: loading.effects['list/syncMetadata'],
}))
class addModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      current: 1,
      showPlanModal: false,
      dylygsPlanEntry: '0', // 单一来源公示采购计划进入
      env: '', // 当前环境
      /**
       * 区分交投环境 
      */
      isJiaoTou: false,
    };
    props.dispatch({
      type: 'list/setSelectedProject',
      payload: {
        selectedProject: null,
      },
    });
  }
  componentDidMount() {
    // 获取环境标识
    // 1. 区分环境
    // 2. 区分交投环境
    fetchCurrentEnvApi().then((res) => {
      if (res && res.success) {
        const tenant = _get(res, 'result.tenant', '');
        const isJiaoTou = _get(res, 'result.isJiaoTou', '');
        this.setState({
          env: tenant,
          isJiaoTou,
        });
      }
    });
    this.getConfig();
  }
  getConfig = async () => {
    const { distCode = window?.currentUserDistrict?.code, districtId } = this.props;
    const { result, success } = await request('/announcement/config/allConfig', {
      params: {
        districtCode: districtId || distCode,
      },
    }) || {};
    if (success) {
      this.setState({
        // 单一来源公示采购计划进入
        dylygsPlanEntry: result?.dylygsPlanEntry,
        // dylygsPlanEntry: '1',
      });
    }
  }
  handleCancel1 = () => {
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'categoryModalVisible',
        visible: false,
      },
    });
  }
  handleCancel2 = () => {
    if (!this.props.syncLoading) {
      this.props.dispatch({
        type: 'list/setModalVisible',
        payload: {
          type: 'projectModalVisibel',
          visible: false,
        },
      });
      this.props.dispatch({
        type: 'list/setModalVisible',
        payload: {
          type: 'categoryModalVisible',
          visible: true,
        },
      });
      this.value = '';
    }
  }
  // 新建公告-从已有项目发起
  addByExistProject = (item) => {
    this.params = item || {};
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'categoryModalVisible',
        visible: false,
      },
    });
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'projectModalVisibel',
        visible: true,
      },
    });
    let projectListUrl = '';
    if (ANNOUNMENTS.includes(this.params?.typeId)) {
      if (this.state.env === 'gz') {
        projectListUrl = '/announcement/api/project/projectList';
      } else {
        projectListUrl = '/announcement/api/project/announcementProjectList';
      }
    } else {
      projectListUrl = '/announcement/api/project/announcementProjectList';
    }
    // 选择项目列表 或者抽屉
    this.props.dispatch({
      type: 'list/projectList',
      payload: {
        url: projectListUrl,
        pageNo: 1,
        pageSize: 10,
        announcementType: this.params?.typeId,
        purchaseWay: this.params?.procurementMethodCode,
        districtCode: this.props.districtId || undefined,
      },
    });
  }

  // 分页
  pageChange = (page) => {
    this.page = page;
    this.setState({
      current: page,
    });
    this.props.dispatch({
      type: 'list/projectList',
      payload: {
        url: ANNOUNMENTS.includes(this.params?.typeId) ?
          '/announcement/api/project/projectList' : '/announcement/api/project/announcementProjectList',
        pageNo: page,
        pageSize: 10,
        projectName: this.value,
        announcementType: this.params?.typeId,
        purchaseWay: this.params?.procurementMethodCode,
      },
    });
  }

  // 选中项目，确定的会调
  onOk = () => {
    const project = this.props.list.selectedProject[0];
    this.props.dispatch({
      type: 'list/syncMetadata',
      payload: {
        serialNum: project.projectId,
        announcementType: this.params.typeId,
      },
    }).then(() => {
      this.props.dispatch({
        type: 'list/setModalVisible',
        payload: {
          type: 'categoryModalVisible',
          visible: false,
        },
      });
      this.props.dispatch({
        type: 'list/setModalVisible',
        payload: {
          type: 'projectModalVisibel',
          visible: false,
        },
      });
      if (!!this.params.formPageCode) {
        this.props.dispatch(
          routerRedux.push(
            `/dynamic/create?formPageCode=${this.params.formPageCode}&announcementTypeName=${escape(this.params.name)}&announcementType=${this.params.typeId}&orderId=${project.projectId}&projectCode=${escape(project.projectNo)}&districtId=${escape(project.districtId)}&projectName=${escape(project.projectName)}&annBigType=${this.props.annBigType}&ids=${this.params.id}`
          )
        );
      } else {
        this.props.dispatch(
          routerRedux.push(//
            `/flow/create?announcementTypeName=${escape(this.params.name)}&announcementType=${this.params.typeId}&orderId=${project.projectId}&projectCode=${escape(project.projectNo)}&districtId=${escape(project.districtId)}&projectName=${escape(project.projectName)}&annBigType=${this.props.annBigType}&ids=${this.params.id}`
          )
        );
      }
    });
  }
  // 新建公告-手动录入公告
   addByHand = async (params) => {
    // 面向中小企业预留项目执行情况公告 主管单位判断
     if (params.typeId === 14001) {
       try {
         const res = await request(
           `/announcement/api/config/form/checkMonitorEnterprises?announcementType=${params.typeId}`
         );
         const { result } = res || {};
         if(!result) {
          message.info('面向中小企业预留项目执行情况公告由主管单位负责发布，您不是主管单位，不需要发布该公告');
          return;
         }
       } catch (error) {
          message.info(error?.message || JSON.stringify(error));
       }
     }
    // 处理公告会有distCode传进来
    // console.log
    const { distCode, districtId } = this.props;
    let url = '';
    if (!!params.formPageCode) {
      url = `/dynamic/create?formPageCode=${params.formPageCode}&announcementTypeName=${escape(params.name)}&announcementType=${params.typeId}&annBigType=${this.props.annBigType}&ids=${params.id}`;
      if (districtId) {
        url = `${url}&districtId=${districtId}`;
      }
    } else {
      url = `/flow/create?announcementTypeName=${escape(params.name)}&announcementType=${params.typeId}&annBigType=${this.props.annBigType}&ids=${params.id}`;
      if (districtId) {
        url = `/flow/create?announcementTypeName=${escape(params.name)}&announcementType=${params.typeId}&annBigType=${this.props.annBigType}&ids=${params.id}&districtId=${districtId}`;
      }
    }

    // 3005 更正公告
    // 3006 澄清（修改）公告
    if (params.typeId === 3005 || params.typeId === 3006) {
      const that = this;
      const host = window.envHref.index;
      const isSH = this.state.env === 'sh';
      // 上海环境的采购文件更正、资格预审文件更正未拆分
      const text = isSH ? (
        <span>
          当前为手工录入更正公告，如您的项目在平台项目采购模块中进行，请前往【项目采购】-【项目管理】-
          <a href={`${host}/bidding-entrust/#/correction/list`}>【更正信息发布】</a>
          中发布更正公告以便同步更新项目信息。
        </span>
      ) : (
        <div>
          当前为手工录入更正公告，如您的项目在平台项目采购模块中进行，请前往项目采购应用发布更正公告，以便同步更新项目信息。
          <p>
            采购文件更正路径：【项目采购】-【项目管理】- <a href={getUrlWithAppCode(`${host}/bid-inviting/correction/list`, getAppCode())}>【采购文件更正】</a>
          </p>
          <p>
            资格预审文件更正路径：【项目采购】-【资格预审】- <a href={getUrlWithAppCode(`${host}/bid-inviting/prequalification/correctionList`, getAppCode())}>【资格预审文件更正】</a>
          </p>
        </div>
      );
      const confirmParams = {
        content: text,
        cancelText: '继续手工创建',
        onCancel() {
          that.visible();
          that.props.dispatch(
            routerRedux.push(url)
          );
        },
      };
      if (isSH) {
        confirmParams.okText = '前往';
        confirmParams.onOk = () => {
          that.visible();
          window.location.href = `${host}/bidding-entrust/#/correction/list`;
        };
      }
      confirm(confirmParams);
    } else {
      this.visible();
      if (distCode) {
        url = `/flow/adminCreate?announcementTypeName=${escape(params.name)}&announcementType=${params.typeId}&annBigType=${this.props.annBigType}&ids=${params.id}&districtId=${distCode || ''}`;
      }
      this.props.dispatch(
        routerRedux.push(url)
      );
    }
  }
 
  // 关闭弹窗
  visible = () => {
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'categoryModalVisible',
        visible: false,
      },
    });
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'projectModalVisibel',
        visible: false,
      },
    });
  }

  onSearch = (value) => {
    this.value = value;
    this.setState({
      current: 1,
    });
    this.props.dispatch({
      type: 'list/projectList',
      payload: {
        url: ANNOUNMENTS.includes(this.params?.typeId) ?
          '/announcement/api/project/projectList' : '/announcement/api/project/announcementProjectList',
        projectName: value,
        pageNo: 1,
        pageSize: 10,
        announcementType: this.params?.typeId,
        purchaseWay: this.params?.procurementMethodCode,
        districtCode: this.props.districtId || undefined,
      },
    });
  }
  hideModal = () => {
    this.setState({
      showPlanModal: false,
    });
  }
  showPlan = () => {
    this.setState({
      showPlanModal: true,
    });
    this.handleCancel1();
  }

  getBtns = (item = {}) => {
    const { dylygsPlanEntry, env, isJiaoTou } = this.state;
    const { distCode } = this.props;
    const btnFromProcurement = (
      <div className="btn-container">
        <Button
          type="primary"
          size="small"
          onClick={() => {
            this.showPlan(item);
          }}
        >
          从采购资金发起
        </Button>
      </div>
    );
    const btnFromProject = (
      <div className="btn-container">
        <Button
          type="primary"
          size="small"
          onClick={() => {
            this.addByExistProject(item);
          }}
        >
          从已有项目发起
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            this.addByHand(item);
          }}
        >
          手动录入公告
        </Button>
      </div>
    );
    const btnFromManual = (
      <div className="btn-container">
        <Button
          type="primary"
          size="small"
          onClick={() => {
            this.addByHand(item);
          }}
        >
          手动录入公告
        </Button>
      </div>
    );
    // 贵州的【公共服务项目验收结果公告】和 【其他履约验收公告】要从已有项目发起，贵州其他公告只要手工
    if (env === 'gz') {
      if ([6003, 3016].includes(item.typeId)) {
        return btnFromProject;
      } 
      return btnFromManual;
    }
    // 大连 交投 只要手动录入公告
    if (['ah', 'dl'].includes(env) || isJiaoTou) {
      return btnFromManual;
    } else if (item.typeId === 3012 && dylygsPlanEntry === '1') {
      return btnFromProcurement;
      // 政府采购意向公告不展示“从已有项目发起”按钮。原因：采购意向公告只能从采购计划发起，但是从已有项目发起是拿的项目采购的数据，是不正确的
    } else if (!distCode && env !== 'sh' && ![10016, 14001].includes(item.typeId)) {
      return btnFromProject;
    }
    return btnFromManual;
  }

  render() {
    const { dispatch, list, annBigType, tableLoading, syncLoading } = this.props;
    const { showPlanModal } = this.state;
    return (

      <div>
        <Modal
          visible={list.categoryModalVisible}
          title="新建公告"
          footer={null}
          onCancel={this.handleCancel1}
          wrapClassName="category-modal"
          style={{ top: '50px' }}
          destroyOnClose
          width={annBigType === '9' ? 680 : 520}
        >
          <Divider>请选择新建公告类别</Divider>
          <div className="category-list">
            {list.annType.map((item, index) => {
              return (
                <div className="item" key={item.id}>
                  <div className="icon">
                    <Icon type={ICONS[index]} zcy />
                  </div>
                  <div className="title">
                    {item.name}
                  </div>
                  {this.getBtns(item)}
                </div>
              );
            })}
          </div>
        </Modal>
        <Modal
          visible={list.projectModalVisibel}
          title="选择项目"
          onCancel={this.handleCancel2}
          // mask={false}
          width={800}
          style={{ top: '50px' }}
          footer={
            <div>
              <Button onClick={this.handleCancel2}>取消</Button>
              <Button
                onClick={this.onOk}
                type="primary"
                disabled={list.selectedProject == null}
                loading={syncLoading}
              >确认
              </Button>
            </div>
          }
        >
          <Spin spinning={tableLoading}>
            {
              list.projectModalVisibel ? (
                <div className="search-content" >
                  <Input.Search
                    placeholder="请输入项目名称"
                    enterButton="搜索"
                    onSearch={value => this.onSearch(value)}
                  />
                </div>
              ) : null
            }
            <Table
              rowKey="projectId"
              rowSelection={getRowSelection(dispatch)}
              columns={tableColumns}
              pagination={
                {
                  current: this.state.current,
                  total: list.projectData.total,
                  onChange: this.pageChange,
                }
              }
              dataSource={list.projectData.data}
            />
          </Spin>
        </Modal>
        {
          showPlanModal ? (
            <PlanModal
              show={showPlanModal}
              hideModal={this.hideModal}
            />
          ) : null
        }
      </div>
    );
  }
}

export default addModal;
