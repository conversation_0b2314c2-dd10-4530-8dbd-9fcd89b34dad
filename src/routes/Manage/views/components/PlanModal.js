import React, { Component } from 'react';
import { connect } from 'dva';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Button, Tag, message, Tooltip, Input, Alert, request } from 'doraemon';
import LargeContent from 'components/LargeShow';
import { moneyFormat } from '../../../../utils/utils.js';
import './PlanModal.less';

const columns = [
  {
    title: '采购编号',
    dataIndex: 'purchaseNo',
    key: 'purchaseNo',
    width: 250,
    render: (text, record) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >
          <a
            href={`${window.envHref.settlement}/purchaseplan_front/#/plan/list/view?id=${record.id}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {text}
          </a>
          {!!record.isTemporary && <span className="temporary">临</span>}
        </Tooltip>
      );
    },
  },
  {
    title: '资金总额',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 140,
    align: 'right',
    render: (text) => {
      return (
        <div className="colPlanInfo">
          <div>
            <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
          </div>
        </div>
      );
    },
  },
  {
    title: '采购编号名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 140,
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >

          <LargeContent width={105} content={text} />
        </Tooltip>
      );
    },
  },
  {
    title: '组织形式',
    dataIndex: 'procurementType',
    key: 'procurementType',
    width: 120,
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >

          <LargeContent width={105} content={text} />
        </Tooltip>
      );
    },
  },
  {
    title: '采购方式',
    dataIndex: 'procurementMethod',
    key: 'procurementMethod',
    width: 100,
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >

          <LargeContent width={85} content={text} />
        </Tooltip>
      );
    },
  },
];

@connect(({ appCommon, loading }) => ({
  appCommon,
  loading,
}))
export default class PlanModal extends Component {
  state = {
    plans: [],
    plansTotal: 0,
    pageNo: 1, // 当前页
    result: null, // 选中项的id
    selected: [], // 选中项对象
    pageSelected: {}, // 每页选中项对象{page1: []}
  };
  componentWillUnmount() {
    this.setState = () => {
      // This is intentional
    };
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.show && !this.props.show) {
      this.onSearch();
    }
  }
  componentDidMount() {
    this.onSearch();
  }
  onSearch = (params) => {
    const { dispatch } = this.props;
    const url = '/api/purchaseplan/intention/announcement/pagingSignleSourceData';
    const myparams = params ? {
      ...params,
      url,
      signleSourcePublished: 0,
    } : undefined;
    dispatch({
      type: 'appCommon/fetchData',
      payload: myparams || {
        pageNo: 1,
        pageSize: 6,
        url,
        signleSourcePublished: 0,
      },
    }).then((res) => {
      if (res) {
        const info = {
          plans: res.data,
          plansTotal: res.total,
          pageNo: params ? params.pageNo : 1,
        };
        this.setState(info);
      }
    });
  }
  onOk = async () => {
    const { result, selected } = this.state;
    if (!result.length || !selected.length) {
      message.error('请选择采购计划');
      return;
    }
    const res = await request('/api/purchaseplan/intention/announcement/buildPurchaseSignleSource', {
      method: 'post',
      data: {
        ids: result,
        pageType: selected[0].pageType,
      },
    });
    const data = res.result;
    const params = [
      `announcementType=${data.announcementType}`,
      `orderId=${data.serialNum}`,
      `districtId=${data.districtCode}`,
      `isRelationProject=${true}`,
      `appCode=${data.appCode}`,
      `title=${data.title ? escape(data.title) : ''}`,
      'annBigType=1',
    ];
    if (res.success) {
      location.href = `#/flow/create?${params.join('&')}`;
    }
  }
  del = (id) => {
    const { pageSelected } = this.state;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          if (pageSelected[item][j].id !== id) {
            result.push(pageSelected[item][j].id);
            selected.push(pageSelected[item][j]);
          } else {
            pageSelected[item].splice(j, 1);
            j -= 1;
          }
        }
      }
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  onSelectChange = (ids, rows) => {
    const { pageNo, pageSelected } = this.state;
    pageSelected[`page${pageNo}`] = rows;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          result.push(pageSelected[item][j].id);
          selected.push(pageSelected[item][j]);
        }
      }
    }
    if (result.length > 30) {
      message.destroy();
      message.error('批量变更总数不可超过30条！');
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  render() {
    const { plansTotal, plans, result, selected } = this.state;
    const { hideModal, loading } = this.props;
    return (
      <Modal
        title="采购资金选择"
        visible={this.props.show}
        width={980}
        onCancel={hideModal}
        onOk={this.onOk}
        className="tableModal modalList"
        footer={[
          <Button key="back" onClick={hideModal}>取消</Button>,
          <Button key="submit" onClick={this.onOk} type="primary" >
            确定
          </Button>,
        ]}
      >
        <Alert
          message="本列表仅展示未发布过单一来源公示且未使用的采购资金，每条采购资金仅支持发布一次单一来源公示。"
          type="firstinfo"
          style={{
            margin: '-5px -18px 10px',
          }}
          closable
        />
        <div className="PlanModal">
          <ZcyList
            customItem={[
              {
                label: '采购编号',
                id: 'purchaseNo',
                labelCol: { span: 4 },
                render: () => {
                  return <Input placeholder="请输入" />;
                },
              },
              {
                label: '采购编号名称',
                id: 'projectName',
                render: () => {
                  return <Input placeholder="请输入" />;
                },
              },
            ]}
            column={2}
            onSearch={this.onSearch}
            tabs={{
              tabList: [{
                label: '采购资金列表',
                value: 'all',
                key: 'all',
              }],
            }}
            preTabelContent={(
              <div style={{
                marginBottom: '14px',
              }}
              >
                {selected.map(item => (
                  <Tag
                    closable
                    color="orange"
                    key={item.id}
                    onClose={() => {
                      this.del(item.id);
                    }}
                    style={{ marginBottom: '4px' }}
                  >
                    {item.purchaseNo}
                  </Tag>
                ))}
              </div>
            )}
            table={{
              rowKey: 'id',
              columns,
              dataSource: plans,
              pagination: {
                total: plansTotal,
                defaultPageSize: 6,
              },
              rowSelection: {
                selectedRowKeys: result,
                type: 'checkbox',
                onChange: this.onSelectChange,
                getCheckboxProps: record => ({
                  disabled: record.canReturn === 0,
                }),
              },
              loading: loading.effects['appCommon/getPlans'],
            }}
          />
        </div>
      </Modal>
    );
  }
}
