import {
  PageAnnDataGetResponse,
} from 'src/api/announcement/api';

export type PageAnnData = NonNullable<PageAnnDataGetResponse['result']>;

export type PageListDataResponse = PageAnnData

export type PageListDataItem = NonNullable<PageAnnData['data']>[0];


export interface SourceItem { code: string; name: string }

export type SourceType = 'type' | 'createOrgId' | 'status' | 'tagCodes';

export interface CustomItemProps {
  annTypeList: SourceItem[];
  orgList: SourceItem[];
  annStatusList: SourceItem[];
  tagList: SourceItem[];
  getSelectToType: (type: SourceType, value: string) => void;
}
