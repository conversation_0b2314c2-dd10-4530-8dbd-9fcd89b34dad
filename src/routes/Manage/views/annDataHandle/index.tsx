/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-06-16 14:57:26
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-06-27 10:04:18
 * @FilePath: /zcy-announcement-v2-front/src/routes/Manage/views/annDataHandle.tsx
 * @Description: 
 */
import React, { useEffect, useRef, useState } from 'react';
import {
  ZcyBreadcrumb,
  ZcyList,
  Spin,
  message,
} from 'doraemon';
import DistrictCascader from 'src/components/DistrictCascader';
import { FFC } from 'src/types/component';
import EmptyBlock from 'src/components/Empty';
import { debounce } from '@zcy/utils';
import {
  annTypesGet,
  listOrgGet,
  annStatusesGet,
} from 'src/api/announcement/config';
import {
  listValidTagConfigGet,
  pageAnnDataPost,
} from 'src/api/announcement/api';
import { getBatchBtn, getColumns, getCustomItem } from './config';
import { PageListDataResponse, PageListDataItem, SourceItem, SourceType } from './types';

const ROUTES = [
  {
    label: '公告数据处理',
  },
];

const AnnDataHandle: FFC = ({ history }) => {
  const listRef = useRef<{
    handleReset:() => void
  }>();
  const [firstTag, setFirstTag] = useState(true);
  const [districtInfo, setDistrictInfo] = useState({
    name: '',
    code: '',
  });
  const [loading, setLoading] = useState(false);
  const [annData, setAnnData] = useState<PageListDataResponse>({});
  const [selectedRows, setSelectedRows] = useState<PageListDataItem[]>([]);

  const [annTypeList, setAnnTypeList] = useState<SourceItem[]>([]);
  const [orgList, setOrgList] = useState<SourceItem[]>([]);
  const [annStatusList, setAnnStatusList] = useState<SourceItem[]>([]);
  const [tagList, setTagList] = useState<SourceItem[]>([]);
  /**
   * 暂存当前页请求数据，用于当前页刷新
  */
  const [paramsData, setParamsData] = useState({
    
  });

  useEffect(() => {
    getSelectToType('createOrgId');
    getSelectToType('status');
    getSelectToType('tagCodes');
    getSelectToType('type');
  }, []);

  useEffect(() => {
    if (!districtInfo.code) return;
    if (firstTag) return;
    listRef.current?.handleReset();
  }, [districtInfo.code]);

  const onRefresh = () => {
    onSearch({ ...paramsData });
  };

  const onChangeCode = (codeArr, arr) => {
    if (codeArr?.length) {
      const code = codeArr[codeArr.length - 1];
      const name = arr[arr.length - 1].name;

      if (code === '000000') {
        return message.warning('该接口不支持全国区划查询，请选择具体区划');
      }
      setDistrictInfo({
        name,
        code,
      });
    }
  };

  const onSearch = (params) => {
    if (!districtInfo.code) return;
    if (firstTag) {
      if (params?.createOrgId?.label) {
        getSelectToType('createOrgId', params?.createOrgId?.label);
      }
      if (params?.type?.label) {
        getSelectToType('type', params?.type?.label);
      }
    }
    setFirstTag(false);
    setParamsData({ ...params });


    const payload = {
      ...params,
      startReleasedAt: params.publishTime?.length ? params.publishTime[0] : '',
      endReleasedAt: params.publishTime?.length ? params.publishTime[1] : '',
      createOrgId: params.createOrgId?.key,
      type: params.type?.key,
      tagCodes: params?.tagCodes?.map(ele => ele.key).join(','),
      parentDistrictCode: districtInfo.code,
    };
    delete payload.publishTime;
    delete payload.announcementId;

    setLoading(true);
    pageAnnDataPost({
      ...payload,
    }).then((res) => {
      setLoading(false);
      if (!res.success) return;
      setAnnData(res.result!);
    }).finally(() => {
      return setLoading(false);
    });
  };

  const getSelectToType = (type: SourceType, value = '') => {
    switch (type) {
      case 'type':
        return annTypesGet({ name: value }).then((res) => {
          if (!res.success) return;
          setAnnTypeList(res.result as SourceItem[]);
        });
      case 'createOrgId':
        return listOrgGet({ orgName: value }).then((res) => {
          if (!res.success) return;
          setOrgList(res.result as SourceItem[]);
        });
      case 'tagCodes':
        return listValidTagConfigGet().then((res) => {
          if (!res.success) return;
          setTagList((res.result || []).map(ele => ({
            name: ele.tagDisplay!,
            code: ele.tagCode!,
          })));
        });
      case 'status':
        return annStatusesGet().then((res) => {
          if (!res.success) return;
          setAnnStatusList(res.result as SourceItem[]);
        });
    }
  };

  const getSelectToTypeDebounce: any = debounce(getSelectToType, 800);

  const onSelectChange = (keys, rows) => {
    const tempRows = [...selectedRows, ...rows];
    setSelectedRows(keys.map(id => tempRows.find(ele => ele.id === id)));
  };

  const emptyText = () => {
    return districtInfo.code === '000000' ? '请选择一个下级区划进行查询' : '暂无数据';
  };

  return (
    <Spin spinning={loading}>
      <ZcyBreadcrumb
        routes={ROUTES}
        extraContent={
          <DistrictCascader
            changeOnSelect
            localKey="annDataHandle"
            disabled={false}
            onChangeCode={onChangeCode}
          />
        }
      />

      {
        districtInfo.code ? (
          <ZcyList
            customItem={
              getCustomItem({
                annTypeList,
                orgList,
                annStatusList,
                tagList,
                getSelectToType: getSelectToTypeDebounce,
              })
            }
            batchBtn={getBatchBtn({ selectedRows, onRefresh })}
            openSearchCache
            table={{
              rowKey: 'id',
              columns: getColumns({ onRefresh, setLoading, history }),
              dataSource: annData?.data,
              locale: {
                emptyText: emptyText(),
              },
              pagination: {
                pageSizeOptions: ['10', '20', '30', '40'],
                showQuickJumper: true,
                showSizeChanger: true,
                total: annData?.total || 0,
              },
              rowSelection: {
                type: 'checkbox',
                onChange: onSelectChange,
              },
            }}
            onSearch={onSearch}
            ref={listRef}
          />
        ) : <EmptyBlock />
      }
    </Spin>
  );
};

export default AnnDataHandle;

