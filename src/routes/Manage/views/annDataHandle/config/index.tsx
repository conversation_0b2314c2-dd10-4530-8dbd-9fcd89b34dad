import React from 'react';
import { Select, DatePicker, Input, But<PERSON>, Badge } from 'doraemon';
import moment from 'moment';
import { ANN_SOURCE_TYPE } from 'src/constants';
import { TAG_SOURCE_TYPE_MAP, renderTags } from 'src/routes/Manage/config/custom-tag';
import { fixNull, getPubType } from 'src/utils/utils';
import OutUrlContent from 'src/components/OutUrlContent';
import { isEmpty } from 'lodash';
import { CustomItemProps } from '../types';
import { handleBatchRePush, handlerBatchRevoke, handlerGoDetail, handlerPullAnn, handlerReEditAnn, handlerRePushAnn } from '../handler';

const Option = Select.Option;
const RangePicker = DatePicker.RangePicker;


const getAnnTime = (start, end) => {
  if (!start && !end) {
    return '-';
  }
  const annStart = start ? moment(start).format('YYYY-MM-DD') : '*';
  const annEnd = end ? moment(end).format('YYYY-MM-DD') : '*';
  return `${annStart} ~ ${annEnd}`;
};

export const getCustomItem = ({
  annTypeList = [],
  orgList = [],
  annStatusList = [],
  tagList = [],
  getSelectToType,
}: CustomItemProps) => {
  return (
    [
      {
        label: '公告标题',
        id: 'title',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '公告类型',
        id: 'type',
        render: () => (
          <Select
            labelInValue
            allowClear
            showSearch
            onSearch={value => getSelectToType('type', value)}
            optionFilterProp="name"
          >
            {
              annTypeList.map((item) =>
                <Option name={item.name} key={item.code} value={item.code}>{item.name}</Option>
              )
            }
          </Select>
        ),
      },
      {
        label: '项目名称',
        id: 'projectName',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '项目编号',
        id: 'projectCode',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '发布时间',
        id: 'publishTime',
        render: () => {
          return (
            <RangePicker
              showTime={{
                defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
              }}
            />
          );
        },
      },
      {
        label: '发布单位',
        id: 'createOrgId',
        render: () => (
          <Select
            labelInValue
            allowClear
            showSearch
            onSearch={value => getSelectToType('createOrgId', value)}
            optionFilterProp="name"
          >
            {
              orgList.map((item) =>
                <Option name={item.name} key={item.code} value={item.code}>{item.name}</Option>
              )
            }
          </Select>
        ),
      },
      {
        label: '状态',
        id: 'status',
        decoratorOptions: {
          initialValue: '-1',
        },
        render: () => {
          return (
            <Select>
              <Option key="-1" value="-1">全部</Option>
              {
                annStatusList.map((item) =>
                  <Option key={item.code} value={item.code}>{item.name}</Option>
                )
              }
            </Select>
          );
        },
      },
      {
        label: '关键字',
        id: 'contentKeyWord',
        render: () => {
          return <Input placeholder="请输入" />;
        },
      },
      {
        label: '公告标签',
        id: 'tagCodes',
        render: () => (
          <Select
            labelInValue
            allowClear
            showSearch
            mode="multiple"
          >
            {
              tagList.map((item) =>
                <Option key={item.code} value={item.code}>{item.name}</Option>
              )
            }
          </Select>
        ),
      },
      {
        label: '公告来源',
        id: 'annSource',
        decoratorOptions: {
          initialValue: -1,
        },
        render: () => {
          return (
            <Select>
              {
                ANN_SOURCE_TYPE.map((item) =>
                  <Option key={item.value} value={item.value}>{item.key}</Option>
                )
              }
            </Select>
          );
        },
      },
    ]
  );
};


export const getColumns = ({ onRefresh, setLoading, history }) => {
  return [
    {
      title: '公告标题',
      dataIndex: 'title',
      key: 'title',
      // width: 200,
      render: (text, record) => {
        return (
          <div>
            <span className="customLinkBlue" onClick={() => handlerGoDetail(record, { history })}>
              {text}
            </span>
            <br />
            {renderTags(
              TAG_SOURCE_TYPE_MAP.business,
              record.announcementTagDtoList,
              { maxWidth: 180 }
            )}
          </div>
        );
      },
    },
    {
      title: '公告信息',
      width: 265,
      dataIndex: 'info',
      key: 'info',
      render: (text, record) => {
        return (
          <React.Fragment>
            <p title={record.projectName}>
              <span>项目名称：{fixNull(record.projectName)}</span>
            </p>
            <p title={record.projectCode}>
              <span>项目编号：{fixNull(record.projectCode)}</span>
            </p>
            <p title={record.amount} >
              <span>项目金额：{fixNull(record.amount)}</span>
            </p>
            <p title={record.announcementTypeName} >
              <span>公告类型：{fixNull(record.announcementTypeName)}</span>
            </p>
            <p title={record.annSourceDesc} >
              <span>公告来源：{fixNull(record.annSourceDesc)}</span>
            </p>
            <p title={record.creatorName} >
              <span>创建人：{fixNull(record.creatorName)}</span>
            </p>
          </React.Fragment>
        );
      },
    },
    {
      title: '发布信息',
      dataIndex: 'pubType',
      width: 265,
      render: (_, record) => {
        return (
          <React.Fragment>
            <p title={record.releasedAt ? moment(record.releasedAt).format('YYYY-MM-DD') : '-'}>
              <span>发布时间：{record.releasedAt ? moment(record.releasedAt).format('YYYY-MM-DD') : '-'}</span>
            </p>
            <p title={getPubType(record.pubType)} >
              <span>发布方式：{getPubType(record.pubType)}</span>
            </p>
            <p title={record.creatorOrgDistrictName} >
              <span>发布区划：{fixNull(record.creatorOrgDistrictName)}</span>
            </p>
            <p title={record.creatorOrgName} >
              <span>发布单位：{fixNull(record.creatorOrgName)}</span>
            </p>
            <p title={getAnnTime(record.releasedAt, record.expiredAt)} >
              <span>公示期：{getAnnTime(record.releasedAt, record.expiredAt)}</span>
            </p>
            <p title={record.creatorName} >
              <span>发布人：{fixNull(record.creatorName)}</span>
            </p>
          </React.Fragment>
        );
      },
    },
    {
      title: '推送时间',
      width: 115,
      dataIndex: 'publishTime',
      key: 'publishTime',
      render: (_, record) => {
        return (
          <span >
            {record.publishTime ? moment(record.publishTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </span>
        );
      },
    },
    {
      title: '公告状态',
      width: 96,
      dataIndex: 'statusName',
      key: 'statusName',
      render: (text, record) => {
        let badgeStatus;
        badgeStatus = 'processing';
        if ([3].indexOf(record.status) !== -1) {
          badgeStatus = 'warning';
        }
        if ([4, 5].indexOf(record.status) !== -1) {
          badgeStatus = 'success';
        }
        return (
          <div>
            <Badge status={badgeStatus} text={text} />
            <br />
            {renderTags(
              TAG_SOURCE_TYPE_MAP.system,
              record.announcementTagDtoList
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      width: 120,
      dataIndex: 'pushDetails',
      key: 'pushDetails',
      render: (_, record) => {
        return (
          <>
            {
              !isEmpty(record.pushDetails) ? (
                <OutUrlContent
                  hideDivider
                  placement="left"
                  list={record.pushDetails.map((ele) => ({
                    ...ele,
                    outName: ele.targetName,
                  }))}
                />
              ) : ''
            }
            <Button type="text" onClick={() => handlerPullAnn(record, { onRefresh })}>下架</Button>
            {
              record?.canDataCorrection ? <Button type="text" onClick={() => handlerReEditAnn(record, { history })}>数据订正</Button> : null
            }
            <Button type="text" onClick={() => handlerRePushAnn(record, { onRefresh })}>重新推送</Button>
          </>
        );
      },
    },
  ];
};


export const getBatchBtn = ({ selectedRows, onRefresh }) => {
  const disabled = !selectedRows.length;
  return [
    {
      label: '批量下架',
      handleClick: () => {
        handlerBatchRevoke(selectedRows, { onRefresh });
      },
      disabled,
    }, {
      label: '批量重推',
      handleClick: () => {
        handleBatchRePush(selectedRows, { onRefresh });
      },
      disabled,
    },
  ];
};
