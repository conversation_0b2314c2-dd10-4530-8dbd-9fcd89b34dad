import React from 'react';
import {
  message,
  Modal,
} from 'doraemon';
import PullAnnDrawer from 'src/routes/Manage/components/PullAnnDrawer';
import { customRender } from 'src/utils/customRender';
import BatchRevokeModal from 'src/routes/Manage/components/RevokeModal';
import BatchRepushModal from 'src/routes/Manage/components/BatchRepushModal';
import { rePushByAnnouncementPost } from 'src/api/announcement/api';
import { pushPost } from 'src/api/announcement/api/admin/batch';
import { PageListDataItem } from '../types';

export const handlerAction = () => {
  //
};


export const handlerGoDetail = (record: PageListDataItem, { history }) => {
  const { formPageCode, id, district, annBigType } = record;
  if (formPageCode) {
    return history.push(`/dynamic/detail?formPageCode=${formPageCode}&annId=${id}&districtId=${district}&annBigType=${annBigType}`);
  }
  return history.push(`/detail/${annBigType}/${id}`);
};

export const handlerPullAnn = (record: PageListDataItem, { onRefresh }) => {
  return customRender(<PullAnnDrawer id={record.id} onSubmit={onRefresh} />);
};

export const handlerReEditAnn = (record: PageListDataItem, { history }) => {
  return history.push(`/manage/reEdit/${record.id}`);
};

export const handlerRePushAnn = (record: PageListDataItem, { onRefresh }) => {
  return Modal.confirm({
    title: '确定重新推送公告吗？',
    onOk: () => {
      return rePushByAnnouncementPost({ announcementId: record.id }).then((res) => {
        if (!res.success) return;
        if (res.result) {
          message.success('重新推送成功', () => {
            onRefresh();
          });
        }
      });
    },
  });
};

export const handlerBatchRevoke = (recordList: PageListDataItem[], { onRefresh }) => {
  return customRender(<BatchRevokeModal annList={recordList} onSubmit={onRefresh} />);
};

export const handleBatchRePush = (recordList: PageListDataItem[], { onRefresh }) => {
  return Modal.confirm({
    title: '确定要批量重推所选公告吗？',
    content: `已选择 ${recordList.length} 个公告，批量重推不可预览`,
    onOk: () => {
      return pushPost({
        announcementIds: recordList.map(ele => +ele.id!),
      }).then(res => {
        if (!res.success) return;
        const totalNum = res.result!.totalNum!;
        const successNum = res.result!.successNum!;
        const failNum = res.result!.failNum!;
        const details = res.result!.details!;
        if (totalNum === successNum) {
          message.success('批量重推成功', () => {
            onRefresh();
          });
          return;
        }
        return customRender(
          <BatchRepushModal
            info={{ successCount: successNum, errorCount: failNum, dataSource: details }}
            onSubmit={onRefresh}
          />);
      });
    },
  });
};
