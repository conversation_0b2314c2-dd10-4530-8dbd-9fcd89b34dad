/* eslint-disable no-extra-boolean-cast */
/* eslint-disable react/no-unused-state */
import React, { Component } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import moment from 'moment';
import {
  ZcyList,
  Input,
  ZcyBreadcrumb,
  DatePicker,
  Select,
  Tooltip,
  Badge,
  message,
  Modal,
  Icon,
  Form,
  Tag,
  Table,
} from 'doraemon';
import _ from 'lodash';
import SMSModal from 'components/SMS/SMS-Modal';
import TimeCountDown from 'components/TimeCountDown';
import OutUrlContent from 'components/OutUrlContent';
import ModalSelect from './components/modal-select';
import ObjectModal from './components/ObjectModal';
import DistrictModal from './components/DistrictModal';
import {
  deleteAnnouncement,
  releaseAnnouncement,
  revokeAnnouncement,
  canCreateAnnouncement,
  obtainOpeatorId,
  deleteFormAnnouncement,
  recallFormAnnouncement,
} from '../services';
import './index.less';
import RePushModal from './components/re-push-modal';

const Actions = Table.Actions;
const status = [
  {
    key: 0,
    value: '待完善',
  },
  {
    key: 1,
    value: '审核中',
  },
  {
    key: 2,
    value: '待发布',
  },
  {
    key: 3,
    value: '被退回',
  },
  {
    key: 4,
    value: '已发布',
  },
  {
    key: 5,
    value: '已结束',
  },
  {
    key: 7,
    value: '已废弃',
  },
  {
    key: 41,
    value: '异议待确认',
  },
  {
    value: '取消审核中',
    key: 8,
  },
  {
    value: '已取消',
    key: 9,
  },
];


const confirm = Modal.confirm;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};


@connect(({ list, loading }) => ({
  list,
  tableLoading: loading.models.list,
}))
@Form.create()
export default class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      permission: false,
      annBigType: null,
      loadAnnType: true,
      cancelVisible: false,
      operateId: null,
      canRevokeList: [],
      objectModal: false,
      districtModalVisible: false,
      objectAnnId: null,
      districtId: undefined,
      SMSVisible: false, // 短信通知弹窗
      // announcementId: undefined, // 当前选中行的公告 Id
      currentRecord: {}, // 当前选中行
      hideRevertBtn: {},
      rePushVisible: false, // 重新推送公告弹窗
      announcementTypes: '', // 公告小类型
    };
  }

handleSearch = (params) => {
  if (params.date && params.date.length === 2) {
    params.releaseStartAt = params.date[0];
    params.releaseEndAt = params.date[1];
  }
  delete params.date;
  this.props.dispatch({
    type: 'list/setParams',
    payload: params,
  });
  this.props.dispatch({
    type: 'list/getAnnouncementList',
    payload: {
      annBigTypes: this.state.annBigType,
      announcementTypes: this.state.announcementTypes,
      ...params,
    },
  });
};

componentWillReceiveProps(nextProps) {
  const match = nextProps.location.pathname.split('/');
  if (this.state.annBigType !== match[3] && this.state.annBigType !== null) {
    this.setState(
      {
        annBigType: match[3],
      },
      () => {
        this.props
          .dispatch({
            type: 'list/setParams',
            payload: {
              pageNo: 1,
              pageSize: 10,
              type: '1',
            },
          })
          .then(() => {
            this.getInitData();
          });
      },
    );
  }
}

componentDidMount = () => {
  const { annBigType, annSubType } = this.props.match.params;
  setTimeout(() => {
    this.setState(
      {
        annBigType,
        announcementTypes: annSubType,
      },
      () => {
        this.getInitData();
      },
    );
  });
  canCreateAnnouncement().then((res) => {
    this.setState({
      permission: res.result,
    });
  });
  obtainOpeatorId().then((res) => {
    this.setState({
      operateId: res.result,
    });
  });
};

getInitData = () => {
  this.props
    .dispatch({
      type: 'list/getCanRevokeList',
      payload: {
        parentId: this.state.annBigType,
      },
    })
    .then((res) => {
      const { result: canRevokeList = [] } = res;
      this.setState({
        canRevokeList,
      });
    });
  this.props
    .dispatch({
      type: 'list/getAnnTypeByAnnBigType',
      payload: {
        annBigType: this.state.annBigType,
      },
    })
    .then(() => {
      this.setState({
        loadAnnType: false,
      });
    });
  this.props
    .dispatch({
      type: 'list/getAnnouncementList',
      payload: {
        annBigTypes: this.state.annBigType,
        announcementTypes: this.state.announcementTypes,
        ...this.props.list.params,
      },
    })
    .then((res) => {
      if (!res.success) {
        Modal.error({
          title: '提示',
          content: '公告不存在或无权查看此类型公告！',
          onOk: () => {
            this.props.dispatch(routerRedux.replace('/overview'));
          },
        });
      }
    });
};

handelAddAnnouncement = () => {
  this.props.dispatch({
    type: 'list/setModalVisible',
    payload: {
      type: 'categoryModalVisible',
      visible: true,
    },
  });
};
showDistrictModalVisible = () => {
  this.setState({
    districtModalVisible: true,
  });
};
detail = (item) => {
  if (!!item.formPageCode) {
    this.props.dispatch(
      routerRedux.push(
        `/dynamic/detail?formPageCode=${item.formPageCode}&annId=${item.id}&districtId=${item.district}&
        annBigType=${item.annBigType}`,
      ),
    );
  } else {
    this.props.dispatch(routerRedux.push(`/detail/${this.state.annBigType}/${item.id}`));
  }
};
reload = () => {
  this.props.dispatch({
    type: 'list/getAnnouncementList',
    payload: {
      annBigTypes: this.state.annBigType,
      announcementTypes: this.state.announcementTypes,
      ...this.props.list.params,
    },
  });
};
onCancelOk = () => {
  const { currentRecord = {} } = this.state;
  this.props.form.validateFields((err, values) => {
    if (!err) {
      if (!!currentRecord.formPageCode) {
        recallFormAnnouncement({
          id: currentRecord.id,
          pageCode: currentRecord.formPageCode,
          processDefineKey: currentRecord.processDefineKey,
          taskId: currentRecord.taskModelId,
          reason: values.revokeRes || '',
        }).then((res) => {
          if (res && res.success) {
            message.success('撤回成功！');
            this.setState({
              cancelVisible: false,
            });
            this.reload();
          } else {
            message.error(res.error);
          }
        });
      } else {
        revokeAnnouncement({
          ...values,
          announcementId: this.announcementIdForCancel,
        }).then((res) => {
          if (res.success) {
            message.success('撤回成功！');
            this.setState({
              cancelVisible: false,
            });
            this.reload();
          } else {
            Modal.error({
              title: res.error,
            });
          }
        });
      }
    }
  });
};
objectAction = (record) => {
  this.setState({
    objectModal: true,
    objectAnnId: record.id,
  });
};
hideModal = () => {
  this.setState({
    objectModal: false,
    districtModalVisible: false,
  });
};
onDistrictChange = (values) => {
  this.setState(values);
  this.props.dispatch({
    type: 'list/getAnnTypeByAnnBigType',
    payload: {
      annBigType: this.state.annBigType,
      districtCode: values.districtId,
    },
  });
  this.hideModal();
  this.handelAddAnnouncement();
};

// 短信弹窗
handleSMSVisible = (record) => {
  if (!_.isEmpty(record)) {
    this.setState({
      // announcementId: record.id,
      currentRecord: record,
    });
  }
  this.setState({
    SMSVisible: !this.state.SMSVisible,
  });
};

handleRePushVisible = (record) => {
  if (!_.isEmpty(record)) {
    this.setState({
      currentRecord: record,
    });
  }
  this.setState({
    rePushVisible: !this.state.rePushVisible,
  });
};

// 撤回发布操作
handleRevokePublish = (record) => {
  Modal.confirm({
    title: '是否确认撤回发布该公告？',
    onOk: () => {
      revokeAnnouncement({
        announcementId: record.id,
      }).then((res) => {
        // es查询延迟需要延迟一秒进行查询状态才会变更
        setTimeout(() => {
          this.reload();
        }, 1000);
        if (res && res.success) {
          message.success('撤回发布成功！');
        } else {
          message.error(res.error);
        }
      });
    },
  });
};

render() {
  const { list, tableLoading } = this.props;
  const {
    objectModal,
    objectAnnId,
    districtModalVisible,
    districtId,
    SMSVisible,
    currentRecord = {},
    hideRevertBtn = {},
    rePushVisible,
  } = this.state;
  const { getFieldDecorator } = this.props.form;
  const breadcrumb = [
    {
      label: '资格预审信息发布',
    },
  ];
  const customItem = [
    {
      label: '公告标题',
      id: 'title',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    },
    {
      label: '项目名称',
      id: 'projectName',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    },
    {
      label: '项目编号',
      id: 'projectCode',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    },
    {
      label: '发布时间',
      id: 'date',
      render: () => {
        return (
          <DatePicker.RangePicker
            showTime={{
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
            }}
          />
        );
      },
    },
    {
      label: '状态',
      id: 'queryStatus',
      decoratorOptions: {
        initialValue: '',
      },
      render: () => {
        return (
          <Select placeholder="请选择">
            <Select.Option value="">全部</Select.Option>
            {status.map(item => (
              <Select.Option value={item.key} key={item.key}>
                {item.value}
              </Select.Option>
            ))}
          </Select>
        );
      },
    },
  ];
  const tabs = {
    tabList: [
      {
        label: '我的待办',
        value: 'all',
        key: '1',
      },
      {
        label: '全部',
        value: 'complete',
        key: '0',
        isAll: true,
      },
    ],
    defaultActiveKey: this.props.list.params.type,
  };
  const columns = [
    {
      title: '公告标题',
      width: 200,
      dataIndex: 'title',
      render: (text, item) => {
        const { objectionState } = item;
        let objStr;
        if (objectionState === 0) {
          objStr = '无异议';
        }
        if (objectionState === 1) {
          objStr = '有异议';
        }
        let identiNames = [];
        if (item.metaData) {
          const metaData = JSON.parse(item.metaData);
          identiNames = metaData.identiNames || [];
        }
        identiNames = identiNames.join('，');
        return (
          <div className="ann-title">
            <span className="short-str">
              {identiNames.length > 0 && (
                <Tag color="green" title={identiNames}>
                  {identiNames}
                </Tag>
              )}
              {objStr ? <span>[{objStr}]&nbsp;</span> : ''}
              <Tooltip title={text} placement="topLeft">
                <a
                  lassname="customLink"
                  onClick={() => {
                    this.detail(item);
                  }}
                >
                  {text}
                </a>
              </Tooltip>
            </span>
          </div>
        );
      },
    },
    {
      title: '项目信息',
      dataIndex: 'projectName',
      width: 200,
      render: (text, record) => {
        const { projectCode } = record;
        const projectInfo = (
          <div>
            <p>
              <span>项目编号：</span>
              {projectCode || '-'}
            </p>
            <p>
              <span>项目名称：</span>
              {text || '-'}
            </p>
          </div>
        );
        return (
          <div>
            <Tooltip title={projectInfo}>{projectInfo}</Tooltip>
          </div>
        );
      },
    },
    {
      title: '时间',
      dataIndex: 'releasedAt',
      width: 240,
      render: (text, record) => {
        const { expiredAt } = record;
        const timeInfo = (
          <div>
            <p>
              <span>发布时间：</span>
              {text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </p>
            <p>
              <span>截止时间：</span>
              {expiredAt ? moment(expiredAt).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </p>
          </div>
        );
        return (
          <div>
            <Tooltip title={timeInfo}>{timeInfo}</Tooltip>
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'showStatus',
      width: 120,
      render: (key, item, i) => {
        let badgeStatus;
        if ([0, 1, 2, 41, 8].indexOf(key) !== -1) {
          badgeStatus = 'processing';
        }
        if ([3].indexOf(key) !== -1) {
          badgeStatus = 'warning';
        }
        if ([4, 5].indexOf(key) !== -1) {
          badgeStatus = 'success';
        }
        if ([7, 9].indexOf(key) !== -1) {
          badgeStatus = 'default';
        }

        if (key !== null) {
          const text = status.find(s => s.key === key).value;
          return (
            <div>
              <Badge status={badgeStatus} text={text} />
              {item.canRevokePublishSeconds > 0 && !hideRevertBtn[`revertBtn${i}`] && (
                <TimeCountDown
                  time={(item.canRevokePublishSeconds || 0) * 1000}
                  cb={() => {
                    this.setState((state) => {
                      const { hideRevertBtn: btns } = state;
                      btns[`revertBtn${i}`] = true;
                      return {
                        hideRevertBtn: btns,
                      };
                    });
                  }}
                />
              )}
              <div>
                {/** 当公告状态为已发布时（code=4），展示外网推送结果 */}
                {key === 4 && !!item.rePushStatusName ? `(${item.rePushStatusName})` : ''}
              </div>
            </div>
          );
        }
        return '';
      },
    },
    {
      title: '操作',
      width: 180,
      render: (item, record, i) => {
        const maxShowSize = 3;
        /**
* formPageCode: 对接表单后的 pageCode，如果 formPageCode 有值，则跳转对接处理后的页面和接口，无值走老逻辑
* secondEdit：公告正文是否允许二次编辑，对接表单后使用。允许：编辑跳转富文本编辑器，不允许，跳转表单页
*/
        const {
          objectionState,
          // canNotify,
          formPageCode,
          // secondEdit,
          options = {},
          canRevokePublishSeconds = 0, // 剩余可撤回时间
        } = record;
        const {
          canNotify,
          canRevoke, // 是否可撤回
          canRePush, // 是否需要重新推送
        } = options;
        const operation = [
          {
            key: 1,
            value: '发布',
          },
          {
            key: 2,
            value: '审批',
          },
          {
            key: 4,
            value: '编辑',
          },
          {
            key: 8,
            value: '删除',
          },
        ];
        const itemList = [];
        operation.forEach((op) => {
          if ((op.key & item.dealCode) === op.key) {
            if (op.key === 1) {
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  confirm({
                    title: '提示',
                    content: '确认发布此公告？',
                    onOk: () => {
                      releaseAnnouncement({
                        announcementId: item.id,
                      }).then((res) => {
                        if (res && res.success) {
                          message.success('公告发布中');
                          this.reload();
                        } else {
                          message.error(res.error);
                        }
                      });
                    },
                  });
                },
              });
            }
            if (op.key === 2) {
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  if (!!formPageCode) {
                    this.detail(record);
                  } else {
                    this.props.dispatch(routerRedux.push(`/review/${this.state.annBigType}/${item.id}`));
                  }
                },
              });
            }
            if (op.key === 4) {
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  if (!!formPageCode) {
                    this.props.dispatch(
                      routerRedux.push(
                        `/dynamic/edit?announcementType=${record.announcementType}&formPageCode=${formPageCode}&annId=${record.id}&districtId=${record.district}&annBigType=${record.annBigType}`,
                      ),
                    );
                  } else {
                    this.props.dispatch(routerRedux.push(`/edit/${this.state.annBigType}/${item.id}`));
                  }
                },
              });
            }
            if (op.key === 8) {
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  confirm({
                    title: '提示',
                    content: '确认删除此公告？',
                    onOk: () => {
                      if (!!formPageCode) {
                        deleteFormAnnouncement({
                          id: record.id,
                          pageCode: formPageCode,
                          processDefineKey: record.processDefineKey,
                        }).then(() => {
                          message.success('删除成功！');
                          this.reload();
                        });
                      } else {
                        deleteAnnouncement({
                          id: item.id,
                        }).then(() => {
                          message.success('删除成功！');
                          this.reload();
                        });
                      }
                    },
                  });
                },
              });
            }
          }
        });
        // 收否能重新推送
        if (!!canRePush) {
          itemList.push({
            label: '重新推送',
            onRowClick: this.handleRePushVisible,
          });
        }
        if (!!canRevoke) {
          itemList.push({
            label: '撤回',
            onRowClick: () => {
              if (item.revokedLogicDelete) {
                confirm({
                  title: '是否继续撤回?',
                  content: `撤回后公告列表将不再展示该公告，编辑操作请至${item.appName}公告发起模块`,
                  closable: true,
                  onOk: () => {
                    this.announcementIdForCancel = item.id;
                    this.props.form.resetFields(['revokeRes']);
                    this.setState({
                      cancelVisible: true,
                      currentRecord: record,
                    });
                  },
                });
              } else {
                this.announcementIdForCancel = item.id;
                this.props.form.resetFields(['revokeRes']);
                this.setState({
                  cancelVisible: true,
                  currentRecord: record,
                });
              }
            },
          });
        }
        if (canRevokePublishSeconds > 0 && !hideRevertBtn[`revertBtn${i}`]) {
          itemList.push({
            label: '撤回发布',
            onRowClick: this.handleRevokePublish,
          });
        }
        if (objectionState === -1) {
          itemList.push({
            label: '异议确认',
            onRowClick: this.objectAction,
          });
        }
        if (!!canNotify) {
          itemList.push({
            label: '短信通知',
            onRowClick: this.handleSMSVisible,
          });
        }

        const hasOurUrlList = !!item.announcementOutUrlDtoList?.length;
        const getMaxShowSize = () => {
          if (hasOurUrlList) return maxShowSize - 1;
          return maxShowSize;
        };

        if (itemList.length === 0 && !hasOurUrlList) {
          return (
            <a
              className="customLinkBlue"
              onClick={() => {
                this.detail(item);
              }}
            >
              查看
            </a>
          );
        }

        const actions = itemList;

        return (
          <React.Fragment>
            {hasOurUrlList ? (
              <OutUrlContent
                hideDivider={!actions.length}
                list={item.announcementOutUrlDtoList}
              />
            ) : null}
            <Actions actions={actions} record={record} maxShowSize={getMaxShowSize()} />
          </React.Fragment>
        );
      },
    },
  ];
  const pagination = {
    total: list.announcementList.total,
    showSizeChanger: true,
    defaultCurrent: list.params.pageNo,
    defaultPageSize: list.params.pageSize,
  };
  const table = {
    columns,
    pagination,
    rowKey: 'id',
  };

  return (
    <div className="announcement-list" key={this.state.annBigType}>
      <ZcyBreadcrumb routes={breadcrumb} />
      <ZcyList
        customItem={customItem}
        tabs={tabs}
        tabKey="type"
        table={{
          ...table,
          loading: tableLoading,
          dataSource: list.announcementList.data,
          rowClassName: 'ann-row',
        }}
        onSearch={this.handleSearch}
      />
      <ModalSelect annBigType={this.state.annBigType} districtId={districtId} />
      <Modal
        title="公告撤回"
        visible={this.state.cancelVisible}
        onOk={this.onCancelOk}
        width={540}
        onCancel={() => {
          this.setState({
            cancelVisible: false,
          });
        }}
      >
        <Form layout="horizontal">
          <FormItem label="撤回理由" {...formItemLayout}>
            {getFieldDecorator('revokeRes', {
              rules: [{ required: true, message: '请输入' }],
            })(<Input.TextArea maxLength={255} placeholder="请输入" />)}
          </FormItem>
        </Form>
        <div className="revoke-note">
          <Icon type="exclamation-circle" /> 提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作。
        </div>
      </Modal>
      {objectModal && (
        <ObjectModal show={objectModal}
          hideMe={this.hideModal}
          annId={objectAnnId}
          reload={this.reload}
        />
      )}
      {
        districtModalVisible && (
          <DistrictModal
            show={districtModalVisible}
            hideMe={this.hideModal}
            onDistrictChange={this.onDistrictChange}
          />
        )
      }
      {SMSVisible ? (
        <SMSModal
          visible={SMSVisible}
          onCancel={this.handleSMSVisible}
          announcementId={currentRecord.id}
          distCode={currentRecord.district}
        />
      ) : null}
      {rePushVisible ? (
        <RePushModal
          visible={rePushVisible}
          onCancel={this.handleRePushVisible}
          announcementId={currentRecord.id}
          reload={this.reload}
        />
      ) : null}
    </div>
  );
}
}
