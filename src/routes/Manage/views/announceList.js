import React, { Component } from 'react';
import { connect } from 'dva';
import {
  <PERSON>cy<PERSON><PERSON>,
  Select,
  ZcyList,
} from 'doraemon';
import _get from 'lodash/get';
import searchConfig from '../config/publicQuery-searchConfig';
import { columnConfig } from '../config/publicQuery-tableConfig';
import { selectConfig } from '../config/publicQuery-selectConfig';
import './index.less';
import tracer from 'src/utils/tracer';
import pushLog from 'src/utils/pushLog';

@connect(({ publicQuery, loading }) => ({
  publicQuery,
  annTypesLoading: loading.effects['publicQuery/fetchAnnTypes'],
  publishOrgListLoading: loading.effects['publicQuery/fetchPublishOrgList'],
  pageLoading: loading.effects['publicQuery/fetchList'],
  treeLoading: loading.effects['publicQuery/fetchDistrictTree'],
}))
export default class publicQuery extends Component {
  constructor(props) {
    super(props);
    const listParams = _get(this.props, 'publicQuery.listParams', {}) || {};
    // const searchParams = _get(this.props, 'publicQuery.listParams.searchParams', {}) || {};
    this.state = {
      currentPage: listParams.pageNo || 1,
      currentPageSize: listParams.pageSize || 10,
      privilege: {},
      // searchParams: {}, // 用于传给后端的参数
    };

    // 用于返回时 数据回填
    this.initSearchParams = {
      ...listParams,
      pageNo: listParams.pageNo || 1,
      pageSize: listParams.pageSize || 10,
    };
  }

  componentDidMount = () => {
    this.fetchDistrictTree();
    this.fetchAnnTypes();
    this.fetchPublishOrgList();
    this.myAnnDataConfig();
    this.fetchTags();
  }

  fetchTags = () => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchTagTypes',
    });
  }
  myAnnDataConfig = async () => {
    const res = await this.props.dispatch({
      type: 'publicQuery/myAnnDataConfig',
    });
    this.setState({
      privilege: res.result || {},
    });
  }

  // 获取列表大接口数据
  fetchAnnList = (params) => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/newFetchList',
      payload: params,
    });
  }

  // 获取区划树
  fetchDistrictTree = () => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchDistrictTree',
      payload: {
        filterByPrivilege: true,
      },
    });
  }

  // 获取公告类型
  fetchAnnTypes = (params) => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchAnnTypes',
      payload: params,
    });
  }

  // 获取发布单位
  fetchPublishOrgList = (params) => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchPublishOrgList',
      payload: {
        ...params,
        filterByPrivilege: true,
      },
    });
  }

  // 拉取页面数据的时候要先整合参数，再调用接口
  fetchListData = (params) => {
    this.initSearchParams = {
      ...params,
    };
    const data = {
      ...params,
    };
    // const { dispatch } = this.props;
    const { deliveryDate = [] } = params;
    if (deliveryDate.length) {
      data.startReleasedAt = deliveryDate[0];
      data.endReleasedAt = deliveryDate[1];
    }
    delete data.deliveryDate;
    this.fetchAnnList({
      ...data,
    });
  }

  // 搜索
  onSearch = (params) => {
    if (params.tagCodes) {
      params.tagCodes = params.tagCodes.join(',');
    }

    try {
      tracer({
        utmCD: ['cZcyList', 'dSearchBtn'],
      });
      const paramsKeys = Object.keys({ ...params }).filter(key => !['type', 'pageSize', 'pageNo', 'annBigTypes'].includes(key)).filter(key => params[key]).join('_');
      tracer({
        utmCD: ['cZcyList', 'dCollectSearchParams'],
        business: {
          keyWord: paramsKeys,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
    this.fetchListData({
      ...params,
    });
  }

  getAllSelectOption = (searchType, value) => {
    switch (searchType) {
    case 'annTypes':
      return this.fetchAnnTypes({
        name: value,
      });
    case 'publishOrgList':
      return this.fetchPublishOrgList({
        orgName: value,
      });
    default:
      break;
    }
  }

  // 获取下拉框选项公共方法
  getSelectOptions = (searchType) => {
    const { annTypesLoading, publishOrgListLoading } = this.props;

    const optionList = searchType === 'annTypes' ?
      (_get(this.props, 'publicQuery.annTypes', []) || []) :
      (_get(this.props, 'publicQuery.publishOrgList', []) || []);

    const params = {
      searchFun: value => this.getAllSelectOption(searchType, value),
      loading: searchType === 'annTypes' ? annTypesLoading : publishOrgListLoading,
    };
    return (
      <Select
        {...selectConfig(params, searchType)}
      >
        {
          optionList.length ? optionList.map(item => (
            <Select.Option
              key={item.code}
              value={item.code}
              title={item.name}
            >{item.name}
            </Select.Option>
          )) : null
        }
      </Select>
    );
  }

  render() {
    const { pageLoading } = this.props;
    const { currentPage, currentPageSize, privilege } = this.state;
    const annList = _get(this.props, 'publicQuery.annListInfo.data', []) || [];
    const total = _get(this.props, 'publicQuery.annListInfo.total', 0) || 0;
    // 列表过滤项
    const filterColumns = ['pushDetails', 'annSourceDesc'];
    if (!privilege.showOrg) {
      filterColumns.push('creatorOrgName');
    }
    if (!privilege.showCreator) {
      filterColumns.push('creatorName');
    }
    // 搜索过滤项
    const filterSearch = ['annSource'];
    if (!privilege.showOrgSearch) {
      filterSearch.push('createOrgId');
    }
    if (!privilege.showDistrictSearch) {
      filterSearch.push('district');
    }
    return (
      <div className="public-query-page">
        <ZcySpin spinning={!!pageLoading}>
          <ZcyList
            openSearchCache
            {...searchConfig(this, filterSearch)}
            table={{
              columns: columnConfig.filter(item => !filterColumns.includes(item.dataIndex)),
              dataSource: annList,
              rowKey: 'id',
              bordered: true,
              pagination: {
                showSizeChanger: true,
                total,
                defaultCurrent: currentPage,
                defaultPageSize: currentPageSize,
              },
            }}
          />
        </ZcySpin>
      </div>
    );
  }
}
