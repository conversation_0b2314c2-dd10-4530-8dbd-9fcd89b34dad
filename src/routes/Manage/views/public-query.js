import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Zcy<PERSON><PERSON>crum<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>cy<PERSON>pin,
  Select,
  Panel,
} from 'doraemon';
import EmptyBlock from 'src/components/Empty';
import _get from 'lodash/get';
import { breadcrumbConfig } from '../config/publicQuery-breadcrumb';
import searchConfig from '../config/publicQuery-searchConfig';
import { columnConfig } from '../config/publicQuery-tableConfig';
import { selectConfig } from '../config/publicQuery-selectConfig';
import './index.less';
import DistrictCascader from 'src/components/DistrictCascader';

const initSearchParams = {
  pageNo: 1,
  pageSize: 10,
};

/**
 * 公告查询页面
 * 可以查询平台下所有的公告信息
 * 仅为平台运营开放
 */
@connect(({ publicQuery, loading }) => ({
  publicQuery,
  annTypesLoading: loading.effects['publicQuery/fetchAnnTypes'],
  publishOrgListLoading: loading.effects['publicQuery/fetchPublishOrgList'],
  pageLoading: loading.effects['publicQuery/fetchList'],
}))
export default class publicQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {
      districtCode: null,
    };
  }

  componentDidMount = () => {
    this.fetchAnnTypes();
    this.fetchPublishOrgList();
    this.fetchTags();
  }

  fetchTags = () => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchTagTypes',
    });
  }

  // 获取列表大接口数据
  fetchAnnList = (params) => {
    if (params?.tagCodes?.length) {
      params.tagCodes = params.tagCodes.join(',');
    } else {
      delete params.tagCodes;
    }

    const { districtCode } = this.state;

    if (districtCode) {
      params.parentDistrictCode = districtCode;
    }


    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchList',
      payload: params,
    });
  }

  // 获取公告类型
  fetchAnnTypes = (params) => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchAnnTypes',
      payload: params,
    });
  }

  // 获取发布单位
  fetchPublishOrgList = (params) => {
    const { dispatch } = this.props;
    return dispatch({
      type: 'publicQuery/fetchPublishOrgList',
      payload: params,
    });
  }

  // 拉取页面数据的时候要先整合参数，再调用接口
  fetchListData = (params) => {
    const data = {
      ...initSearchParams,
      ...params,
    };
    const { deliveryDate = [] } = params;
    if (deliveryDate.length) {
      data.startReleasedAt = deliveryDate[0];
      data.endReleasedAt = deliveryDate[1];
    }
    delete data.deliveryDate;
    this.fetchAnnList({
      ...data,
    });
  }

  // 搜索
  onSearch = (params) => {
    this.fetchListData({
      ...params,
    });
  }

  getAllSelectOption = (searchType, value) => {
    switch (searchType) {
    case 'annTypes':
      return this.fetchAnnTypes({
        name: value,
      });
    case 'publishOrgList':
      return this.fetchPublishOrgList({
        orgName: value,
      });
    default:
      break;
    }
  }

  // 获取下拉框选项公共方法
  getSelectOptions = (searchType) => {
    const { annTypesLoading, publishOrgListLoading } = this.props;

    const optionList = searchType === 'annTypes' ?
      (_get(this.props, 'publicQuery.annTypes', []) || []) :
      (_get(this.props, 'publicQuery.publishOrgList', []) || []);

    const params = {
      searchFun: value => this.getAllSelectOption(searchType, value),
      loading: searchType === 'annTypes' ? annTypesLoading : publishOrgListLoading,
    };
    return (
      <Select
        {...selectConfig(params, searchType)}
      >
        {
          optionList.length ? optionList.map(item => (
            <Select.Option
              key={item.code}
              value={item.code}
              title={item.name}
            >{item.name}
            </Select.Option>
          )) : null
        }
      </Select>
    );
  }

    // 区划选择
    onChangeCode = (value) => {
      const hasReset = !!this.state.districtCode;
      this.setState({
        districtCode: value[value.length - 1],
      }, () => {
        if (hasReset) {
          this.listRef.handleReset();
        }
      });
    }

    render() {
      const { pageLoading } = this.props;
      const { districtCode } = this.state;
      const annList = _get(this.props, 'publicQuery.annListInfo.data', []) || [];
      const total = _get(this.props, 'publicQuery.annListInfo.total', 0) || 0;

      return (
        <div className="public-query-page">
          <ZcySpin spinning={!!pageLoading}>
            <ZcyBreadcrumb
              {...breadcrumbConfig()}
              extraContent={
                (
                  <DistrictCascader
                    localKey="query"
                    changeOnSelect
                    onChangeCode={this.onChangeCode}
                  />
                )
              }
            />
            <Panel>
              {
                districtCode ? (
                  <ZcyList
                    openSearchCache
                    {...searchConfig(this, ['district'])}
                    // 在searchConfig中定义onsearch
                    // onSearch={this.onSearch}
                    table={{
                      columns: columnConfig.filter(item => !['releasedAt', 'creatorOrgName'].includes(item.dataIndex)),
                      dataSource: annList,
                      rowKey: 'id',
                      bordered: true,
                      pagination: {
                        showSizeChanger: true,
                        total,
                      },
                    }}
                    initSearchParams={{
                      ...initSearchParams,
                      districtCode: this.state.districtCode,
                    }}
                    ref={(node) => { this.listRef = node; }}
                  />
                ) : <EmptyBlock />
              }
            </Panel>
          </ZcySpin>
        </div>
      );
    }
}
