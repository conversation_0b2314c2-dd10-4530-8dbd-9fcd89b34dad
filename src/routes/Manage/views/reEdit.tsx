import React, { useEffect, useRef, useState } from 'react';
import {
  ZcyBreadcrumb,
  Spin,
  Panel,
  Form,
  Input,
  FormGrid,
  message,
} from 'doraemon';
import { connect } from 'dva';
import FormUpload from 'src/routes/Flow/views/components/form-upload';
import loadEditor from '@zcy/zcy-ueditor-front';
import { SensitiveEditorStyle, attachSensitiveWordsTag, clearSensitiveTag, getSensitiveWords, renderAlert, showSensitiveMsg } from 'src/utils/listSensitiveWords';
import pushLog, { SentryTypeEnum } from 'src/utils/pushLog';
import { FFC } from 'src/types/component';
import { getAnnouncementDetailGet, saveAnnouncementDetailPost, saveAndRePushAnnouncementDetailPost } from 'src/api/announcement/api/admin';
import type { GetAnnouncementDetailGetResponse } from 'src/api/announcement/api/admin';
import { useAnnouncementContentPreviewDrawer } from 'src/components/AnnouncementContentBlock';

type TAnnDetail = Required<GetAnnouncementDetailGetResponse>['result']

const ROUTES = [{
  label: '公告数据处理',
}, {
  label: '数据订正',
}];


const ReEdit: FFC<{
  dispatch: any
  flow: any,
  match: {
    params: {
      announcementId: string
    }
  }
}> = ({ match, form, history, flow, dispatch }) => {
  const ueditorRef = useRef<any>(null);
  const announcementId = match?.params?.announcementId; // 公告id
  const [loading, setLoading] = useState(false);
  const [sensitiveMap, setSensitiveMap] = useState({
    hasBlock: false, 
    hasRemind: false,
  });
  const [annDetail, setAnnDetail] = useState<TAnnDetail>({});
  const [open] = useAnnouncementContentPreviewDrawer({});
  

  const { validateFieldsAndScroll, getFieldDecorator, getFieldValue } = form;

  const checkSensitive = ({
    title,
    district,
    content,
    announcementType,
  }) => {
    return getSensitiveWords({
      announcementType,
      content: `${title}--${content}`,
      district,
    }).then(res => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;

      setSensitiveMap({
        hasBlock, hasRemind,
      });

      if (hasBlock || hasRemind) {
        const {
          contentWithSensitiveTag,
        } = attachSensitiveWordsTag(blockWords, remindWords, content);
        ueditorRef.current.setContent(contentWithSensitiveTag);
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }

      return true;
    });
  };

  const getFileList = () => {
    const fileList = [...flow.fileInfo.defaultFileList,
      ...flow.fileInfo.fileList];
    return (fileList.filter(item => item.status === 'done') || []).map(fl => ({
      fileId: fl.fileId,
      isShow: (fl.noPublic !== true),
      name: fl.name,
      size: fl.size,
    }));
  };

  const beforeSave = () => {
    return new Promise<{ title: string, reason: string }>((resolve, reject) => {
      validateFieldsAndScroll((err, values) => {
        if (err) return reject();
        const { title, reason } = values;
        return resolve({ title, reason });
      });
    }).then(({ title, reason }) => {
      const content = ueditorRef.current.getContent();
      if (!content) return message.error('请输入公告内容');
      return checkSensitive({
        title,
        content,
        district: annDetail.district,
        announcementType: annDetail.announcementType,
      }).then(checkRes => {
        if (!checkRes) return;
        return {
          id: +announcementId,
          content: clearSensitiveTag(content),
          title,
          attachments: getFileList(),
          reason,
        };
      });
    });
  };

  const save = async () => {
    const res = await beforeSave();
    if (!res) return;
    const { id, content, title, attachments, reason } = res;
    const saveRes = await saveAnnouncementDetailPost({
      id,
      content,
      title,
      attachments,
      reason,
    });
    if (!saveRes.success) return;
    return message.success('保存成功');
  };

  const saveAndPush = async () => {
    const res = await beforeSave();
    if (!res) return;
    const { id, content, title, attachments, reason } = res;
    const saveAndPushRes = await saveAndRePushAnnouncementDetailPost({
      id,
      content,
      title,
      attachments,
      reason,
    });
    if (!saveAndPushRes.success) return;
    return message.success('重新推送成功');
  };

  const goBack = () => history.goBack();

  const getGlobalBtn = () => {
    return [
      {
        label: '返回',
        onClick: () => {
          goBack();
        },
      },
      {
        label: '公告预览',
        onClick: () => {
          const annTitle = getFieldValue('title') || '';
          const annContent = ueditorRef.current.getContent();
          open({
            annTitle,
            annContent,
          });
        },
      },
      {
        label: '保存',
        onClick: async () => {
          setLoading(true);
          const res = await save().finally(() => {
            setLoading(false);
          });
          if (!res) return;
          goBack();
        },
      },
      {
        label: '保存并推送',
        type: 'primary',
        onClick: async () => {
          setLoading(true);
          const res = await saveAndPush().finally(() => {
            setLoading(false);
          });
          if (!res) return;
          goBack();
        },
      },
    ];
  };


  const getDetailData = () => {
    if (!announcementId) return;
    getAnnouncementDetailGet({ announcementId }).then(res => {
      if (!res.success) return;
      setAnnDetail(res.result!);
      ueditorRef.current.setContent(res.result?.content!);
      const defaultFileList = (res.result!.attachments || []).map(item => ({
        ...item,
        status: 'done',
        noPublic: !item.isShow,
      }));
      dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo: {
            fileList: [],
            defaultFileList,
          },
        },
      });
    });
  };

  useEffect(() => {
    try {
      loadEditor().then(ue => {
        if (ue) {
          window.UE.getEditor('ueditor', {
            allowDivTransToP: false,
            // 注入敏感词提示css
            initialStyle: SensitiveEditorStyle,
          }).ready(() => {
            ueditorRef.current = window?.UE?.getEditor('ueditor');
            getDetailData();
          });
        }
        if (!ue) return pushLog('初始化富文本编辑失败', SentryTypeEnum.warning);
      });
    } catch (error) {
      pushLog(JSON.stringify(error), SentryTypeEnum.info);
    }

    return () => {
      ueditorRef?.current?.destroy();
    };
  }, []);

  const formGridItem = [{
    label: '标题',
    colSpan: 2,
    render: () => {
      return getFieldDecorator('title', {
        initialValue: annDetail.title,
        validateTrigger: 'onSubmit',
        rules: [{
          required: true,
          message: '请输入',
        }, {
          validator: (_, value, callback) => {
            getSensitiveWords({
              announcementType: annDetail.announcementType,
              content: value,
              district: annDetail.announcementType,
            }).then(res => {
              if (!res) return;
              const {
                hasBlock,
                blockWords,
              } = res;

              if (hasBlock) {
                callback(new Error(`请修改敏感词：${blockWords.map(ele => ele.emitWord).join('、')}`));
                return;
              }
              callback();
            });
          },
        }],
      })(
        <Input placeholder="请输入" maxLength={200} />
      );
    },
  }];

  const formGridItemInfo = [{
    label: '原因说明',
    colSpan: 2,
    render: () => {
      return getFieldDecorator('reason', {
        initialValue: undefined,
        rules: [{
          required: true,
          message: '请输入',
        }],
      })(
        <Input.TextArea placeholder="请输入" maxLength={30} />
      );
    },
  }];

  return (
    <Spin spinning={loading}>
      <ZcyBreadcrumb
        routes={ROUTES}
        globalBtn={getGlobalBtn()}
      />
      <Form>
        <Panel title="公告内容">
          {
            renderAlert(sensitiveMap.hasBlock, sensitiveMap.hasRemind)
          }
          <FormGrid bordered formGridItem={formGridItem} />
          <div className="zcy-mg-t-10" id="ueditor" style={{ clear: 'both' }} />
        </Panel>
        <FormUpload
          editable
          announcementType={annDetail?.announcementType}
          districtCode={annDetail?.district}
          panelDescription={
            <span style={{ color: 'red' }}>注意：此处附件不会重新拼接到公告末尾，请手动编辑上方富文本内容中的附件链接，并保持两者一致！</span>
          }
        />
        <Panel title="订正说明">
          <FormGrid bordered formGridItem={formGridItemInfo} />
        </Panel>
      </Form>
    </Spin>
  );
};

export default Form.create()(connect(({ flow }) => ({ flow }))(ReEdit));
