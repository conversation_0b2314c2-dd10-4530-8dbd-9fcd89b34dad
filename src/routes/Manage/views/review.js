import React, { Component } from 'react';
import { connect } from 'dva';
import {
  ZcyStatusBar,
  Panel,
  ZcyBreadcrumb,
  Modal,
  message,
  Form,
  Upload,
  Alert,
  request,
  Spin,
} from 'doraemon';
import { AuditModal } from '@zcy/zcy-audit-flow-back';
import _Get from 'lodash/get';
import _IsEmpty from 'lodash/isEmpty';
import { obtainAnnouncementDetail, checkAnnouncement } from '../services';
import { OPERATOR_STATUS } from 'src/constants';
import AnnouncementContentBlock from 'src/components/AnnouncementContentBlock';
import { handWorkFlowDotsData } from '../config/workflowConfig';
import DetailSteps from './components/detail-steps';
import Marking from '../../Flow/views/components/marking';
import './components/modal-review.less';
import './index.less';


@connect(({ list }) => ({
  list,
}))
@Form.create()
export default class Detail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      fileList: [],
      announcement: {
        showStatus: 1,
        announcementFlowLogDtos: [],
      },
      reviewModalVisible: false,
      markingList: [],
      showSensitive: false,
      supportCheckerMultiSelect: false,
      auditParams: {}, // 审核流参数
    };
  }

  // 获取工作流审核弹窗参数
  openAuditModal = () => {
    const { dispatch } = this.props;
    const announcementId = this.props.match.params.announcementId;
    dispatch({
      type: 'dynamicCreate/fetchauditParams',
      payload: {
        announcementId,
      },
    }).then((res = {}) => {
      if (res.success) {
        const auditParams = res?.result || {};
        this.setState({
          auditParams,
          reviewModalVisible: true,
        });
      }
    });
  }

  // 获取公告详情
  getDetailData = () => {
    const announcementId = this.props.match.params.announcementId;
    obtainAnnouncementDetail({
      announcementId,
    }).then(({ result, success, error }) => {
      if (!success) {
        return message.error(error);
      }
      const newAnnouncementFlowLogDtos = handWorkFlowDotsData(result?.announcementFlowLogDtos);
      result.announcementFlowLogDtos = newAnnouncementFlowLogDtos;
      this.setState({
        announcement: result,
        fileList: result.attachments || [],
      });

      this.initMarking({
        distCode: result.district,
        annType: result.announcementType,
        annId: result.id,
      });
      const { district, content, title } = result;
      this.listSensitiveCensor({
        title,
        district,
        content,
      });
    });
  }

  componentDidMount() {
    this.getDetailData();
  }
  // 确认审批时的回调函数
  sureAuditHandler = (processFrontParam) => {
    this.onOk(processFrontParam);
  }

  getAuditConfig = async () => { // 审核提交是否多选
    const { announcement } = this.state;
    const res = await request('/announcement/config/getAnnouncementCheckConfig', {
      params: {
        districtCode: announcement.district || window.currentUserDistrict.code,
        announcementType: announcement.announcementType,
      },
    });
    this.setState({
      supportCheckerMultiSelect: _Get(res, 'result.supportCheckerMultiSelect', false),
    });
  }
  /**
   * 敏感词校验
   * <AUTHOR>
   * @DateTime 2019-03-05T14:10:21+0800
   * @param    {district, content}                 payload [包含区划码，待校验内容]
   * @return   {[type]}                         [description]
   */
  listSensitiveCensor = (payload) => {
    this.props.dispatch({
      type: 'list/listSensitiveCensor',
      payload,
    }).then((res) => {
      let { result: listSensitive } = res;
      if (!listSensitive) {
        listSensitive = [];
      }
      const { announcement } = this.state;
      let { content, title } = announcement;
      title = `<span>${title}<span>`;
      for (let i = 0; i < listSensitive.length; i += 1) {
        if (listSensitive[i]) {
          content = content.replace(
            new RegExp(listSensitive[i], 'g'),
            `<span style="color: red;font-weight: bold;">${listSensitive[i]}</span>`
          );
          title = title.replace(
            new RegExp(listSensitive[i], 'g'),
            `<span style="color: red;font-weight: bold;">${listSensitive[i]}</span>`
          );
        }
      }
      if (listSensitive && listSensitive.length) {
        this.setState({
          announcement: {
            ...announcement,
            sensitiveContent: content,
            sensitiveTitle: title,
          },
        });
      }
    });
  }

  getStatus = () => {
    const match = OPERATOR_STATUS.find(item => item.key === this.state.announcement.showStatus);
    let text = '';
    if (match) {
      text = match.value;
    }
    return (
      <ZcyStatusBar
        value={text}
        status="progressing"
      />
    );
  }
  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (payload) => {
    this.props.dispatch({
      type: 'list/getIdentifications',
      payload,
    }).then((res) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }
  handleCancel = () => {
    this.setState({
      reviewModalVisible: false,
    });
  }

  goPageBack = () => {
    const { history } = this.props;
    const { announcement } = this.state;
    const { annBigType, bizId } = announcement;
    if (history.length <= 1 && bizId) {
      history.replace(`/detail/${annBigType}/${bizId}`);
      return;
    }
    history.goBack();
  }

  // processFrontParam 审批结果
  onOk = (processFrontParam) => {
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        if (this.state.supportCheckerMultiSelect) {
          delete err.nextTaskUserId;
        } else {
          delete err.nextTaskUserIds;
        }
      }
      if (!err || _IsEmpty(err)) {
        const data = {
          id: this.props.match.params.announcementId,
          pass: values.isPass === 1,
          checkOpinion: values.checkOpinion,
          processFrontParam: JSON.stringify(processFrontParam),
        };
        if (values.isPass === 1) {
          data.nextTaskUserId = values.nextTaskUserId || '';
          data.nextTaskUserIds = values.nextTaskUserIds || undefined;
        }
        this.setState({
          loading: true,
        });
        if (this.state.loading) return;
        checkAnnouncement(data).then((res) => {
          this.setState({
            loading: false,
          });
          if (!res.success) return;
          message.success('审核成功');
          this.goPageBack();
        }).catch((e) => {
          this.setState({
            loading: false,
          });
          if (e.status === 403) {
            message.error('您无权审核该公告！');
          }
        });
      }
    });
  }

  onCloseAlert = () => {
    const { announcement } = this.state;
    this.setState({
      announcement: {
        ...announcement,
        sensitiveContent: '',
        sensitiveTitle: '',
      },
      showSensitive: false,
    });
  }
  sensitiveChange = (showSensitive) => {
    this.setState({
      showSensitive,
    });
  }
  render() {
    const {
      reviewModalVisible,
      markingList,
      announcement,
      showSensitive,
      auditParams,
      loading,
      supportCheckerMultiSelect,
    } = this.state;
    const { isTransmit } = announcement;
    const breadcrumb = [{
      label: '公告管理',
    }, {
      label: '公告审核',
    }];
    const globalBtn = [{
      label: '返回',
      onClick: () => {
        this.props.history.goBack();
      },
    }, 
    {
      label: isTransmit ? '转发' : '审批',
      type: 'primary',
      onClick: async () => {
        await this.getAuditConfig();
        await this.openAuditModal();
      },
    }];

    return (
      <div className="announcement-flow">
        <Spin spinning={loading}>
          <ZcyBreadcrumb
            routes={breadcrumb}
            globalBtn={globalBtn}
          />
          <DetailSteps announcementDetail={announcement} />
          <div className="ann-detail">
            {(announcement.sensitiveContent || announcement.sensitiveTitle) && (
              <Alert
                message={(
                  <p>
                    <span>该公告中包含敏感词，</span>
                    {
                      showSensitive
                        ? <a onClick={() => { this.sensitiveChange(false); }}>隐藏</a>
                        : <a onClick={() => { this.sensitiveChange(true); }}>点击查看</a>
                    }
                  </p>
                )}
                type="firstinfo"
                closable
                onClose={this.onCloseAlert}
              />
            )}
            <Panel >
              <AnnouncementContentBlock title={announcement.title} content={announcement.content} />
            </Panel>
          </div>
          {
            announcement?.attachmentShow ? (
              <div className="file-down" style={{ marginBottom: 20 }}>
                <Panel
                  title="附件"
                >
                  <Upload.Download bizCode="1014" fileList={this.state.fileList} />
                </Panel>
              </div>
            ) : null
          }
         
          <Marking
            markingList={markingList}
            disabled
            showSelected
            autoShowALl={false}
          />
          {
            reviewModalVisible && (
              <AuditModal
                visible={reviewModalVisible}
                // 相关 config 来源于 https://middle.test.zcygov.cn/announcement/api/form/common/v3/initAndGetProcessParam 接口获取
                config={auditParams}
                onCancel={this.handleCancel}
                onOk={this.sureAuditHandler}
                showUpload={false}
                isUserRadio={!supportCheckerMultiSelect}
                ModalProps={{
                  confirmLoading: false,
                  title: isTransmit ? '转发' : '审核',
                }}
                resultLabel={isTransmit ? '转发结果' : '审批结果'}
                textareaLabel={isTransmit ? '转发意见' : '审批意见'}
              />
            )
          }
        </Spin>
      </div>
    );
  }
}
