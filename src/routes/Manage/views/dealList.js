import React, { Component } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import moment from 'moment';
import {
  ZcyList, Input, ZcyBreadcrumb, Select, Tooltip,
  Badge, message, Modal, Icon, Form, Tag, Table,
} from 'doraemon';
import OutUrlContent from 'components/OutUrlContent';
import ModalSelect from './components/modal-select';
import { deleteAnnouncement, releaseAnnouncement, revokeAnnouncement, canCreateAnnouncement } from '../services';
import './index.less';
import { getPubType } from 'utils/utils';
import DistrictCascader from 'src/components/DistrictCascader';

const Actions = Table.Actions;
const status = [{
  key: 0,
  value: '待完善',
}, {
  key: 1,
  value: '审核中',
}, {
  key: 2,
  value: '待发布',
}, {
  key: 3,
  value: '被退回',
}, {
  key: 4,
  value: '已发布',
}, {
  key: 5,
  value: '已结束',
}];

const confirm = Modal.confirm;

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 14 },
};

@connect(({ list, loading, openConfig }) => ({
  list,
  tableLoading: loading.models.list,
  openConfig,
}))
@Form.create()
export default class List extends Component {
  constructor(props) {
    super(props);
    this.state = {
      permission: false,
      annBigType: props.match.params.annBigType,
      cancelVisible: false,
      zcylistKey: new Date().getTime(),
      districtCode: '',
    };
  }

  handleSearch = (params) => {
    if (params.date && params.date.length === 2) {
      params.releaseStartAt = params.date[0];
      params.releaseEndAt = params.date[1];
    }
    delete params.date;
    this.props.dispatch({
      type: 'list/setParams',
      payload: params,
    });
    this.reflesh(params);
  }

  componentWillReceiveProps(nextProps) {
    const match = nextProps.location.pathname.split('/');
    if (this.state.annBigType !== match[3] && this.state.annBigType !== null) {
      this.setState({
        annBigType: match[3],
      }, () => {
        this.props.dispatch({
          type: 'list/setParams',
          payload: {
            pageNo: 1,
            pageSize: 10,
            myTodo: 'true',
          },
        }).then(() => {
          this.getInitData();
          this.setState({
            zcylistKey: new Date().getTime(),
          });
        });
      });
    }
  }


  componentDidMount = () => {
    this.getInitData();
    canCreateAnnouncement().then((res) => {
      this.setState({
        permission: res.result,
      });
    });
  }

  getInitData = () => {
    this.loadAnnTypes();
    this.reflesh();
  }
  loadAnnTypes = () => {
    this.props.dispatch({
      type: 'list/getAnnTypeByAnnBigType',
      payload: {
        annBigType: this.state.annBigType,
      },
    });
  }

  handelAddAnnouncement = () => {
    const { districtCode } = this.state;
    if (!districtCode || !districtCode.length) {
      message.error('请选择区划！');
      return;
    }
    this.props.dispatch({
      type: 'list/setModalVisible',
      payload: {
        type: 'categoryModalVisible',
        visible: true,
      },
    });
  }

  detail = (item) => {
    this.props.dispatch(routerRedux.push(`/detail/${this.state.annBigType}/${item.id}`));
  }

  onCancelOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        revokeAnnouncement({
          ...values,
          announcementId: this.announcementIdForCancel,
        }).then((res) => {
          if (res.success) {
            message.success('撤回成功！');
            this.setState({
              cancelVisible: false,
            });
            setTimeout(() => {
              this.reflesh();
            }, 200);
          } else {
            Modal.error({
              title: res.error,
            });
          }
        });
      }
    });
  }
  onTenantChange = (districtCode) => {
    this.setState({
      districtCode,
    }, () => {
      this.reflesh();
    });
  }
  reflesh = (params) => {
    const { districtCode: district, annBigType } = this.state;
    this.props.dispatch({
      type: 'list/getAnnouncementList',
      payload: {
        district: (district && district.length) ? district[district.length - 1] : undefined,
        annBigTypes: annBigType,
        ...(params || this.props.list.params),
      },
    });
  }
  render() {
    const { list, tableLoading } = this.props;
    const { districtCode } = this.state;
    const distCode = districtCode ? districtCode[districtCode.length - 1] : '';
    const { getFieldDecorator } = this.props.form;
    const breadcrumb = [{
      label: '处理公告',
    }];
    const customItem = [{
      label: '公告类型',
      id: 'announcementType',
      decoratorOptions: {
        initialValue: '',
      },
      render: () => {
        return (
          <Select dropdownMatchSelectWidth={false} placeholder="请选择" showSearch optionFilterProp="name">
            <Select.Option value="">全部</Select.Option>
            {
              list.annType.map((item) => {
                return (
                  <Select.Option 
                    name={item.name} 
                    value={item.typeId}
                    key={item.typeId}
                  >{item.name}
                  </Select.Option>
                );
              })
            }
          </Select>
        );
      },
    }, {
      label: '公告标题',
      id: 'title',
      render: () => {
        return <Input placeholder="请输入" />;
      },
    }, {
      label: '状态',
      id: 'queryStatus',
      decoratorOptions: {
        initialValue: '',
      },
      render: () => {
        return (
          <Select placeholder="请选择">
            <Select.Option value="">全部</Select.Option>
            {status.map(item => (
              <Select.Option value={item.key} key={item.key}>{item.value}</Select.Option>
            ))}
          </Select>
        );
      },
    }];
    const tabs = {
      tabList: [{
        label: '我的待办',
        value: 'all',
        key: 'true',
      }, {
        label: '全部',
        value: 'complete',
        key: 'false',
        isAll: true,
      }],
      defaultActiveKey: this.props.list.params.myTodo,
    };
    const columns = [
      {
        title: '公告标题',
        dataIndex: 'title',
        width: 200,
        render: (text, item) => {
          let identiNames = [];
          if (item.metaData) {
            const metaData = JSON.parse(item.metaData);
            identiNames = metaData.identiNames || [];
          }
          identiNames = identiNames.join('，');
          return (
            <div className="ann-title">
              {
                identiNames.length > 0 && <Tag color="green" title={identiNames}>{identiNames}</Tag>
              }
              <div className="short-str">
                <Tooltip title={text} placement="top">
                  <a className="customLinkBlue" onClick={() => { this.detail(item); }}>{text}</a>
                </Tooltip>
              </div>
            </div>
          );
        },
      }, {
        title: '公告类型',
        dataIndex: 'announcementTypeName',
        width: 120,
        render: (text) => {
          return (
            <div className="short-str">
              <Tooltip title={text}>
                <span>{text}</span>
              </Tooltip>
            </div>
          );
        },
      }, {
        title: '发布区划',
        dataIndex: 'districtName',
        width: 120,
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        width: 120,
      }, {
        title: '发布方式',
        dataIndex: 'pubType',
        width: 120,
        render: (text) => {
          return getPubType(text);
        },
      }, {
        title: '发布时间',
        dataIndex: 'releasedAt',
        width: 98,
        render: (text) => {
          if (text) {
            return (
              <div>
                {moment(text).format('YYYY-MM-DD')}
                <br />
                {moment(text).format('HH:mm:ss')}
              </div>
            );
          }
          return '-';
        },
      }, {
        title: '状态',
        dataIndex: 'showStatus',
        width: 120,
        render: (key) => {
          let badgeStatus;
          if ([0, 1, 2].indexOf(key) !== -1) {
            badgeStatus = 'processing';
          }
          if ([3].indexOf(key) !== -1) {
            badgeStatus = 'warning';
          }
          if ([4, 5].indexOf(key) !== -1) {
            badgeStatus = 'success';
          }
          if (key !== null) {
            return (
              <div>
                <Badge status={badgeStatus} text={status[key].value} />
                {/* {time} */}
              </div>
            );
          }
          return '';
        },
      }, {
        title: '操作',
        width: 150,
        render: (item, record) => {
          const maxShowSize = 3;
          const pub = {
            label: '发布',
            onRowClick: () => {
              confirm({
                title: '提示',
                content: '确认发布此公告？',
                onOk: () => {
                  releaseAnnouncement({
                    announcementId: item.id,
                  }).then((res) => {
                    if (res && res.success) {
                      message.success('公告发布中');
                      this.reflesh();
                    } else {
                      message.error(res.error);
                    }
                  });
                },
              });
            },
          };
          const edit = {
            label: '编辑',
            onRowClick: () => {
              this.props.dispatch(routerRedux.push(`/adminEdit/${this.state.annBigType}/${item.id}`));
            },
          };
          const del = {
            label: '删除',
            onRowClick: () => {
              confirm({
                title: '提示',
                content: '确认删除此公告？',
                onOk: () => {
                  deleteAnnouncement({
                    id: item.id,
                  }).then(() => {
                    message.success('删除成功！');
                    setTimeout(() => {
                      this.reflesh();
                    }, 200);
                  });
                },
              });
            },
          };
          const back = {
            label: '撤回',
            onRowClick: () => {
              if (item.revokedLogicDelete) {
                confirm({
                  title: '是否继续撤回?',
                  content: `撤回后公告列表将不再展示该公告，编辑操作请至${item.appName}公告发起模块`,
                  closable: true,
                  onOk: () => {
                    this.announcementIdForCancel = item.id;
                    this.props.form.resetFields(['revokeRes']);
                    this.setState({
                      cancelVisible: true,
                    });
                  },
                });
              } else {
                this.announcementIdForCancel = item.id;
                this.props.form.resetFields(['revokeRes']);
                this.setState({
                  cancelVisible: true,
                });
              }
            },
          };
          let itemList = [];
          switch (item.showStatus) {
          case 0:
            itemList = [edit, del];
            break;
          case 2:
            itemList = [edit, pub, del];
            break;
          case 4:
            itemList = [back];
            break;
          default:
            itemList = [];
          }

          const hasOurUrlList = !!item.announcementOutUrlDtoList?.length;
          const getMaxShowSize = () => {
            if (hasOurUrlList) return maxShowSize - 1;
            return maxShowSize;
          };


          if (itemList.length === 0 && !hasOurUrlList) {
            return (
              <a className="customLinkBlue" onClick={() => { this.detail(item); }}>查看</a>
            );
          }

          const actions = itemList;
          return (
            <React.Fragment>
              {hasOurUrlList ? (
                <OutUrlContent 
                  hideDivider={!actions.length}
                  list={item.announcementOutUrlDtoList}
                />
              ) : null}
              <Actions actions={actions} record={record} maxShowSize={getMaxShowSize()} />
            </React.Fragment>
          );
        },
      }];
    const pagination = {
      total: list.announcementList.total,
      showSizeChanger: true,
      defaultCurrent: list.params.pageNo,
      defaultPageSize: list.params.pageSize,
    };
    const table = {
      columns,
      pagination,
      rowKey: 'id',
    };
    const globalBtn = [{
      label: '新增',
      type: 'primary',
      onClick: this.handelAddAnnouncement,
    }];
    const extraContent = (
      <DistrictCascader
        localKey="deal"
        onChangeCode={this.onTenantChange}
      />
    );
    return (
      <div className="announcement-list" key={this.state.annBigType}>
        <ZcyBreadcrumb
          extraContent={extraContent}
          routes={breadcrumb}
          globalBtn={this.state.permission ? globalBtn : []}
        />
        <ZcyList
          key={this.state.zcylistKey}
          customItem={customItem}
          tabs={tabs}
          tabKey="myTodo"
          table={{
            ...table,
            loading: tableLoading,
            dataSource: list.announcementList.data,
            rowClassName: 'ann-row',
          }}
          onSearch={this.handleSearch}
        />
        <ModalSelect distCode={distCode} annBigType={this.state.annBigType} />
        <Modal
          title="公告撤回"
          visible={this.state.cancelVisible}
          onOk={this.onCancelOk}
          width={540}
          onCancel={
            () => {
              this.setState({
                cancelVisible: false,
              });
            }
          }
        >
          <Form layout="horizontal">
            <FormItem label="撤回理由" {...formItemLayout}>
              {getFieldDecorator('revokeRes', {
                rules: [{ required: true, message: '请输入' }],
              })(
                <Input.TextArea maxLength={255} placeholder="请输入" />
              )}
            </FormItem>
          </Form>
          <div className="revoke-note">
            <Icon type="exclamation-circle" /> 提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作。
          </div>
        </Modal>
      </div>
    );
  }
}
