/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-04-26 15:37:41
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-06-19 10:01:38
 * @FilePath: /zcy-announcement-v2-front/src/routes/Manage/index.js
 * @Description: 
 */
module.exports = [
  {
    // 处理公告
    url: '/manage/adminlist/:annBigType',
    view: 'dealList',
    pageId: 'list',
    models: ['list'],
  },
  {
    url: '/manage/list/:annBigType',
    view: 'list',
    pageId: 'list',
    models: ['list'],
  },
  {
    // 资格预审信息发布
    url: '/manage/subList/:annBigType/:annSubType',
    view: 'subList',
    pageId: 'subList',
    models: ['list'],
  },
  {
    // 公告查询
    url: '/manage/search',
    // view: 'searchList',
    view: 'query',
    pageId: 'search',
    models: ['search'],
  },
  {
    // 数据处理
    url: '/manage/annDataHandle',
    view: 'annDataHandle',
    pageId: 'annDataHandle',
  },
  {
    // 数据订正
    url: '/manage/reEdit/:announcementId',
    view: 'reEdit',
    pageId: 'reEdit',
  },
  {
    // 非表单的公告详情
    url: '/detail/:annBigType/:announcementId',
    view: 'detail',
    pageId: 'detail',
  },
  {
    // 非表单公告-公告审核
    url: '/review/:annBigType/:announcementId',
    view: 'review',
    pageId: 'review',
  },
  {
    // 公告查询页面，平台运营可以查询平台所有的公告
    url: '/public-query',
    view: 'public-query',
    pageId: 'public-query',
    models: ['public-query'],
  },
  {
    // 公告分组
    url: '/manage/listByGroup',
    view: 'listByGroup',
    pageId: 'listByGroup',
    models: ['list'],
  },
];
