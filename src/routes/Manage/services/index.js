import request from 'src/utils/request';

export async function queryList(params) {
  return request('/api/test', {
    params: {
      ...params,
    },
  }).then((res) => {
    if (!res) {
      return [];
    }
    return res.map((item, index) => {
      return {
        ...item,
        key: index,
      };
    });
  });
}

export async function queryAnnTypeByAnnBigType(params) {
  return request('/announcement/api/queryAnnTypeByAnnBigType', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}
export async function printAnnouncementContent(params) {
  return request('/announcement/api/printAnnouncementContentNew', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      isNeedHeaderLine: false,
      isNeedFooterLine: false,
      pageMarginLeft: 20,
      pageMarginRight: 20,
      pageMarginTop: 20,
      pageMarginBottom: 20,
      ...params,
    },
  });
}
// // 我的公告列表接口
// export async function getAnnouncementList(data) {
//   return request('/announcement/api/myAnnouncementPage', {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     data,
//   });
// }

// 我的公告列表接口(对接表单后，新接口)
export async function getAnnouncementList(data) {
  return request('/announcement/api/announcementList', {
    method: 'POST',
    data,
  });
}


// 公告敏感词校验
export async function listSensitiveCensor(data) {
  return request('/announcement/api/listSensitiveCensor', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export async function raiseObjection(data) {
  return request('/announcement/api/raiseObjection', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 公告查询接口(按项目查询)
export async function projectAnnouncementQuery(data) {
  return request('/announcement/api/projectAnnouncementQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}
// 公告通过
export async function operObjection(data) {
  return request('/announcement/objection/operObjection', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 查询已有项目
export async function projectList(params) {
  const { url, ...rest } = params;
  return request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params: rest,
  });
}

// 获取公告详情
export async function obtainAnnouncementDetail(params) {
  return request('/announcement/api/obtainAnnouncementDetail', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取公告工作流下一步审核人
export async function getNextTaskUsers(params) {
  return request('/announcement/api/getNextTaskUsers', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取下一流程
export async function getNextNode(params) {
  return request('/announcement/api/getNextNode', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 审核
export async function checkAnnouncement(data) {
  return request('/announcement/api/checkAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 删除公告
export async function deleteAnnouncement(data) {
  return request('/announcement/api/deleteAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 发布公告
export async function releaseAnnouncement(data) {
  return request('/announcement/api/releaseAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 用户是否有新增公告权限
export async function canCreateAnnouncement(params) {
  return request('/announcement/api/canCreateAnnouncement', {
    method: 'GET',
    params,
    headers: {
      'Content-Type': 'application/json',
    },
  }, {
    // 过滤无权限的msg toast
  });
}

// 公告撤回
export async function revokeAnnouncement(params) {
  return request('/announcement/api/revokeAnnouncement', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取当前登录用户operateId
export async function obtainOpeatorId() {
  return request('/announcement/api/obtainOpeatorId', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 获取公告标识
export async function getIdentifications(params) {
  return request('/announcement/api/getIdentifications', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 同步填充元数据至公告
export async function syncMetadata(params) {
  return request('/announcement/api/project/syncMetadata', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 查询可撤回公告id
export async function getCanRevokeList(params) {
  return request('/announcement/api/getAnnTypeCanRevoke', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 平台运营公告查询列表大接口
export function fetchPublicAnnListApi(params) {
  return request('/announcement/api/pageAnnData', {
    method: 'POST',
    data: { ...params },
  });
}

export function newFetchList(params) {
  return request('/announcement/api/pageMyAnnData', {
    params: { ...params },
  });
}

// 发布单位列表、获取预算单位列表
export function fetchPublishOrgListApi(params) {
  return request('/announcement/config/listOrg', {
    params: { ...params },
  });
}

// 获取区划
export async function getDistTree(params) {
  params = {
    filterByOperatorDist: true,
    ...params,
  };
  return request('/announcement/api/getDistTree', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 意见征询下的页面中的获取区划数据
export async function getAnnounceMentListDistTree(params) {
  return request('/announcement/api/getSubDistTree', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取发布单位
export async function getUnitApi(params) {
  return request('/announcement/api/listInstitution', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

export async function myAnnDataConfig(params) {
  return request('/announcement/api/myAnnDataConfig', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取详情页含推送情况的公告基本信息
export async function fetchAnnounceBasicInfoApi(params) {
  return request('/announcement/api/getAnnContainsPushInfo', {
    params: { ...params },
  });
}

// 获取公告类型
export async function fetchAnnTypesApi(params) {
  return request('/announcement/config/annTypes', {
    params: { ...params },
  });
}

// 获取当前环境
export async function fetchCurrentEnvApi() {
  return request('/announcement/api/currentEnvConfig', {
  });
}

// 对接表单后，调用表单接口删除公告
export async function deleteFormAnnouncement(data) {
  return request('/announcement/api/form/common/v3/delete', {
    method: 'POST',
    data,
  });
}

// 对接表单后，撤回
export async function recallFormAnnouncement(data) {
  return request('/announcement/api/form/common/v3/recall', {
    method: 'POST',
    data,
  });
}

// 外网推送失败后，重新推送接口
export async function rePushApi(data) {
  return request('/announcement/api/rePush', {
    method: 'POST',
    data,
  });
}


export async function getUserApi(params) {
  return request('/announcement/api/flowCondition', {
    method: 'GET',
    params,
  });
}

export async function getBusinessWorkFlowConfigAPI(params) {
  return request('/announcement/api/workflow/business/progress', {
    params,
  });
}

export async function listValidTagConfig() {
  return request('/announcement/api/listValidTagConfig');
}

// 中小企业工作流状态
export async function getSmallFlowConfigAPI(params) {
  return request('/announcement/api/small/business/progress', {
    params,
  });
}
