/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-06-03 20:44:09
 * @FilePath: /zcy-announcement-v2-front/src/routes/Manage/components/PullAnnDrawer/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import {
  Drawer,
  Input,
  Form,
  message,
} from 'doraemon';
import { revokeByAnnouncementPost } from 'src/api/announcement/api';

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const TextArea = Input.TextArea;

const FormItem = Form.Item;

const PullAnnDrawer = (props) => {
  const {
    visible,
    onCancel,
    form,
    id,
    onSubmit,
  } = props;
  const { getFieldDecorator } = props.form;

  const onOkPullAnn = () => {
    form.validateFields(['revokeReason'], (err, value) => {
      if (!err) {
        revokeByAnnouncementPost({
          announcementId: id,
          revokeReason: value.revokeReason,
        }).then((res) => {
          if (!res.success) return;
          if (res.result) {
            message.success('下架成功', () => {
              onCancel();
              onSubmit();
            });
          } else {
            message.success('下架失败');
          }
        });
      }
    });
  };

  return (
    <Drawer
      title={
        <div className="drawer-titleNode-wrapper">
          公告下架
        </div>
      }
      openConfirmBtn
      onOk={onOkPullAnn}
      onCancel={onCancel}
      destroyOnClose
      closable={false}
      width="580"
      visible={visible}
    >
      <Form>
        <FormItem
          label="下架原因"
          required
          {...formItemLayout}
        >
          {getFieldDecorator('revokeReason', {
            rules: [{
              required: true, message: '请输入下架原因',
            }],
          })(
            <TextArea placeholder="请输入" maxLength={1000} />
          )}
        </FormItem>
      </Form>
    </Drawer>
  );
};

export default Form.create()(PullAnnDrawer);
