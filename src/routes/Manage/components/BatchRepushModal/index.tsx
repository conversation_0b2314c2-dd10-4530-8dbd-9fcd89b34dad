

import React from 'react';
import { Modal, Alert, Table } from 'doraemon';

const ErrorContainer = ({
  dataSource,
  successCount,
  errorCount,
}) => {
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 70,
      render: (_, __, index) => {
        return index + 1;
      },
    },
    {
      title: '公告名称',
      dataIndex: 'title',
      width: 230,
    }, {
      title: '失败原因',
      dataIndex: 'errorMsg',
      width: 230,
    },
  ];

  return (
    <React.Fragment>
      <Alert
        message={`本次批量重推 ${successCount + errorCount} 个公告，${successCount} 个重推成功，${errorCount} 个重推失败，失败原因详见下方列表`}
        type="firstinfo"
        showIcon
        iconType="exclamation-circle-o"
      />
      <Table className="zcy-mg-b-20 zcy-mg-t-20 " dataSource={dataSource} columns={columns} />
    </React.Fragment>
  );
};

const BatchRepushModal = props => {
  const {
    onCancel,
    visible,
    form,
    info,
    onSubmit,
    ...rest
  } = props;
  const onOk = () => {
    onSubmit?.();
    onCancel();
  };

  const {
    successCount,
    errorCount,
    dataSource,
  } = info;

  return (
    <Modal
      title="公告批量重推"
      visible={visible}
      {...rest}
      onOk={onOk}
      onCancel={onCancel}
      okText="确认"
      okCancel={false}
    >
      <ErrorContainer successCount={successCount} errorCount={errorCount} dataSource={dataSource} />
    </Modal>
  );
};

export default BatchRepushModal;
