

import React, { useState } from 'react';
import { Modal, Form, Alert, Input, Spin, message } from 'doraemon';
import useModal from 'src/hooks/useModal';
import { revokeBySitePost, revokeByAnnouncementPost } from 'src/api/announcement/api';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

const RevokeModal = ({
  onCancel,
  onSubmit,
  visible,
  form,
  announcementId,
  siteList,
  isAllSite,
  ...rest
}) => {
  const { getFieldDecorator, validateFieldsAndScroll } = form;
  const [loading, setLoading] = useState(false);

  const onOk = () => {
    validateFieldsAndScroll((error, { revokeReason }) => {
      if (error) {
        return;
      }
      setLoading(true);
      if (isAllSite) {
        revokeByAnnouncementPost({
          announcementId,
          revokeReason,
        }).then(res => {
          if (!res.success) return;
          if (res.result) {
            return message.success('全部下架成功', () => {
              onCancel();
              onSubmit();
            });
          }
          return message.success('部分下架失败，详情请查看公告推送列表', () => {
            onCancel();
            onSubmit();
          });
        }).finally(() => {
          setLoading(false); 
        });
      } else {
        revokeBySitePost({
          announcementId,
          announcementSiteId: siteList[0].siteId,
          revokeReason,
        }).then(res => {
          if (!res.success) return;
          if (res.result) {
            return message.success('下架成功', () => {
              onCancel();
              onSubmit();
            });
          }
          return message.success('下架失败', () => {
            onCancel();
            onSubmit();
          });
        }).finally(() => {
          setLoading(false); 
        });
      }
    });
  };

  return (
    <Modal
      title={`${isAllSite ? '公告全部下架' : '公告下架'} `}
      visible={visible}
      {...rest}
      onOk={onOk}
      onCancel={onCancel}
      okText="确定"
      okButtonProps={
        { loading }
      }
    >
      <Spin spinning={loading}>
        <React.Fragment>
          <Alert
            message="请填写下架原因，点击确定后将立即执行下架"
            type="firstinfo"
            showIcon
            iconType="exclamation-circle-o"
          />
          <div className="zcy-mg-b-20 zcy-mg-t-8 ">
            <Form >
              <Form.Item
                label={`${isAllSite ? '全部站点' : '下架站点'}`}
                required
                {...formItemLayout}
              >
                <span>{siteList?.map(ele => ele.title).join('，')}</span>
              </Form.Item>
              <Form.Item
                label="下架原因"
                required
                {...formItemLayout}
              >
                {getFieldDecorator('revokeReason', {
                  rules: [{
                    required: true, message: '请输入下架原因',
                  }],
                })(
                  <Input.TextArea placeholder="请输入" maxLength={1000} />
                )}
              </Form.Item>
            </Form>
          </div>
        </React.Fragment>
      </Spin>
    </Modal>
  );
};

const RevokeModalByForm = Form.create()(RevokeModal);
const useRevokeModal = (props) => useModal(RevokeModalByForm, props);


export { useRevokeModal };
export default RevokeModalByForm;
