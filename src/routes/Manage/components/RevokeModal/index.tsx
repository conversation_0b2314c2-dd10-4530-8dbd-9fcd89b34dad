

import React, { useState } from 'react';
import { Modal, Form, Alert, Input, Spin, Table, message } from 'doraemon';
import { revokePost } from 'src/api/announcement/api/admin/batch';

enum EActionStatus {
  create = 0,
  errorResult = 1
}

const formItemLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 19,
  },
};


const ErrorContainer = ({
  dataSource,
  successCount,
  errorCount,
}) => {
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 70,
      render: (_, __, index) => {
        return index + 1;
      },
    },
    {
      title: '公告名称',
      dataIndex: 'title',
      width: 230,
    }, {
      title: '失败原因',
      dataIndex: 'errorMsg',
      width: 230,
    },
  ];

  return (
    <React.Fragment>
      <Alert
        message={`本次批量下架 ${successCount + errorCount} 个公告，${successCount} 个下架成功，${errorCount} 个下架失败，失败原因详见下方列表`}
        type="firstinfo"
        showIcon
        iconType="exclamation-circle-o"
      />
      <Table className="zcy-mg-b-20 zcy-mg-t-20 " dataSource={dataSource} columns={columns} />
    </React.Fragment>
  );
};

const BatchRevokeModal = ({
  onCancel,
  onSubmit,
  visible,
  form,
  annList,
  ...rest
}) => {
  const { getFieldDecorator, validateFieldsAndScroll } = form;
  const [loading, setLoading] = useState(false);
  const [actionStatus, setActionStatus] = useState(EActionStatus.create);
  const [errorTableInfo, setErrorTableInfo] = useState<{
    successCount: number
    errorCount: number
    dataSource: any[]
  }>({
    successCount: 0,
    errorCount: 0,
    dataSource: [],
  });

  const onOk = () => {
    validateFieldsAndScroll((error, { reason }) => {
      if (error) return;
      setLoading(true);
      revokePost({
        announcementIds: annList.map(ele => ele.id),
        reason,
      }).then(res => {
        if (!res.success) return;
        const totalNum = res.result!.totalNum!;
        const successNum = res.result!.successNum!;
        const failNum = res.result!.failNum!;
        const details = res.result!.details!;
        if (totalNum === successNum) {
          setActionStatus(EActionStatus.create);
          message.success('批量下架成功', () => {
            onCancel();
            onSubmit?.();
          });
          return;
        }
        setActionStatus(EActionStatus.errorResult);
        setErrorTableInfo({
          successCount: successNum,
          errorCount: failNum,
          dataSource: details,
        });
      }).finally(() => {
        setLoading(false);
      });
    });
  };

  return (
    <Modal
      title="公告批量下架"
      visible={visible}
      {...rest}
      onOk={() => {
        if (actionStatus === EActionStatus.create) return onOk();
        onSubmit?.();
        return onCancel();
      }}
      onCancel={onCancel}
      okText="确定"
      okButtonProps={
        { loading }
      }
    >
      <Spin spinning={loading}>
        {
          actionStatus === EActionStatus.create ? (
            <React.Fragment>
              <Alert
                message={`已选 ${annList?.length ?? 0} 个公告进行下架，请填写下架原因，点击确定后将立即执行下架`}
                type="firstinfo"
                showIcon
                iconType="exclamation-circle-o"
              />
              <div className="zcy-mg-b-20 zcy-mg-t-8 ">
                <Form >
                  <Form.Item
                    label="下架公告"
                    required
                    {...formItemLayout}
                  >
                    <span>{annList?.map(ele => ele.title).join('，')}</span>
                  </Form.Item>
                  <Form.Item
                    label="下架原因"
                    required
                    {...formItemLayout}
                  >
                    {getFieldDecorator('reason', {
                      rules: [{
                        required: true, message: '请输入下架原因',
                      }],
                    })(
                      <Input.TextArea placeholder="请输入" maxLength={1000} />
                    )}
                  </Form.Item>
                </Form>
              </div>
            </React.Fragment>
          ) : null
        }

        {
          actionStatus === EActionStatus.errorResult ? (
            <ErrorContainer 
              successCount={errorTableInfo.successCount}
              errorCount={errorTableInfo.errorCount} 
              dataSource={errorTableInfo.dataSource}
            />
          ) : null
        }

      </Spin>
    </Modal>
  );
};

export default Form.create()(BatchRevokeModal);
