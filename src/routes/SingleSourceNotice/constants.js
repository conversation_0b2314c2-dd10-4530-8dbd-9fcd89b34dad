
// 单一来源列表page_code
export const LIST_PAGE_CODE = 'singleSourceNotice';

export const BTN_STATUS = {
  /** 显示可用 */
  enabled: 1,
  /** 隐藏 */
  hide: 2,
  /** 显示禁用 */
  disabled: 3,
};

export const FORM_PAGE_CODE = 'singleSourcePublicity';
export const ANNOUNCEMENT_TYPE = 3012;
export const ANNBIG_TYPE = 1;
export const LAYOUTMODE = 'project';

export const STATUS_MAP = {
  /** 待提交 */
  DRAFT: 0,
  /** 审核中 */
  AUDITING: 10,
  /** 待发布 */
  WAITING_PUBLISH: 20,
  /** 被退回 */
  CHECK_FAILED: 30,
  /** 发布中 */
  PUBLISHING: 40,
  /** 已结束 */
  PUBLISH_END: 60,
  /** 已作废 */
  ABOLISHED: 70,
  /** 异议中 */
  OBJECTION: 80,
  /** 已发布 */
  PUBLISHED: 50,
  /** 未知状态 */
  UNKNOWN: -100,
};
