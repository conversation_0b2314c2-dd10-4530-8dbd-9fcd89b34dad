import hecateRequest from 'src/utils/request';

/**
 * 单一来源
*/

// 获取单一来源公告关联关系列表
export const pageSingleSourceRelation = data => hecateRequest('/api/procurement/requirement/single/pageSingleSourceRelation', {
  method: 'POST',
  data,
});


// 列表-关联，列表-重新关联
export const pageSingleSourceRecord = data => hecateRequest('/api/procurement/requirement/single/pageSingleSourceRecord', {
  method: 'POST',
  data,
});


// 根据项目id获取需求包列表数据
export const listRequirementPackages = data => hecateRequest('/api/procurement/requirement/single/listRequirementPackages', {
  method: 'POST',
  data,
});

// 点击申请来源展示弹窗后，点击确认后的回调
export const associateSingleSourceRelation = data => hecateRequest('/api/procurement/requirement/single/associateSingleSourceRelation', {
  method: 'POST',
  data,
});


// 撤回单一来源公告   列表-撤回公告
export const revokeSingleSourceAnnouncementRelation = data => hecateRequest('/api/procurement/requirement/single/revokeSingleSourceAnnouncementRelation', {
  method: 'POST',
  data,
});

// 撤回单一来源公告审核  列表-撤回按钮
export const revokeSingleSourceRelation = data => hecateRequest('/api/procurement/requirement/single/revokeSingleSourceRelation', {
  method: 'POST',
  data,
});


// 查询单一来源公告-流程变量信息 申请单一来源按钮状态 
export const getSingleSourceFlowParam = data => hecateRequest('/api/procurement/requirement/single/getSingleSourceFlowParam', {
  method: 'POST',
  data,
});


// 公告发布接口 
export const releaseAnnouncement = data => hecateRequest('/api/procurement/requirement/single/releaseAnnouncement', {
  method: 'POST',
  data,
});

// 填充单一来源公告申请单草稿 
export const fillSingleSourceRelation = data => hecateRequest('/api/procurement/requirement/single/fillSingleSourceRelation', {
  method: 'POST',
  data,
});

