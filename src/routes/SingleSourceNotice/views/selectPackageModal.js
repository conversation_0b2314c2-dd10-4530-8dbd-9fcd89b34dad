import React, { useEffect, useState } from 'react';
import { Modal, Alert, Table, message } from '@zcy/doraemon';
import { listRequirementPackages } from '../services';

const COLUMNS = [{
  title: '采购包号',
  dataIndex: 'packageName',
}, {
  title: '采购标的',
  dataIndex: 'bdNames',
}];

const SelectPackageModal = ({
  visible, onCancel, onSubmit, projectId,
}) => {
  const [list, setList] = useState([]);
  const [selectRows, setSelectRows] = useState([]);
  const [relationList, setRelationList] = useState([]);

  useEffect(() => {
    if (visible) {
      setSelectRows([]);
      listRequirementPackages({
        projectId,
      }).then((res) => {
        if (!res.success) return;
        setList(res.result);
        setRelationList(res.result.filter(ele => ele.relationed));
      });
    }
  }, [visible]);

  const onSelectChange = (_, selectedRows = []) => {
    setSelectRows(selectedRows);
  };

  const onOk = () => {
    if (!selectRows?.length) {
      message.error('请选择一条采购包');
    }
    return onSubmit(selectRows);
  };

  return (
    <Modal
      title="选择采购包"
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
    >
      {
        relationList.length ? 
          <Alert
            style={{ marginBottom: 16 }}
            message={`注意：${(relationList || [])?.map((relationItem) => relationItem.packageName)?.filter((name) => !!name)?.join('、')}，在需求文件中已有关联的单据，申请单据提交后，会自动替换掉已关联的单据。`}
            type="firstinfo"
            showIcon
            iconType="exclamation-circle-o"
          />
          : null
      }
      <Table
        columns={COLUMNS}
        dataSource={list}
        rowKey="packageId"
        rowSelection={{
          selectedRowKeys: selectRows.map(ele => ele.packageId),
          onChange: onSelectChange,
        }}
        pagination={false}
      />
    </Modal>
  );
};

export default SelectPackageModal;
