import React, { useEffect } from 'react';
import { Modal, Form, Alert, Input, message } from 'doraemon';
import { revokeSingleSourceRelation } from '../services';


const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};

const RevokeModal = (props = {}) => {
  const { visible, onCancel, onSubmit, form, id } = props;
  const { getFieldDecorator, validateFields, resetFields } = form;


  useEffect(() => {
    if (visible) {
      resetFields();
    }
  }, [visible]);

  const onOk = () => {
    validateFields((err, values) => {
      if (err) return;
      const { reason } = values;
      revokeSingleSourceRelation({
        reason, id,
      }).then((res) => {
        if (!res.success) return;
        message.success('回撤成功');
        onSubmit();
      });
    });
  };


  return (
    <Modal
      title="公告撤回"
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Alert
        message="提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作"
        type="firstinfo"
        showIcon
        iconType="exclamation-circle-o"
      />
      <Form style={{ marginTop: 24 }} layout="inline">
        <Form.Item 
          {...formItemLayout}
          label="撤回理由"
        >
          {getFieldDecorator('reason', {
            rules: [{ required: true, message: '请输入' }],
          })(
            <Input.TextArea />
          )}
        </Form.Item>
      </Form>

    </Modal>
  );
};

export default Form.create()(RevokeModal);
