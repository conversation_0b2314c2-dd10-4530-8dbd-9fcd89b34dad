import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import moment from 'moment';
import { Panel, Select, DatePicker, Icon, Badge } from 'doraemon';
import tracer from 'src/utils/tracer';
import pushLog from 'src/utils/pushLog';
import CountCard from './components/countCard';
import Chart from './components/chart';
import Shortcut from './components/shortcut';
import ShortcutSet from './components/shortcutSet';
import { staticsAnnouncementCountByType, obtainAnnBigTypeByUser, obtainMyBacklogCounts } from '../services';
import './index.less';

const { MonthPicker, RangePicker } = DatePicker;


const initValue = {
  day: new Date(new Date().getTime() - (24 * 60 * 60 * 1000)),
  month: new Date(),
  year: parseInt(moment(new Date()).format('YYYY'), 10),
  range: [new Date().getTime() - (10 * 24 * 60 * 60 * 1000), new Date().getTime()],
};

@connect(({ index }) => ({
  index,
}))
export default class Index extends Component {
  static defaultProps = {
    todoList: [{
      id: 1,
      name: '意见征询或公示',
      icon: 'yijianzhengxun1',
      color: '#389AFB',
      borderColor: 'rgba(55,153,250,0.15)',
    }, {
      id: 2,
      name: '采购项目公告',
      icon: 'caigouhetonggonggao1',
      color: '#20BD88',
      borderColor: 'rgba(32,189,136,0.15)',
    }, {
      id: 3,
      name: '更正公告',
      icon: 'gengzhenggonggao1',
      color: '#FF7253',
      borderColor: 'rgba(255,114,83,0.15)',
    }, {
      id: 4,
      name: '采购结果公告',
      icon: 'caigoujieguogonggao1',
      color: '#20BD88',
      borderColor: 'rgba(32,189,136,0.15)',
    }, {
      id: 5,
      name: '采购合同公告',
      icon: 'caigouhetonggongshi',
      color: '#389AFB',
      borderColor: 'rgba(55,153,250,0.15)',
    }, {
      id: 6,
      name: '履约验收公告',
      icon: 'lvyueyanshougonggao',
      color: '#20BD88',
      borderColor: 'rgba(32,189,136,0.15)',
    }, {
      id: 8,
      name: '电子卖场公告',
      icon: 'dianzimaichanggonggao',
      color: '#389AFB',
      borderColor: 'rgba(55,153,250,0.15)',
    }, {
      id: 7,
      name: '政府采购监管公告',
      icon: 'zhengfucaigoujianguangonggao',
      color: '#389AFB',
      borderColor: 'rgba(55,153,250,0.15)',
    },
    {
      id: 9,
      name: '非政府采购公告',
      icon: 'feizhengfucaigougonggao',
      color: '#389AFB',
      borderColor: 'rgba(55,153,250,0.15)',
    }],
  }
  constructor(props) {
    super(props);
    this.state = {
      countType: 2,
      countUnit: 1,
      countParams: {
        day: initValue.day,
        month: initValue.month,
        year: initValue.year,
        range: initValue.range,
      },
      menuList: [],
      todoNumber: [],
      total: 0,
      countSum: [{
        id: 1,
        annSum: 0,
      }, {
        id: 2,
        annSum: 0,
      }, {
        id: 3,
        annSum: 0,
      }, {
        id: 4,
        annSum: 0,
      }, {
        id: 5,
        annSum: 0,
      }, {
        id: 6,
        annSum: 0,
      }, {
        id: 7,
        annSum: 0,
      }, {
        id: 8,
        annSum: 0,
      }, {
        id: 9,
        annSum: 0,
      }],
    };
  }
  handelCountTypeChange = (countType) => {
    this.setState({
      countType,
      countParams: {
        ...initValue,
      },
    }, () => {
      this.getCountData(countType);
    });
  }
  changeCountUnit = (countUnit) => {
    this.setState({
      countUnit,
    }, () => {
      this.getCountData(this.state.countType);
      this.chartRef.getChartData(countUnit === 1);
    });
  }
  dateChange = (day) => {
    this.setState({
      countParams: {
        day,
      },
    }, () => {
      this.getCountData('1');
    });
  }
  monthChange = (month) => {
    this.setState({
      countParams: {
        month,
      },
    }, () => {
      this.getCountData('2');
    });
  }
  yearChange = (year) => {
    this.setState({
      countParams: {
        year,
      },
    }, () => {
      this.getCountData('3');
    });
  }
  rangeChange = (range) => {
    this.setState({
      countParams: {
        range,
      },
    }, () => {
      this.getCountData('4');
    });
  }
  getCountData = (countType) => {
    let params = {};
    if (countType === '1') {
      params = {
        annYear: moment(this.state.countParams.day).format('YYYY'),
        annMonth: moment(this.state.countParams.day).format('MM'),
        annDay: moment(this.state.countParams.day).format('DD'),
      };
    }
    if (countType === '2') {
      params = {
        annYear: moment(this.state.countParams.month).format('YYYY'),
        annMonth: moment(this.state.countParams.month).format('MM'),
      };
    }
    if (countType === '3') {
      params = {
        annYear: this.state.countParams.year,
      };
    }
    if (countType === '4') {
      params = {
        startDate: moment(this.state.countParams.range[0]).format('YYYY-MM-DD'),
        endDate: moment(this.state.countParams.range[1]).format('YYYY-MM-DD'),
      };
    }
    this.setState({
      countType,
    });
    staticsAnnouncementCountByType({
      isOrg: this.state.countUnit === 1,
      ...params,
    }).then((res) => {
      // res.result = [
      //   {
      //     annBigType: 1,
      //     annSum: Math.random() * 1000,
      //   },
      //   {
      //     annBigType: 2,
      //     annSum: Math.random() * 1000,
      //   },
      // ];
      const countSum = [];
      for (let i = 1; i <= 9; i += 1) {
        const match = res.result.find(item => item.annBigType === i);
        if (match) {
          countSum.push({
            id: match.annBigType,
            annSum: match.annSum,
          });
        } else {
          countSum.push({
            id: i,
            annSum: 0,
          });
        }
      }
      this.setState({
        countSum,
      });
    });
  }
  componentDidMount() {
    this.getCountData('2');
    // 根据用户信息获取大类别
    obtainAnnBigTypeByUser().then((res) => {
      if (res.success) {
        this.setState({
          menuList: res.result,
        });
      }
    });
    // 获取我的待办数量
    obtainMyBacklogCounts().then((res) => {
      if (res.success) {
        let total = 0;
        Object.keys(res.result).forEach((item) => {
          total = res.result[item] + total;
        });
        this.setState({
          todoNumber: res.result,
          total,
        });
      }
    });
  }
  linkTo = (id) => {
    this.props.dispatch(routerRedux.push(`/manage/list/${id}`));
  }

  sendTrace = (d) => {
    try {
      tracer({
        utmCD: ['cStats', d],
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
  }

  render() {
    const { todoList } = this.props;
    const { operation } = this.props.index.shortcut;
    const { menuList, todoNumber, total, countParams } = this.state;
    const yearNow = parseInt(moment(new Date()).format('YYYY'), 10);
    let yearCount = yearNow;
    const years = [];
    while (yearCount >= 2016) {
      years.push(yearCount);
      yearCount -= 1;
    }
    const titleTools = (
      <div className="title-tools">
        <span className="title">数据统计</span>
        <Select className="select-type" defaultValue="2" onChange={this.handelCountTypeChange}>
          <Select.Option onClick={() => this.sendTrace('dSwitchDay')} data-utm-click="dSwtichYearMonthDay" value="1">日</Select.Option>
          <Select.Option onClick={() => this.sendTrace('dSwitchMonth')} value="2">月</Select.Option>
          <Select.Option onClick={() => this.sendTrace('dSwitchYear')} value="3">年</Select.Option>
          <Select.Option value="4">自定义</Select.Option>
        </Select>
        {[
          (<DatePicker onChange={this.dateChange} format="YYYY-MM-DD" value={countParams.day} allowClear={false} />),
          (<MonthPicker onChange={this.monthChange} format="YYYY-MM" value={countParams.month} allowClear={false} />),
          (
            <Select onChange={this.yearChange} defaultValue={yearNow} style={{ width: '220px' }}>
              {
                years.map((item) => {
                  return (
                    <Select.Option value={item} key={item}>{item}</Select.Option>
                  );
                })
              }
            </Select>
          ),
          (<RangePicker
            onChange={this.rangeChange}
            defaultValue={[new Date(new Date().getTime() - (10 * 24 * 60 * 60 * 1000)), new Date()]}
            format="YYYY-MM-DD"
            allowClear={false}
          />
          )][this.state.countType - 1]}
        <div className="radio-group">
          <span onClick={() => { this.changeCountUnit(1); }} className={this.state.countUnit === 1 ? 'active' : ''}>单位</span>
          <span data-utm-click="dSwitchToPerson" onClick={() => { this.changeCountUnit(2); }} className={this.state.countUnit === 2 ? 'active' : ''}>个人</span>
          <div className={`selected unit-${this.state.countUnit}`} />
        </div>
      </div>
    );
    return (
      <div
        className="overview"
      >
        <Shortcut />
        {operation ? <ShortcutSet /> :
          (
            <div className="main-view">
              <div data-utm-c="cStats" className="header-no-padding">
                <Panel
                  title={titleTools}
                >
                  <CountCard countSum={this.state.countSum} />
                  <Chart ref={(ref) => { this.chartRef = ref; }} />
                </Panel>
              </div>
              <Panel
                title={`我的任务（${total}）`}
              >
                <ul 
                  data-utm-c="cTasks"
                  className="todo"
                >
                  {todoList.filter(item => menuList.indexOf(item.id) !== -1).map(item => (
                    <li key={item.name} style={{ width: `${100 / menuList.length}%` }} >
                      <div data-utm-click="dAnnBtn" onClick={() => this.linkTo(item.id)} className="circle" style={{ borderColor: item.borderColor }} />
                      <Icon type={item.icon} zcy style={{ color: item.color }} />
                      <div className="title" onClick={() => this.linkTo(item.id)}>{item.name}</div>
                      <Badge count={todoNumber[item.id]} />
                    </li>
                  ))}
                </ul>
              </Panel>
            </div>
          )
        }


      </div>
    );
  }
}
