import React, { Component } from 'react';
import { Tag, Icon } from 'doraemon';

class announcement extends Component {
  render() {
    const {
      icon,
      data,
      disable,
      onClick,
    } = this.props;
    const icons = {
      plus: 'plus-circle',
      minus: 'minus-circle',
      check: 'check-circle',
    };
    return (
      <Tag
        className={`announcement-tag ${icon} ${disable ? 'disable' : ''}`}
        onClick={() => { !disable && onClick(); }}
      >
        {data.value}
        <Icon type={icons[icon]} />
      </Tag>
    );
  }
}

export default announcement;
