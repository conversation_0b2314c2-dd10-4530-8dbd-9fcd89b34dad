import React, { Component } from 'react';
import { connect } from 'dva';
import Announcement from './announcement';

@connect(({ index }) => ({
  index,
}))
class shortcutSet extends Component {
  constructor(props) {
    super(props);
    this.props.dispatch({
      type: 'index/setShortcut',
      payload: {
        tempSaveData: props.index.shortcut.savedData ? props.index.shortcut.savedData.map(
          item => ({ key: item.key, value: item.value })
        ) : [],
      },
    });
  }

  delete = (item) => {
    const { tempSaveData } = this.props.index.shortcut;
    const newSaveData = tempSaveData.filter(t => t.key !== item.key);
    this.props.dispatch({
      type: 'index/setShortcut',
      payload: {
        tempSaveData: newSaveData,
      },
    });
  }

  add = (item) => {
    const { tempSaveData } = this.props.index.shortcut;
    this.props.dispatch({
      type: 'index/setShortcut',
      payload: {
        tempSaveData: [...tempSaveData, item],
      },
    });
  }

  render() {
    const { allData, tempSaveData } = this.props.index.shortcut;
    return (
      <div className="shortcut-set">
        <div className="title">
          首页已添加（{tempSaveData.length}）
        </div>
        <div className="content">
          {
            tempSaveData.map(item => (
              <Announcement
                key={item.key}
                data={item}
                icon="minus"
                onClick={() => {
                  this.delete(item);
                }}
              />
            ))
          }
        </div>
        <div className="title">
          公告类型添加池
        </div>
        <div className="content-border">
          {
            allData.map((item) => {
              return (
                <div className="category" key={item.key}>
                  <div className="name">
                    {item.label}
                  </div>
                  <div className="items">
                    {
                      item.children && item.children.map(
                        children => (
                          <Announcement
                            key={children.key}
                            data={children}
                            icon={tempSaveData.some(
                              s => s.key === children.key
                            ) ? 'check' : 'plus'}
                            disable={tempSaveData.some(
                              s => s.key === children.key
                            ) || tempSaveData.length === 12}
                            onClick={() => {
                              this.add(children);
                            }}
                          />
                        )
                      )
                    }
                  </div>
                </div>
              );
            })
          }
        </div>
      </div>
    );
  }
}

export default shortcutSet;
