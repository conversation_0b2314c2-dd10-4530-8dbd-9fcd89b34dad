import React, { Component } from 'react';
import G2 from '@antv/g2';
import { Icon } from 'doraemon';
import { staticsAnnouncementCountByLastMonth } from '../../services';

class Chart extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }
  componentDidMount() {
    this.initChart();
    this.getChartData(true);
  }

  initChart = () => {
    G2.track(false);
    this.chart = new G2.Chart({
      container: 'chart',
      forceFit: true,
      height: 200,
      padding: 'auto',
    });
    // chart.source([]);
    this.chart.scale('annSum', {
      alias: '公告发布数',
    });
    this.chart.scale('annMonth', {
    });
    this.chart.line().position('annMonth*annSum').shape('smooth').style({
      stroke: '#3177FD',
      lineWidth: 2,
    });
    this.chart.render();
  }
  getChartData=(isOrg) => {
    staticsAnnouncementCountByLastMonth(
      { isOrg }
    ).then((res) => {
      if (res.success) {
        // const res = {};
        // res.result = [{
        //   annMonth: 4,
        //   annSum: 1,
        //   annYear: 2018,
        // }, {
        //   annMonth: 5,
        //   annSum: 10,
        //   annYear: 2018,
        // }, {
        //   annMonth: 6,
        //   annSum: 10,
        //   annYear: 2018,
        // }];
        res.result.forEach((item) => {
          item.annMonth = `${item.annYear}-${item.annMonth < 10 ? `0${item.annMonth}` : item.annMonth}`;
        });
        try {
          this.chart.changeData(res.result);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log(error);
        }
      }
    });
  }
  showOrHide = () => {
    this.setState(prevState => ({
      visible: !prevState.visible,
    }));
  }
  render() {
    return (
      <div className={`chart-container ${this.state.visible ? 'show' : 'hide'}`}>
        <div className="title">
          最近一年公告发布数统计
          <a data-utm-click={this.state.visible ? 'dCloseBtn' : 'dExpandBtn'} onClick={this.showOrHide}>{this.state.visible ? '收起' : '展开'}</a>
        </div>
        <div className="unit">
          <span><Icon type="tushi" zcy />公告发布数</span>
          <span>单位：个</span>
        </div>
        <div id="chart" />
      </div>
    );
  }
}

export default Chart;
