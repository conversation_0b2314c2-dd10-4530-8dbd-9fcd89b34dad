import React, { Component } from 'react';
import { connect } from 'dva';
import { message, Icon } from 'doraemon';
import SelectedAnn from './selectedAnn';

const isProxyUnit = window.currentUserIdentity.categoryName.startsWith('0302');

@connect(({ index }) => ({
  index,
}))
class shortcut extends Component {
  state = {
    canCreateAnnouncement: false,
  }
  componentDidMount() {
    // 是否有创建公告权限
    this.props.dispatch({
      type: 'index/getCanCreateAnnouncement',
    }).then((res) => {
      if (res.success) {
        if (res.result) {
          this.getQuickEntries();
        }
      }
    });
  }

  getQuickEntries = () => {
    this.props.dispatch({
      type: 'index/getQuickEntries',
    }).then(() => {
      if (this.ref) {
        this.props.dispatch({
          type: 'index/setShortcut',
          payload: {
            height: this.ref.offsetHeight,
          },
        });
        this.setState({
          canCreateAnnouncement: true,
        });
      }
    });
  }

  getAllEntries = () => {
    this.props.dispatch({
      type: 'index/getAllEntries',
    });
  }

  onRef = (ref) => {
    this.ref = ref;
  }
  setting = () => {
    this.updateOperation(true);
    if (!this.fetched) {
      this.getAllEntries();
      this.fetched = true;
    }
  }

  cancel = () => {
    this.updateOperation(false);
  }

  submit = () => {
    const { tempSaveData = [] } = this.props.index.shortcut;
    this.props.dispatch({
      type: 'index/modifyQuickEntries',
      payload: {
        idStr: tempSaveData.map(item => item.key).join(','),
      },
    }).then(({ success, error }) => {
      if (!success) {
        message.error(error);
        return;
      }
      message.success('设置成功！');
      this.getQuickEntries();
      this.updateOperation(false);
    });
  }

  updateOperation = (operation) => {
    this.props.dispatch({
      type: 'index/setShortcut',
      payload: {
        operation,
      },
    }).then(() => {
      if (this.ref) {
        this.props.dispatch({
          type: 'index/setShortcut',
          payload: {
            height: this.ref.offsetHeight,
          },
        });
      }
    });
  }

  render() {
    const { canCreateAnnouncement } = this.state;
    const { operation, height, savedData } = this.props.index.shortcut;
    return (
      <div
        data-utm-c="cHeader"
        className={canCreateAnnouncement ? 'shortcut show' : 'shortcut'}
        ref={this.onRef}
      >
        {canCreateAnnouncement ? (
          <div className="container">
            <span className="title">{isProxyUnit ? '公告' : '公告创建快捷入口：'}</span>
            {isProxyUnit && (
              <span>&nbsp;<Icon type="info-circle-o" />&nbsp;快捷入口功能升级中，请到具体菜单列表发布所需公告。</span>
            )}
            {!isProxyUnit && ((!operation && Array.isArray(savedData)) ? (
              savedData.length === 0 ? <span className="items">（未设置）</span> : savedData.map(item => <SelectedAnn key={item.key} data={item} />)
            ) : <span className="items"><Icon type="info-circle-o" /> 可选择要加入快捷入口的公告类型，最多支持12个</span>)}
          </div>
        ) : null}
        {(!isProxyUnit && canCreateAnnouncement) && (
          <div className="operation" style={{ lineHeight: `${height}px` }}>
            <div className={`setting ${operation ? 'hide' : ''}`}>
              <a data-utm-click="dEnterSetting" onClick={this.setting}>快捷入口设置</a>
            </div>
            <div className={`options ${operation ? 'show' : ''}`}>
              <a onClick={this.cancel}>取消</a><a onClick={this.submit}>完成</a>
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default shortcut;
