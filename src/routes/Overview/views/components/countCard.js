import React, { Component } from 'react';
import { Icon } from 'doraemon';
import CountUp from 'react-countup';
import { connect } from 'dva';

@connect(({ index }) => ({
  announcementCount: index.announcementCount,
}))class CountCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      swiperIndex: 1,
    };
  }
  switch = () => {
    switch (this.state.swiperIndex) {
    case 1:
      this.setState({
        swiperIndex: 2,
      });
      break;
    case 2:
      this.setState({
        swiperIndex: 1,
      });
      break;
    default:
      break;
    }
  }
  switchLeft = () => {
    this.setState({
      swiperIndex: this.state.swiperIndex - 1,
    });
  }
  switchRight = () => {
    this.setState({
      swiperIndex: this.state.swiperIndex + 1,
    });
  }
  render() {
    const { swiperIndex } = this.state;
    const { countSum } = this.props;
    const conutUpConfig = {
      start: 0,
      separator: ',',
      duration: 1.5,
    };
    return (

      <div className="swiper">
        <div className="count" style={{ transform: `translateX(${100 * (1 - swiperIndex)}%)` }}>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="yijianzhengxun" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">意见征询或公示</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 1).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="caigouhetonggonggao" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">采购项目公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 2).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="gengzhenggonggao" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">更正公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 3).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="caigoujieguogonggao" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">采购结果公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 4).annSum}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="count" style={{ transform: `translateX(${100 * (2 - swiperIndex)}%)` }}>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="caigougonggao" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">采购合同公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 5).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="lvyueyanshouhetong" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">履约验收公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 6).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="dianzimaichanggonggao1" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">电子卖场公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 8).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="zhengfucaigoujianguangonggao" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">政府采购监管公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 7).annSum}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="count" style={{ transform: `translateX(${100 * (3 - swiperIndex)}%)` }}>
          <div className="card">
            <div className="icon-content">
              <div className="circle">
                <Icon type="feizhengfucaigougonggao1" zcy />
              </div>
            </div>
            <div className="description">
              <div className="title">非政府采购公告</div>
              <div className="number">
                <CountUp
                  {...conutUpConfig}
                  end={countSum.find(item => item.id === 9).annSum}
                />
              </div>
            </div>
          </div>
          <div className="card" style={{ border: 'none' }} />
          <div className="card" style={{ border: 'none' }} />
          <div className="card" style={{ border: 'none' }} />
        </div>
        <div className={`switch pre ${this.state.swiperIndex === 1 ? 'fade-out' : 'fade-in'}`} onClick={this.switchLeft}><Icon type="zuohua" zcy /></div>
        <div data-utm-click="dRightArrowBtn" className={`switch next ${this.state.swiperIndex === 3 ? 'fade-out' : 'fade-in'}`} onClick={this.switchRight}><Icon type="youhua" zcy /></div>
        <div className="stage">
          <div className="top" />
          <div className="bottom" />
        </div>
      </div>
    );
  }
}

export default CountCard;

