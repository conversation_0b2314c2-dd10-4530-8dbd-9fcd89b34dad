/* eslint-disable no-extra-boolean-cast */
import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import { Icon, Button, Modal, message, request } from 'doraemon';
import { getUrlWithAppCode } from '@zcy/basic-sdk';
import { fetchCurrentEnvApi } from '../../../Manage/services';
import { getAppCode } from 'src/utils/roleType';

const confirm = Modal.confirm;

@connect(({ index }) => ({
  index,
}))
class selectedAnn extends Component {
  constructor(props) {
    super(props);
    this.state = {
      env: '', // 当前环境
    };
  }
  componentDidMount() {
    fetchCurrentEnvApi().then((res) => {
      if (res && res.success) {
        const tenant = res?.result?.tenant;
        this.setState({
          env: tenant,
        });
      }
    });
  }

  render() {
    const { data } = this.props;
    return (
      <Button
        className="selected-ann"
        type="secondary"
        data-utm-click="dAnnBtn"
        onClick={
          async () => {
            // 面向中小企业预留项目执行情况公告 主管单位判断
            if (data.key === 14001) {
              try {
                const res = await request(
                  `/announcement/api/config/form/checkMonitorEnterprises?announcementType=${data.key}`
                );
                const { result } = res || {};
                if (!result) {
                  message.info('面向中小企业预留项目执行情况公告由主管单位负责发布，您不是主管单位，不需要发布该公告');
                  return;
                }
              } catch (error) {
                message.info(error?.message || JSON.stringify(error));
              }
            }
            if (data.key === 3005 || data.key === 3006) {
              const that = this;
              const host = window.envHref.index;
              const isSH = this.state.env === 'sh';
              const text = isSH ? (
                <span>
                  当前为手工录入更正公告，如您的项目在平台项目采购模块中进行，请前往【项目采购】-【项目管理】-
                  <a href={`${host}/bidding-entrust/#/correction/list`}>【更正信息发布】</a>
                  中发布更正公告以便同步更新项目信息。
                </span>
              ) : (
                <div>
                  当前为手工录入更正公告，如您的项目在平台项目采购模块中进行，请前往项目采购应用发布更正公告，以便同步更新项目信息。
                  <p>
                    采购文件更正路径：【项目采购】-【项目管理】- <a href={getUrlWithAppCode(`${host}/bid-inviting/correction/list`, getAppCode())}>【采购文件更正】</a>
                  </p>
                  <p>
                    资格预审文件更正路径：【项目采购】-【资格预审】- <a href={getUrlWithAppCode(`${host}/bid-inviting/prequalification/correctionList`, getAppCode())}>【资格预审文件更正】</a>
                  </p>
                </div>
              );
              const confirmParams = {
                content: text,
                cancelText: '继续手工创建',
                onCancel() {
                  if (!!data.formPageCode) {
                    // formPageCode 不为空意味着已经对接表单中心，需要跳转新链接
                    that.props.dispatch(
                      routerRedux.push(
                        `/dynamic/create?formPageCode=${data.formPageCode}&announcementTypeName=${escape(data.value)}&announcementType=${data.key}&annBigType=${data.label}&ids=${data.id}`
                      )
                    );
                  } else {
                    that.props.dispatch(
                      routerRedux.push(
                        `/flow/create?announcementTypeName=${escape(data.value)}&announcementType=${data.key}&annBigType=${data.label}&ids=${data.id}`
                      )
                    );
                  }
                },
              };
              if (isSH) {
                confirmParams.okText = '前往';
                confirmParams.onOk = () => {
                  window.location.href = `${host}/bidding-entrust/#/correction/list`;
                };
              }
              confirm(confirmParams);
            } else if (!!data.formPageCode) {
              // formPageCode 不为空意味着已经对接表单中心，需要跳转新链接
              this.props.dispatch(
                routerRedux.push(
                  `/dynamic/create?formPageCode=${data.formPageCode}&announcementTypeName=${escape(data.value)}&announcementType=${data.key}&annBigType=${data.label}&ids=${data.id}`
                )
              );
            } else {
              this.props.dispatch(
                routerRedux.push(
                  `/flow/create?announcementTypeName=${escape(data.value)}&announcementType=${data.key}&annBigType=${data.label}&ids=${data.id}`
                )
              );
            }
          }
        }
      >
        <Icon type="plus" />{data.value}
      </Button>
    );
  }
}

export default selectedAnn;
