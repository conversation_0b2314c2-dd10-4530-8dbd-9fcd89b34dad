@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.overview {
  position: relative;
  .header-no-padding {
    margin-bottom: 20px;
    .doraemon-panel-header {
      padding: 0 20px;
    }
  }
  .title-tools {
    .title {
      line-height: 65px;
    }
    
    .select-type {
      width: 90px !important;
      margin-left: 10px;
      margin-right: 10px;
    }
    .radio-group {
      position: relative;
      font-size: 12px;
      float: right;
      margin-top: 15px;
      line-height: 34px;
      background-color: #f3f3f3;
      box-shadow: 1px 1px 4px #f7f3f3 inset;
      border: 1px solid #efefef;
      border-radius: 20px;
      span {
        display: inline-block;
        color: #767776;
        width: 54px;
        text-align: center;
        border-radius: 20px;
        cursor: pointer;
        transition: all .3s ease-in-out;
      }
      .active {
        font-weight: 800;
        color: #2a72ff;
        background: #fff;
      }
      .selected {
        position: absolute;
        top: 0;
        width: 54px;
        height: 34px;
        border: 1px solid #2a72ff;
        border-radius: 20px;
        transition: all .3s ease-in-out;
      }
      .selected.unit-1 {
        transform: translateX(0);
      }
      .selected.unit-2 {
        transform: translateX(54px);
      }
    }
  }
  .swiper {
    position: relative;
    height: 120px;
    overflow: hidden;
    margin-left: -20px;
    margin-right: -20px;
  }
  .count {
    position: absolute;
    left: 20px;
    right: 20px;
    display: flex;
    padding: 0 20px;
    z-index: 1;
    transition: all .6s ease-in-out;
    .card {
      background: #fff;
      flex: 1 1 0;
      margin: 0 7px;
      border: 1px solid #e9e9e9;
      border-radius: 6px;
      min-height: 100px;
      width: calc(25% - 14px);
      display: inline-block;
      .icon-content {
        float: left;
        height: 100%;
        width: 100px;
        text-align: center;
        padding: 20px;
        .circle {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background-color: #3177fd;
          i {
            font-size: 30px;
            line-height: 60px;
            color: #fff;
          }
        }
      }
      .description {
        padding-top: 20px;
        .title {
          font-size: 14px;
          color: #777;
        }
        .number {
          font-family: ArialMT;
          font-size: 28px;
          color: #272727;
        }
      }
    }
  }
  .count.count-1 {
    transform: translateX(-100%);
  }
  .count.count-1.show {
    transform: translateX(0);
  }
  .count.count-2 {
    transform: translateX(100%);
  }
  .count.count-2.show {
    transform: translateX(0);
  }
  .count.count-3 {
    transform: translateX(100%);
  }
  .count.count-3.show {
    transform: translateX(0);
  }
  .switch {
    z-index: 2;
    position: absolute;
    height: 36px;
    width: 36px;
    cursor: pointer;
    text-align: center;
    background: #fff;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.22);
    opacity: 0.5;
    .zcyicon {
      color: #757575;
      line-height: 36px;
    }
  }
  .pre {
    left: 28px;
    top: 32px;
    border-radius: 50%;
  }
  .next {
    right: 28px;
    top: 32px;
    border-radius: 50%;
  }
  .fade-in {
    opacity: 1;
  }
  .fade-out {
    opacity: 0;
    z-index: 0;
  }
  .stage {
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    .top {
      position: relative;
      height: 16px;
      background: #f4f8ff;
      background: linear-gradient(225deg, transparent 10px, #f4f8ff 0) right, linear-gradient(-225deg, transparent 10px, #f4f8ff 0) left;
      background-size: 50% 100%;
      background-repeat: no-repeat;
    }
    .bottom {
      height: 8px;
      background-color: #eaf1fe;
    }
  }
  .chart-container {
    overflow: hidden;
    margin-top: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding-top: 20px;
    transition: all 0.2s ease-in-out;
    .title {
      font-family: MicrosoftYaHei;
      font-size: 14px;
      color: #485465;
      letter-spacing: -0.09px;
      margin-left: 20px;
      margin-right: 20px;
      a {
        color: #3177fd;
        letter-spacing: 0.14px;
        float: right;
      }
    }
    .unit {
      font-size: 12px;
      padding: 10px 20px;
      span {
        i {
          margin-right: 10px;
          color: #3177fd;
        }
        &:last-child {
          float: right;
        }
      }
    }
    #chart {
      padding: 0 10px;
    }
    #chart.show {
      height: 220px;
    }
    #chart.hide {
      height: 0;
    }
  }
  .chart-container.show {
    height: 307px;
  }
  .chart-container.hide {
    height: 41px;
    padding-top: 10px;
  }
  ul.todo {
    margin: 0;
    padding: 0;
    height: 100px;
    margin-top: 10px;
    padding-bottom: 10px;
    li {
      position: relative;
      display: inline-block;
      float: left;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      list-style: none;
      width: 12.5%;
      text-align: center;
      height: 60px;
      .circle {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 60px;
        border: 4px solid rgba(55, 153, 250, 0.15);
        border-radius: 50%;
        cursor: pointer;
        transition: all .3s linear;
        &:hover {
          border-width: 30px;
        }
      }
      i {
        font-size: 30px;
        line-height: 60px;
      }
      .title {
        font-size: 14px;
        margin-top: 10px;
        padding: 0 10px;
        cursor: pointer;
      }
      .doraemon-badge {
        position: absolute;
        top: 0;
        left: 50%;
        margin-left: 15px;
      }
    }
  }
}

.shortcut {
  &.show {
    margin-top: -15px;
  }
  position: relative;
  background-color: #fff;
  margin: -15px -20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-top: -80px;
  padding: 5px 20px;
  min-height: 60px;
  &::before {
    content: '';
    position: absolute;
    right: 150px;
    width: 1px;
    top: 10px;
    bottom: 10px;
    background-color: #e9e9e9;
  }
  .container {
    padding-right: 150px;
    .title {
      font-size: 16px;
      color: #202020;
      line-height: 50px;
    }
    .items {
      font-size: 14px;
      color: #777;
    }
    i {
      color: #1b7aff;
    }
  }
  .operation {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 150px;
    text-align: center;
    overflow: hidden;
    .setting,
    .options {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      transition: all .3s ease-in-out;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .setting.hide {
      opacity: 0;
      transform: translateY(-100%);
    }
    .options {
      transform: translateY(100%);
      &.show {
        transform: translateY(0);
      }
      a {
        &:last-of-type {
          margin-left: 20px;
        }
      }
    }
    a {
      font-size: 14px;
      color: #1b7aff;
    }
  }
}

.shortcut-set {
  min-height: 300px;
  background: #fff;
  border-radius: 6px;
  animation: fadeIn 300ms ease-in-out;
  padding: 20px;
  .title {
    position: relative;
    font-size: 16px;
    color: #202020;
    padding-left: 10px;
    &::before {
      content: '';
      position: absolute;
      top: 5px;
      left: -5px;
      bottom: 5px;
      width: 3px;
      background-color: #3177fd;
    }
  }
  .content {
    padding: 20px 10px;
  }
  .content-border {
    padding: 30px 0;
    margin-top: 25px;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
  }
  .category {
    display: flex;
    margin-top: 20px;
    &:first-of-type {
      margin-top: 0;
    }
    .name {
      flex: 0;
      min-width: 150px;
      text-align: right;
      line-height: 30px;
      font-size: 14px;
      color: #202020;
    }
    .items {
      flex: 1;
    }
  }
}

.announcement-tag.doraemon-tag {
  line-height: 30px;
  height: 30px;
  background: #f3f3f3;
  font-size: 14px;
  color: #202020;
  padding: 0 14px;
  box-shadow: 0 0 1px #dbd8d8;
  border-radius: 17px;
  margin: 0 10px 15px 10px;
  user-select: none;
  border: 1px solid #f3f3f3;
  &.minus {
    &:hover {
      border-color: #ff625c;
    }
  }
  &:hover {
    border-color: #3177fd;
  }
  &.disable {
    color: #a5a5a5;
    &:hover {
      cursor: default;
      border-color: #f3f3f3;
    }
    .anticon-plus-circle {
      color: rgb(172, 168, 168);
    }
  }
  .anticon {
    font-size: 16x;
    margin-left: 10px;
    transform: scale(1.3);
  }
  .anticon-plus-circle {
    color: #3177fd;
  }
  .anticon-minus-circle {
    color: #ff625c;
  }
  .anticon-check-circle {
    color: rgb(172, 168, 168);
  }
}

.selected-ann {
  border: 1px solid #d8d8d8;
  border-radius: 17px;
  height: 30px;
  line-height: 30px;
  margin: 10px 0;
  text-align: center;
  font-size: 14px;
  color: #777;
  padding: 0 10px;
  cursor: pointer;
  user-select: none;
  outline: none;
  margin-right: 10px;
  &:hover {
    background: #ecf5ff;
    border: 1px solid #99c4ff;
    border-radius: 17px;
    color: #5ba0ff;
  }
  &:active {
    background: #ecf5ff;
    border: 1px solid #1b7aff;
    border-radius: 17px;
    color: #1b7aff;
  }
}
