import {
  staticsAnnouncementCountByType,
  staticsAnnouncementCountByLastMonth,
  getQuickEntries,
  getAllEntries,
  modifyQuickEntries,
  canCreateAnnouncement,
} from '../services';

export default {
  namespace: 'index',
  state: {
    announcementCount: [],
    shortcut: {
      savedData: [],
      allData: [],
      tempSaveData: [],
      operation: false,
      height: 65,
    },
  },

  effects: {
    * getStaticsAnnouncementCountByType({ payload }, { call, put }) {
      const response = yield call(staticsAnnouncementCountByType, payload);
      yield put({
        type: 'setStaticsAnnouncementCountByType',
        payload: response,
      });
    },
    * getStaticsAnnouncementCountByLastMonth({ payload }, { call, put }) {
      const response = yield call(staticsAnnouncementCountByLastMonth, payload);
      yield put({
        type: 'setStaticsAnnouncementCountByLastMonth',
        payload: response,
      });
    },
    * setShortcut({ payload }, actoin) {
      yield actoin.put({
        type: 'updateShortcut',
        payload,
      });
    },
    * getQuickEntries({ payload }, { call, put }) {
      const response = yield call(getQuickEntries, payload);
      yield put({
        type: 'updateShortcut',
        payload: {
          savedData: response.result,
        },
      });
    },
    * getAllEntries({ payload }, { call, put }) {
      const response = yield call(getAllEntries, payload);
      yield put({
        type: 'updateShortcut',
        payload: {
          allData: response.result,
        },
      });
    },
    * modifyQuickEntries({ payload }, actoin) {
      const response = yield actoin.call(modifyQuickEntries, payload);
      return response;
    },
    * getCanCreateAnnouncement({ payload }, actoin) {
      const response = yield actoin.call(canCreateAnnouncement, payload);
      return response;
    },
  },

  reducers: {
    setStaticsAnnouncementCountByType(state, action) {
      return {
        ...state,
        announcementCount: action.payload.announcementCount,
      };
    },
    setStaticsAnnouncementCountByLastMonth(state, action) {
      return {
        ...state,
        announcementCount: action.payload.announcementCount,
      };
    },
    updateShortcut(state, action) {
      return {
        ...state,
        shortcut: {
          ...state.shortcut,
          ...action.payload,
        },
      };
    },
  },
};
