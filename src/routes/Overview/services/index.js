/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-11-22 11:20:12
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-09 17:43:52
 * @FilePath: /zcy-announcement-v2-front/src/routes/Overview/services/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from 'doraemon';

// 公告总览按列表统计接口
export async function staticsAnnouncementCountByType(data) {
  return request('/announcement/api/staticsAnnouncementCountByType', {
    method: 'POST',
    data,
  });
}

// 公告总览按列表统计接口
export async function staticsAnnouncementCountByLastMonth(params) {
  return request('/announcement/api/staticsAnnouncementCountByLastMonth', {
    method: 'GET',
    params,
  });
}

// 根据用户类别获取公告大类别
export async function obtainAnnBigTypeByUser(params) {
  return request('/announcement/api/obtainAnnBigTypeByUser', {
    method: 'GET',
    params,
  });
}

// 我的待办数量
export async function obtainMyBacklogCounts(params) {
  return request('/announcement/api/obtainMyBacklogCounts', {
    method: 'GET',
    params,
  });
}

// 获取公告总览页面的公告快速创建入口
export async function getQuickEntries(params) {
  return request('/announcement/api/getQuickEntries', {
    method: 'GET',
    params,
  });
}

// 获取所有的公告创建入口列表
export async function getAllEntries(params) {
  return request('/announcement/api/getAllEntries', {
    method: 'GET',
    params,
  });
}

// 设置公告总览页面的公告快速创建入口
export async function modifyQuickEntries(data) {
  return request('/announcement/api/modifyQuickEntries', {
    method: 'POST',
    data,
  });
}

// 用户是否有新增公告权限
export async function canCreateAnnouncement() {
  return request('/announcement/api/canCreateAnnouncement', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 获取采购意向&单一来源公示面包屑按钮
export async function getBreadcrumbButton(params) {
  return request('/announcement/api/buttonDisplay', {
    method: 'GET',
    params,
  });
}

// 获取中小企业面包屑按钮
export async function getSmallBreadcrumbButton() {
  return request('/announcement/api/small/barButtons', {
    method: 'GET',
  });
}

// 获取中小企业面包屑按钮
export async function getSmallListYear(params) {
  return request('/announcement/api/small/listYear', {
    method: 'GET',
    params,
  });
}
