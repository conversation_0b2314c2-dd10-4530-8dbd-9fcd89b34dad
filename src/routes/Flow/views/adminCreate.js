import React, { Component } from 'react';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import moment from 'moment';
import {
  Panel, Form, Input, Select, DatePicker, Radio,
  TreeSelect, TimePicker, Button, Icon, Popover,
  Modal, message, Steps, ZcyBreadcrumb, Spin, InputNumber,
} from 'doraemon';
import CustomFormGrid from 'components/FormGrid';
import {
  getNextTaskUsers, getEnvByDistrictAndType, getDownLoadUrl,
  submitAnnouncement, tempSaveAnnouncement, modifyAnnouncement,
  checkWorkdayByDate, obtainAnnouncementByProCode,
  checkSocialCreditCode,
} from '../services';
import FormTable from './components/form-table';
import FormTableMulti from './components/form-table-multi';
import FormLinkPurchase from './components/form-linkPurchase';
import FormUpload from './components/form-upload';
import Marking from './components/marking';
import PurchaseName from './components/purchase-name';
import SensitiveAlert from './components/sensitiveAlert';
import loadEditor from '@zcy/zcy-ueditor-front';
import pushLog from 'utils/pushLog';


import './index.less';
import { getSensitiveWords, attachSensitiveWordsTag, showSensitiveMsg, SensitiveEditorStyle, clearSensitiveTag, renderAlert } from 'src/utils/listSensitiveWords';


const FormItem = Form.Item;
const InputGroup = Input.Group;
const RadioGroup = Radio.Group;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};
const formItemLayoutForColSpan2 = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const { RangePicker } = DatePicker;

const allTable = [];
@connect(({ flow, openConfig }) => ({
  flow, openConfig,
}))
@Form.create()
export default class add extends Component {
  constructor(props) {
    super(props);
    this.orginkey = {};
    this.tableForm = {};
    this.routerInfo = {};// 路由参数信息
    this.nextTaskUserId = null; // 下级审核人Id
    this.dateCom = [];// 表单中type为2的且为日期控件的key
    this.state = {
      submiting: 1, // 提交状态，1:生成公告，2：暂存，3：提交
      preview: false, // 预览
      editable: false, // 预览页是否可编辑
      pubType: undefined, // 发布方式
      createModalShow: false, // 创建公告，选择审核人弹窗
      nextTaskUsers: {
        taskUser: [],
      }, // 下级审核人
      glggList: [], // 关联公告列表
      envs: {}, // 监管者信息
      attachmentsNeed: false, // 附件是否必填
      announcementType: undefined,
      markingList: [], // 当前公告标识列表
      isLinkPurchase: '0', // 是否关联采购计划
      config: {}, // 配置规则
      sensitiveVisible: false, // 敏感词信息弹窗
      sensitiveMsg: {}, // 敏感词信息
      correction: [], // 更正类型
      hasRemind: false,
      hasBlock: false,
      // 标题的禁止级敏感词
      sensitiveWords: [],
    };
  }
  componentDidMount() {
    this.clearStore();
    this.getRouterInfo();
    Promise.all([
      this.getTowTreeData(),
      this.getGpcatalog(),
      this.getAnnouncementType(),
      this.getPublishTypeConfig(),
    ]).then(() => {
      this.initForm();
    });
    this.getDistrictTree();
    this.getGpcatalog();
    this.initEditor();
    this.initGlggList();
    this.initMarking();
    // this.initForm();
  }
  // 获取下级审核人信息
  getNextTaskUser = () => {
    getNextTaskUsers({
      announcementType: this.routerInfo.announcementType,
      districtCode: this.routerInfo.districtId || undefined,
    }).then((res = {}) => {
      if (res.success) {
        if (res.result.existNextNode) {
          this.setState({
            nextTaskUsers: res.result,
          });
          this.nextTaskUserId = res.result.taskUser[0].userId;
        } else {
          Modal.error({
            title: '提示',
            content: '未配置下级审核人！',
            closable: false,
            onOk: () => {
              this.props.dispatch(routerRedux.goBack());
            },
          });
        }
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
          closable: false,
          onOk: () => {
            this.props.dispatch(routerRedux.goBack());
          },
        });
      }
    });
  }
  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (distCode) => {
    this.props.dispatch({
      type: 'flow/getIdentifications',
      payload: {
        distCode: distCode || this.routerInfo.districtId || window.currentUserDistrict.code,
        annType: this.routerInfo.announcementType,
        annId: this.id || 0,
      },
    }).then((res = {}) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }
  // 公告标识change
  onSelectMarking = (marking) => {
    const { markingList } = this.state;
    marking.isChoosed = !marking.isChoosed;
    const markingListHandel = markingList.filter(item => item.isChoosed && item.type === 2);
    if (markingListHandel.length > 4) {
      message.info('最多只能选取4个标识！');
    } else {
      this.setState({
        markingList: [...this.state.markingList],
      });
    }
  }
  // 获取监管信息
  initJgInfo = (districtCode) => {
    getEnvByDistrictAndType({
      districtCode,
      announcementType: this.routerInfo.announcementType,
    }).then((res) => {
      if (res.result) {
        this.props.form.resetFields(['regulatoryContactAddr', 'regulatoryContactFax', 'regulatoryContactPerson', 'regulatoryContactPhone', 'regulatoryOrgName']);
        setTimeout(() => {
          this.setState({
            envs: res.result.envs || {},
          });
        }, 100);
      } else {
        this.setState({
          envs: {},
        });
      }
    });
  }

  getPublishTypeConfig = (nextDistrictId) => {
    const { announcementType, districtId } = this.routerInfo;
    const districtCode = nextDistrictId || districtId || window.currentUserDistrict.code;
    this.props.dispatch({
      type: 'openConfig/getPublishTypeConfig',
      payload: {
        announcementType,
        districtCode,
      },
    }).then(({
      publishTypeDefaultValue,
    }) => {
      this.setState({
        pubType: publishTypeDefaultValue,
      });
    });
  }

  // 电子卖场采购公告 - 获取项目所在行政区划选项下拉数据
  fetchTreeDataIfPro = () => {
    this.props.dispatch({
      type: 'flow/getDistTreeFilterCorrespondingLevel',
    });
  }
  // 获取动态表单数据
  initForm = () => {
    const payload = {
      announcementType: this.routerInfo.announcementType,
      orderId: this.routerInfo.orderId,
      isRelationProject: false,
      projectCode: this.routerInfo.projectCode,
    };
    if (this.routerInfo.projectCode) {
      payload.isRelationProject = true;
    }
    if (this.routerInfo.isRelationProject) {
      payload.isRelationProject = this.routerInfo.isRelationProject;
    }
    // 如果是从项目创建的公告，需要带上appCode（固定值）
    if (this.routerInfo.projectCode) {
      payload.appCode = 'zcy.project.manage';
    }
    // 如果从项目采购创建的公告，appCode取自url
    if (this.routerInfo.isRelationProject) {
      payload.appCode = this.routerInfo.appCode;
    }
    payload.districtCode = this.routerInfo.districtId || undefined;
    this.props.dispatch({
      type: 'flow/getDynamicForm',
      payload,
    }).then((res1 = {}) => {
      this.setState({
        submiting: 0,
      });
      if (res1.success) {
        // 更正类型
        const correction = res1.result.find(att => att.key === 'correctType');
        if (correction && correction.value) {
          this.setState({
            correction: this.corrections(correction.value),
          });
        }
        const attachments = res1.result.find(att => att.key === 'attachments');
        if (attachments) {
          // 附件是否必填
          if (attachments.nullable === 2) {
            this.setState({
              attachmentsNeed: true,
            });
          }
          if (attachments.value) {
            const defaultFileList = JSON.parse(attachments.value);
            defaultFileList.forEach((item) => {
              item.isShow = true;
              item.size = parseInt(item.size, 10);
              item.status = 'done';
            });
            this.props.dispatch({
              type: 'flow/setFileList',
              payload: {
                fileInfo: {
                  fileList: [],
                  defaultFileList,
                },
              },
            });
          }
        }
        // 获取监管信息
        const districtCode = this.routerInfo.districtId || window.currentUserDistrict.code;
        this.initJgInfo(districtCode);
      } else {
        Modal.error({
          title: '获取模板信息失败',
          content: res1.error,
          closable: true,
          onOk: () => {
            this.props.dispatch(routerRedux.goBack());
          },
        });
      }
    });
  }
  // 更正类型
  corrections = (data) => {
    data && data.map((i) => {
      i.label = i.desc;
      i.value = i.desc;
    });
    return data;
  }
  // 如果从项目创建，存在项目编号，且未更正公告，则初始化原公告列表
  initGlggList = () => {
    const proCode = this.routerInfo.projectCode;
    if (proCode && this.routerInfo.announcementType === '3005') {
      obtainAnnouncementByProCode({
        proCode,
      }).then((res) => {
        if (res.success) {
          const list = res.result;
          for (let i = 0; i < list.length; i += 1) {
            list[i].value = list[i].outUrl;
            list[i].label = list[i].title;
          }
          this.setState({
            glggList: list,
          });
        }
      });
    }
  }
  // 清空缓存
  clearStore = () => {
    this.props.dispatch({
      type: 'flow/clearStore',
    });
  };
  // util
  GetQueryString = (name) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const searchName = unescape(this.props.location.search);
    const r = searchName && searchName.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
  }
  // 获取路由参数
  getRouterInfo = () => {
    this.routerInfo = {
      announcementTypeName: this.GetQueryString('announcementTypeName') || '',
      announcementType: this.GetQueryString('announcementType') || '',
      orderId: this.GetQueryString('orderId') || '',
      projectCode: this.GetQueryString('projectCode') || '',
      projectName: this.GetQueryString('projectName') || '',
      districtId: this.GetQueryString('districtId') || '',
      annBigType: this.GetQueryString('annBigType') || '',
      isRelationProject: this.GetQueryString('isRelationProject') || '',
      appCode: this.GetQueryString('appCode') || '',
      ids: this.GetQueryString('ids') || '',
      isDealAnn: this.GetQueryString('isDealAnn') || '',
      serialType: this.GetQueryString('serialType') || '',
    };
    this.setState({
      announcementType: this.routerInfo.announcementType,
    });
  }
  // 行政区划下拉列表
  getDistrictTree = () => {
    this.props.dispatch({
      type: 'flow/getDistTree',
    }).then(() => {
      this.props.form.setFieldsValue({
        district: this.routerInfo.districtId || window.currentUserDistrict.code,
      });
    });
  }
  // 加载采购区域、采购类型数据
  getTowTreeData = () => {
    return this.props.dispatch({
      type: 'flow/obtainCGLXANDCGFS',
      payload: {
        districtCode: this.routerInfo.districtId || window.currentUserDistrict.code,
        annTypeId: this.routerInfo.announcementType,
      },
    });
  }
  // 加载采购目录数据
  getGpcatalog = (code) => {
    if (code) {
      this.props.form.setFieldsValue({ gpCatalogName: undefined });
    }
    return this.props.dispatch({
      type: 'flow/getGpcatalog',
      payload: {
        districtCode: code || this.routerInfo.districtId || window.currentUserDistrict.code,
        year: new Date().getFullYear(),
      },
    });
  }
  // 获取配置信息
  getAnnouncementType = () => {
    this.props.dispatch({
      type: 'flow/getAnnouncementType',
      payload: {
        id: this.routerInfo.ids,
        typeId: this.routerInfo.announcementType,
      },
    }).then((data) => {
      const config = data?.result || {};
      this.setState({
        config,
        isLinkPurchase: `${config.isForcedControl}`,
      });
    });
  }
  // 返回按钮
  handelBack = () => {
    if (!this.state.preview) {
      Modal.confirm({
        title: '确认离开当前页面？',
        content: '如果返回，您更改的数据将会丢失',
        onOk: () => {
          this.props.dispatch(routerRedux.goBack());
        },
      });
    } else {
      this.setState({
        preview: false,
      });
    }
  }
  announcementCensor = () => {
    this.props.form.validateFields({ force: true }, (err, { title, district }) => {
      const content = this.ueditor.getContent();
      this.listSensitiveWords({
        title,
        content,
        district,
      }).then((isValid) => {
        if (isValid) return;
        this.submit(3);
      });
    });
  }
  listSensitiveWords = ({ title, content, district, type }) => {
    return getSensitiveWords({
      content: `${title}--${content}`,
      district,
      announcementType: this.routerInfo.announcementType,
    }).then((res) => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;
      const {
        contentWithSensitiveTag,
      } = attachSensitiveWordsTag(blockWords, remindWords, content);
      this.ueditor?.setContent(contentWithSensitiveTag);
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        hasRemind,
        // eslint-disable-next-line react/no-unused-state
        hasBlock,
      });
      if (type === 1) {
        return Promise.resolve(true);
      }
      if (hasBlock || hasRemind) {
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }
      return Promise.resolve(true);
    });
  }

 titleSensitiveWordsFn = (cb) => {
   const { form } = this.props;
   const { getFieldValue } = form;
   getSensitiveWords({
     announcementType: this.routerInfo.announcementType,
     content: getFieldValue('title'),
     district: getFieldValue('district'),
   }).then((res) => {
     if (!res) return;
     // eslint-disable-next-line react/no-unused-state
     this.setState({ sensitiveWords: res.blockWords }, () => {
       cb && cb();
     });
   });
 }

  submit = (type) => {
    if (!this.state.pubType) {
      message.error('请选择发布时间');
      return;
    }
    const { flow: { dynamicForm }, form } = this.props;
    form.validateFields({ force: true }, async (err, params) => {
      const { purchaseName = {} } = params;
      const values = {
        ...params,
        ...purchaseName,
      };
      function getNameByValue(data, matchCode) {
        let match = null;
        data.forEach((item) => {
          if (item.value === matchCode) {
            match = item.label;
          } else if (item.children) {
            match = getNameByValue(item.children, matchCode) || match;
          }
        });
        return match;
      }
      if (!err) {
        const fileList = [...this.props.flow.fileInfo.defaultFileList,
          ...this.props.flow.fileInfo.fileList];
        // 检测是否存在上传中的文件
        const uploadingFileNumber = fileList.filter(item => item.status === 'uploading').length;
        if (uploadingFileNumber !== 0) {
          Modal.error({
            title: '提示',
            content: `还有${uploadingFileNumber}个文件在上传中，请等待文件上传完毕！`,
          });
          return;
        }
        // 文件是必填项，则验证是否有已成功上传的文件
        const validFile = fileList.filter(item => item.status === 'done') || [];
        if (this.state.attachmentsNeed && validFile.length === 0) {
          Modal.error({
            title: '提示',
            content: '请上传文件！',
          });
          return;
        }
        const metaData = {
          ...values,
        };
        Object.keys(metaData).forEach((key) => {
          if (metaData[key] === undefined || metaData[key] === null) {
            metaData[key] = '';
          }
        });
        // 格式化表单数据
        dynamicForm.forEach((item) => {
          // 日期
          if (item.type === 2) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('YYYY-MM-DD');
            } else {
              metaData[item.key] = '';
            }
          }
          // 时间
          if ([3, 4, 13].includes(item.type)) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('HH:mm');
            } else {
              metaData[item.key] = '';
            }
          }
          // 日期时间
          if (item.type === 14) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('YYYY-MM-DD HH:mm:ss');
            } else {
              metaData[item.key] = '';
            }
          }
          // 时间区间
          if (item.type === 15) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('HH:mm')}-${moment(metaData[`${item.key}_end`]).format('HH:mm')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 日期区间
          if ([16].includes(item.type)) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('YYYY-MM-DD')}-${moment(metaData[`${item.key}_end`]).format('YYYY-MM-DD')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 日期时间区间
          if (item.type === 17) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('YYYY-MM-DD HH:mm:ss')}-${moment(metaData[`${item.key}_end`]).format('YYYY-MM-DD HH:mm:ss')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 时间段
          if ([18, 19].includes(item.type)) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('HH:mm')}-${moment(metaData[`${item.key}_end`]).format('HH:mm')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // tags select
          if (item.type === 11) {
            if (item.key === 'purchaseName') {
              metaData.purchases = values.purchases;
              metaData.otherPurchaseNames = values.otherPurchaseNames;
              metaData.purchaseName = values.purchaseName;
            } else if (metaData[item.key]) {
              metaData[item.key] = metaData[item.key].join('、');
            } else {
              metaData[item.key] = '';
            }
          }
        });
        // table数据处理
        allTable.forEach((table) => {
          metaData[table] = [];
        });
        Object.keys(this.props.flow.tableData).forEach((tbKey) => {
          const rows = [];
          this.props.flow.tableData[tbKey].forEach((tbRow) => {
            delete tbRow.formValue.undefined;
            // 设置空值，防止序列化后key值丢失
            Object.keys(tbRow.formValue).forEach((tk) => {
              if (tbRow.formValue[tk] === undefined) {
                tbRow.formValue[tk] = null;
              }
            });
            rows.push(tbRow.formValue);
          });
          metaData[tbKey] = rows;
        });
        // 单一来源公示，预算金额需要必填并大于0
        if (this.routerInfo.announcementType === '3012') {
          if (metaData.biddingProject.length === 0) {
            message.warning('请填写采购项目概况');
            return;
          }
          const isInvalid = metaData.biddingProject.some((item) => {
            return isNaN(item.budgetPrice) || item.budgetPrice <= 0;
          });
          if (isInvalid) {
            message.warning('采购项目概况 - 预算金额不能为空或小于0');
            return;
          }
        }
        // 采购方式、采购类型、采购目录获取label值（存在有初始值情况，必须手动递归获取）
        const { cgfs, cglx, cgml } = this.props.flow;
        if (metaData.procurementMethod) {
          metaData.procurementMethodCode = metaData.procurementMethod;
          metaData.procurementMethod = getNameByValue(cgfs, metaData.procurementMethodCode) || '';
        }
        if (metaData.procurementType) {
          metaData.procurementTypeCode = metaData.procurementType;
          metaData.procurementType = getNameByValue(cglx, metaData.procurementTypeCode) || '';
        }
        if (metaData.gpCatalogName) {
          metaData.gpCatalogCode = metaData.gpCatalogName;
          metaData.gpCatalogName = getNameByValue(cgml, metaData.gpCatalogCode) || '';
        }
        // 更正公告所选关联公告
        this.glgg = this.state.glggList.find(item => item.outUrl === metaData.relateOldNotice);
        this.setState({
          submiting: type,
        });
        // 是否加密字段
        /* eslint no-prototype-builtins: 0 */
        if (metaData.hasOwnProperty('isSecret')) {
          metaData.secret = metaData.isSecret === '1';
          delete metaData.isSecret;
        }
        // 关联项目编号，存在的话已逗号分隔
        const planNum = metaData.procurementPlanNumber;
        if (Array.isArray(planNum)) {
          metaData.procurementPlanNumber = planNum.join(',');
        }
        // 处理项目所在行政区划、编码
        if (metaData.disForPro) {
          metaData.districtCode = metaData.disForPro;
          metaData.districtName = this.disNameForPro;
          delete metaData.disForPro;
        }
        // 附件
        const attachments = [];
        validFile.forEach((fl) => {
          attachments.push({
            fileId: fl.fileId,
            isShow: !(fl.noPublic === true),
            name: fl.name,
            size: fl.size,
          });
        });
        // appCode回传
        let appCode;
        const appCodeItem = dynamicForm.find(item => item.key === 'appCode');
        if (appCodeItem) {
          appCode = appCodeItem.value;
        }
        // 公告标识
        const { markingList } = this.state;
        const selectMarking = markingList.filter(item => item.isChoosed && item.type === 2);
        const identificationIds = selectMarking.map(item => item.id);
        // 校验 社会信用代码
        const { error: errorMSG } = await checkSocialCreditCode({
          announcementType: this.routerInfo.announcementType,
          metadata: metaData,
        });
        if (errorMSG) {
          this.setState({
            submiting: 0,
          });
          return message.error(errorMSG);
        }
        // 公告预览
        if (type === 1) {
          this.props.dispatch({
            type: 'flow/getAnnouncementContent',
            payload: {
              announcementType: this.routerInfo.announcementType,
              metaData: JSON.stringify(metaData),
            },
          }).then((res) => {
            this.setState({
              submiting: 0,
            });
            if (res.result !== null) {
              this.setState({
                preview: true,
                editable: false,
              });
              if (this.glgg) {
                res.result = res.result.replace(this.glgg.outUrl, `<a href="${this.glgg.outUrl}" target= "_blank">${this.glgg.title}</a>`);
              }
              this.listSensitiveWords({
                title: values.title,
                content: res.result,
                district: metaData.district || metaData.districtCode,
                type,
              });
              this.ueditor.setDisabled('fullscreen');
              // 生成公告即暂存
              // 修复table预览无边框样式
              let content = res.result;
              const fixTableStyle = '<style id="fixTableStyle" type="text/css">table {border-spacing:0}th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
              const hasFixed = content?.indexOf('fixTableStyle');
              if (hasFixed === -1) {
                content = fixTableStyle + content;
              }
              // 公告暂存
              tempSaveAnnouncement({
                id: this.id,
                announcementType: this.routerInfo.announcementType,
                attachments,
                content,
                description: '',
                district: values.district,
                title: values.title,
                metaData: JSON.stringify(metaData),
                projectCode: this.routerInfo.projectCode || values.projectCode || '',
                projectName: this.routerInfo.projectName || values.projectName || '',
                pubType: this.state.pubType,
                releasedAt: values.releasedAt || '',
                serialNum: this.routerInfo.orderId || '',
                serialType: this.routerInfo.serialType || '',
                showDuration: (this.showDuration === null || this.showDuration === undefined) ? '' : this.showDuration,
                url: '',
                appCode,
                identificationIds,
              }).then((res1) => {
                this.setState({
                  submiting: 0,
                });
                if (res1.success) {
                  this.id = res1.result;
                } else {
                  Modal.error({
                    title: '提示',
                    content: res1.error,
                  });
                }
              });
            } else {
              message.error('参数错误');
            }
          });
        }
        // 修复table预览无边框样式
        let content = this.ueditor.getContent();
        const fixTableStyle = '<style id="fixTableStyle" type="text/css">table {border-spacing:0}th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
        const hasFixed = content?.indexOf('fixTableStyle');
        if (hasFixed === -1) {
          content = fixTableStyle + content;
        }
        // 公告暂存-手动
        if (type === 2) {
          this.setState({
            submiting: 0,
          });
          this.listSensitiveWords({
            title: values.title,
            content,
            district: metaData.district,
        
          }).then((isValid) => {
            if (!isValid) return;
            this.setState({
              submiting: 2,
            });
            tempSaveAnnouncement({
              id: this.id,
              announcementType: this.routerInfo.announcementType,
              attachments,
              content: clearSensitiveTag(content),
              description: '',
              district: values.district,
              title: values.title,
              metaData: JSON.stringify(metaData),
              projectCode: this.routerInfo.projectCode || values.projectCode || '',
              projectName: this.routerInfo.projectName || values.projectName || '',
              pubType: this.state.pubType,
              releasedAt: values.releasedAt || '',
              serialNum: this.routerInfo.orderId || '',
              serialType: this.routerInfo.serialType || '',
              showDuration: (this.showDuration === null || this.showDuration === undefined) ? '' : this.showDuration,
              url: '',
              appCode,
              identificationIds,
            }).then((res) => {
              this.setState({
                submiting: 0,
              });
              if (res.success) {
                this.id = res.result;
                Modal.success({
                  title: '提示',
                  content: '暂存成功！',
                });
              } else {
                Modal.error({
                  title: '提示',
                  content: res.error,
                });
              }
            });
          });
        }
        // 公告提交
        if (type === 3) {
          const json = {
            id: this.id,
            announcementType: this.routerInfo.announcementType,
            attachments,
            content,
            description: '',
            district: values.district,
            title: values.title,
            metaData: JSON.stringify(metaData),
            projectCode: this.routerInfo.projectCode || values.projectCode || '',
            projectName: this.routerInfo.projectName || values.projectName || '',
            pubType: this.state.pubType,
            releasedAt: values.releasedAt || '',
            serialNum: this.routerInfo.orderId || '',
            serialType: this.routerInfo.serialType || '',
            showDuration: (this.showDuration === null || this.showDuration === undefined) ? '' : this.showDuration,
            url: '',
            nextTaskUserId: this.nextTaskUserId,
            appCode,
            identificationIds,
            ifManualAnnouncement: !this.routerInfo.projectCode,
          };
          const tasks = [];
          const needShowFiles = attachments.filter(fl => fl.isShow);
          let html = '';
          if (needShowFiles.length > 0) {
            html = '<divider></divider><p style="white-space: normal; line-height: 2em;font-size: 16px;font-family: MicrosoftYaHei;"><strong><span>附件信息:</span></strong></p>';
            needShowFiles.forEach((fl) => {
              tasks.push(getDownLoadUrl({
                fileId: fl.fileId,
                bizCode: '1014',
              }).then((res) => {
                fl.url = res.result;
              }));
            });
            // 获取文件url地址，拼接模板
            Promise.all(tasks).then(() => {
              html += needShowFiles.map((fl) => {
                let fileSize = '';
                if (fl.size) {
                  fl.size = parseInt(fl.size, 10);
                  fileSize = '0.1 KB';
                  const fileSizeK = (fl.size / 1024).toFixed(1);
                  if (fileSizeK.split('.')[1] > 0) {
                    fileSize = `${fileSizeK} KB`;
                  }
                  if (fileSizeK.split('.')[0] > 1000) {
                    const fileSizeM = (fl.size / 1024 / 1024).toFixed(1);
                    fileSize = `${fileSizeM} M`;
                  }
                }
                return `<ul class="fjxx" style="font-size: 16px;text-indent: 2em;color: #0065ef;list-style-type: none;font-family: MicrosoftYaHei;line-height: 2em;"><li><p style="display:inline-block;text-indent: 0;"><a href="${fl.url}">${fl.name}</a></p><p style="display:inline-block;margin-left:20px">${fileSize}</p></li></ul>`;
              }).join('');
              json.content += html;
              this.submitAnn(json);
            });
          } else {
            this.submitAnn(json);
          }
        }
      } else {
        message.warning('请完善信息！');
      }
    });
  }
  // type = 3 提交公告，判断是否存在id
  submitAnn = (json = {}) => {
    if (json.content) {
      json.content = clearSensitiveTag(json.content);
    }
    if (json.id) {
      modifyAnnouncement(json).then((res) => {
        this.setState({
          submiting: 0,
        });
        if (res.success) {
          Modal.success({
            title: '提示',
            content: '提交成功！',
            onOk: () => {
              this.props.dispatch(
                routerRedux.push(
                  `/manage/adminList/${this.routerInfo.annBigType}`
                )
              );
            },
          });
        } else {
          Modal.error({
            title: '提示',
            content: res.error,
          });
        }
      });
    } else {
      submitAnnouncement(json).then((res) => {
        this.setState({
          submiting: 0,
        });
        if (res.success) {
          Modal.success({
            title: '提示',
            content: '提交成功！',
            onOk: () => {
              this.props.dispatch(
                routerRedux.push(
                  `/manage/adminList/${this.routerInfo.annBigType}`
                )
              );
            },
          });
        } else {
          Modal.error({
            title: '提示',
            content: res.error,
          });
        }
      });
    }
  }
  // 验证是否非工作日
  checkIsWorkday = (date, key) => {
    // 招标文件发售开始/结束日期
    if (['saleStartDate', 'saleEndDate'].indexOf(key) !== -1) {
      const strDate = moment(date).format('YYYY-MM-DD HH:mm:ss');
      checkWorkdayByDate({
        strDate,
        districtCode: this.routerInfo.districtId || window.currentUserDistrict.code,
      }).then((res = {}) => {
        if (!res.success) {
          Modal.confirm({
            title: '您选择的是非工作日',
            content: '是否重新选择日期？',
            onOk: () => {
              this.props.form.resetFields([key]);
            },
          });
        }
      });
    }
  }
  // 监听采购项目编号change事件
  handelXmbhChange = (e) => {
    const proCode = e.target.value;
    if (proCode) {
      this.props.form.resetFields(['glygg']);
      this.glgg = null;
      obtainAnnouncementByProCode({
        proCode,
      }).then((res) => {
        if (res.success) {
          const list = res.result;
          for (let i = 0; i < list.length; i += 1) {
            list[i].value = list[i].outUrl;
            list[i].label = list[i].title;
          }
          this.setState({
            glggList: list,
          });
        }
      });
    }
  }

  // 树菜单change
  onTreeChange = (key) => {
    if (key === 'disForPro') {
      return this.onDisForProChange;
    }
    if (key === 'relateOldNotice') {
      return this.onRelateOldNoticeChange;
    }
    return undefined;
  }
  onRelateOldNoticeChange = (value) => {
    const { glggList } = this.state;
    const selected = glggList.find(glgg => glgg.outUrl === value);
    this.props.form.setFieldsValue({
      oldNoticePubDate: selected ? selected.releasedAt : undefined,
    });
  }
  onDisForProChange = (value, label) => {
    if (Array.isArray(label)) {
      this.disNameForPro = label[0];
    }
  }
  // 生成动态表单
  renderDynamicForm = () => {
    const {
      form: { getFieldDecorator, getFieldsValue },
      flow: { dynamicForm, disDataForPro, cgfs, cglx, cgml },
    } = this.props;
    const { announcementType, config, glggList } = this.state;
    const formNodes = [];
    // 非政府采购公告下所有公告，采购方式、采购目录、采购类型、监管信息均设置为非必填
    const isGov = this.routerInfo.annBigType === '9';
    // 政府采购监管公告下所有公告，采购方式、采购目录、采购类型、项目名称、项目编号、采购人名称不显示
    const TYPR_7 = this.routerInfo.annBigType === '7';
    const purchaseNameInitValue = {};
    for (let i = 0; i < dynamicForm.length; i += 1) {
      if (dynamicForm[i].key === 'purchaseName') {
        purchaseNameInitValue.purchaseName = dynamicForm[i].value || '';
      }
      if (dynamicForm[i].key === 'otherPurchaseNames') {
        purchaseNameInitValue.otherPurchaseNames = dynamicForm[i].value || '';
      }
      if (dynamicForm[i].key === 'purchases') {
        purchaseNameInitValue.purchases = JSON.parse(dynamicForm[i].value) || [];
      }
    }
    dynamicForm.forEach((item) => {
      if (TYPR_7) {
        if (['procurementMethod', 'procurementType', 'gpCatalogName', 'purchaseName', 'correctType'].includes(item.key)) {
          return;
        }
      }
      if (item.key === 'showDuration') {
        this.showDuration = item.value;
      }
      // 校验规则
      let formRules = [];
      if (item.nullable === 2) {
        formRules.push({ required: true, message: '不能为空！' });
        if (item.key === 'purchaseName') {
          formRules.push({
            validator: (rule, value, callback) => {
              const { purchaseName = '' } = value || {};
              if (!purchaseName) {
                callback(new Error('不能为空！'));
              }
              callback();
            },
          });
        }
      }
      /* eslint no-case-declarations: 0 */
      /* eslint no-inner-declarations: 0 */
      switch (item.type) {
      // 采购方式、采购类型、采购目录，展示name，默认值取code
      case 1:
        // 下拉框数据
        let treeData = [];
        // 默认值
        let defaultValue;
        if (item.key === 'procurementMethod') {
          const find = dynamicForm.find(i => i.key === 'procurementMethodCode');
          defaultValue = find ? find.value : undefined;
          treeData = cgfs;
        }
        if (item.key === 'procurementType') {
          const find = dynamicForm.find(i => i.key === 'procurementTypeCode');
          defaultValue = find ? find.value : undefined;
          treeData = cglx;
        }
        if (item.key === 'gpCatalogName') {
          const find = dynamicForm.find(i => i.key === 'gpCatalogCode');
          defaultValue = find ? find.value : undefined;
          treeData = cgml;
        }
        // 项目所在行政区划编码
        if (item.key === 'disForPro') {
          treeData = disDataForPro;
          defaultValue = undefined;
        }
        if (item.key === 'relateOldNotice') {
          treeData = glggList;
          defaultValue = undefined;
        }
        // 更正类型
        if (item.key === 'correctType') {
          treeData = this.state.correction;
          defaultValue = undefined;
        }
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                {
                  required: item.key === 'relateOldNotice'
                    ? item.nullable === 2 : !isGov,
                  message: '请选择！',
                },
              ],
              initialValue: defaultValue || undefined,
            })(
              <TreeSelect
                treeNodeFilterProp="label"
                treeData={treeData}
                allowClear
                showSearch
                placeholder="请选择"
                searchPlaceholder="输入关键字搜索"
                onChange={this.onTreeChange(item.key)}
              />
            )}
          </FormItem>
        );
        break;
        // 日期选择控件，如【2018-08-24】
      case 2:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <DatePicker
                onChange={
                  (date) => {
                    this.checkIsWorkday(date, item.key);
                  }
                }
              />
            )}
          </FormItem>
        );
        break;
        // 上午时间选择，如【10:00】，限定可选上午
      case 3:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout} >
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <TimePicker
                format="HH:mm"
                placeholder="请选择"
                className="times"
              />
            )}
          </FormItem>
        );
        break;
        // 下午时间选择，如【14:00】,限定可选下午
      case 4:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout} >
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <TimePicker
                format="HH:mm"
                placeholder="请选择"
                className="times"
                // onChange={(st) => {
                //   if (parseInt(st.format('HH'), 10) < 12) {
                //     message.error('请选择上午时间');
                //     setTimeout(() => {
                //       this.props.form.resetFields([`${item.key}`]);
                //     }, 0);
                //   }
                // }}
              />
            )}
          </FormItem>
        );
        break;
        // 数值
      case 5:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value,
            })(
              <InputNumber
                placeholder="请输入数值"
              />
            )}
          </FormItem>
        );
        break;
        // 单行文本输入框
      case 6:
        let disabled = false;
        let initialValue = item.value;
        let jginfo = '';
        // 监管信息
        if (this.state.envs[item.key]) {
          disabled = true;
          jginfo = this.state.envs[item.key];
        }
        if (['regulatoryContactAddr', 'regulatoryContactFax', 'regulatoryContactPerson', 'regulatoryContactPhone', 'regulatoryOrgName'].includes(item.key)) {
          initialValue = jginfo;
          if (isGov) {
            formRules = [];// 监管信息非必填
          }
        }
        // 更正公告3005，原公告信息根据项目编码联动
        const onGzggChange = (e) => {
          if (item.key === 'projectCode' && this.routerInfo.announcementType === '3005') {
            const proCode = e.target.value;
            if (proCode) {
              this.props.form.resetFields(['oldNotice']);
              this.glgg = null;
              obtainAnnouncementByProCode({
                proCode,
              }).then((res) => {
                if (res.success) {
                  const list = res.result;
                  for (let i = 0; i < list.length; i += 1) {
                    list[i].value = list[i].outUrl;
                    list[i].label = list[i].title;
                  }
                  this.setState({
                    glggList: list,
                  });
                }
              });
            }
          }
        };
        // 项目名称、项目编号,从接口中带出，有值则禁用
        if (item.key === 'projectName') {
          initialValue = this.routerInfo.projectName || item.value;
          if (initialValue) {
            disabled = true;
          }
        }
        if (item.key === 'projectCode') {
          initialValue = this.routerInfo.projectCode || item.value;
          if (initialValue) {
            disabled = true;
          }
        }
        // 统一社会信用代码 字段,增加校验
        if (item.key === 'socialCreditCode') {
          formRules.push({
            validator: async (rule, value, callback) => {
              if (!value) return;
              const { error: errorMSG } = await checkSocialCreditCode({
                socialCreditCode: value,
                announcementType,
              });
              if (errorMSG) {
                callback(new Error(errorMSG));
              }
              callback();
            },
          });
        }
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue,
            })(
              <Input
                disabled={disabled}
                placeholder="请输入"
                onChange={onGzggChange}
              />
            )}
          </FormItem>
        );
        break;
        // 多行文本输入框
      case 7:
        formNodes.push(
          <FormItem colSpan={2} key={item.key} label={`${item.name}：`} {...formItemLayoutForColSpan2} className="create-textarea">
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value,
            })(<TextArea placeholder="请输入" rows={2} maxLength={3000} />)}
          </FormItem>
        );
        break;
        // Table表格面板输入
      case 10:
        allTable.push(item.key);
        formNodes.push(
          <FormTable
            colSpan={2}
            key={item.key}
            tableInfo={item}
            announcementType={announcementType}
          />
        );
        break;
        // Table表格面板输入 - 表格合并（暂时固定适配项目保证金信息）
      case 22:
        allTable.push(item.key);
        formNodes.push(
          <FormTableMulti colSpan={2} key={item.key} tableInfo={item} />
        );
        break;
        // 文本tag自由输入
      case 11:
        if (item.key === 'purchaseName') {
          // 采购人名称特殊处理
          const {
            district,
          } = getFieldsValue(['district']);
          const noteLabel = (
            <Popover content={(
              <div style={{ width: '200px' }}>
                若采购单位不在平台上，可选择自定义后在下方输入框中输入单位名称。
              </div>
            )}
            >
              <span style={{ cursor: 'pointer' }}>
                {`${item.name}`} &nbsp;
                <Icon style={{ marginRight: 5, color: '#999' }} type="question-circle-o" />
              </span>
            </Popover>
          );
          formNodes.push(
            <FormItem
              label={noteLabel}
              key={item.key}
              {...formItemLayout}
            >
              {getFieldDecorator(item.key, {
                rules: [
                  ...formRules,
                ],
                initialValue: purchaseNameInitValue,
              })(
                <PurchaseName distCode={district} />
              )}
            </FormItem>
          );
        } else {
          formNodes.push(
            <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
              {getFieldDecorator(item.key, {
                rules: [
                  ...formRules,
                ],
                initialValue: item.value ? item.value.split(',') : [],
              })(
                <Select
                  mode="tags"
                  placeholder="请输入"
                  notFoundContent="请输入"
                />
              )}
            </FormItem>
          );
        }
        break;
        // 附件上传
        // case 12:
        //   // 附件固定字段uploadAttachment
        //   break;
        // 时间选择，如【11：00】
      case 13:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout} >
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <TimePicker
                format="HH:mm"
                placeholder="请选择"
                className="times"
              />
            )}
          </FormItem>
        );
        break;
        // 日期时间选择，如【2018-08-24 10:34:42】
      case 14:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                onChange={
                  (date) => {
                    this.checkIsWorkday(date, item.key);
                  }
                }
              />
            )}
          </FormItem>
        );
        break;
        // 时间区间选择控件，如【10:00 - 11:00】
      case 15:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            <InputGroup compact>
              {getFieldDecorator(`${item.key}_start`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="开始时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(st) => {
                    const et = this.props.form.getFieldValue(`${item.key}_end`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_start`]);
                      }, 0);
                    }
                  }}
                />
              )}
              {getFieldDecorator(`${item.key}_end`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="结束时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(et) => {
                    const st = this.props.form.getFieldValue(`${item.key}_start`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_end`]);
                      }, 0);
                    }
                  }}
                />
              )}
            </InputGroup>
          </FormItem>
        );
        break;
        // 日期区间选择控件，如【2018-08-24 - 2018-08-24】
      case 16:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <RangePicker />
            )}
          </FormItem>
        );
        break;
        // 日期时间区间选择控件，如【2018-08-24 10:40:25 - 2018-08-24 10:40:28】
      case 17:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            {getFieldDecorator(item.key, {
              rules: [
                ...formRules,
              ],
              initialValue: item.value ? moment(item.value) : undefined,
            })(
              <RangePicker
                showTime={{
                  hideDisabledOptions: true,
                  defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('00:00:00', 'HH:mm:ss')],
                }}
                format="YYYY-MM-DD HH:mm:ss"
              />
            )}
          </FormItem>
        );
        break;
        // 上午时间区间选择控件，如【40:25 - 10:40】
      case 18:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            <InputGroup compact>
              {getFieldDecorator(`${item.key}_start`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="开始时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(st) => {
                    // if (parseInt(st.format('HH'), 10) >= 12) {
                    //   message.error('请选择上午时间');
                    //   setTimeout(() => {
                    //     this.props.form.resetFields([`${item.key}_start`]);
                    //   }, 0);
                    //   return;
                    // }
                    const et = this.props.form.getFieldValue(`${item.key}_end`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_start`]);
                      }, 0);
                    }
                  }}
                />
              )}
              {getFieldDecorator(`${item.key}_end`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="结束时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(et) => {
                    // if (parseInt(et.format('HH'), 10) >= 12) {
                    //   message.error('请选择上午时间');
                    //   setTimeout(() => {
                    //     this.props.form.resetFields([`${item.key}_end`]);
                    //   }, 0);
                    //   return;
                    // }
                    const st = this.props.form.getFieldValue(`${item.key}_start`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_end`]);
                      }, 0);
                    }
                  }}
                />
              )}
            </InputGroup>
          </FormItem>
        );
        break;
        // 下午日期时间区间选择控件，如【14:40 - 14:40】
      case 19:
        formNodes.push(
          <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
            <InputGroup compact>
              {getFieldDecorator(`${item.key}_start`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="开始时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(st) => {
                    // if (parseInt(st.format('HH'), 10) < 12) {
                    //   message.error('请选择下午时间');
                    //   setTimeout(() => {
                    //     this.props.form.resetFields([`${item.key}_start`]);
                    //   }, 0);
                    //   return;
                    // }
                    const et = this.props.form.getFieldValue(`${item.key}_end`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_start`]);
                      }, 0);
                    }
                  }}
                />
              )}
              {getFieldDecorator(`${item.key}_end`)(
                <TimePicker
                  format="HH:mm"
                  placeholder="结束时间"
                  className="times"
                  style={{
                    width: '50%',
                  }}
                  onChange={(et) => {
                    // if (parseInt(et.format('HH'), 10) < 12) {
                    //   message.error('请选择下午时间');
                    //   setTimeout(() => {
                    //     this.props.form.resetFields([`${item.key}_end`]);
                    //   }, 0);
                    //   return;
                    // }
                    const st = this.props.form.getFieldValue(`${item.key}_start`);
                    if (st && et && st > et) {
                      message.error('开始时间不能大于结束时间');
                      setTimeout(() => {
                        this.props.form.resetFields([`${item.key}_end`]);
                      }, 0);
                    }
                  }}
                />
              )}
            </InputGroup>
          </FormItem>
        );
        break;
      case 99:
        // 单一来源与允许采购进口产品公示中增加“是否涉密”字段，默认选择“否”，选择“是”则不会同步至外网仅存于系统内部
        const noteLabel = (
          <Popover content="选择涉密将不对外网展示">
            <span style={{ cursor: 'pointer' }}>
              {`${item.name}`} &nbsp;
              <Icon style={{ marginRight: 5, color: '#999' }} type="question-circle-o" />
            </span>
          </Popover>
        );
        if (item.key === 'isSecret') {
          formNodes.push(
            <FormItem key={item.key} label={noteLabel} {...formItemLayout}>
              {getFieldDecorator(item.key, {
                rules: [
                  ...formRules,
                ],
                initialValue: item.value === true ? '1' : '0',
              })(
                <RadioGroup>
                  <Radio value="0">否</Radio>
                  <Radio value="1">是</Radio>
                </RadioGroup>
              )}
            </FormItem>
          );
        }
        if (item.key === 'isBindProcurementPlan') {
          formNodes.push(
            <FormItem key={item.key} label={`${item.name}：`} {...formItemLayout}>
              {getFieldDecorator('procurementPlanNumber', {
                rules: [
                  { required: this.state.isLinkPurchase === '1', message: '不能为空！' },
                ],
                initialValue: [],
              })(
                <FormLinkPurchase
                  isForcedControl={config.isForcedControl}
                  onLinkChange={(isLinkPurchase) => {
                    this.props.form.resetFields(['procurementPlanNumber']);
                    this.setState({
                      isLinkPurchase,
                    });
                  }}
                />
              )}
            </FormItem>
          );
        }
        break;
      default:
        break;
      }
    });
    return formNodes;
  }
  // 选择审核人弹窗
  createModalOk = () => {
    this.setState({
      createModalShow: false,
    });
    this.submit(3);
  }
  // 选择审核人弹窗
  createModalCancel = () => {
    this.setState({
      createModalShow: false,
    });
  }
  getTitlePlaceholder = (placeholder) => {
    const orgName = window.currentUserIdentity.orgName || '';// 采购代理机构
    const projectName = this.routerInfo.projectName || '';// 项目名称
    const announcementTypeName = this.routerInfo.announcementTypeName;
    let noteText = '';
    if (placeholder) {
      noteText = '请输入标题，例：';
    }

    return `${noteText}${orgName}${projectName ? `关于${projectName}的` : ''}${announcementTypeName}`;
  }
  componentWillUnmount() {
    try {
      window.UE?.delEditor('ueditor', {
        autoHeight: false,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  initEditor = () => {
    try {
      loadEditor().then((res) => {
        if (res) {
          this.ueditor = window.UE.getEditor('ueditor', {
            allowDivTransToP: false,
            // 注入敏感词提示css
            initialStyle: SensitiveEditorStyle,
          });
        }
        if (!res) return pushLog('初始化富文本编辑失败', 'warning');
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  clearPurchaseName = () => {
    this.props.form.setFieldsValue({
      purchaseName: {},
    });
  }
  render() {
    const { form, flow: { districtTree }, openConfig } = this.props;
    const { publishTypeOptions = [] } = openConfig;
    const { markingList, sensitiveVisible, sensitiveMsg,
      hasRemind, hasBlock, sensitiveWords, config } = this.state;
    const { getFieldDecorator } = form;
    const { attachmentShow } = config ?? {};
    let globalBtn = [];
    if (!this.state.preview) {
      globalBtn = [{
        label: '返回',
        onClick: this.handelBack,
      }, {
        label: '生成公告',
        type: 'primary',
        loading: this.state.submiting === 1 || districtTree.length === 0,
        onClick: () => {
          this.titleSensitiveWordsFn(() => this.submit(1));
        },
      }];
    } else {
      globalBtn = [{
        label: '返回',
        onClick: () => {
          this.setState({
            preview: false,
          });
        },
      }, {
        label: '暂存',
        type: 'primary',
        loading: this.state.submiting === 2,
        onClick: () => {
          this.titleSensitiveWordsFn(() => this.submit(2));
        },
      }, {
        label: '提交',
        type: 'primary',
        loading: this.state.submiting === 3,
        onClick: () => {
          this.titleSensitiveWordsFn(() => this.announcementCensor());
        },
      }];
    }
    return (
      <div className="announcement-flow">
        <ZcyBreadcrumb
          routes={[{
            label: this.routerInfo.announcementTypeName,
          }, {
            label: '创建',
          }]
          }
          globalBtn={globalBtn}
        />
        <Panel
          title="公告"
          className="flowHead"
        >
          <Steps
            current={0}
          >
            <Steps.Step
              title="创建公告"
              description={
                (
                  <span>
                    当前步：创建公告<br />
                    下一步：公告审核
                  </span>
                )
              }
            />
            <Steps.Step
              title="公告审核"
            />
            <Steps.Step
              title="公告发布"
            />
          </Steps>
        </Panel>
        <Spin spinning={this.state.submiting !== 0}>
          <div style={{ margin: '20px 0' }}>
            <Panel
              className="editorPanel"
              title={
                (
                  <div>
                    <span style={{ lineHeight: '35px' }}>公告内容</span>
                    <Button
                      className={this.state.preview ? 'show' : 'hide'}
                      style={{ float: 'right' }}
                      type="primary"
                      onClick={
                        () => {
                          const editable = this.state.editable;
                          try {
                            if (editable) {
                              window.UE.getEditor('ueditor').setDisabled('fullscreen');
                            } else {
                              window.UE.getEditor('ueditor').setEnabled();
                            }
                          } catch (error) {
                            // eslint-disable-next-line no-console
                            console.log(error);
                          }
                          this.setState({
                            editable: !editable,
                          });
                        }
                      }
                    >
                      {this.state.editable ? '预览' : '编辑'}
                    </Button>
                  </div>
                )
              }
            >
              {
                renderAlert(hasBlock, hasRemind)
              }
              <Form onSubmit={this.handleSubmit}>
                <div className="formgrid">
                  <CustomFormGrid>
                    <FormItem label="标题：" colSpan={2} {...formItemLayoutForColSpan2}>
                      {getFieldDecorator('title', {
                        rules: [{
                          required: true, message: '不能为空！',
                        }, {
                          max: 200, message: '不能超过200字符！',
                        }, {
                          validator: (rule, value, callback) => {
                            const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
                              if (value.includes(cur.emitWord)) {
                                arr.push(cur.emitWord);
                              }
                              return arr;
                            }, []);
                            if (titleSensitiveWords.length) {
                              callback(new Error(`请修改敏感词：${titleSensitiveWords.join(',')}`));
                              return;
                            }
                            callback();
                          },
                        }],
                        initialValue: this.routerInfo.projectCode ?
                          this.getTitlePlaceholder() : undefined,
                      })(
                        <Input
                          disabled={this.state.preview && !this.state.editable}
                          placeholder={this.getTitlePlaceholder(true)}
                        />
                      )}
                    </FormItem>
                    <FormItem label="行政区划：" {...formItemLayout}>
                      {getFieldDecorator('district', {
                        rules: [
                          { required: true, message: '请选择！' },
                        ],
                      })(
                        <TreeSelect
                          disabled={(this.state.preview
                            || this.routerInfo.isRelationProject !== null)
                            || districtTree.length === 0}
                          treeData={districtTree}
                          allowClear
                          treeNodeFilterProp="title"
                          showSearch
                          onSelect={
                            (value) => {
                              this.getGpcatalog(value);
                              this.initJgInfo(value);
                              this.initMarking(value);
                              this.clearPurchaseName();
                              this.getPublishTypeConfig(value);
                              this.setState({
                                pubType: undefined,
                              });
                            }
                          }
                          placeholder="请选择"
                          searchPlaceholder="输入关键字搜索"
                        />
                      )}
                    </FormItem>
                    <FormItem label="发布时间：" {...formItemLayout}>
                      <InputGroup compact className={this.state.pubType === 1 ? 'hastime timeCom' : 'timeCom'}>
                        <Select
                          disabled={this.state.preview && !this.state.editable}
                          defaultValue={this.state.pubType}
                          onChange={(pubType) => {
                            this.props.form.resetFields(['releasedAt']);
                            this.setState({
                              pubType,
                            });
                          }}
                        >
                          {publishTypeOptions.map(ele => <Option value={+ele.v}>{ele.k}</Option>)}
                        </Select>
                        {getFieldDecorator('releasedAt', {
                          rules: [
                            { required: this.state.pubType === 1, message: '不能为空！' },
                          ],
                        })(
                          <DatePicker
                            disabled={
                              this.state.pubType !== 1 ||
                              (this.state.preview && !this.state.editable)
                            }
                            disabledDate={
                              (current) => {
                                return current <
                                  new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
                              }
                            }
                            showTime={{
                              format: 'HH:mm',
                            }}
                            format="YYYY-MM-DD HH:mm"
                            placeholder={this.state.pubType === 1 ? '请选择发布时间' : ''}
                          />
                        )}
                      </InputGroup>
                    </FormItem>
                    {this.state.preview ?
                      (
                        <div id="formCtt" style={{ display: 'none' }}>
                          {this.renderDynamicForm()}
                        </div>
                      ) : this.renderDynamicForm()}
                  </CustomFormGrid>
                </div>
                <div id="ueditor" style={{ clear: 'both' }} className={this.state.preview ? 'show' : 'hide'} />
              </Form>
            </Panel>
          </div>
          {
            attachmentShow ? (
              <FormUpload
                required={this.state.attachmentsNeed}
                editable={!this.state.preview || this.state.editable}
                announcementType={this.routerInfo.announcementType}
              />
            ) : null
          }
         
          <Marking
            markingList={markingList}
            onChange={this.onSelectMarking}
            disabled={this.state.preview && !this.state.editable}
          />
        </Spin>
        <Modal
          title="确认提交"
          visible={this.state.createModalShow}
          onOk={this.createModalOk}
          onCancel={this.createModalCancel}
        >
          <Form layout="horizontal">
            <FormItem label="下一环节：" {...formItemLayout2}>
              {this.state.nextTaskUsers.taskName}
            </FormItem>
            <FormItem label="执行人：" {...formItemLayout2}>
              <Select
                style={{ width: '100%' }}
                placeholder="请选择"
                getPopupContainer={triggerNode => triggerNode.parentNode}
                defaultValue={
                  (this.state.nextTaskUsers.taskUser &&
                    this.state.nextTaskUsers.taskUser.length) > 0
                    ? this.state.nextTaskUsers.taskUser[0].userId : undefined}
                onChange={(value) => {
                  this.nextTaskUserId = value;
                }}
              >
                {
                  this.state.nextTaskUsers.taskUser.map(item => (
                    <Option
                      value={item.userId}
                      key={item.userId}
                    >
                      {item.userName}
                    </Option>
                  ))
                }
              </Select>
            </FormItem>
          </Form>
        </Modal>
        <SensitiveAlert
          visible={sensitiveVisible}
          sensitiveMsg={sensitiveMsg}
          onOk={() => {
            this.setState({
              sensitiveVisible: false,
            });
            this.setState({
              createModalShow: true,
            });
          }}
          onCancel={() => {
            this.setState({
              sensitiveVisible: false,
            });
          }}
        />
      </div>
    );
  }
}
