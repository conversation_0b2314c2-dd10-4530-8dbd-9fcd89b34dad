/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-10-29 16:06:02
 * @FilePath: /zcy-announcement-v2-front/src/routes/Flow/views/adminEdit.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import {
  Panel, Form, Input, Select, DatePicker,
  TreeSelect, Steps, ZcyBreadcrumb, message, Modal,
  Spin,
} from 'doraemon';
import CustomFormGrid from 'components/FormGrid';
import {
  obtainAnnouncementDetail, getTreeNameByCode, getDownLoadUrl,
} from '../services';
import FormUpload from './components/form-upload';
import SensitiveAlert from './components/sensitiveAlert';
import Marking from './components/marking';
import './index.less';
import loadEditor from '@zcy/zcy-ueditor-front';
import pushLog from 'utils/pushLog';
import { getSensitiveWords, attachSensitiveWordsTag, showSensitiveMsg, SensitiveEditorStyle, clearSensitiveTag, renderAlert } from 'src/utils/listSensitiveWords';

const FormItem = Form.Item;
const InputGroup = Input.Group;
const { Option } = Select;
const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};
const formItemLayoutForColSpan2 = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const annBigTypeName = ['意见征询或公示', '采购项目公告', '更正公告', '采购结果公告', '采购合同公告', '履约验收公告', '政府采购监管公告', '电子卖场公告', '非政府采购公告'];

@connect(({ flow, loading, openConfig }) => ({
  flow,
  loading: loading.models.flow,
  openConfig,
}))
@Form.create()
export default class Edit extends Component {
  constructor(props) {
    super(props);
    this.districtCode = null;
    this.state = {
      annBigType: this.props.match.params.annBigType,
      districtTree: [],
      detail: {
        content: '',
      },
      createModalShow: false,
      nextTaskUsers: {
        taskUser: [],
      }, // 下级审核人
      sensitiveVisible: false,
      sensitiveMsg: '',
      markingList: [],
      hasRemind: false,
      hasBlock: false,
      // 标题的禁止级敏感词
      sensitiveWords: [],
    };
  }
  componentDidMount() {
    // 清空file
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo: {
          fileList: [],
          defaultFileList: [],
        },
      },
    });
    obtainAnnouncementDetail({
      announcementId: this.props.match.params.announcementId,
    }).then((res) => {
      // 去除之前生成模板的文件信息
      if (res.result.content.indexOf('<p style="font-size" class="fjxx">附件信息：</p>') !== -1) {
        res.result.content = res.result.content.split('<p style="font-size" class="fjxx">附件信息：</p>')[0];
      }
      if (res.result.content.indexOf("<p style='font-size' class='fjxx'>附件信息：</p>") !== -1) {
        res.result.content = res.result.content.split("<p style='font-size' class='fjxx'>附件信息：</p>")[0];
      }
      if (res.result.content.indexOf('<divider></divider>') !== -1) {
        res.result.content = res.result.content.split('<divider></divider>')[0];
      }
      try {
        loadEditor().then((ue) => {
          if (ue) {
            window.UE.getEditor('ueditor', {
              // 注入敏感词提示css
              initialStyle: SensitiveEditorStyle,
            }).ready(() => {
              this.ueditor = window.UE.getEditor('ueditor');
              this.listSensitiveWords({
                title: res.result.title,
                district: res.result.district,
                content: res.result.content,
                type: 1,
              });
            });
          }
          if (!ue) return pushLog('初始化富文本编辑失败', 'warning');
        });
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error);
      }
      // file
      const defaultFileList = res.result.attachments || [];
      defaultFileList.forEach((item) => {
        item.status = 'done';
        item.noPublic = !item.isShow;
      });
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo: {
            fileList: [],
            defaultFileList,
          },
        },
      });
      this.props.dispatch({
        type: 'openConfig/getPublishTypeConfig',
        payload: {
          announcementType: res.result.announcementType,
          districtCode: res.result.district,
        },
      });
      this.setState({
        detail: res.result,
      });
      getTreeNameByCode({
        code: res.result.district,
      }).then((res1) => {
        this.districtCode = res1.data.code;
        this.setState({
          districtName: res1.data.name,
        });
      });
      this.initMarking({
        distCode: res.result.district,
        annType: res.result.announcementType,
        annId: res.result.id,
      });
    });
  }
  componentWillUnmount() {
    try {
      window.UE?.delEditor('ueditor');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (payload) => {
    this.props.dispatch({
      type: 'flow/getIdentifications',
      payload,
    }).then((res) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }
  // 公告标识change
  onSelectMarking = (marking) => {
    const { markingList } = this.state;
    marking.isChoosed = !marking.isChoosed;
    const markingListHandel = markingList.filter(item => item.isChoosed && item.type === 2);
    if (markingListHandel.length > 4) {
      message.info('最多只能选取4个标识！');
    } else {
      this.setState({
        markingList: [...this.state.markingList],
      });
    }
  }
  
  // 选择审核人弹窗
  createModalOk = () => {
    this.submit(2);
  }
  // 选择审核人弹窗
  createModalCancel = () => {
    this.setState({
      createModalShow: false,
    });
  }

  listSensitiveWords = ({ title, content, district, type }) => {
    return getSensitiveWords({
      content: `${title}--${content}`,
      district,
      announcementType: this.state.detail.announcementType,
    }).then((res) => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;
      const {
        contentWithSensitiveTag,
      } = attachSensitiveWordsTag(blockWords, remindWords, content);
      this.ueditor?.setContent(contentWithSensitiveTag);
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        hasRemind,
        // eslint-disable-next-line react/no-unused-state
        hasBlock,
      });
      if (type === 1) {
        return Promise.resolve(true);
      }
      if (hasBlock || hasRemind) {
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }
      return Promise.resolve(true);
    });
  }

  submit = (type) => {
    // 修复table预览无边框样式
    let content = this.ueditor.getContent();
    const fixTableStyle = '<style id="fixTableStyle" type="text/css">th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
    const hasFixed = content?.indexOf('fixTableStyle');
    if (hasFixed === -1) {
      content = fixTableStyle + content;
    }
    // 公告标识
    const { markingList } = this.state;
    const selectMarking = markingList.filter(item => item.isChoosed && item.type === 2);
    const identificationIds = selectMarking.map(item => item.id);
    if (type === 1) {
      this.props.form.validateFields({ force: true }, (err, values) => {
        // 是否需要上传文件
        const fileList = [...this.props.flow.fileInfo.defaultFileList,
          ...this.props.flow.fileInfo.fileList];
        const validFile = fileList.filter(item => item.status === 'done') || [];
        if (!err) {
          const data = { ...values };
          // 处理区划初始值
          if (this.districtCode !== null) {
            data.district = this.districtCode;
          }
          // 处理文件
          const attachments = [];
          validFile.forEach((fl) => {
            attachments.push({
              fileId: fl.fileId,
              isShow: !(fl.noPublic === true),
              name: fl.name,
              size: fl.size,
            });
          });
          this.listSensitiveWords({
            title: values.title,
            district: data.district,
            content,
          }).then((isValid) => {
            if (!isValid) return;
            this.props.dispatch({
              type: 'flow/tempSaveAnnouncement',
              payload: {
                announcementType: this.state.detail.announcementType,
                attachments,
                content: clearSensitiveTag(content),
                district: data.district,
                id: this.state.detail.id,
                projectCode: this.state.detail.projectCode,
                projectName: this.state.detail.projectName,
                pubType: this.state.detail.pubType,
                releasedAt: this.state.detail.pubType !== 1 ? '' : data.releasedAt,
                showDuration: this.state.detail.showDuration,
                status: this.state.detail.status,
                title: data.title,
                identificationIds,
              },
            }).then((res) => {
              if (res.success) {
                this.setState({
                  createModalShow: false,
                });
                message.success('暂存成功！');
              } else {
                Modal.error({
                  title: '提示',
                  content: res.error,
                });
              }
            });
          });
        } else {
          message.warning('请完善信息！');
        }
      });
    } else {
      this.props.form.validateFields({ force: true }, (err, values) => {
        if (!err) {
          const data = { ...values };
          // 处理区划初始值
          if (this.districtCode !== null) {
            data.district = this.districtCode;
          }
          // 是否需要上传文件
          const fileList = [...this.props.flow.fileInfo.defaultFileList,
            ...this.props.flow.fileInfo.fileList];
          const validFile = fileList.filter(item => item.status === 'done') || [];
          // 处理文件
          const attachments = [];
          const tasks = [];
          validFile.forEach((fl) => {
            attachments.push({
              fileId: fl.fileId,
              isShow: !(fl.noPublic === true),
              name: fl.name,
              size: fl.size,
            });
          });
          const needShowFiles = attachments.filter(fl => fl.isShow);
          let html = '';
          needShowFiles.forEach((fl) => {
            tasks.push(getDownLoadUrl({
              fileId: fl.fileId,
              bizCode: '1014',
            }).then((res) => {
              fl.url = res.result;
            }));
          });
          // 获取文件url地址，拼接模板
          Promise.all(tasks).then(() => {
            if (needShowFiles.length > 0) {
              html = '<divider></divider><p style="white-space: normal; line-height: 2em;font-size: 16px;font-family: MicrosoftYaHei;"><strong><span>附件信息:</span></strong></p>';
              html += needShowFiles.map((fl) => {
                let fileSize = '0.1 KB';
                let fileHtml = '';
                if (fl.size) {
                  fl.size = parseInt(fl.size, 10);
                  const fileSizeK = (fl.size / 1024).toFixed(1);
                  if (fileSizeK.split('.')[1] > 0) {
                    fileSize = `${fileSizeK} KB`;
                  }
                  if (fileSizeK.split('.')[0] > 1000) {
                    const fileSizeM = (fl.size / 1024 / 1024).toFixed(1);
                    fileSize = `${fileSizeM} M`;
                  }
                  fileHtml = `<p style="display:inline-block;margin-left:20px">${fileSize}</p>`;
                }
                return `<ul class="fjxx" style="font-size: 16px;text-indent: 2em;color: #0065ef;list-style-type: none;font-family: MicrosoftYaHei;line-height: 2em;"><li><p style="display:inline-block;text-indent: 0;"><a href="${fl.url}">${fl.name}</a></p>${fileHtml}</li></ul>`;
              }).join('');
            }
            this.props.dispatch({
              type: 'flow/modifyAnnouncement',
              payload: {
                announcementType: this.state.detail.announcementType,
                attachments,
                content: clearSensitiveTag(content + html),
                district: data.district,
                id: this.state.detail.id,
                projectCode: this.state.detail.projectCode,
                projectName: this.state.detail.projectName,
                pubType: this.state.detail.pubType,
                releasedAt: this.state.detail.pubType !== 1 ? '' : data.releasedAt,
                showDuration: this.state.detail.showDuration,
                title: data.title,
                nextTaskUserId: this.nextTaskUserId,
                identificationIds,
              },
            }).then((res) => {
              if (res.success) {
                message.success('提交成功！');
                this.props.dispatch(
                  routerRedux.replace(
                    `/manage/adminList/${this.state.annBigType}`
                  )
                );
              } else {
                Modal.error({
                  title: '提示',
                  content: res.error,
                });
              }
            });
          });
        }
      });
    }
  }

  // 暂存按钮click
  onTempSave = () => {
    // 是否需要上传文件
    const fileList = [...this.props.flow.fileInfo.defaultFileList,
      ...this.props.flow.fileInfo.fileList];
    const validFile = fileList.filter(item => item.status === 'done') || [];
    if (this.state.detail.hasAttachment) {
      if (validFile.length === 0) {
        Modal.error({
          title: '提示',
          content: '请上传文件！',
        });
        return;
      }
    }
    return this.handleTitleSensitiveWords(() => this.submit(1));
  }

  handleTitleSensitiveWords = (cb) => {
    const { getFieldValue } = this.props.form;
    getSensitiveWords({
      announcementType: this.state.detail.announcementType,
      content: getFieldValue('title'),
      district: getFieldValue('district'),
    }).then((res) => {
      if (!res) return;
      // eslint-disable-next-line react/no-unused-state
      this.setState({ sensitiveWords: res.blockWords }, () => {
        cb && cb();
      });
    });
  } 

  onSubmit = () => {
    const onSubmitAfterCheck = () => this.props.form.validateFields({ force: true }, 
      (err, values) => {
        if (!err) {
        // 是否需要上传文件
          const fileList = [...this.props.flow.fileInfo.defaultFileList,
            ...this.props.flow.fileInfo.fileList];
          const validFile = fileList.filter(item => item.status === 'done') || [];
          if (this.state.detail.hasAttachment) {
            if (validFile.length === 0) {
              Modal.error({
                title: '提示',
                content: '请上传文件！',
              });
              return;
            }
          }
          // const needShowFiles = validFile.filter(item => item.noPublic !== true);
          // const fileHtml = needShowFiles.map(item => `<p>${item.name}</p>`).join('');
          this.listSensitiveWords({
            title: values.title,
            district: this.districtCode,
            content: this.ueditor.getContent(),
          }).then((isValid) => {
            if (isValid) return;
            this.submit(2);
          });
        } else {
          message.warning('请完善信息！');
        }
      });
    this.handleTitleSensitiveWords(onSubmitAfterCheck);
  }

  render() {
    const { detail, districtTree, annBigType, sensitiveVisible, sensitiveMsg,
      markingList, hasRemind, hasBlock, sensitiveWords } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { loading, openConfig } = this.props;
    const { publishTypeOptions = [] } = openConfig;
    const { attachmentShow } = detail ?? {};
    const breadcrumb = [{
      label: annBigTypeName[parseInt(annBigType, 10) - 1],
    }, {
      label: '编辑公告',
    }];
    const globalBtn = [{
      label: '返回',
      onClick: () => {
        this.props.dispatch(
          routerRedux.push(
            `/manage/adminList/${this.state.annBigType}`
          )
        );
      },
    }, {
      label: '暂存',
      type: 'primary',
      onClick: this.onTempSave,
    }, {
      label: '提交',
      type: 'primary',
      onClick: this.onSubmit,
    }];
    return (
      <Spin spinning={loading}>
        <div className="announcement-flow">
          <ZcyBreadcrumb
            routes={breadcrumb}
            globalBtn={globalBtn}
          />
          <Panel
            title="流程"
            className="flowHead"
          >
            <Steps
              current={0}
            >
              <Steps.Step
                title="创建公告"
                description={
                  (
                    <span>
                      当前步：创建公告<br />
                      下一步：公告审核
                    </span>
                  )
                }
              />
              <Steps.Step
                title="公告审核"
              />
              <Steps.Step
                title="公告发布"
              />
            </Steps>
          </Panel>
          <Panel
            title="公告内容"
          >
            {
              renderAlert(hasBlock, hasRemind)
            }
            <Form>
              <div className="formgrid">
                <CustomFormGrid>
                  <FormItem label="标题：" colSpan={2} {...formItemLayoutForColSpan2}>
                    {getFieldDecorator('title', {
                      rules: [{
                        required: true, message: '不能为空！',
                      }, {
                        max: 200, message: '不能超过200字符！',
                      }, {
                        validator: (rule, value, callback) => {
                          const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
                            if (value.includes(cur.emitWord)) {
                              arr.push(cur.emitWord);
                            }
                            return arr;
                          }, []);
                          if (titleSensitiveWords.length) {
                            callback(new Error(`请修改敏感词：${titleSensitiveWords.join(',')}`));
                            return;
                          }
                          callback();
                        },
                      }],
                      initialValue: detail.title,
                    })(
                      <Input placeholder="请输入标题" />
                    )}
                  </FormItem>
                  <FormItem label="行政区划：" {...formItemLayout}>
                    {getFieldDecorator('district', {
                      rules: [
                        { required: true, message: '不能为空！' },
                      ],
                      initialValue: this.state.districtName,
                    })(
                      <TreeSelect
                        disabled
                        treeData={districtTree}
                        allowClear
                        showSearch
                        onSelect={
                          () => {
                            this.districtCode = null;
                          }
                        }
                        placeholder="请选择"
                        searchPlaceholder="输入关键字搜索"
                      />
                    )}
                  </FormItem>
                  <FormItem label="发布时间：" {...formItemLayout}>
                    <InputGroup compact className={this.state.detail.pubType === 1 ? 'hastime timeCom' : 'timeCom'}>
                      <Select
                        value={detail.pubType}
                        onChange={(pubType) => {
                          this.state.detail.pubType = pubType;
                          this.props.form.resetFields(['releasedAt']);
                          this.setState({
                            detail: this.state.detail,
                          });
                        }}
                      >
                        {publishTypeOptions.map(ele => <Option value={+ele.v}>{ele.k}</Option>)}
                      </Select>
                      {getFieldDecorator('releasedAt', {
                        rules: [
                          { required: detail.pubType === 1, message: '不能为空！' },
                        ],
                        initialValue: detail.pubType === 1 ? detail.releasedAt : undefined,
                      })(
                        <DatePicker
                          disabled={detail.pubType !== 1}
                          showTime={{
                            format: 'HH:mm',
                          }}
                          disabledDate={
                            (current) => {
                              return current <
                                new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
                            }
                          }
                          format="YYYY-MM-DD HH:mm"
                          placeholder={this.state.pubType === 1 ? '请选择发布时间' : ''}
                        />
                      )}
                    </InputGroup>
                  </FormItem>
                </CustomFormGrid>
              </div>
              <div id="ueditor" style={{ clear: 'both' }} />
            </Form>
          </Panel>
          {
            attachmentShow ? (
              <FormUpload
                required={detail.hasAttachment}
                editable
                announcementType={this.state.detail?.announcementType}
              />
            ) : null
          }
          <Modal
            title="确认提交"
            visible={this.state.createModalShow}
            onOk={this.createModalOk}
            onCancel={this.createModalCancel}
            confirmLoading={loading}
          >
            <Form layout="horizontal">
              <FormItem label="下一环节：" {...formItemLayout2}>
                {this.state.nextTaskUsers.taskName}
              </FormItem>
              <FormItem label="执行人：" {...formItemLayout2}>
                <Select
                  style={{ width: '100%' }}
                  placeholder="请选择"
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  defaultValue={
                    this.state.nextTaskUsers.taskUser.length > 0
                      ? this.state.nextTaskUsers.taskUser[0].userId : undefined}
                  onChange={(value) => {
                    this.nextTaskUserId = value;
                  }}
                >
                  {
                    this.state.nextTaskUsers.taskUser.map(item => (
                      <Option value={item.userId} key={item.userId}>{item.userName}</Option>
                    ))
                  }
                </Select>
              </FormItem>
            </Form>
          </Modal>
          <SensitiveAlert
            visible={sensitiveVisible}
            sensitiveMsg={sensitiveMsg}
            onOk={() => {
              this.setState({
                sensitiveVisible: false,
              });
              this.submit(2);
            }}
            onCancel={() => {
              this.setState({
                sensitiveVisible: false,
              });
            }}
          />
          <Marking
            markingList={markingList}
            onChange={this.onSelectMarking}
          />
        </div>
      </Spin>
    );
  }
}
