import React, { Component } from 'react';
import { Input, Select } from 'doraemon';

const { Option } = Select;

const yearOpts = [];
const yearMap = {};
for (let i = 50; i >= 0; i -= 1) {
  yearMap[`${2050 - i}`] = `${2050 - i}年`;
  yearOpts.push(`${2050 - i}`);
}
const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

export default class TitleInput extends Component {
  componentDidMount() {
    this.doInit();
  }
  doInit = () => {
    const { value } = this.props;
    if (!value) {
      const orgName = window.currentUserIdentity.orgName;
      const year = new Date().getFullYear();
      const month = new Date().getMonth() + 1;
      setTimeout(() => {
        this.props.form.setFieldsValue({
          title: `${orgName}${year}年${month}月政府采购意向`,
          openingTimeOfIntention: `${year}年${month}月`,
          deadlineForIntention2Disclose: `${year}年${month}月`,
          purStartAndEndDate: `${year}年${month}月`,
        });
      }, 100);
    }
  }
  onChange = (values, v, index) => {
    const { onChange } = this.props;
    values[index] = v;
    if (values[3] === values[5]) {
      values.splice(5, 1);
      values.splice(4, 1);
    }
    onChange(values.join(''), values);
  }
  onInputChange = (values, v) => {
    const { onChange } = this.props;
    values[0] = v.target.value;
    if (values[3] === values[5]) {
      values.splice(5, 1);
      values.splice(4, 1);
    }
    onChange(values.join(''), values);
  }
  getValues = () => {
    let { value } = this.props;
    const values = [
      undefined,
      undefined,
      '年',
      undefined,
      '月至',
      undefined,
      '月政府采购意向',
    ];
    let lastIndex;
    // 滨江区采购人单位2020年6月（至）7月政府采购意向
    if (value && typeof value === 'string' && value.indexOf('（至）') !== -1) {
      lastIndex = value.lastIndexOf('月政府采购意向');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6月（至）7
      lastIndex = value.lastIndexOf('）');
      if (lastIndex === -1) {
        return values;
      }
      values[5] = value.substr(lastIndex + 1); // 7
      lastIndex = value.lastIndexOf('月（至）');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6
      lastIndex = value.lastIndexOf('年');
      if (lastIndex === -1) {
        return values;
      }
      values[3] = value.substr(lastIndex + 1); // 6
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020
      values[1] = value.substr(-4);
      if (!yearMap[values[1]]) {
        values[1] = undefined;
        values[0] = value;
      } else {
        values[0] = value.substr(0, value.length - 4);
      }
      return values;
    }
    // 滨江区采购人单位2020年6月政府采购意向
    if (value && typeof value === 'string' && value.indexOf('（至）') === -1) {
      lastIndex = value.lastIndexOf('月政府采购意向');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6
      lastIndex = value.lastIndexOf('年');
      if (lastIndex === -1) {
        return values;
      }
      values[5] = value.substr(lastIndex + 1); // 6
      values[3] = values[5]; // 6
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020
      values[1] = value.substr(-4);
      if (!yearMap[values[1]]) {
        values[1] = undefined;
        values[0] = value;
      } else {
        values[0] = value.substr(0, value.length - 4);
      }
      return values;
    }
    return values;
  }
  getValue = () => {
    let { value } = this.props;
    const values = [
      undefined,
      undefined,
      '年',
      undefined,
      '月至',
      undefined,
      '月政府采购意向',
    ];
    let lastIndex;
    // 滨江区采购人单位2020年6月（至）7月政府采购意向
    if (value && typeof value === 'string' && value.indexOf('月至') !== -1) {
      lastIndex = value.lastIndexOf('月政府采购意向');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6月（至）7
      lastIndex = value.lastIndexOf('至');
      if (lastIndex === -1) {
        return values;
      }
      values[5] = value.substr(lastIndex + 1); // 7
      lastIndex = value.lastIndexOf('月至');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6
      lastIndex = value.lastIndexOf('年');
      if (lastIndex === -1) {
        return values;
      }
      values[3] = value.substr(lastIndex + 1); // 6
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020
      values[1] = value.substr(-4);
      if (!yearMap[values[1]]) {
        values[1] = undefined;
        values[0] = value;
      } else {
        values[0] = value.substr(0, value.length - 4);
      }
      return values;
    }
    // 滨江区采购人单位2020年6月政府采购意向
    if (value && typeof value === 'string' && value.indexOf('月至') === -1) {
      lastIndex = value.lastIndexOf('月政府采购意向');
      if (lastIndex === -1) {
        return values;
      }
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020年6
      lastIndex = value.lastIndexOf('年');
      if (lastIndex === -1) {
        return values;
      }
      values[5] = value.substr(lastIndex + 1); // 6
      values[3] = values[5]; // 6
      value = value.substr(0, lastIndex); // 滨江区采购人单位2020
      values[1] = value.substr(-4);
      if (!yearMap[values[1]]) {
        values[1] = undefined;
        values[0] = value;
      } else {
        values[0] = value.substr(0, value.length - 4);
      }
      return values;
    }
    return values;
  }
  render() {
    const { disabled, value } = this.props;
    const values = (value && value.includes('（至）')) ? this.getValues() : this.getValue();
    return (
      <span style={{ width: '100%' }}>
        <Input
          disabled={disabled}
          value={values[0] || undefined}
          style={{ width: '200px' }}
          placeholder="单位名称"
          onChange={(v) => {
            this.onInputChange(values, v);
          }}
        />&nbsp;
        <Select
          disabled={disabled}
          value={values[1] || undefined}
          style={{ width: '100px' }}
          placeholder="年份"
          onChange={(v) => {
            this.onChange(values, v, 1);
          }}
        >
          {yearOpts.map(year => (
            <Option key={year} value={year}>{year}</Option>
          ))}
        </Select>
        &nbsp;年&nbsp;
        <Select
          disabled={disabled}
          value={values[3] || undefined}
          style={{ width: '100px' }}
          placeholder="开始月份"
          onChange={(v) => {
            this.onChange(values, v, 3);
          }}
        >
          {months.map(month => (
            <Option key={month} value={`${month}`}>{month}</Option>
          ))}
        </Select>
        月至
        <Select
          disabled={disabled}
          value={values[5] || undefined}
          style={{ width: '100px' }}
          placeholder="结束月份"
          onChange={(v) => {
            this.onChange(values, v, 5);
          }}
        >
          {months.map(month => (
            <Option key={month} value={`${month}`}>{month}</Option>
          ))}
        </Select>
        &nbsp;
        月政府采购意向
      </span>
    );
  }
}
