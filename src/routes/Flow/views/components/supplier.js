import React, { Component } from 'react';
import {
  Select,
  request,
  message,
} from 'doraemon';

const { Option } = Select;

export default class Supplier extends Component {
  state = {
    options: [],
  };
  componentDidMount() {
    const { distCode } = this.props;
    this.fetchData('/announcement/api/searchSupplierList', {
      distCode,
      pageSize: 10,
    });
  }
  componentWillReceiveProps(nextProps) {
    const { distCode } = nextProps;
    if (distCode !== this.props.distCode) {
      this.fetchData('/announcement/api/searchSupplierList', {
        distCode,
        pageSize: 10,
      });
    }
  }
  fetchData = (url, params) => {
    if (url) {
      request(url, {
        params,
      }).then((data) => {
        let options = data.result;
        if (!options) {
          options = [];
        }
        this.setState({
          options,
        });
      });
    }
  }
  onSearch=(name) => {
    const { distCode } = this.props;
    this.fetchData('/announcement/api/searchSupplierList', {
      distCode,
      name,
      pageSize: 10,
    });
  }
  onChange=(v) => {
    const { onChange, value = {}, distCode } = this.props;
    const {
      otherPurchaseNames: oName = '',
    } = value;
    if (!distCode) {
      message.info('请先选择行政区划！');
      return;
    }
    // 定义值变量
    const otherPurchaseNames = oName ? oName.split(',') : [];
    const purchases = [];
    let purchaseName = [];

    let temp;
    let hasOther = false;
    for (let i = 0; i < v.length; i += 1) {
    //   console.log('v[i].key', v[i].key.toString());
      temp = v[i].key.toString().split('-');
      if (v[i].key === '-1') {
        // 有自定义时，名称不录入
        temp = ['-1', '-1'];
        hasOther = true;
      } else {
        // 非自定义选项，名称录入
        purchaseName.push(v[i].label);
      }
      // 选中值push进来
      purchases.push({
        orgId: temp[0],
        orgName: v[i].label,
      });
    }
    // 有自定义值时purchaseName取自定义值和选中值得名称，无自定义值时只取选中值名称。
    purchaseName = hasOther ? [...otherPurchaseNames, ...purchaseName] : [...purchaseName];
    purchaseName = purchaseName.join(',');
    const nValue = {
      purchases,
      otherPurchaseNames: hasOther ? oName : '', // 有自定义时取原值，没有时为空
      purchaseName,
    };
    onChange(nValue);
  }
  onInputChange=(otherPurchaseNames) => {
    const { onChange, value = {} } = this.props;
    const { purchases = [] } = value;
    const purchaseName = [...otherPurchaseNames];
    // 输入自定义名称是更新purchaseName值
    for (let i = 0; i < purchases.length; i += 1) {
      if (purchases[i].orgId !== '-1') {
        purchaseName.push(purchases[i].orgName);
      }
    }
    const nValue = {
      purchases,
      otherPurchaseNames: otherPurchaseNames.join(','), // 自定义名称直接做值
      purchaseName: purchaseName.join(','),
    };
    onChange(nValue);
  }
  render() {
    const { distCode, disabled, value = {} } = this.props;
    const { options } = this.state;
    const {
      purchases = [],
    //   otherPurchaseNames = '',
    } = value;
    let hasOther = false;
    const selValue = [];
    for (let i = 0; i < purchases.length; i += 1) {
      if (`${purchases[i].orgId}` === '-1') {
        hasOther = true;
        selValue.push({
          key: '-1',
          label: purchases[i].orgName,
        });
      } else {
        selValue.push({
          key: `${purchases[i].orgId}-${purchases[i].budgetCode}`,
          label: purchases[i].orgName,
        });
      }
    }
    // const inputValue = otherPurchaseNames ? otherPurchaseNames.split(',') : [];
    return (
      <span style={{ width: '100%' }}>
        <Select
          mode="multiple"
          labelInValue
          placeholder="请选择"
          notFoundContent="暂无选项"
          onSearch={this.onSearch}
          filterOption={false}
          key={`Select${distCode}`}
          // value={selValue}
          onChange={this.onChange}
          onBlur={() => this.onSearch('')}
          style={{ width: '100%' }}
          disabled={disabled}
        >
          <Option value="-1">自定义</Option>
          {options.map(d => (
            <Option
              value={d.supplierId}
              key={d.supplierId}
            >
              {d.supplierName}
            </Option>
          ))}
        </Select>
        <br />
        {hasOther ? (
          <Select
            mode="tags"
            placeholder="请输入"
            notFoundContent="请选择"
            key={`SelectTags${distCode}`}
            // value={inputValue}
            onChange={this.onInputChange}
            style={{ width: '100%' }}
            disabled={disabled}
          />
        ) : ''}
      </span>
    );
  }
}
