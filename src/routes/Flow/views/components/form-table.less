.form-table {

  .table-title {
    padding: 13px 16px;
    border: 1px solid #eee;
    margin-top: 10px;

    span.title {
      font-size: 14px;
      font-weight: bold;
    }

    button,.upload {
      float: right;
    }
  }

  .table-input {
    width: 100%;
  }
}

.multi-input-content {
  width: 900px;

  .item-title {
    .doraemon-col-5 {
      color: #6e6e6e;
    }
  }

  input {
    width: 150px
  }

  .bank-item {
    margin: 10px 0;
  }
}

.fileList {
  li {
    .inline {
      display: inline-block;
      background-color: #f7f7f7;
      padding: 10px;
      border-radius: 5px;
      margin-top: 5px;
      transition: background-color .3s ease-in-out;

      &:hover {
        background-color: #f5f6fa;

        .file-del {
          opacity: 1;
        }
      }

      .file-name {
        margin-left: 10px;
        margin-right: 20px;
      }

      .file-del {
        opacity: 0;
        margin-left: 20px;
        transition: opacity .3s ease-in-out;
      }
    }

    .upload-error {
      font-size: 12px;
      color: #ff3100;
    }
  }

  .doraemon-progress {
    margin-right: -30px;
  }
}

.subTitle {
  font-size: 14px;
  color: #24344e;
  margin: 30px 0;
  padding-left: 10px;
  position: relative;

  &:before {
    content: '';
    height: 14px;
    display: inline-block;
    width: 3px;
    background: #3177fd;
    position: absolute;
    left: 0;
    top: 3px;
  }
}

th {
  white-space:nowrap;
}

th.table-required {
  &:before {
    content: '*';
    color: #f00;
    margin-right: 4px;
    float: left;
  }
}

.upload-link {
  margin: 0 6px;
  font-size: 14px;
  display: inline-block;
  position: absolute;
  width: 6em;
  top: 8px;
}
