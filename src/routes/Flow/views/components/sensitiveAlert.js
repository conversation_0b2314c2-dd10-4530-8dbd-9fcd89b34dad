import React, { Component } from 'react';
import {
  Modal,
} from 'doraemon';

class sensitiveAlert extends Component {
  render() {
    const { sensitiveMsg, onOk, onCancel } = this.props;
    return (
      <Modal
        {...this.props}
        className="sensitive-alert-modal"
        title="提示"
        onOk={onOk}
        onCancel={onCancel}
        okText="继续提交"
        cancelText="返回修改"
      >
        {sensitiveMsg}
      </Modal>
    );
  }
}

export default sensitiveAlert;
