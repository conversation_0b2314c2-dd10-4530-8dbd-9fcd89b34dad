import React, { Component } from 'react';
import { connect } from 'dva';
import debounce from 'lodash/debounce';
import moment from 'moment';
import {
  Button,
  Table,
  Input,
  Popconfirm,
  Select,
  InputNumber,
  DatePicker,
  Popover,
  Icon,
  Upload,
  Modal,
  message,
} from 'doraemon';
import _ from 'lodash';
import pushLog from 'utils/pushLog';
import { guid } from '../../../../utils/utils';
import { checkSocialCreditCode } from '../../services';
import SelectItem from '../../../../components/SelectItem';
import './form-table.less';

const { MonthPicker } = DatePicker;
const { Option } = Select;

@connect(({ flow }) => ({
  flow,
}))
class FormTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      columns: [],
      data: [],
      key: '',
      name: '',
    };
    // 防抖处理，不然频繁更新state导致输入会很卡
    this.updateState = debounce(this.doUpdateState, 300);
  }
  doUpdateState = () => {
    this.props.dispatch({
      type: 'flow/setTableData',
      payload: {
        tableData: {
          ...this.props.flow.tableData,
          [this.state.key]: this.state.data,
        },
      },
    });
  }
  inputChange = async (key, fileName, type, e) => {
    let value;
    if (type === 5) {
      value = _.isNil(e) ? '' : e;
      // 修复预算金额字段不能输入0问题
      if (fileName === 'budgetPrice') {
        try {
          value = e.toString();
        } catch (error) {
          //
        }
      }
    }
    if (type === 6) {
      value = e.target.value;
    }
    if (type === 1) {
      value = fileName === 'itemSmallMediumName' ? e : e.join(',');
    }
    if (type === 'month') {
      value = moment(e).format('YYYY年M月');
    }
    if (fileName === 'socialCreditCode') {
      const { error: errorMSG } = await checkSocialCreditCode({
        socialCreditCode: value,
        announcementType: this.props.announcementType,
      });
      if (errorMSG) {
        message.destroy();
        message.error(errorMSG);
      }
    }
    const match = this.state.data.find(item => item.key === key);
    match.formValue = {
      ...match.formValue,
      [fileName]: value,
    };

    this.updateState();
  }
  onSelectChange = (key, v, opt) => {
    const match = this.state.data.find(item => item.key === key);
    match.formValue = {
      ...match.formValue,
      winningSupplierName: _.get(opt, 'props.children', ''),
      winningSupplierId: v,
    };
    this.updateState();
  }
  add = (formValue = {}) => {
    const row = {};
    row.formValue = formValue;
    row.key = guid();
    this.state.columns.forEach((item) => {
      if (item.key !== 'action') {
        let file;
        if (item.type === 5) {
          file = (
            <InputNumber
              defaultValue={formValue[item.dataIndex]}
              placeholder="请输入数字"
              disabled={!item.canEdit}
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
              className="table-input"
              min={0}
              max={10000000000000}
            />
          );
        }
        if (item.type === 6) {
          file = (
            <Input
              defaultValue={formValue[item.dataIndex]}
              placeholder="请输入"
              disabled={!item.canEdit}
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
              className="table-input"
            />
          );
          if (item.dataIndex === 'estimatedPurchaseTime') {
            let ivalue = formValue[item.dataIndex] || undefined;
            if (ivalue) {
              ivalue = ivalue.replace('年', '-');
              ivalue = ivalue.replace('月', '');
            }
            file = (
              <MonthPicker
                defaultValue={ivalue ? moment(ivalue) : undefined}
                disabled={!item.canEdit}
                onChange={(e) => {
                  this.inputChange(row.key, item.dataIndex, 'month', e);
                }}
                format="YYYY年M月"
              />
            );
          }
        }
        if (item.type === 1) {
          file = (
            <Select
              style={{ width: '120px' }}
              mode={item.dataIndex === 'itemSmallMediumName' ? undefined : 'multiple'}
              defaultValue={formValue[item.dataIndex]}
              placeholder="请选择"
              disabled={!item.canEdit}
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
            >
              {
                item.value.map((v) => {
                  return (
                    <Select.Option key={v.value}>{v.value}</Select.Option>
                  );
                })
              }
            </Select>
          );
        }
        if (item.dataIndex === 'winningSupplierName' && this.props.env === 'sh') {
          file = (
            <SelectItem
              showSearch
              url="/announcement/api/searchSupplier"
              placeholder="请选择（输入搜索）"
              onChange={(...args) => {
                this.onSelectChange(row.key, ...args);
              }}
              transform={options => (options ? options.map(option => (
                <Option key={option.supplierId} value={option.supplierId}>
                  {option.supplierName}
                </Option>
              )) : [])}
            />
          );
        }
        row[item.dataIndex] = file;
        row.formValue[item.dataIndex] = formValue[item.dataIndex] || '';
      }
    });
    this.setState({
      data: [...this.state.data, row],
    }, () => {
      this.updateState();
    });
  }
  del = (record) => {
    this.setState({
      data: this.state.data.filter(item => item.key !== record.key),
    }, () => {
      this.updateState();
    });
  }

  initTable = () => {
    const columns = [];
    if (this.props.tableInfo.metadataDtos === null) {
      this.props.tableInfo.metadataDtos = [];
    }
    // 附件type也为4，需特殊处理
    if (this.props.tableInfo.key === 'attachments') {
      this.props.tableInfo.metadataDtos.forEach((item) => {
        columns.push({
          title: item.name,
          dataIndex: item.key,
          type: item.type,
          value: item.value,
          canEdit: item.canEdit,
          className: item.nullable === 2 ? 'table-required' : '',
        });
      });
    } else {
      this.props.tableInfo.metadataDtos.forEach((item) => {
        // 采购人发布采购意向公开时，在“采购需求概况”处增加 hover 提示
        const showHover = item.annnouncementType === '10016' && item.key === 'purchaseRequirementDetail';
        columns.push({
          title: showHover ? (
            <React.Fragment>
              {item.name}
              <Popover
                content={
                  <p
                    style={{ width: '300px' }}
                  >采购需求概况应包括采购标的需实现的主要功能，主要目标，及数量，质量，服务，安全，时限等要求。
                  </p>
                }
              >
                <Icon type="question-circle-o" style={{ marginLeft: 5, cursor: 'pointer' }} />
              </Popover>
            </React.Fragment>
          ) : item.name,
          dataIndex: item.key,
          type: item.type,
          value: item.value,
          canEdit: item.canEdit,
          className: item.nullable === 2 ? 'table-required' : '',
        });
      });
      columns.push({
        title: '操作',
        key: 'action',
        width: 70,
        fixed: 'right',
        render: (text, record) => (
          <Popconfirm title=" 确定要删除此行? " placement="left" onConfirm={() => this.del(record)}>
            <a className="customLinkBlue">删除</a>
          </Popconfirm>
        ),
      });
    }
    const values = this.props.tableInfo.value;
    const rows = [];
    if (values && values.length > 0) {
      values.forEach((value) => {
        const row = {};
        row.formValue = {};
        row.key = guid();
        columns.forEach((item) => {
          if (item.dataIndex !== 'action') {
            let file;
            // 数字
            if (item.type === 5) {
              /** 数字类型的兼容处理，业务方可能会传入带元的数字（23元），需要转换为数字 */
              let number = value[item.dataIndex];
              if (number && typeof number === 'string' && number.indexOf('元') !== -1) {
                number = number.replace('元', '');
                number = parseFloat(number);
              }
              value[item.dataIndex] = number;
              /** end */
              file = (
                <InputNumber
                  defaultValue={number}
                  placeholder="请输入数字"
                  disabled={!item.canEdit}
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                  className="table-input"
                  min={0}
                  max={10000000000000}
                />
              );
            }
            // 文本
            if (item.type === 6) {
              file = (
                <Input
                  defaultValue={value[item.dataIndex]}
                  disabled={!item.canEdit}
                  placeholder="请输入"
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                  className="table-input"
                />
              );
            }
            // 下拉框
            if (item.type === 1) {
              file = (
                <Select
                  style={{ width: '120px' }}
                  mode="multiple"
                  placeholder="请选择"
                  disabled={!item.canEdit}
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                >
                  {
                    item.value.map((v) => {
                      return (
                        <Select.Option key={v.value}>{v.value}</Select.Option>
                      );
                    })
                  }
                </Select>
              );
            }
            if (item.dataIndex === 'winningSupplierName') {
              file = (
                <SelectItem
                  url="/announcement/api/searchSupplier"
                />
              );
            }
            row[item.dataIndex] = file;
          }
          row.formValue[item.dataIndex] = value[item.dataIndex] || '';
        });
        rows.push(row);
      });
    }
    this.setState({
      key: this.props.tableInfo.key,
      name: this.props.tableInfo.name,
      columns,
      data: rows,
    }, () => {
      setTimeout(() => {
        this.updateState();
      }, 0);
    });
  }

  componentDidMount() {
    this.initTable();
  }
  downloadTemp = async () => {
    // 下载 POST 方法，模拟 form 表单提交
    const form = document.createElement('form');
    document.body.appendChild(form);
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'fields';
    input.value = JSON.stringify(this.props.tableInfo.metadataDtos || []);
    const input1 = document.createElement('input');
    input1.type = 'text';
    input1.name = 'name';
    input1.value = this.props.tableInfo.name;
    form.appendChild(input);
    form.appendChild(input1);
    form.method = 'post';
    form.action = '/announcement/api/transterExcelTemplate';
    form.submit();
    document.body.removeChild(form);
    try {
      if (!input1.value || !input.value) {
        pushLog(JSON.stringify({
          type: 'flow',
          metadataDtos: input.value,
          tableInfoName: input1.value,
        }), 'info');
      }
    } catch (error) {
      //
    }
  }
  downloadText = (text, filename) => {
    const element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
    element.setAttribute('download', filename);
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }
  onUploadChange = ({
    file,
  }) => {
    if (file.status === 'done') {
      const { error, result, success } = file.response;
      if (!success) {
        Modal.error({
          title: '提示',
          content: '导入表格内容校验未通过，请修改后再次导入',
          okText: '下载错误详情',
          onOk: () => {
            this.downloadText(error, '导入错误详情.txt');
          },
        });
      } else {
        result.forEach((item) => {
          this.add(item);
        });
      }
    }
  }
  render() {
    const { name, columns, data, key } = this.state;
    const { tableInfo, announcementType, canImport, disabled } = this.props;

    return (
      <div className="form-table">
        <div className="table-title" key={key}>
          <span className={key === 'biddingProject' && announcementType === '3012' ? 'subTitle title need' : 'title subTitle'}>{name}</span>
          {(tableInfo.key !== 'attachments' && !disabled) ? <Button type="primary" size="small" ghost onClick={() => { this.add(); }}>新增</Button> : null}
          {canImport && (
            <Upload
              showUploadList={false}
              action="/announcement/api/excel2Bookmark"
              onChange={this.onUploadChange}
              maxFileSize={10 * 1024 * 1024}
              className="upload"
              successPrompt={false}
              data={{
                fields: JSON.stringify(this.props.tableInfo.metadataDtos || []),
              }}
            >
              <Button
                style={{ marginRight: 10 }}
                type="primary"
                size="small"
                ghost
              >
                导入
              </Button>
            </Upload>
          )}
          {canImport && (
            <a
              style={{ marginRight: 10, lineHeight: '28px' }}
              className="upload"
              onClick={this.downloadTemp}
            >
              下载导入模板
            </a>
          )}
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={data}
          scroll={{ x: columns.length * 150 }}
        />
      </div>
    );
  }
}

export default FormTable;
