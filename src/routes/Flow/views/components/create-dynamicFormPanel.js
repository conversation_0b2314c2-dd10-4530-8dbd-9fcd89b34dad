import React, { useEffect } from 'react';
import { Form, Alert, Input, TreeSelect, Select, DatePicker, Panel, Button } from 'doraemon';
import { formItemLayoutForColSpan2, formItemLayout, NO50ANN, isProxyUnit, hideTitleAnn } from '../../configs/const';
import TitleInput from './title-input';
import TitleWithSuffix from './titleWithSuffix';
import { renderDynamicForm } from '../../configs/renderDynamicForm';
import CustomFormGrid from 'components/FormGrid';
import { renderAlert } from 'src/utils/listSensitiveWords';

const FormItem = Form.Item;
const InputGroup = Input.Group;
const Option = Select.Option;
// 7004行政处罚信息公告
// 7006监督检查处理结果公告
const NOTICE_CODE_ARRAY = ['7004', '7006'];

export default ({ _this = { props: {}, state: {} } }) => {
  const {
    hasRemind, hasBlock, sensitiveWords, 
    config, isPubTypeDisabled,
  } = _this.state;
  const { form, flow: { districtTree, dynamicForm }, openConfig } = _this.props || {};
  const { publishTypeOptions = [], publishTypeDefaultValue } = openConfig;
  const { getFieldDecorator, getFieldsValue } = form;

  const iniTitle = _this.routerInfo.title || (
    _this.routerInfo.projectCode 
      ?
      _this.getTitlePlaceholder() 
      : undefined
  );

  // 如果是浙江省，并且为指定类型的公告，则展示 50 号文相关提示
  const isZJProvince = /^(33)/.test(getFieldsValue(['district'])?.district);
  const isSpecialAnn = !!NO50ANN.includes(_this.routerInfo.announcementType);
  const formItems = renderDynamicForm(_this);
  const districtSchema = dynamicForm.find(item => (item.key === 'disForPro')) || { canEdit: true };

  // 发布时间的取值逻辑
  // state.pubType的值不在下拉数据的时候，select的值为默认值（publishTypeDefaultValue）
  let currentPubType = publishTypeDefaultValue;
  const detailPubType = _this.state.pubType;
  if (detailPubType && publishTypeOptions.some(ele => +ele.v === detailPubType)) {
    currentPubType = detailPubType;
  }

  // 发布时间选择定时发布时间选择器不做时间限制
  // 满足条件：山西区划，公告类型为7004和7006
  // 14开头为山西区划，999900为山西运营区划
  const isShanxiDistrict = ['14', '999900'].some(i => window.currentUserDistrict?.code?.startsWith(i));
  const isCertainAnnouncementType = NOTICE_CODE_ARRAY.includes(_this.routerInfo?.announcementType);
  const isDatePickerShowAllDate = isShanxiDistrict && isCertainAnnouncementType;

  useEffect(() => {
    if (config.distCode) {
      _this.handleNonGovDrainageModal(config.distCode);
    }
  }, [config.distCode]);

  return (
    <Panel
      className="editorPanel"
      title={
        (
          <span>
            <span style={{ lineHeight: '35px' }}>公告内容</span>
            <Button
              className={(_this.state.preview && config.secondEdit) ? 'show' : 'hide'}
              style={{ float: 'right' }}
              type="primary"
              onClick={
                () => {
                  const editable = _this.state.editable;
                  try {
                    if (editable) {
                      window.UE.getEditor('ueditor').setDisabled('fullscreen');
                    } else {
                      window.UE.getEditor('ueditor').setEnabled();
                    }
                  } catch (error) {
                    // eslint-disable-next-line no-console
                    console.log(error);
                  }
                  _this.setState({
                    editable: !editable,
                  });
                }
              }
            >
              {_this.state.editable ? '预览' : '编辑'}
            </Button>
          </span>
        )
      }
    >
      {
        renderAlert(hasBlock, hasRemind)
      }
      {
        (isZJProvince && isSpecialAnn && _this.state.preview && config.secondEdit) ?
          (
            <Alert
              message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，请遵循模板要求勿自行调整，否则将影响相关公告发布！"
              type="warning"
            />
          ) : ''
      }
      {
        (_this.state.preview && !config.secondEdit) && (
          <Alert
            message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，公告样式及内容不允许修改！"
            type="warning"
          />
        )
      }
      <Form onSubmit={_this.handleSubmit}>
        <div className="formgrid">
          <CustomFormGrid>
            {(_this.routerInfo.announcementType === '10016' || _this.state.preview) && (
              <FormItem
                style={(!hideTitleAnn[_this.routerInfo.announcementType] || _this.state.preview)
                  ? {} : {
                    display: 'none',
                  }}
                label="标题："
                colSpan={2}
                {...formItemLayoutForColSpan2}
              >
                {getFieldDecorator('title', {
                  rules: [{
                    required: true, message: '不能为空！',
                  }, {
                    validator: (rule, v, callback) => {
                      if (Array.isArray(v)) {
                        if (!v[0]) {
                          callback(new Error('请输入单位名称'));
                          return;
                        }
                        if (!v[1]) {
                          callback(new Error('请输入年份'));
                          return;
                        }
                        if (!v[3]) {
                          callback(new Error('请输入开始月份'));
                          return;
                        }
                        if (!v[5]) {
                          callback(new Error('请输入结束月份'));
                          return;
                        }
                      }
                      const value = Array.isArray(v) ? v.join('') : v;
                      const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
                        if (value.includes(cur.emitWord)) {
                          arr.push(cur.emitWord);
                        }
                        return arr;
                      }, []);
                      if (titleSensitiveWords.length) {
                        callback(new Error(`请修改敏感词：${titleSensitiveWords.join(',')}`));
                        return;
                      }
                      if (typeof value === 'string' && value.length > 200) {
                        callback(new Error('不能超过 200 字符'));
                        return;
                      }
                      if (typeof value === 'string') {
                        const reg = /[\u4e00-\u9fa5]/g;
                        const matchNum = (`${value || ''}`).match(reg) || [];
                        if (matchNum.length < 10) {
                          callback('至少需要输入 10 个中文字符！');
                          return;
                        }
                      }
                      callback();
                    },
                  }],
                  initialValue: iniTitle,
                })(
                  (_this.routerInfo.announcementType === '10016' && !iniTitle && formItems.length > 0) ? (
                    <TitleInput
                      form={form}
                      onChange={_this.titleChange}
                      disabled={_this.state.preview && !_this.state.editable}
                    />
                  ) : (
                    config.customizedTitle ? (
                      <TitleWithSuffix form={form} config={config} />
                    ) :
                      (
                        <Input
                          // disabled={_this.state.preview && !_this.state.editable}
                          placeholder={_this.getTitlePlaceholder(true)}
                        />
                      )
                  )
                )}
              </FormItem>
            )}
            <FormItem label="行政区划：" {...formItemLayout}>
              {getFieldDecorator('district', {
                rules: [
                  { required: true, message: '请选择！' },
                ],
              })(
                <TreeSelect
                  disabled={(isProxyUnit || _this.state.preview
                          || _this.routerInfo.isRelationProject !== null)
                          || districtTree.length === 0
                          || !districtSchema.canEdit || (_this.routerInfo.projectCode && getFieldsValue(['district']))}
                  treeData={districtTree}
                  treeNodeFilterProp="title"
                  showSearch
                  onSelect={
                    (value) => {
                      _this.handleNonGovDrainageModal(value);
                      _this.getGpcatalog(value);
                      _this.initJgInfo(value);
                      _this.initMarking(value);
                      _this.getPublishTypeConfig(value);
                      _this.clearPurchaseName();
                      _this.getFixedTimeFlag(value);
                    }
                  }
                  placeholder="请选择"
                  searchPlaceholder="输入关键字搜索"
                />
              )}
            </FormItem>
            <FormItem label="发布时间：" {...formItemLayout}>
              <InputGroup compact className={_this.state.pubType === 1 ? 'hastime timeCom' : 'timeCom'}>
                <Select
                  disabled={(_this.state.preview && !_this.state.editable) || isPubTypeDisabled}
                  value={currentPubType}
                  onChange={(pubType) => {
                    _this.props.form.resetFields(['releasedAt']);
                    _this.setState({
                      pubType,
                    });
                  }}
                >
                  {publishTypeOptions.map(ele => <Option value={+ele.v}>{ele.k}</Option>)}
                </Select>
                {getFieldDecorator('releasedAt', {
                  rules: [
                    { required: _this.state.pubType === 1, message: '不能为空！' },
                    {
                      validator: _this.timeValidator,
                    },
                  ],
                })(
                  <DatePicker
                    disabled={
                      _this.state.pubType !== 1 ||
                            (_this.state.preview && !_this.state.editable)
                    }
                    disabledDate={
                      isDatePickerShowAllDate ? '' :
                        (current) => {
                          return current <
                                new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
                        }
                    }
                    showTime={{
                      format: 'HH:mm',
                    }}
                    format="YYYY年MM月DD日HH:mm"
                    placeholder={_this.state.pubType === 1 ? '请选择发布时间' : ''}
                  />
                )}
              </InputGroup>
            </FormItem>
          </CustomFormGrid>
          <div id="formCtt" style={{ display: _this.state.preview ? 'none' : 'block', marginTop: -1 }}>
            <CustomFormGrid>
              {formItems}
            </CustomFormGrid>
          </div>
        </div>
        <div id="ueditor" style={{ clear: 'both' }} className={_this.state.preview ? 'show' : 'hide'} />
      </Form>
    </Panel>
  );
};
