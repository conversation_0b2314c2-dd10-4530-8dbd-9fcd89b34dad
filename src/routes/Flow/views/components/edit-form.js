import React from 'react';
import { Form, Input, TreeSelect, Select, DatePicker } from 'doraemon';
import CustomFormGrid from 'components/FormGrid';
import { formItemLayout, formItemLayoutForColSpan2 } from '../../configs/const';
import TitleInput from './title-input';
import TitleWithSuffix from './titleWithSuffix';

const FormItem = Form.Item;
const InputGroup = Input.Group;
const Option = Select.Option;

export default ({ _this = { props: {}, state: {} } }) => {
  const { sensitiveWords, detail, config, districtTree } = _this?.state;
  const { getFieldDecorator } = _this?.props?.form;
  const { publishTypeOptions = [] } = _this.props?.openConfig;
  return (
    <CustomFormGrid>
      <FormItem label="标题：" colSpan={2} {...formItemLayoutForColSpan2}>
        {getFieldDecorator('title', {
          rules: [{
            required: true, message: '不能为空！',
          }, {
            max: 200, message: '不能超过200字符！',
          }, {
            validator: (rule, value, callback) => {
              const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
                if (value.includes(cur.emitWord)) {
                  arr.push(cur.emitWord);
                }
                return arr;
              }, []);
              if (titleSensitiveWords.length) {
                callback(new Error(`请修改敏感词：${titleSensitiveWords.join(',')}`));
                return;
              }
              callback();
            },
          }],
          initialValue: detail.title,
        })(
          detail.announcementType === 10016 ? (
            <TitleInput
              form={_this.props.form}
            />
          ) : (config.customizedTitle ? <TitleWithSuffix config={config} /> : <Input placeholder="请输入标题" />)
        )}
      </FormItem>
      <FormItem label="行政区划：" {...formItemLayout}>
        {getFieldDecorator('district', {
          rules: [
            { required: true, message: '不能为空！' },
          ],
          initialValue: _this.state.districtName,
        })(
          <TreeSelect
            disabled
            treeData={districtTree}
            allowClear
            showSearch
            onSelect={
              () => {
                _this.districtCode = null;
              }
            }
            placeholder="请选择"
            searchPlaceholder="输入关键字搜索"
          />
        )}
      </FormItem>
      <FormItem label="发布时间：" {...formItemLayout}>
        <InputGroup compact className={_this.state.detail.pubType === 1 ? 'hastime timeCom' : 'timeCom'}>
          <Select
            value={detail.pubType}
            onChange={(pubType) => {
              _this.state.detail.pubType = pubType;
              _this.props.form.resetFields(['releasedAt']);
              _this.setState({
                detail: _this.state.detail,
              });
            }}
          >
            {publishTypeOptions.map(ele => <Option value={+ele.v}>{ele.k}</Option>)}
          </Select>
          {getFieldDecorator('releasedAt', {
            rules: [
              { required: detail.pubType === 1, message: '不能为空！' },
            ],
            initialValue: detail.pubType === 1 ? detail.releasedAt : undefined,
          })(
            <DatePicker
              disabled={detail.pubType !== 1}
              showTime={{
                format: 'HH:mm',
              }}
              disabledDate={
                (current) => {
                  return current <
                                  new Date(new Date().getTime() - (24 * 60 * 60 * 1000));
                }
              }
              format="YYYY-MM-DD HH:mm"
              placeholder={_this.state.pubType === 1 ? '请选择发布时间' : ''}
            />
          )}
        </InputGroup>
      </FormItem>
    </CustomFormGrid>
  );
};
