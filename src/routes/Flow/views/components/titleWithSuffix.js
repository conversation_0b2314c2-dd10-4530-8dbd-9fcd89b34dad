import React, { Component } from 'react';
import { Input } from 'doraemon';
import _ from 'lodash';

export default class TitleWithSuffix extends Component {
  componentWillReceiveProps(nProps) {
    const propKeys = ['config'];
    const nextProps = _.pick(nProps, propKeys);
    const nowProps = _.pick(this.props, propKeys);
    if (!_.isEqual(nextProps, nowProps)) {
      this.doInit(nProps);
    }
  }
  componentDidMount() {
    this.doInit(this.props);
  }
  doInit = (props) => {
    const { config, form } = props;
    const { titleSuffix, titlePrefix } = config || {};
    const title = `${titlePrefix || ''}${titleSuffix || ''}` || '';
    if (form && title) {
      setTimeout(() => {
        form.setFieldsValue({
          title,
        });
      });
    }
  }
  onChange = (e) => {
    const val = e.target.value;
    const { onChange, config } = this.props;
    const { titleSuffix } = config || {};
    if (val) {
      onChange(`${val}${titleSuffix || ''}`);
    } else {
      onChange(undefined);
    }
  }
  render() {
    const { config, value, ...rest } = this.props;
    const { titleSuffix } = config || {};
    const v = value ? value.replace(titleSuffix, '') : undefined;
    return (
      <span style={{ width: '100%' }}>
        <Input
          style={{ width: '60%' }}
          placeholder="请输入"
          value={v}
          {...rest}
          onChange={this.onChange}
        />
        &nbsp;
        {titleSuffix}
      </span>
    );
  }
}
