import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import {
  Panel,
  Upload,
  Button,
  Icon,
  Progress,
  Checkbox,
  FormGrid,
  Popover,
  Modal,
} from 'doraemon';
import { download } from '../../../../utils/download';
import { specialAttachmentSize } from '../../configs/const';
import '../index.less';
import { getSensitiveWords } from 'src/utils/listSensitiveWords';

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};


/**
 * 文件上传的组件
 * 仅有qualificationType包含特色逻辑。qualificationType === true的时候 展示「资格预审文件word」、「资格预审文件pdf」
 * 分别对应序号1与2、即是fileList1、fileList2
 * 非无想表单新建页才有 /src/routes/Flow/views/create.js
 * 数据存储在dva的model中、flow中。通过「flow/setFileList」去更新
 * 
 */  
@connect(({ flow }) => ({
  flow,
}))
class FormUpload extends Component {
  beforeUpload = (file) => {
    const { announcementType, districtCode } = this.props;
    return new Promise((resolve, reject) => {
      getSensitiveWords({
        content: file.name,
        district: districtCode,
        announcementType,
      }).then((res) => {
        if (!res) return;
        const {
          hasBlock,
          hasRemind,
          remindMessageHtml,
          blockMessageHtml,
        } = res;

        if (hasBlock) {
          Modal.error({
            title: '提交失败',
            content: (
              /* eslint-disable-next-line react/no-danger */
              <span dangerouslySetInnerHTML={{ __html: blockMessageHtml || '' }} />
            ),
          });
          reject();
          return;
        }
        if (hasRemind) {
          Modal.warning({
            title: '警告',
            content: (
              /* eslint-disable-next-line react/no-danger */
              <div dangerouslySetInnerHTML={{ __html: remindMessageHtml || '' }} />
            ),
            okText: '继续提交',
            okCancel: true,
            onOk: () => resolve(true),
            onCancel: () => reject(),
          });
          return;
        }
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  }
  
  // 文件上传事件
  onUploadChange = (info) => {
    let fileList = this.props.flow.fileInfo.fileList;
    let flag = true;
    fileList = fileList.map((item) => {
      if (item.fileId === info.file.fileId) {
        flag = false;
        return info.file;
      }
      return item;
    }).filter(e => e);

    if (flag) {
      info.file.fileId && fileList.push(info.file);
    }
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo: {
          ...this.props.flow.fileInfo,
          fileList,
        },
      },
    });
  }
  onUploadChange1= (info) => {
    let fileList = this.props.flow.fileInfo1.fileList1;
    let flag = true;
    fileList = fileList.map((item) => {
      if (item.fileId === info.file.fileId) {
        flag = false;
        return info.file;
      }
      return item;
    }).filter(e => e);

    if (flag) {
      info.file.fileId && fileList.push(info.file);
    }
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo1: {
          ...this.props.flow.fileInfo1,
          fileList1: fileList,
        },
      },
    });
  }
  onUploadChange2= (info) => {
    let fileList = this.props.flow.fileInfo2.fileList2;
    let flag = true;
    fileList = fileList.map((item) => {
      if (item.fileId === info.file.fileId) {
        flag = false;
        return info.file;
      }
      return item;
    }).filter(e => e);

    if (flag) {
      info.file.fileId && fileList.push(info.file);
    }
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo2: {
          ...this.props.flow.fileInfo2,
          fileList2: fileList,
        },
      },
    });
  }
  // 文件上传事件模板
  handleUploadChange = (infoKey, listKey) => (info) => {
    const targetState = this.props.flow[infoKey];
    const fileList = targetState[listKey];
    
    const updatedList = fileList
      .map(item => (item.fileId === info.file.fileId ? info.file : item))
      .filter(e => e);

    if (!fileList.some(item => item.fileId === info.file.fileId)) {
      info.file.fileId && updatedList.push(info.file);
    }

    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        [infoKey]: {
          ...targetState,
          [listKey]: updatedList,
        },
      },
    });
  }
  // 外网展示checkbox
  onFileCheckboxChange = (e, item, type) => {
    const { fileInfo } = this.props.flow;
    const fileList = fileInfo[type];
    fileList.forEach((fl) => {
      if (fl.fileId === item.fileId) {
        fl.noPublic = !e.target.checked;
      }
    });
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo: {
          ...this.props.flow.fileInfo,
          [type]: fileList,
        },
      },
    });
  }

  // 删除文件
  onFileDelete = ({ fileId }, type) => {
    const { fileInfo = {}, fileInfo1 = {}, fileInfo2 = {}, fileInfo3 } = this.props.flow;
    const fileList = fileInfo[type] || [];
    const fileList1 = fileInfo1[type] || [];
    const fileList2 = fileInfo2[type] || [];
    const fileList3 = fileInfo3[type] || [];
    if (type === 'fileList' || type === 'defaultFileList') {
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo: {
            ...this.props.flow.fileInfo,
            [type]: fileList.filter(fl => fl.fileId !== fileId),
          },
        },
      });
    } else if (type === 'fileList1' || type === 'defaultFileList1') {
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo1: {
            ...this.props.flow.fileInfo1,
            [type]: fileList1.filter(fl => fl.fileId !== fileId),
          },
        },
      });
    } else if (type === 'fileList2' || type === 'defaultFileList2') {
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo2: {
            ...this.props.flow.fileInfo2,
            [type]: fileList2.filter(fl => fl.fileId !== fileId),
          },
        },
      });
    } else {
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo3: {
            ...this.props.flow.fileInfo3,
            [type]: fileList3.filter(fl => fl.fileId !== fileId),
          },
        },
      });
    }
  }

  // 文件上传状态
  getStatus = ({ percent, status }) => {
    if (status === 'uploading') {
      // percent返回100%时，可能并未上传完成，这里处理为99%
      if (parseInt(percent, 10) === 100) {
        return <Progress percent={99} size="small" status="active" />;
      }
      return <Progress percent={parseInt(percent, 10)} size="small" status="active" />;
    }
    if (status === 'done') {
      return null;
    }
    if (status === 'error') {
      return <div className="upload-error">文件上传失败，请重新上传！</div>;
    }
  }

  renderFileList = (source, type, disabled) => {
    return source.map(item => (
      <li key={item.fileId}>
        <div className="inline">
          <Icon type="file" />
          <span className="file-name">{item.name}</span>
          <Checkbox checked={!item.noPublic}
            disabled={disabled}
            onChange={(e) => {
              this.onFileCheckboxChange(e, item, type);
            }}
          >外网展示
          </Checkbox>
          {
            item.status !== 'uploading' && (
              <a
                title="删除"
                className="file-del"
                onClick={() => {
                  this.onFileDelete(item, type);
                }}
              ><Icon type="close" />
              </a>
            )
          }
          {this.getStatus(item)}
        </div>
      </li>
    ));
  }
  getToolTipAndTemp = (announcementType) => {
    const results = {
      3012: { // 单一来源
        tooltip: '请上传专业人员论证意见',
        templateName: '“单一来源采购方式专业人员论证意见”模板下载',
        fileName: '单一来源采购方式专业人员论证意见.docx',
        fileLink: 'https://sitecdn.zcycdn.com/f2e-assets/61a7c931-8fe0-4ee9-9009-dd67c4dd6c60.docx',
      },
      3017: { // 结果更正公告
        tooltip: (
          <div>
            <p>在附件中公告变更后的中标（成交）供应商的相关信息；</p>
            <p>中标、成交供应商为中小企业的，应公告其《中小企业声明函》；</p>
            <p>中标、成交供应商为残疾人福利性单位的，应公告其《残疾人福利性单位声明函》；</p>
            <p>中标、成交供应商为注册地在国家级贫困县域内物业公司的，应公告注册所在县扶贫部门出具的聘用建档立卡贫困人员具体数量的证明。</p>
          </div>
        ),
      },
      3010: { // 采购合同公告
        tooltip: '请上传合同（采购人应当按照《政府采购法实施条例》有关要求，将政府采购合同中涉及国家秘密、商业秘密的内容删除后予以公开）',
      },
      3004: { // 中标（成交）结果公告
        tooltip: (
          <div>
            <p>采用书面推荐供应商参加采购活动的，需带入采购人和专家的推荐意见；</p>
            <p>中标、成交供应商为中小企业的，应公告其《中小企业声明函》；</p>
            <p>中标、成交供应商为残疾人福利性单位的，应公告其《残疾人福利性单位声明函》；</p>
            <p>中标、成交供应商为注册地在国家级贫困县域内物业公司的，应公告注册所在县扶贫部门出具的聘用建档立卡贫困人员具体数量的证明。</p>
          </div>
        ),
      },
    };
    return results[announcementType] || {};
  }
  download = () => {
    const { fileLink = '', fileName = '' } = this.getToolTipAndTemp(this.props.announcementType);
    download(fileLink, fileName);
  }
  render() {
    const { required, editable,
      qualificationType,
      isShowReservedQuotaAttachment,
      flow:
      { fileInfo, fileInfo1, fileInfo2, fileInfo3 }, announcementType, 
      attachmentUploadDesc, attachmentExternalDisplayRules } = this.props;
    const { fileList, defaultFileList } = fileInfo;
    const { fileList1, defaultFileList1 } = fileInfo1;
    const { fileList2, defaultFileList2 } = fileInfo2;
    const { fileList3, defaultFileList3 } = fileInfo3;
    const { tooltip = '', fileLink = '', templateName } = this.getToolTipAndTemp(announcementType);
    const fileSize = specialAttachmentSize?.includes(Number(announcementType)) ? 100 : 50;

    return (
      <Panel
        title={
          (
            <span
              className={
                required ? 'need' : ''
              }
            >附件
            </span>
          )
        }

        description={this.props.panelDescription}
      >
        <FormGrid
          bordered
          formGridItem={[{
            label: (
              <span>
                附件
                {tooltip && (
                  <Popover overlayStyle={{ maxWidth: 300 }} content={tooltip}>
                    &nbsp;<Icon style={{ marginRight: 5, color: '#999' }} type="question-circle-o" />
                  </Popover>
                )}
              </span>
            ),
            colSpan: 2,
            ...formItemLayout,
            render: () => (
              <Fragment>
                <div className={editable ? 'show' : 'hide'}>
                  <Upload
                    className="form-file"
                    showUploadList={false}
                    bizCode="1014"
                    onChange={this.onUploadChange}
                    beforeUpload={this.beforeUpload}
                    maxFileSize={fileSize * 1024 * 1024}
                    multiple
                  >
                    <Button>
                      <Icon type="upload" /> 上传附件
                    </Button>
                  </Upload>
                  {fileLink && (
                    <a
                      className="upload-link"
                      style={{ width: '400px' }}
                      onClick={this.download}
                    >
                      {templateName || '附件上传模板'}
                    </a>
                  )}
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }} className="upload-note">文件大小不超过{fileSize}MB</div>

                  {
                    attachmentExternalDisplayRules === 1 ? (
                      <div style={{ fontSize: '14px', color: 'red', marginTop: '4px' }} className="upload-note">说明：上传文件后可选是否外网展示，如未勾选则仅作内部流转</div>
                    ) : (
                      <div style={{ fontSize: '14px', color: 'red', marginTop: '4px' }} className="upload-note">说明：根据当前区划要求，本公告上传的附件需全部对外公开</div>
                    ) 
                  }
                  
                  {
                    attachmentUploadDesc ? (
                      <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }} className="upload-note">{attachmentUploadDesc}</div>
                    ) : null
                  }
                  <ul className="fileList">
                    {this.renderFileList(defaultFileList, 'defaultFileList', false)}
                    {this.renderFileList(fileList, 'fileList', false)}
                  </ul>
                </div>
                <div className={!editable ? 'show' : 'hide'}>
                  <Upload.Download bizCode="1014" fileList={[...defaultFileList, ...fileList]} />
                </div>
              </Fragment>
            ),
          }, qualificationType && {
            label: (
              <span>
                资格预审文件word
                {tooltip && (
                  <Popover overlayStyle={{ maxWidth: 300 }} content={tooltip}>
                    &nbsp;<Icon style={{ marginRight: 5, color: '#999' }} type="question-circle-o" />
                  </Popover>
                )}
              </span>
            ),
            colSpan: 2,
            ...formItemLayout,
            render: () => (
              <Fragment>
                <div className={editable ? 'show' : 'hide'}>
                  <Upload
                    className="form-file"
                    bizCode={1014}
                    showUploadList={false}
                    onChange={this.onUploadChange1}
                    maxFileSize={100 * 1024 * 1024}
                    multiple
                    accept=".doc,.docx"
                  >
                    <Button>
                      <Icon type="upload" /> 上传附件
                    </Button>
                  </Upload>
                  {fileLink && (
                    <a
                      className="upload-link"
                      style={{ width: '400px' }}
                      onClick={this.download}
                    >
                      {templateName || '附件上传模板'}
                    </a>
                  )}
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }} className="upload-note">格式限制为word，不超过100MB大小</div>
                  <ul className="fileList">
                    {this.renderFileList(defaultFileList1, 'defaultFileList1', true)}
                    {this.renderFileList(fileList1, 'fileList1', true)}
                  </ul>
                </div>
                <div className={!editable ? 'show' : 'hide'}>
                  <Upload.Download bizCode="1014" fileList={[...defaultFileList1, ...fileList1]} />
                </div>
              </Fragment>
            ),
          }, qualificationType && {
            label: (
              <span>
                资格预审文件PDF
                {tooltip && (
                  <Popover overlayStyle={{ maxWidth: 300 }} content={tooltip}>
                    &nbsp;<Icon style={{ marginRight: 5, color: '#999' }} type="question-circle-o" />
                  </Popover>
                )}
              </span>
            ),
            colSpan: 2,
            ...formItemLayout,
            render: () => (
              <Fragment>
                <div className={editable ? 'show' : 'hide'}>
                  <Upload
                    className="form-file"
                    bizCode={1014}
                    showUploadList={false}
                    onChange={this.onUploadChange2}
                    maxFileSize={100 * 1024 * 1024}
                    multiple
                    accept=".pdf"
                  >
                    <Button>
                      <Icon type="upload" /> 上传附件
                    </Button>
                  </Upload>
                  {fileLink && (
                    <a
                      className="upload-link"
                      style={{ width: '400px' }}
                      onClick={this.download}
                    >
                      {templateName || '附件上传模板'}
                    </a>
                  )}
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }} className="upload-note">格式限制为pdf，不超过100MB大小</div>
                  <ul className="fileList">
                    {this.renderFileList(defaultFileList2, 'defaultFileList2', true)}
                    {this.renderFileList(fileList2, 'fileList2', true)}
                  </ul>
                </div>
                <div className={!editable ? 'show' : 'hide'}>
                  <Upload.Download bizCode="1014" fileList={[...defaultFileList2, ...fileList2]} />
                </div>
              </Fragment>
            ),
          }, isShowReservedQuotaAttachment && {
            label: (
              <>
                <span style={{ color: 'red', marginRight: '2px' }}>*</span>
                <span>未达到规定预留份额比例的情况说明</span>
              </>
            ),
            colSpan: 2,
            ...formItemLayout,
            render: () => (
              <>
                <div className={editable ? 'show' : 'hide'}>
                  <Upload
                    className="form-file"
                    showUploadList={false}
                    bizCode="1014"
                    onChange={this.handleUploadChange('fileInfo3', 'fileList3')}
                    beforeUpload={this.beforeUpload}
                    maxFileSize={fileSize * 1024 * 1024}
                    multiple
                  >
                    <Button>
                      <Icon type="upload" /> 上传附件
                    </Button>
                  </Upload>
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }} className="upload-note">文件大小不超过{fileSize}MB</div>
                  <div style={{ fontSize: '14px', color: 'red', marginTop: '4px' }} className="upload-note">说明：根据当前区划要求，本公告上传的附件需全部对外公开</div>
                  <ul className="fileList">
                    {this.renderFileList(defaultFileList3, 'defaultFileList3', true)}
                    {this.renderFileList(fileList3, 'fileList3', true)}
                  </ul>
                </div>
                <div className={!editable ? 'show' : 'hide'}>
                  <Upload.Download bizCode="1014" fileList={[...defaultFileList3, ...fileList3]} />
                </div>
              </>
              
            ),
          }].filter(Boolean)}
        />
      </Panel>
    );
  }
}

export default FormUpload;
