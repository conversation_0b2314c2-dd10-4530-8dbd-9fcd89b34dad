import React, { Component } from 'react';
import debounce from 'lodash/debounce';
import { connect } from 'dva';
import { Button, Table, Input, Popconfirm, Select, InputNumber, Row, Col } from 'doraemon';
import { guid } from '../../../../utils/utils';
import './form-table.less';

@connect(({ flow }) => ({
  flow,
}))
class FormTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      columns: [],
      data: [],
      key: '',
      name: '',
    };
    // 防抖处理，不然频繁更新state导致输入会很卡
    this.updateState = debounce(this.doUpdateState, 300);
  }


  doUpdateState = () => {
    const newData = [];

    this.state.data.forEach((column) => {
      column.expends.forEach((item, index) => {
        const cell = {
          formValue: {
            ...column.formValue,
            ...item,
          },
        };
        delete cell.formValue.key;
        if (index !== 0) {
          cell.formValue.sectionNo = null;
          cell.formValue.bidItemName = null;
          cell.formValue.marginAmount = null;
        }
        newData.push(cell);
      });
    });
    this.props.dispatch({
      type: 'flow/setTableData',
      payload: {
        tableData: {
          ...this.props.flow.tableData,
          [this.state.key]: newData,
        },
      },
    });
  }
  inputChange = (key, fileName, type, e) => {
    let value;
    if (type === 5) {
      value = e;
    }
    if (type === 6) {
      value = e.target.value;
    }
    if (type === 1) {
      value = e.join(',');
    }
    const match = this.state.data.find(item => item.key === key);
    match.formValue = {
      ...match.formValue,
      [fileName]: value,
    };
    this.updateState();
  }
  add = () => {
    const row = {};
    row.formValue = {};
    row.key = guid();
    this.state.columns.forEach((item) => {
      if (item.key !== 'action') {
        let file;
        if (item.type === 5) {
          file = (
            <InputNumber
              placeholder="请输入数字"
              disabled={!item.canEdit}
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
              className="table-input"
              min={0}
              max={10000000000000}
            />
          );
        }
        if (item.type === 6) {
          file = (
            <Input
              placeholder="请输入"
              disabled={!item.canEdit}
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
              className="table-input"
            />
          );
        }
        if (item.type === 1) {
          file = (
            <Select
              style={{ width: '120px' }}
              mode="multiple"
              disabled={!item.canEdit}
              placeholder="请选择"
              onChange={(e) => {
                this.inputChange(row.key, item.dataIndex, item.type, e);
              }}
            >
              {
                item.value.map((v) => {
                  return (
                    <Select.Option key={v.value}>{v.value}</Select.Option>
                  );
                })
              }
            </Select>
          );
        }
        row[item.dataIndex] = file;
        row.formValue[item.dataIndex] = '';
        // 添加银行信息keys
        row.expends = [{
          depositBank: '',
          accountNo: '',
          marginDeliverMeth: '',
          remarks: '',
          key: guid(),
        }];
      }
    });

    this.setState({
      data: [...this.state.data, row],
    });
  }
  del = (record) => {
    this.setState({
      data: this.state.data.filter(item => item.key !== record.key),
    }, () => {
      this.updateState();
    });
  }

  initTable = () => {
    const columns = [];
    this.props.tableInfo.metadataDtos = [{
      key: 'sectionNo',
      name: '序号',
      type: 5,
    }, {
      key: 'bidItemName',
      name: '标项名称',
      type: 6,
    }, {
      key: 'marginAmount',
      name: '投标保证金金额（元）',
      type: 5,
    }, {
      key: 'depositBank',
      name: '开户银行',
      type: 6,
    }, {
      key: 'accountNo',
      name: '收款账号',
      type: 6,
    }, {
      key: 'marginDeliverMeth',
      name: '交付方式',
      type: 6,
    }, {
      key: 'remarks',
      name: '备注',
      type: 6,
    }];
    this.props.tableInfo.metadataDtos.forEach((item) => {
      if (['sectionNo', 'bidItemName', 'marginAmount'].includes(item.key)) {
        columns.push({
          title: item.name,
          dataIndex: item.key,
          type: item.type,
          canEdit: item.canEdit,
          value: item.value,
        });
      }
    });
    columns.push({
      title: '操作',
      key: 'action',
      width: 60,
      render: (text, record) => (
        <Popconfirm title=" 确定要删除此行? " placement="left" onConfirm={() => this.del(record)}>
          <a>删除</a>
        </Popconfirm>
      ),
    });
    const values = this.props.tableInfo.value;
    const rows = [];
    if (values && values.length > 0) {
      values.forEach((value) => {
        const row = {};
        row.formValue = {};
        row.key = guid();
        columns.forEach((item) => {
          if (item.dataIndex !== 'action') {
            let file;
            // 数字
            if (item.type === 5) {
              file = (
                <InputNumber
                  defaultValue={value[item.dataIndex]}
                  placeholder="请输入数字"
                  disabled={!item.canEdit}
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                  className="table-input"
                  min={0}
                  max={10000000000000}
                />
              );
            }
            // 文本
            if (item.type === 6) {
              file = (
                <Input
                  defaultValue={value[item.dataIndex]}
                  placeholder="请输入"
                  disabled={!item.canEdit}
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                  className="table-input"
                />
              );
            }
            // 下拉框
            if (item.type === 1) {
              file = (
                <Select
                  style={{ width: '120px' }}
                  mode="multiple"
                  placeholder="请选择"
                  disabled={!item.canEdit}
                  onChange={(e) => {
                    this.inputChange(row.key, item.dataIndex, item.type, e);
                  }}
                >
                  {
                    item.value.map((v) => {
                      return (
                        <Select.Option key={v.value}>{v.value}</Select.Option>
                      );
                    })
                  }
                </Select>
              );
            }
            row[item.dataIndex] = file;
          }
          row.formValue[item.dataIndex] = value[item.dataIndex] || '';
        });
        rows.push(row);
      });
    }
    this.setState({
      key: this.props.tableInfo.key,
      name: this.props.tableInfo.name,
      columns,
      data: rows,
    }, () => {
      setTimeout(() => {
        this.updateState();
      }, 0);
    });
  }

  onExpendChange = (fileName, key, columnKey, e) => {
    const { data } = this.state;
    const value = e.target.value;
    const matchColumn = data.find(item => item.key === key);
    const matchExtens = matchColumn.expends.find(item => item.key === columnKey);
    matchExtens[fileName] = value;
    this.updateState();
  }

  delBankInfo = (key, columnKey) => {
    const { data } = this.state;
    const matchColumn = data.find(item => item.key === key);
    matchColumn.expends = matchColumn.expends.filter(item => item.key !== columnKey);
    this.updateState();
  }

  renderExpend = (record) => {
    const { data } = this.state;
    const match = data.find(item => item.key === record.key);
    return (
      <div className="multi-input-content">
        {
          match.expends.length > 0 && (
            <Row gutter={16} className="item-title">
              <Col span={5}>
                开户银行
              </Col>
              <Col span={5}>
                收款账号
              </Col>
              <Col span={5}>
                交付方式
              </Col>
              <Col span={5}>
                备注
              </Col>
            </Row>
          )
        }
        {
          match.expends.map(item => (
            <Row gutter={16} key={item.key} className="bank-item">
              <Col span={5}>
                <Input
                  size="small"
                  placeholder="请输入"
                  onChange={(e) => {
                    this.onExpendChange('depositBank', record.key, item.key, e);
                  }}
                />
              </Col>
              <Col span={5}>
                <Input
                  size="small"
                  placeholder="请输入"
                  onChange={(e) => {
                    this.onExpendChange('accountNo', record.key, item.key, e);
                  }}
                />
              </Col>
              <Col span={5}>
                <Input
                  size="small"
                  placeholder="请输入"
                  onChange={(e) => {
                    this.onExpendChange('marginDeliverMeth', record.key, item.key, e);
                  }}
                />
              </Col>
              <Col span={5}>
                <Input
                  size="small"
                  placeholder="请输入"
                  onChange={(e) => {
                    this.onExpendChange('remarks', record.key, item.key, e);
                  }}
                />
              </Col>
              <Col span={3}>
                {
                  match.expends.length > 1 && (
                    <a onClick={() => {
                      this.delBankInfo(record.key, item.key);
                    }}
                    >删除
                    </a>
                  )
                }
              </Col>
            </Row>
          ))
        }

        <Button
          type="secondary"
          size="small"
          style={{ marginTop: 10 }}
          onClick={() => {
            match.expends.push({
              key: guid(),
              depositBank: '',
              accountNo: '',
              marginDeliverMeth: '',
              remarks: '',
            });
            this.setState({
              data,
            });
          }}
        >新增银行信息
        </Button>
      </div>
    );
  }

  componentDidMount() {
    this.initTable();
  }

  render() {
    const { name, columns, data, key } = this.state;
    const { tableInfo, disabled } = this.props;

    return (
      <div className="form-table">
        <div className="table-title" key={key}>
          <span className="title subTitle">{name}（点击+号添加银行信息）</span>
          {(tableInfo.key !== 'attachments' && !disabled) ? <Button type="primary" size="small" ghost onClick={this.add}>新增</Button> : null}
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={data}
          scroll={{ x: columns.length * 150 }}
          expandedRowRender={this.renderExpend}
        />
      </div>
    );
  }
}

export default FormTable;
