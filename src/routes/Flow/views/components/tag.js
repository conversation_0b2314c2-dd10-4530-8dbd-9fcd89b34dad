import React, { Component } from 'react';
import './marking.less';

class Tag extends Component {
  render() {
    const { checked = false, disabled = false, name } = this.props;
    let className = 'marking-tag ';
    let title = '选择';
    if (checked) {
      className += 'checked ';
      title = '取消选择';
    }
    if (disabled) {
      className += 'disabled ';
    }
    return (
      <div
        title={title}
        className={className}
        onClick={this.props.onClick}
      >
        {name}
      </div>
    );
  }
}

export default Tag;
