import React, { Component } from 'react';
import {
  Select,
  request,
} from 'doraemon';

const { Option } = Select;

export default class PurchaseName extends Component {
  state = {
    options: [],
  };
  componentWillUnmount() {
    this.setState = () => {
      // This is intentional
    };
  }
  componentDidMount() {
    const { distCode } = this.props;
    const { orgName } = window.currentUserIdentity;
    this.fetchData('/announcement/api/listPurchaser', {
      distCode,
      name: orgName,
      pageSize: 10,
    }, orgName);
  }
  componentWillReceiveProps(nextProps) {
    const { distCode } = nextProps;
    if (distCode !== this.props.distCode) {
      this.fetchData('/announcement/api/listPurchaser', {
        distCode,
        pageSize: 10,
      });
    }
  }
  fetchData = (url, params, orgName) => {
    const { form, code } = this.props;
    if (url) {
      request(url, {
        params,
      }).then((data) => {
        let options = data.result;
        if (!options) {
          options = [];
        }
        this.setState({
          options,
        });
        if (orgName && options.length) {
          setTimeout(() => {
            form.setFieldsValue({
              [code]: orgName,
            });
          }, 100);
        }
      });
    }
  }
  onSearch = (name) => {
    const { distCode } = this.props;
    this.fetchData('/announcement/api/listPurchaser', {
      distCode,
      name,
      pageSize: 10,
    });
  }
  onChange = (v) => {
    const { onChange } = this.props;
    onChange(v);
  }
  render() {
    const { value, distCode, disabled } = this.props;
    const { options } = this.state;
    return (
      <Select
        showSearch
        placeholder="请选择"
        notFoundContent="暂无选项"
        onSearch={this.onSearch}
        filterOption={false}
        key={`Select${distCode}`}
        value={value}
        onChange={this.onChange}
        style={{ width: '100%' }}
        disabled={disabled}
      >
        {options.map(d => (
          <Option
            value={d.orgName}
            key={d.orgId}
          >
            {d.orgName}
          </Option>
        ))}
      </Select>
    );
  }
}
