import React, { Component } from 'react';
import { Input, Select } from 'doraemon';
import './form-linkPurchase.less';

const InputGroup = Input.Group;
const Option = Select.Option;

/**
 * 是否关联采购计划的form组件
 */
class formLinkPurchase extends Component {
  constructor(props) {
    super(props);
    const { isForcedControl = 0 } = this.props;
    this.state = {
      link: String(isForcedControl),
      value: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if ('value' in nextProps) {
      const value = nextProps.value;
      this.setState({
        value,
      });
      if (value.length > 0) {
        this.setState({
          link: '1',
        });
      }
    }
  }
  onChange = (link) => {
    this.props.onLinkChange(link);
    this.setState({
      link,
    });
  }
  onInputChange = (value) => {
    this.setState({
      value,
    }, this.triggerChange);
  }
  triggerChange = () => {
    this.props.onChange(this.state.value);
  }
  render() {
    const { link, value } = this.state;
    const { isForcedControl, disabled } = this.props;
    return (
      <InputGroup compact className="link-purchase">
        <Select disabled={isForcedControl === 1 || disabled} value={link} onChange={this.onChange}>
          <Option value="0">否</Option>
          <Option value="1">是</Option>
        </Select>
        <Select
          mode="tags"
          style={{ width: 160 }}
          placeholder=""
          notFoundContent="请输入"
          disabled={link === '0' || disabled}
          value={value}
          onChange={this.onInputChange}
        />
      </InputGroup>
    );
  }
}

export default formLinkPurchase;
