import React, { Component } from 'react';
import {
  Select,
  request,
} from 'doraemon';

const { Option } = Select;

export default class PunishUnitName extends Component {
  state = {
    options: [],
  };
  componentDidMount() {
    const { type, distCode } = this.props;
    this.fetchData('/announcement/api/listOrg', {
      type,
      distCode,
      pageSize: 10,
    });
  }
  componentWillReceiveProps(nextProps) {
    const { type, distCode } = nextProps;
    if (type !== this.props.type || distCode !== this.props.distCode) {
      this.fetchData('/announcement/api/listOrg', {
        type,
        distCode,
        pageSize: 10,
      });
    }
  }
  fetchData = (url, params) => {
    if (url && params.type) {
      request(url, {
        params,
      }).then((data) => {
        this.setState({
          options: data.result || [],
        });
      });
    }
  }
  onSearch = (name) => {
    const { type, distCode } = this.props;
    this.fetchData('/announcement/api/listOrg', {
      type,
      distCode,
      name,
      pageSize: 10,
    });
  }
  onChange = (v, opt) => {
    const { onChange, form } = this.props;
    onChange(v);
    setTimeout(() => {
      form.setFieldsValue({
        unitCreditCode: opt.props.creditCode,
        punishedObjectId: opt.props.punishedObjectId,
      });
    });
  }
  render() {
    const { value, type, distCode, disabled } = this.props;
    const { options } = this.state;
    return (
      <Select
        showSearch
        placeholder="请选择"
        notFoundContent="暂无选项"
        onSearch={this.onSearch}
        filterOption={false}
        key={`Select${type}-${distCode}`}
        value={value}
        onChange={this.onChange}
        style={{ width: '100%' }}
        disabled={disabled}
      >
        {options.map(d => (
          <Option
            value={d.name}
            key={d.name}
            creditCode={d.creditCode}
            punishedObjectId={d.id}
          >
            {d.name}
          </Option>
        ))}
      </Select>
    );
  }
}
