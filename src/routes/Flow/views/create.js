import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import _Get from 'lodash/get';
import {
  Form, Modal, message, ZcyBreadcrumb, Spin, Alert, request,
} from 'doraemon';
import { AuditModal } from '@zcy/zcy-audit-flow-back';
import {
  getEnvByDistrictAndType,
  submitAnnouncement, modifyAnnouncement,
  checkWorkdayByDate, obtainAnnouncementByProCode,
} from '../services';
import FormUpload from './components/form-upload';
import Marking from './components/marking';
import SensitiveAlert from './components/sensitiveAlert';
import AnnSteps from '../../DynamicAnnouncementEdit/components/AnnSteps';
import CreateDynamicFormPanel from './components/create-dynamicFormPanel';
import { checkOpenAudit, getGlobalBtn } from '../configs/utils';
import { noticeKeysMap } from '../configs/const';
import submit from '../configs/submit';
import loadEditor from '@zcy/zcy-ueditor-front';
import pushLog from 'utils/pushLog';
import './index.less';
import { getSensitiveWords, attachSensitiveWordsTag, showSensitiveMsg, SensitiveEditorStyle, clearSensitiveTag } from 'src/utils/listSensitiveWords';
import { LcyDrainageModal } from '@/components/LcyDrainageModal';
 
@connect(({ flow, openConfig }) => ({
  flow, openConfig,
}))
@Form.create()
export default class add extends Component {
  constructor(props) {
    super(props);
    this.orginkey = {};
    this.tableForm = {};
    this.routerInfo = {};// 路由参数信息
    this.nextTaskUserId = null; // 下级审核人Id
    this.dateCom = [];// 表单中type为2的且为日期控件的key
    this.state = {
      submiting: 1, // 提交状态，1:生成公告，2：暂存，3：提交
      preview: false, // 预览
      editable: false, // 预览页是否可编辑
      // eslint-disable-next-line react/no-unused-state
      pubType: undefined, // 发布方式
      createModalShow: false, // 创建公告，选择审核人弹窗
      glggList: [], // 关联公告列表
      // eslint-disable-next-line react/no-unused-state
      envs: {}, // 监管者信息
      attachmentsNeed: false, // 附件是否必填
      // eslint-disable-next-line react/no-unused-state
      announcementType: undefined,
      markingList: [], // 当前公告标识列表
      // eslint-disable-next-line react/no-unused-state
      isLinkPurchase: '0', // 是否关联采购计划
      config: {}, // 配置规则
      sensitiveVisible: false, // 敏感词信息弹窗
      sensitiveMsg: {}, // 敏感词信息
      // eslint-disable-next-line react/no-unused-state
      correction: [], // 更正类型
      // eslint-disable-next-line react/no-unused-state
      hasRemind: false,
      // eslint-disable-next-line react/no-unused-state
      hasBlock: false,
      // 标题的禁止级敏感词
      // eslint-disable-next-line react/no-unused-state
      sensitiveWords: [],
      supportCheckerMultiSelect: false,
      // eslint-disable-next-line react/no-unused-state
      gpCatalogStartChart: '', // 采购目录大类 A货物 B工程 C服务
      qualificationType: false,
      isPubTypeDisabled: false,
      workflowInfo: {}, // 工作流节点参数
      auditParams: {}, // 审核弹窗参数
      processFrontParam: {}, // 选择的审核人信息
      // eslint-disable-next-line react/no-unused-state
      printContent: '',
      isShowDrainageModal: false, // 是否展示引流乐采云弹窗
      whetherToSettleIn: false, // 是否已入驻乐采云
      isLeCaiYun: false, // 是否乐采云环境
    };
    this.id = ''; // 公告保存后的 id
  }
  componentDidMount() {
    try {
      this.clearStore();
      this.getRouterInfo();
      Promise.all([
        this.getTowTreeData(),
        this.getGpcatalog(),
        this.getAnnouncementType(),
        this.getPublishTypeConfig(),
      ]).then(() => {
        this.initForm();
      });
      this.getDistrictTree();
      this.initEditor();
      this.initGlggList();
      this.initMarking();
      this.fetchWorkflowInfo();
    } catch (error) {
      pushLog(JSON.stringify(error), 'error');
    }
  }


  // 获取工作流审核弹窗参数
  openAuditModal = async (announcementId) => {
    if (!await checkOpenAudit(announcementId)) return;
    const { dispatch } = this.props;
    // 先获取审核流弹窗参数
    dispatch({
      type: 'dynamicCreate/fetchauditParams',
      payload: {
        announcementId,
      },
    }).then((res = {}) => {
      if (res.success) {
        const auditParams = res?.result || {};
        this.setState({
          auditParams,
          createModalShow: true,
        });
      }
    });
  }

  // 获取工作流节点展示条件参数
  fetchWorkflowInfo = () => {
    const { dispatch } = this.props;
    const { announcementType } = this.routerInfo;
    dispatch({
      type: 'dynamicCreate/fetchWorkflowInfo',
      payload: {
        announcementType,
      },
    }).then((res = {}) => {
      if (res.success) {
        const workflowInfo = res?.result || {};
        this.setState({
          workflowInfo,
        });
      }
    });
  }

  getAuditConfig = async (districtCode) => { // 审核提交是否多选
    const res = await request('/announcement/config/getAnnouncementCheckConfig', {
      params: {
        districtCode: districtCode || this.routerInfo.districtId || window.currentUserDistrict.code,
        announcementType: this.routerInfo.announcementType,
      },
    });
    this.setState({
      supportCheckerMultiSelect: _Get(res, 'result.supportCheckerMultiSelect', false) || false,
    });
  }

  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (distCode) => {
    this.props.dispatch({
      type: 'flow/getIdentifications',
      payload: {
        distCode: distCode || this.routerInfo.districtId || window.currentUserDistrict.code,
        annType: this.routerInfo.announcementType,
        annId: this.id || 0,
      },
    }).then((res = {}) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }
  // 公告标识change
  onSelectMarking = (marking) => {
    const { markingList } = this.state;
    marking.isChoosed = !marking.isChoosed;
    const markingListHandel = markingList.filter(item => item.isChoosed && item.type === 2);
    if (markingListHandel.length > 4) {
      message.info('最多只能选取4个标识！');
    } else {
      this.setState({
        markingList: [...this.state.markingList],
      });
    }
  }
  // 获取监管信息
  initJgInfo = (districtCode) => {
    getEnvByDistrictAndType({
      districtCode,
      announcementType: this.routerInfo.announcementType,
    }).then((res = {}) => {
      if (res.result) {
        this.props.form.resetFields(['regulatoryContactAddr', 'regulatoryContactFax', 'regulatoryContactPerson', 'regulatoryContactPhone', 'regulatoryOrgName']);
        setTimeout(() => {
          this.setState({
            // eslint-disable-next-line react/no-unused-state
            envs: res.result.envs || {},
          });
        }, 100);
      } else {
        this.setState({
          // eslint-disable-next-line react/no-unused-state
          envs: {},
        });
      }
    });
  }
  // 电子卖场采购公告 - 获取项目所在行政区划选项下拉数据
  fetchTreeDataIfPro = () => {
    this.props.dispatch({
      type: 'flow/getDistTreeFilterCorrespondingLevel',
    });
  }
  // 获取动态表单数据
  initForm = () => {
    const payload = {
      announcementType: this.routerInfo.announcementType,
      orderId: this.routerInfo.orderId,
      isRelationProject: false,
      projectCode: this.routerInfo.projectCode,
    };
    if (this.routerInfo.projectCode) {
      payload.isRelationProject = true;
    }
    if (this.routerInfo.isRelationProject) {
      payload.isRelationProject = this.routerInfo.isRelationProject;
    }
    // 如果是从项目创建的公告，需要带上appCode（固定值）
    if (this.routerInfo.projectCode) {
      payload.appCode = 'zcy.project.manage';
    }
    // 如果从项目采购创建的公告，appCode取自url
    if (this.routerInfo.isRelationProject) {
      payload.appCode = this.routerInfo.appCode;
    }
    payload.districtCode = this.routerInfo.districtId || undefined;
    this.props.dispatch({
      type: 'flow/getDynamicForm',
      payload,
    }).then((res1 = {}) => {
      this.setState({
        submiting: 0,
      });
      if (res1.success) {
        // 更正类型
        const correction = res1.result.find(att => att.key === 'correctType');
        if (correction && correction.value) {
          this.setState({
            // eslint-disable-next-line react/no-unused-state
            correction: this.corrections(correction.value),
          });
        }
        const attachments = res1.result.find(att => att.key === 'attachments');
        if (attachments) {
          // 附件是否必填
          if (attachments.nullable === 2) {
            this.setState({
              attachmentsNeed: true,
            });
          }
          if (attachments.value) {
            const defaultFileList = JSON.parse(attachments.value);
            defaultFileList.forEach((item) => {
              item.isShow = true;
              item.size = parseInt(item.size, 10);
              item.status = 'done';
            });
            this.props.dispatch({
              type: 'flow/setFileList',
              payload: {
                fileInfo: {
                  fileList: [],
                  defaultFileList,
                },
              },
            });
          }
        }
        // 获取监管信息
        const districtCode = this.routerInfo.districtId || window.currentUserDistrict.code;
        this.initJgInfo(districtCode);
      } else {
        Modal.error({
          title: '获取模板信息失败',
          content: res1.error,
          closable: true,
          onOk: () => {
            this.goPageBack();
          },
        });
      }
    });
  }
  // 更正类型
  corrections = (data) => {
    data && data.map((i) => {
      i.label = i.desc;
      i.value = i.desc;
    });
    return data;
  }
  // 如果从项目创建，存在项目编号，且未更正公告，则初始化原公告列表
  initGlggList = () => {
    const proCode = this.routerInfo.projectCode;
    if (proCode && this.routerInfo.announcementType === '3005') {
      obtainAnnouncementByProCode({
        proCode,
      }).then((res = {}) => {
        if (res.success) {
          const list = res.result;
          for (let i = 0; i < list.length; i += 1) {
            list[i].value = list[i].outUrl;
            list[i].label = list[i].title;
          }
          this.setState({
            glggList: list,
          });
        }
      });
    }
  }
  // 清空缓存
  clearStore = () => {
    this.props.dispatch({
      type: 'flow/clearStore',
    });
  };
  // util
  GetQueryString = (name) => {
    const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
    const r = unescape(this.props.location.search).substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
  }
  // 获取路由参数
  getRouterInfo = () => {
    this.routerInfo = {
      announcementType: this.GetQueryString('announcementType'),
      orderId: this.GetQueryString('orderId'),
      projectCode: this.GetQueryString('projectCode'),
      projectName: this.GetQueryString('projectName'),
      districtId: this.GetQueryString('districtId'),
      annBigType: this.GetQueryString('annBigType'),
      isRelationProject: this.GetQueryString('isRelationProject'),
      appCode: this.GetQueryString('appCode'),
      ids: this.GetQueryString('ids'),
      title: this.GetQueryString('title'),
      bindProcurementPlan: this.GetQueryString('bindProcurementPlan'),
      canEditBindProcurementPlan: this.GetQueryString('canEditBindProcurementPlan'),
      serialType: this.GetQueryString('serialType'),
    };
    this.setState({
      // eslint-disable-next-line react/no-unused-state
      announcementType: this.routerInfo.announcementType,
    });
  }
  // 行政区划下拉列表
  getDistrictTree = () => {
    this.props.dispatch({
      type: 'flow/getDistTree',
      payload: {
        announcementType: this.routerInfo.announcementType,
      },
    }).then(() => {
      const district = this.routerInfo.districtId || window.currentUserDistrict.code;
      this.props.form.setFieldsValue({
        district,
      });
      this.getFixedTimeFlag(district);
    });
  }
  // 加载采购区域、采购类型数据
  getTowTreeData = () => {
    return this.props.dispatch({
      type: 'flow/obtainCGLXANDCGFS',
      payload: {
        districtCode: this.routerInfo.districtId || window.currentUserDistrict.code,
        annTypeId: this.routerInfo.announcementType,
      },
    });
  }
  // 加载采购目录数据
  getGpcatalog = (code) => {
    if (code) {
      this.props.form.setFieldsValue({ gpCatalogName: undefined });
    }
    return this.props.dispatch({
      type: 'flow/getGpcatalog',
      payload: {
        districtCode: code || this.routerInfo.districtId || window.currentUserDistrict.code,
        year: new Date().getFullYear(),
      },
    });
  }
  // 获取配置信息
  getAnnouncementType = () => {
    this.props.dispatch({
      type: 'flow/getAnnouncementType',
      payload: {
        id: this.routerInfo.ids,
        typeId: this.routerInfo.announcementType,
      },
    }).then((data) => {
      const config = data?.result || {};
      this.setState({
        config,
        // eslint-disable-next-line react/no-unused-state
        isLinkPurchase: `${config.isForcedControl}`,
      });
    });
  }
  // 返回按钮
  handelBack = () => {
    if (!this.state.preview) {
      Modal.confirm({
        title: '确认离开当前页面？',
        content: '如果返回，您更改的数据将会丢失',
        onOk: () => {
          this.goPageBack();
        },
      });
    } else {
      this.setState({
        preview: false,
      });
    }
  }
  goPageBack = (announcementId) => {
    const { history } = this.props;
    if (history.length <= 1 && announcementId) {
      history.replace(`/detail/${this.routerInfo.annBigType}/${announcementId}`);
      return;
    }
    history.goBack();
  }

  titleSensitiveWords = (cb) => {
    const { getFieldValue } = this.props.form;
    getSensitiveWords({
      announcementType: this.routerInfo.announcementType,
      content: getFieldValue('title'),
      district: getFieldValue('district'),
    }).then((res) => {
      if (!res) return;
      // eslint-disable-next-line react/no-unused-state
      this.setState({ sensitiveWords: res.blockWords }, () => {
        cb && cb();
      });
    });
  }

  announcementCensor = () => {
    this.props.form.validateFields({ force: true }, async (err, { district }) => {
      if (err) {
        message.destroy();
        message.warning('请完善信息！');
        return;
      }
      await this.getAuditConfig(district);
      submit(2, this, async (id) => {
        this.openAuditModal(id);
      });
    });
  }

  getPublishTypeConfig = (nextDistrictId) => {
    const { announcementType, districtId } = this.routerInfo;
    const districtCode = nextDistrictId || districtId || window.currentUserDistrict.code;
    this.props.dispatch({
      type: 'openConfig/getPublishTypeConfig',
      payload: {
        announcementType,
        districtCode,
      },
    }).then(({ publishTypeDefaultValue }) => {
      this.setState({
      // eslint-disable-next-line react/no-unused-state
        pubType: publishTypeDefaultValue,
      });
    });
  }

  listSensitiveWords = ({ title, content, district, type }) => {
    return getSensitiveWords({
      content: `${title}--${content}`,
      district,
      announcementType: this.routerInfo.announcementType,
    }).then((res) => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;

      const {
        contentWithSensitiveTag,
      } = attachSensitiveWordsTag(blockWords, remindWords, content);


      this.ueditor?.setContent(contentWithSensitiveTag);
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        hasRemind,
        // eslint-disable-next-line react/no-unused-state
        hasBlock,
      });
      if (type === 1) {
        return Promise.resolve(true);
      }
      if (hasBlock || hasRemind) {
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }
      return Promise.resolve(true);
    });
  }

  // 提交公告，判断是否存在id
  submitAnn = (json = {}) => {
    if (json.content) {
      json.content = clearSensitiveTag(json.content);
    }
    json.processFrontParam = JSON.stringify(this.state.processFrontParam);
    if (json.id) {
      modifyAnnouncement(json).then((res) => {
        this.setState({
          submiting: 0,
        });
        if (res.success) {
          Modal.success({
            title: '提示',
            content: '提交成功！',
            onOk: () => {
              this.goPageBack(json.id);
            },
          });
        } else {
          Modal.error({
            title: '提示',
            content: res.error,
          });
        }
      });
    } else {
      submitAnnouncement(json).then((res) => {
        this.setState({
          submiting: 0,
        });
        if (res.success) {
          Modal.success({
            title: '提示',
            content: '提交成功！',
            onOk: () => {
              this.goPageBack(res.result);
            },
          });
        } else {
          Modal.error({
            title: '提示',
            content: res.error,
          });
        }
      });
    }
  }
  // 验证是否非工作日
  checkIsWorkday = (date, key) => {
    // 招标文件发售开始/结束日期
    if (['saleStartDate', 'saleEndDate'].indexOf(key) !== -1) {
      const strDate = moment(date).format('YYYY-MM-DD HH:mm:ss');
      checkWorkdayByDate({
        strDate,
        districtCode: this.routerInfo.districtId || window.currentUserDistrict.code,
      }).then((res) => {
        if (!res.success) {
          Modal.confirm({
            title: '您选择的是非工作日',
            content: '是否重新选择日期？',
            onOk: () => {
              this.props.form.resetFields([key]);
            },
          });
        }
      });
    }
  }
  // 监听采购项目编号change事件
  handelXmbhChange = (e) => {
    const proCode = e.target.value;

    if (proCode) {
      this.props.form.resetFields(['glygg']);
      this.glgg = null;
      obtainAnnouncementByProCode({
        proCode,
      }).then((res) => {
        if (res.success && res.result) {
          const list = res.result;
          for (let i = 0; i < list.length; i += 1) {
            list[i].value = list[i].outUrl;
            list[i].label = list[i].title;
          }
          this.setState({
            glggList: list,
          });
        }
      });
    }
  }

  // 树菜单change
  onTreeChange = (value, label, extra, key) => {
    if (key === 'disForPro') {
      return this.onDisForProChange;
    }
    if (key === 'relateOldNotice') {
      return this.onRelateOldNoticeChange;
    }
    if (key === 'qualificationType' && value === 1) {
      this.setState({ qualificationType: true });
    } else if (key === 'qualificationType' && value !== 1) {
      this.setState({ qualificationType: false });
    }
    if (key === 'gpCatalogName') {
      return (v) => {
        this.setState({
          // eslint-disable-next-line react/no-unused-state
          gpCatalogStartChart: (Array.isArray(v) && v.length) ? v[0].substr(0, 1) : '',
        });
      };
    }
    return undefined;
  }
  onRelateOldNoticeChange = (value) => {
    const { glggList } = this.state;
    const selected = glggList.find(glgg => glgg.outUrl === value);
    this.props.form.setFieldsValue({
      oldNoticePubDate: selected ? selected.releasedAt : undefined,
    });
  }
  onDisForProChange = (value, label) => {
    if (Array.isArray(label)) {
      this.disNameForPro = label[0];
    }
  }
  bidSum = () => {
    const { tableData } = this.props.flow;
    const { announcementType } = this.routerInfo;
    // 终止 3007
    if (announcementType === '3007') {
      return this.props.form.getFieldValue('budgetPrice') || 0;
    }
    let sum = 0;
    if (noticeKeysMap[announcementType]) {
      const array = tableData[noticeKeysMap[announcementType].key] || [];
      sum = array.reduce((prev, cur) => {
        prev += cur.formValue[noticeKeysMap[announcementType].col] || 0;
        return prev;
      }, 0);
    }
    return sum;
  }
  // 审核弹窗确认 + 提交保存
  createModalOk = (processFrontParam) => {
    this.setState({
      createModalShow: false,
      processFrontParam,
    }, () => {
      submit(3, this);
    });
  }
  // 审核弹窗取消
  createModalCancel = () => {
    this.setState({
      createModalShow: false,
    });
  }
  getTitlePlaceholder = (placeholder) => {
    const orgName = window.currentUserIdentity.orgName || '';// 采购代理机构
    const projectName = this.routerInfo.projectName || '';// 项目名称
    const announcementTypeName = this.state.config.name || '';
    let noteText = '';
    if (placeholder) {
      noteText = '请输入标题，例：';
    }
    return `${noteText}${orgName}${projectName ? `关于${projectName}的` : ''}${announcementTypeName}`;
  }
  componentWillUnmount() {
    try {
      window.UE?.delEditor('ueditor', {
        autoHeight: false,
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  initEditor = () => {
    try {
      loadEditor().then((res) => {
        if (res) {
          this.ueditor = window.UE.getEditor('ueditor', {
            allowDivTransToP: false,
            initialStyle: SensitiveEditorStyle,
          });
        }
        if (!res) return pushLog('初始化富文本编辑失败', 'warning');
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  clearPurchaseName = () => {
    this.props.form.setFieldsValue({
      purchaseName: {},
    });
  }
  titleChange = (v, vs) => {
    // 如果公告标题中的起止月份相同，则 purStartAndEndDate 只展示一个月份，若起止月份不同，则以“至”连接两个时间
    let purStartAndEndDate = '';
    if (vs[1] && vs[3]) {
      this.props.form.setFieldsValue({
        openingTimeOfIntention: `${vs[1]}年${vs[3]}月`,
      });
      purStartAndEndDate = `${vs[1]}年${vs[3]}月`;
    }
    if (vs[1] && vs[5]) {
      this.props.form.setFieldsValue({
        deadlineForIntention2Disclose: `${vs[1]}年${vs[5]}月`,
      });
      purStartAndEndDate += `至${vs[5]}月`;
    }
    if (vs[1] && vs[3] && vs.length === 5) {
      this.props.form.setFieldsValue({
        openingTimeOfIntention: `${vs[1]}年${vs[3]}月`,
        deadlineForIntention2Disclose: `${vs[1]}年${vs[3]}月`,
      });
      purStartAndEndDate = `${vs[1]}年${vs[3]}月`;
    }
    this.props.form.setFieldsValue({
      purStartAndEndDate,
    });
  }
  getConfig = async (data) => {
    const res = await request('/announcement/config/getAnnouncementConfig', {
      method: 'post',
      data,
    });
    try {
      this.setState({
        config: {
          ...this.state.config,
          ...res.result,
        },
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }
  // 通过districtCode判断发布类型是否禁用/选择定时发布
  getFixedTimeFlag = async (data) => {
    const { openConfig } = this.props;
    const { publishTypeOptions = [] } = openConfig;
    try {
      const res = await request(`/announcement/open/getFixedTimeFlag?districtCode=${data}`);
      if (!res.success) {
        message.error(res.error);
        return;
      }
      // result: true不可选，false可选
      const { result = false } = res || {};
      this.setState({
        isPubTypeDisabled: result,
      });
      if (result) {
        if (publishTypeOptions.some(ele => +ele.v === 1)) {
          this.setState({
            // eslint-disable-next-line react/no-unused-state
            pubType: 1,
          });
        }
      }
    } catch (res) {
      const { data: { error } = {} } = res || {};
      message.error(error);
    }
  }
  timeValidator = (rule, value, callback) => {
    const { isPubTypeDisabled } = this.state;
    if (isPubTypeDisabled) {
      if (value) {
        const date = new Date(value);
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const time = hours + ':' + minutes;
        if (time !== '22:0') {
          callback('只能选择晚上10点');
        }
        callback();
      } else {
        callback();
      }
    } else {
      callback();
    }
  }
  
  handlePopupAndSubmit = async (districtCode, callback) => {
    try {
      const { dispatch } = this.props;
      const res = await dispatch({
        type: 'flow/handleNonGovernmentProcurement',
        payload: {
          districtCode,
          announcementType: this.routerInfo.announcementType,
          ifManualAnnouncement: !this.routerInfo.projectCode,
        },
      });

      if (res.success) {
        const { needPopUpWindow, whetherToSettleIn, isLeCaiYun } = res.result;

        this.setState({
          isShowDrainageModal: needPopUpWindow,
          whetherToSettleIn,
          isLeCaiYun,
        }, () => {
          // 确保状态更新后才执行回调
          if (callback && !needPopUpWindow) {
            callback();
          }
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }

  handleNonGovDrainageModal = async (districtCode) => {
    await this.handlePopupAndSubmit(districtCode);
  }

  handleCreateAnn = async () => {
    const { form } = this.props;
    if (form && form.getFieldValue) {
      // const districtCode = form.getFieldValue('district');
      this.titleSensitiveWords(() => submit(1, this));
      // 显示乐彩云弹窗代码
      // await this.handlePopupAndSubmit(
      //   districtCode, 
      //   () => this.titleSensitiveWords(() => submit(1, this))
      // );
    }
  }
  render() {
    const { flow: { districtTree }, form, history } = this.props;
    const {
      markingList, sensitiveVisible, sensitiveMsg,
      config, supportCheckerMultiSelect, submiting, preview,
      workflowInfo, auditParams, createModalShow, isShowDrainageModal,
      whetherToSettleIn, isLeCaiYun,
    } = this.state;
    const { attachmentShow, attachmentExternalDisplayRules, attachmentUploadDesc } = config ?? {};
    const { district } = form.getFieldsValue(['district']) || {};
    const { qualificationType } = this.state;
    return (
      <div className="announcement-flow" id="dynamicForm">
        <Spin spinning={this.state.submiting !== 0}>
          <ZcyBreadcrumb
            routes={[{
              label: config.name,
            }, {
              label: '创建',
            }]
            }
            globalBtn={getGlobalBtn({
              districtTree,
              submiting,
              preview,
              submit: (type) => {
                submit(type, this);
              },
              announcementCensor: this.announcementCensor,
              handelBack: this.handelBack,
              titleSensitiveWords: this.titleSensitiveWords,
              handleCreateAnn: this.handleCreateAnn,
            })}
          />
          <AnnSteps
            processDefineKey={workflowInfo.processDefineKey}
            districtCode={district}
            conditions={workflowInfo}
          />
          {/* 动态表单及富文本组件渲染处 */}
          <CreateDynamicFormPanel _this={this} />

          {
            attachmentShow ? (
              <FormUpload
                required={this.state.attachmentsNeed}
                announcementType={this.routerInfo.announcementType}
                districtCode={district}
                editable={!this.state.preview || this.state.editable}
                qualificationType={qualificationType}
                attachmentExternalDisplayRules={attachmentExternalDisplayRules}
                attachmentUploadDesc={attachmentUploadDesc}
              />
            ) : null
          }
          
          <Marking
            markingList={markingList}
            onChange={this.onSelectMarking}
            disabled={this.state.preview && !this.state.editable}
          />
        </Spin>
        {/* 该敏感词组件没用？ */}
        <SensitiveAlert
          visible={sensitiveVisible}
          sensitiveMsg={sensitiveMsg}
          onOk={() => {
            this.setState({
              sensitiveVisible: false,
            });
            this.setState({
              createModalShow: true,
            });
          }}
          onCancel={() => {
            this.setState({
              sensitiveVisible: false,
            });
          }}
        />
        {
          createModalShow && (
            <AuditModal
              visible={createModalShow}
              config={auditParams}
              onCancel={this.createModalCancel}
              onOk={this.createModalOk}
              showUpload={false}
              ModalProps={{
                confirmLoading: false,
                title: '提交',
              }}
              isUserRadio={!supportCheckerMultiSelect}
              tips={config.isNotify ? (
                <Alert
                  message="本公告已开启短信通知服务，请于本公告发布成功后，在列表页选择通知的对象。"
                  type="warning"
                  style={{ marginBottom: '10px' }}
                />
              ) : undefined}
            />
          )
        }
        {isShowDrainageModal && (
          <LcyDrainageModal
            visible={isShowDrainageModal}
            whetherToSettleIn={whetherToSettleIn}
            isLeCaiYun={isLeCaiYun}
            annBigType={this.routerInfo.annBigType}
            okText={whetherToSettleIn ? '立即跳转"乐采云平台"' : '跳转到乐采云登录页面'}
            handleBack={() => {
              history.goBack();
            }}
          />
        )}
      </div>
    );
  }
}
