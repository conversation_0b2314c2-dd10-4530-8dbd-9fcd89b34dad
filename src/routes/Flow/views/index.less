.step-1 {
  margin-top: 10px;
  .input-center {
    width: 66.6667%;
  }
  .item-title {
    .doraemon-form-item-label {
      text-align: left;
      label {
        font-size: 14px;
      }
    }
  }
  .add-more {
    text-align: center;
  }
}

.announcement-flow {
  #ueditor {
    border-radius: 6px;
    iframe {
      width: calc(100% - 2px);
      background-color: #fff;
    }
    .edui-editor,
    .edui-editor-iframeholder {
      width: auto !important;
    }

  }
  .edui-editor {
    border: 1px solid #d4d4d4;
    border-radius: 6px;
  }
  .doraemon-table-placeholder .empty-wrap {
    margin: 0 auto;
    i {
      font-size: 22px;
    }
    >div {
      font-size: 12px;
      margin-top: 0;
    }
  }
  .doraemon-pro-timeline {
    margin-top: 20px;
  }
  .ann-header {
    .ann-title {
      font-size: 16px;
      margin-bottom: 10px;
      color: #202020;
      .doraemon-tag {
        float: right;
      }
    }
    .ann-des {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      .zcyicon {
        margin-right: 10px;
      }
      span {
        margin-right: 20px;
      }
      > i.anticon {
        margin-right: 5px;
      }
    }
  }
  .show {
    display: block;
    clear: both;
  }
  .hide {
    display: none;
  }
  .timeCom {
    .doraemon-picker {
      display: none;
    }
    .doraemon-select {
      width: 100%;
      .doraemon-select-selection {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
      }
    }
  }
  .timeCom.hastime {
    .doraemon-picker {
      display: inline-block;
      width: 70%;
    }
    .doraemon-select {
      width: 30%;
      .doraemon-select-selection {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: none;
      }
    }
  }
}

.form-base {
  .ant-time-picker.times {
    input {
      width: 128px;
    }
  }
  .doraemon-form-item-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .tag-input {
    .doraemon-form-item-control {
      height: 39px;
    }
  }
}

.form-file {
  .upload-note {
    margin-left: 20px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
  }
}

.doraemon-upload-download {
  a {
    // margin-right: 15px;
  }
}

ul.file-list {
  margin-top: 10px;
  li {
    .inline {
      display: inline-block;
      background-color: #f7f7f7;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 5px;
      transition: background-color .3s ease-in-out;
      &:hover {
        background-color: #f5f6fa;
        .file-del {
          opacity: 1;
        }
      }
      .file-name {
        margin-left: 10px;
        margin-right: 20px;
      }
      .file-del {
        opacity: 0;
        margin-left: 20px;
        transition: opacity .3s ease-in-out;
      }
    }
    .upload-error {
      font-size: 12px;
      color: #ff3100;
    }
  }
  .doraemon-progress {
    margin-right: -30px;
  }
}

.fileList {
  .file-name {
    margin-left: 10px;
    margin-right: 20px;
  }
}

span.need {
  &::before {
    display: inline-block;
    margin-right: 4px;
    content: "*";
    font-family: SimSun;
    line-height: 1;
    font-size: 14px;
    color: #fb607e;
  }
}

.form-table {
  .table-title {
    padding: 13px 16px;
    span.title {
      font-size: 14px;
      font-weight: bold;
    }
    button {
      float: right;
    }
  }
  .table-input {
    width: 100%;
  }
}

.doraemon-select-tree-dropdown {
  max-height: 300px !important;
}

li.doraemon-select-tree-treenode-disabled {
  .doraemon-select-tree-title {
    color: rgba(0, 0, 0, 0.65) !important;
  }
}

.create-textarea {
  .doraemon-input-textarea {
    width: 700px;
  }
}

.doraemon-select {
  outline: none;
}
.formgrid {
  margin: 10px 0;
}
.editorPanel {
  .doraemon-panel-header-title {
    display: block;
  }
}
#dynamicForm {
  .doraemon-form-grid-bordered .doraemon-form-item-label label {
    line-height: 1.6;
  }
}
