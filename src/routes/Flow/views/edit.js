import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Panel, Form, ZcyBreadcrumb, message, Modal,
  Spin, Alert, request,
} from 'doraemon';
import { AuditModal } from '@zcy/zcy-audit-flow-back';
import _Get from 'lodash/get';
import {
  obtainAnnouncementDetail, getTreeNameByCode,
} from '../services';
import AnnSteps from '../../DynamicAnnouncementEdit/components/AnnSteps';
import FormUpload from './components/form-upload';
import SensitiveAlert from './components/sensitiveAlert';
import Marking from './components/marking';
import pushLog from 'utils/pushLog';
import { annBigTypeName, NO50ANN } from '../configs/const';
import EditForm from './components/edit-form';
import './index.less';
import tracer from 'src/utils/tracer';
import loadEditor from '@zcy/zcy-ueditor-front';
import { SensitiveEditorStyle, clearSensitiveTag, getSensitiveWords, attachSensitiveWordsTag, showSensitiveMsg, renderAlert } from 'src/utils/listSensitiveWords';
import { checkOpenAudit } from '../configs/utils';
import { LcyDrainageModal } from '@/components/LcyDrainageModal';
import handlerAttachmentContent from 'src/utils/handlerAttachmentContent';

@connect(({ flow, loading, openConfig }) => ({
  flow,
  loading: loading.models.flow,
  openConfig,
}))
@Form.create()
export default class Edit extends Component {
  constructor(props) {
    super(props);
    this.districtCode = null;
    this.state = {
      annBigType: this.props.match.params.annBigType,
      // eslint-disable-next-line react/no-unused-state
      districtTree: [],
      detail: {
        content: '',
      },
      createModalShow: false,
      sensitiveVisible: false,
      sensitiveMsg: '',
      markingList: [],
      hasRemind: false,
      hasBlock: false,
      /**
       * 标题的禁止级敏感词
       * 
      */
      // eslint-disable-next-line react/no-unused-state
      sensitiveWords: [],
      config: {},
      supportCheckerMultiSelect: false,
      workflowInfo: {}, // 工作流节点参数
      auditParams: {}, // 审核弹窗参数
      processFrontParam: {}, // 选择的审核人信息
      isShowDrainageModal: false, // 是否展示引流乐采云弹窗
      whetherToSettleIn: false, // 是否已入驻乐采云
    };
  }
  componentDidMount() {
    // 清空file
    this.props.dispatch({
      type: 'flow/setFileList',
      payload: {
        fileInfo: {
          fileList: [],
          defaultFileList: [],
        },
      },
    });
    obtainAnnouncementDetail({
      announcementId: this.props.match.params.announcementId,
    }).then((res = {}) => {
      try {
        // 去除之前生成模板的文件信息
        if (res.result?.content?.indexOf('<p style="font-size" class="fjxx">附件信息：</p>') !== -1) {
          res.result.content = res.result.content.split('<p style="font-size" class="fjxx">附件信息：</p>')[0];
        }
        if (res.result?.content?.indexOf("<p style='font-size' class='fjxx'>附件信息：</p>") !== -1) {
          res.result.content = res.result.content.split("<p style='font-size' class='fjxx'>附件信息：</p>")[0];
        }
        if (res.result?.content?.indexOf('<divider></divider>') !== -1) {
          res.result.content = res.result.content.split('<divider></divider>')[0];
        }
        const metaData = res.result.metaData ? JSON.parse(res.result.metaData) : {};
        try {
          loadEditor().then((ue) => {
            if (ue) {
              window.UE.getEditor('ueditor', {
                allowDivTransToP: false,
                // 注入敏感词提示css
                initialStyle: SensitiveEditorStyle,
              }).ready(() => {
                this.ueditor = window.UE.getEditor('ueditor');
                this.listSensitiveWords({
                  title: res.result.title,
                  district: res.result.district,
                  content: res.result.content,
                  type: 1,
                  announcementType: res.result.announcementType,
                });
                this.getConfig({
                  announcementType: _Get(res, 'result.announcementType'),
                  districtCode: _Get(res, 'result.district'),
                  metadata: metaData,
                });
              });
            }
            if (!ue) return pushLog('初始化富文本编辑失败', 'warning');
          });
        } catch (error) {
          pushLog(JSON.stringify(error), 'info');
        }
        // file
        const defaultFileList = res.result.attachments || [];
        defaultFileList.forEach((item) => {
          item.status = 'done';
          item.noPublic = !item.isShow;
        });
        this.props.dispatch({
          type: 'flow/setFileList',
          payload: {
            fileInfo: {
              fileList: [],
              defaultFileList,
            },
          },
        });
        this.props.dispatch({
          type: 'openConfig/getPublishTypeConfig',
          payload: {
            announcementType: _Get(res, 'result.announcementType'),
            districtCode: _Get(res, 'result.district'),
          },
        }).then(({
          publishTypeOptions,
        }) => {
          let detailPubType = res.result.pubType;
          if (detailPubType && publishTypeOptions.some(ele => +ele.v === detailPubType)) {
            //
          } else {
            detailPubType = undefined;
          }

          this.setState({
            detail: {
              ...res.result,
              pubType: detailPubType,
            },
          });
        });

        // 获取流程节点
        this.fetchWorkflowInfo(res.result);
        getTreeNameByCode({
          code: res.result.district,
        }).then((res1 = {}) => {
          this.districtCode = res1.data && res1.data.code;
          this.setState({
            // eslint-disable-next-line react/no-unused-state
            districtName: res1.data && res1.data.name,
          });
        });
        this.districtCode = res.result.district;
        this.initMarking({
          distCode: res.result.district,
          annType: res.result.announcementType,
          annId: res.result.id,
        });
      } catch (error) {
        pushLog(JSON.stringify(error, 'info'));
      }
    });
  }
  componentWillUnmount() {
    try {
      window.UE?.delEditor('ueditor');
    } catch (error) {
      pushLog(JSON.stringify(error, 'info'));
    }
  }


  // 获取工作流审核弹窗参数
  openAuditModal = async (announcementId) => {
    if (!await checkOpenAudit(announcementId)) return;
    const { dispatch } = this.props;
    dispatch({
      type: 'dynamicCreate/fetchauditParams',
      payload: {
        announcementId,
      },
    }).then((res = {}) => {
      if (res.success) {
        const auditParams = res?.result || {};
        this.setState({
          auditParams,
          createModalShow: true,
        });
      }
    });
  }


  // 获取工作流节点展示条件参数
  fetchWorkflowInfo = ({ id, announcementType }) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'dynamicCreate/fetchWorkflowInfo',
      payload: {
        id,
        announcementType,
      },
    }).then((res = {}) => {
      if (res.success) {
        const workflowInfo = res?.result || {};
        this.setState({
          workflowInfo,
        });
      }
    });
  }


  goPageBack = (announcementId) => {
    const { history } = this.props;
    if (history.length <= 1 && announcementId) {
      history.replace(`/detail/${this.state.annBigType}/${announcementId}`);
      return;
    }
    history.goBack();
  }
  getAuditConfig = async (values) => { // 审核提交是否多选
    const res = await request('/announcement/config/getAnnouncementCheckConfig', {
      params: {
        districtCode: values.district || window.currentUserDistrict.code,
        announcementType: this.state.detail.announcementType,
      },
    });
    this.setState({
      supportCheckerMultiSelect: _Get(res, 'result.supportCheckerMultiSelect', false) || false,
    });
  }
  // 获取公告标识信息，没有公告id时annId传0，区划参数需跟随公告区划
  initMarking = (payload) => {
    this.props.dispatch({
      type: 'flow/getIdentifications',
      payload,
    }).then((res) => {
      if (res.success) {
        this.setState({
          markingList: res.result || [],
        });
      } else {
        Modal.error({
          title: '提示',
          content: res.error,
        });
      }
    });
  }
  // 公告标识change
  onSelectMarking = (marking) => {
    const { markingList } = this.state;
    marking.isChoosed = !marking.isChoosed;
    const markingListHandel = markingList.filter(item => item.isChoosed && item.type === 2);
    if (markingListHandel.length > 4) {
      message.info('最多只能选取4个标识！');
    } else {
      this.setState({
        markingList: [...this.state.markingList],
      });
    }
  }

  // 选择审核人弹窗
  createModalOk = (processFrontParam) => {
    this.setState({
      createModalShow: false,
      processFrontParam,
    }, () => {
      this.submit(2);
    });
  }
  // 选择审核人弹窗
  createModalCancel = () => {
    this.setState({
      createModalShow: false,
    });
  }
  listSensitiveWords = ({ title, content, district, type, announcementType }) => {
    return getSensitiveWords({
      announcementType: announcementType || this.state.detail.announcementType,
      content: `${title}--${content}`,
      district,
    }).then((res) => {
      if (!res) return;
      const {
        hasBlock,
        hasRemind,
        blockWords,
        remindWords,
        remindMessageHtml,
        blockMessageHtml,
      } = res;

      const {
        contentWithSensitiveTag,
      } = attachSensitiveWordsTag(blockWords, remindWords, content);

      this.ueditor.setContent(contentWithSensitiveTag);
      this.setState({
        hasRemind,
        hasBlock,
      }, () => {
        // 敏感词信息更新之后触发一下校验
        this.props.form.validateFieldsAndScroll({ force: true });
      });
      
      if (type === 1) {
        return Promise.resolve(true);
      }
      if (hasBlock || hasRemind) {
        return showSensitiveMsg(hasBlock, hasRemind, {
          remindMessageHtml,
          blockMessageHtml,
        });
      }
      return Promise.resolve(true);
    });
  }

  // 1: 暂存 2:提交
  submit = (type, callback) => {
    if (!this.state.detail.pubType) {
      message.error('请选择发布时间');
      return;
    }
    try {
      // 修复table预览无边框样式
      let content = this.ueditor.getContent();
      const fixTableStyle = '<style id="fixTableStyle" type="text/css">th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
      const hasFixed = content?.indexOf('fixTableStyle');
      if (hasFixed === -1) {
        content = fixTableStyle + content;
      }
      // 公告标识
      const { markingList, supportCheckerMultiSelect } = this.state;
      const selectMarking = markingList.filter(item => item.isChoosed && item.type === 2);
      const identificationIds = selectMarking.map(item => item.id);
      if (type === 1) {
        this.props.form.validateFields({ force: true }, (err, values) => {
          // 是否需要上传文件
          const fileList = [...this.props.flow.fileInfo.defaultFileList,
            ...this.props.flow.fileInfo.fileList];
          const validFile = fileList.filter(item => item.status === 'done') || [];
          if (!err) {
            const data = { ...values };
            // 处理区划初始值
            if (this.districtCode !== null) {
              data.district = this.districtCode;
            }
            // 处理文件
            const attachments = [];
            validFile.forEach((fl) => {
              attachments.push({
                fileId: fl.fileId,
                isShow: !(fl.noPublic === true),
                name: fl.name,
                size: fl.size,
              });
            });
            this.listSensitiveWords({
              title: values.title,
              district: data.district,
              content,
            }).then((isValid) => {
              if (!isValid) return;
              if (!(content?.length)) {
                pushLog(JSON.stringify({
                  type: '手工公告-content为空（content）',
                }), 'warning');
              }
              this.props.dispatch({
                type: 'flow/tempSaveAnnouncement',
                payload: {
                  announcementType: this.state.detail.announcementType,
                  attachments,
                  content: clearSensitiveTag(content),
                  district: data.district,
                  id: this.state.detail.id,
                  projectCode: this.state.detail.projectCode,
                  projectName: this.state.detail.projectName,
                  pubType: this.state.detail.pubType,
                  releasedAt: this.state.detail.pubType !== 1 ? '' : data.releasedAt,
                  showDuration: this.state.detail.showDuration,
                  status: this.state.detail.status,
                  title: data.title,
                  identificationIds,
                },
              }).then((res) => {
                if (res.success) {
                  if (callback) {
                    callback(this.state.detail.id);
                  } else {
                    this.setState({
                      createModalShow: false,
                    });
                    message.success('暂存成功！');
                  }
                } else {
                  Modal.error({
                    title: '提示',
                    content: res.error,
                  });
                }
              });
            });
          } else {
            message.warning('请完善信息！');
          }
        });
      } else {
        this.props.form.validateFields({ force: true }, (err, values) => {
          if (!err) {
            const data = { ...values };
            // 处理区划初始值
            if (this.districtCode !== null) {
              data.district = this.districtCode;
            }
            // 是否需要上传文件
            const fileList = [...this.props.flow.fileInfo.defaultFileList,
              ...this.props.flow.fileInfo.fileList];
            const validFile = fileList.filter(item => item.status === 'done') || [];
            // 处理文件
            const attachments = [];
            validFile.forEach((fl) => {
              attachments.push({
                fileId: fl.fileId,
                isShow: !(fl.noPublic === true),
                name: fl.name,
                size: fl.size,
              });
            });
            const needShowFiles = attachments.filter(fl => fl.isShow);

            handlerAttachmentContent(content, needShowFiles).then((attachFileHtml) => {
              this.props.dispatch({
                type: 'flow/modifyAnnouncement',
                payload: {
                  announcementType: this.state.detail.announcementType,
                  attachments,
                  content: clearSensitiveTag(attachFileHtml),
                  district: data.district,
                  id: this.state.detail.id,
                  projectCode: this.state.detail.projectCode,
                  projectName: this.state.detail.projectName,
                  pubType: this.state.detail.pubType,
                  releasedAt: this.state.detail.pubType !== 1 ? '' : data.releasedAt,
                  showDuration: this.state.detail.showDuration,
                  title: data.title,
                  nextTaskUserId: supportCheckerMultiSelect ? undefined : this.nextTaskUserId,
                  nextTaskUserIds: supportCheckerMultiSelect ?
                    (Array.isArray(this.nextTaskUserId) ? this.nextTaskUserId :
                      [this.nextTaskUserId]) : undefined,
                  identificationIds,
                  processFrontParam: JSON.stringify(this.state.processFrontParam),
                },
              }).then((res) => {
                if (res.success) {
                  message.success('提交成功！');
                  this.goPageBack(this.state.detail.id);
                } else {
                  Modal.error({
                    title: '提示',
                    content: res.error,
                  });
                }
              });
            });
          }
        });
      }
    } catch (error) {
      pushLog(JSON.stringify(error, 'info'));
    }
  }

  // 暂存按钮click
  onTempSave = () => {
    try {
      tracer({
        utmCD: ['cBreadcrumb', 'dTempSaveBtn'],
        business: {
          keyWord: this.state.annBigType,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }

    // 是否需要上传文件
    const fileList = [...this.props.flow.fileInfo.defaultFileList,
      ...this.props.flow.fileInfo.fileList];
    const validFile = fileList.filter(item => item.status === 'done') || [];
    if (this.state.detail.hasAttachment) {
      if (validFile.length === 0) {
        Modal.error({
          title: '提示',
          content: '请上传文件！',
        });
        return;
      }
    }

    if (this.state.config.attachmentExternalDisplayRules === 2
      && validFile.some(file => file.noPublic)) {
      Modal.error({
        title: '提示',
        content: '请勾选外网展示！',
      });
      return;
    }
    return this.handleTitleSensitiveWords(() => this.submit(1));
  }

  handleTitleSensitiveWords = (cb) => {
    const { getFieldValue } = this.props.form;
    getSensitiveWords({
      announcementType: this.state.detail.announcementType,
      content: getFieldValue('title'),
      district: getFieldValue('district'),
    }).then((res) => {
      if (!res) return;
      // eslint-disable-next-line react/no-unused-state
      this.setState({ sensitiveWords: res.blockWords }, () => {
        cb && cb();
      });
    });
  } 

  onSubmit = async () => {
    // 显示乐彩云弹窗代码
    // const { detail } = this.state;
    try {
      tracer({
        utmCD: ['cBreadcrumb', 'dSaveBtn'],
        business: {
          keyWord: this.state.annBigType,
        },
      });
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
    const onSubmitAfterCheck = () => this.props.form.validateFields({ force: true },
      async (err, values) => {
        if (!err) {
          await this.getAuditConfig(values);
          // 是否需要上传文件
          const fileList = [...this.props.flow.fileInfo.defaultFileList,
            ...this.props.flow.fileInfo.fileList];
          const validFile = fileList.filter(item => item.status === 'done') || [];
          if (this.state.detail.hasAttachment) {
            if (validFile.length === 0) {
              Modal.error({
                title: '提示',
                content: '请上传文件！',
              });
              return;
            }
          }

          if (this.state.config.attachmentExternalDisplayRules === 2
            && validFile.some(file => file.noPublic)) {
            Modal.error({
              title: '提示',
              content: '请勾选外网展示！',
            });
            return;
          }
          
          // 显示乐彩云弹窗代码
          // const { dispatch } = this.props;
          // const res = await dispatch({
          //   type: 'flow/handleNonGovernmentProcurement',
          //   payload: {
          //     districtCode: detail.district,
          //     announcementType: detail.announcementType,
          //   },
          // });
          // const { result: { needPopUpWindow, whetherToSettleIn } } = res;
          // if (res.success) {
          //   this.setState({ 
          //     isShowDrainageModal: needPopUpWindow, 
          //     whetherToSettleIn,
          //   });
          //   if (!needPopUpWindow) {
          this.submit(1, id => this.openAuditModal(id));
          //   }
          // }
        } else {
          message.warning('请完善信息！');
        }
      });

    this.handleTitleSensitiveWords(onSubmitAfterCheck);
  }

  getConfig = async (data) => {
    const res = await request('/announcement/config/getAnnouncementConfig', {
      method: 'post',
      data,
    });
    this.setState({
      config: {
        ...res.result,
      },
    });
    if (!_Get(res, 'result.secondEdit')) {
      try {
        window.UE.getEditor('ueditor').setDisabled('fullscreen');
      } catch (error) {
        pushLog(JSON.stringify(error, 'info'));
      }
    }
  }

  render() {
    const { detail, annBigType, sensitiveVisible, sensitiveMsg,
      markingList, hasRemind, hasBlock, config, createModalShow,
      auditParams,
      supportCheckerMultiSelect, workflowInfo, 
      isShowDrainageModal, whetherToSettleIn,
    } = this.state;
    const { loading, history } = this.props;
    const breadcrumb = [{
      label: annBigTypeName[parseInt(annBigType, 10) - 1],
    }, {
      label: '编辑公告',
    }];
    const globalBtn = [{
      label: '返回',
      onClick: () => {
        this.goPageBack();
      },
    }, {
      label: '暂存',
      onClick: this.onTempSave,
    }, {
      label: '提交',
      type: 'primary',
      onClick: this.onSubmit,
    }];
    // 是否为浙江省
    const regRule = /^(33)/;
    const isZJProvince = regRule.test(detail.district);
    const isSpecialAnn = !!NO50ANN.includes(`${detail.announcementType}`);
    return (
      <Spin spinning={loading}>
        <div className="announcement-flow">
          <ZcyBreadcrumb
            routes={breadcrumb}
            globalBtn={globalBtn}
          />
          <AnnSteps
            processDefineKey={workflowInfo.processDefineKey}
            districtCode={detail.district}
            bizId={String(detail.bizId)}
            conditions={workflowInfo}
          />
          <Panel
            title="公告内容"
          >
            {
              renderAlert(hasBlock, hasRemind)
            }
            {
              (isZJProvince && isSpecialAnn && config.secondEdit) ?
                (
                  <Alert
                    message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，请遵循模板要求勿自行调整，否则将影响相关公告发布！"
                    type="warning"
                  />
                ) : ''
            }
            {
              !config.secondEdit && (
                <Alert
                  message="公告模板已按照财办库〔2020〕50号文“政府采购公告和公示信息格式规范（2020年版）”要求规范内容，公告样式及内容不允许修改！"
                  type="warning"
                />
              )
            }
            <Form>
              <div className="formgrid">
                <EditForm _this={this} />
              </div>
              <div id="ueditor" style={{ clear: 'both' }} />
            </Form>
          </Panel>
          {
            config?.attachmentShow ? (
              <FormUpload
                required={detail.hasAttachment}
                editable
                announcementType={this.state.detail?.announcementType}
                districtCode={detail.district}
                attachmentExternalDisplayRules={config?.attachmentExternalDisplayRules}
                attachmentUploadDesc={config?.attachmentUploadDesc}
              />
            ) : null
          }
          {
            createModalShow && (
              <AuditModal
                visible={createModalShow}
                config={auditParams}
                onCancel={this.createModalCancel}
                onOk={this.createModalOk}
                showUpload={false}
                ModalProps={{
                  confirmLoading: false,
                  title: '提交',
                }}
                isUserRadio={!supportCheckerMultiSelect}
                tips={config.isNotify ? (
                  <Alert
                    message="本公告已开启短信通知服务，请于本公告发布成功后，在列表页选择通知的对象。"
                    type="warning"
                    style={{ marginBottom: '10px' }}
                  />
                ) : undefined}
              />
            )
          }
          <SensitiveAlert
            visible={sensitiveVisible}
            sensitiveMsg={sensitiveMsg}
            onOk={() => {
              this.setState({
                sensitiveVisible: false,
              });
              this.showNextTastUser();
            }}
            onCancel={() => {
              this.setState({
                sensitiveVisible: false,
              });
            }}
          />
          <Marking
            markingList={markingList}
            onChange={this.onSelectMarking}
          />
        </div>
        {isShowDrainageModal && (
          <LcyDrainageModal
            visible={isShowDrainageModal}
            whetherToSettleIn={whetherToSettleIn}
            annBigType={this.state.annBigType}
            okText={whetherToSettleIn ? '立即跳转"乐采云平台"' : '跳转到乐采云登录页面'}
            handleBack={() => {
              history.goBack();
            }}
          />
        )}
      </Spin>
    );
  }
}
