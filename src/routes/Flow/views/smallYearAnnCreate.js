/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-12-10 14:42:12
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-26 11:25:41
 * @FilePath: /zcy-announcement-v2-front/src/routes/Flow/views/smallYearAnnCreate.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { getQueryParamByName } from '@zcy/utils';
import _Get from 'lodash/get';
import SmallDetailTable from './components/smallDetailTable';
import DetailSteps from '@/routes/Manage/views/components/detail-steps';
import { AuditModal } from '@zcy/zcy-audit-flow-back';
import { routerRedux } from 'dva/router';
import {
  ZcyBreadcrumb, 
  Modal,
  Panel, 
  FormGrid, 
  Space,
  InputAmount,
  message,
  request,
  Icon,
  Tooltip,
} from 'doraemon';
import './smallYearAnnCreate.less';

const confirm = Modal.confirm;

function AnnouncementInfo({ dispatch }) {
  const [loading, setLoading] = useState(true);
  const [formGridItem, setFormGridItem] = useState([]);
  const [totalData, setTotalData] = useState({});
  const [smallDetailList, setSmallDetailList] = useState([]);
  const [saveData, setSaveData] = useState({});
  const [canSave, setCanSave] = useState(false);
  const [canSubmit, setCanSubmit] = useState(false);
  const [canAudit, setCanAudit] = useState(false);
  const [id, setId] = useState(getQueryParamByName('id'));
  const [urlYear, setUrlYear] = useState(getQueryParamByName('year'));
  const [year, setYear] = useState(getQueryParamByName('year'));
  const [orgName, setOrgName] = useState('');
  const [smallMicroAmount, setSmallMicroAmount] = useState('');
  const [smallMediumAmount, setSmallMediumAmount] = useState('');
  const [smallMediumProportion, setSmallMediumProportion] = useState('');
  const [announcementDetail, setAnnouncementDetail] = useState({});
  const [showFinalModal, setShowFinalModal] = useState(false);
  const [colSpan, setColSpan] = useState(1);
  const [showSubmitModal, setShowSubmitModal] = useState(false); // 提交时审核人弹窗
  const [auditParams, setAuditParams] = useState({}); // 审核参数
  const [supportCheckerMultiSelect, setSupportCheckerMultiSelect] = useState();
  const [showAuditModal, setShowAuditModal] = useState(false); // 审核弹窗
  
  useEffect(() => {
    getPageData();
  }, []);

  const getPageData = () => {
    const payload = {};
    let type = '';
    if (year) {
      setYear(year);
      payload.year = year;
      type = 'flow/getSmallYearAnnData';
    }
    if (id) {
      setId(id);
      payload.id = id;
      type = 'flow/getSmallYearAnnDetail';
    }
    dispatch({
      type,
      payload,
    }).then((res) => {
      const { result } = res;
      if (res?.success) {
        // 渲染按钮逻辑
        handleSetData(result);
        setLoading(false);
    
        // 处理流转日志数据
        const newSmallFlowLogDtos = result?.smallAnnouncementFlowLogDTOS?.reverse().map(item => (
          {
            ...item,
            userName: item?.operatorName,
            orgName: item?.operatorOrgName,
            userAction: item?.option,
            remark: item?.mark,
          }));
          // 工作流数据对象
        setAnnouncementDetail({
          title: `${result?.year}年度${result?.orgName}中小企业预留执行情况`,
          creatorName: result?.creatorName,
          createdAt: result?.addTime,
          announcementFlowLogDtos: newSmallFlowLogDtos,
          id: result?.id,
          bizId: result?.id,
          district: result?.sysTenantCode,
          isSmall: true,
          statusDesc: result?.statusDesc,
        });
      } else {
        message.error(res?.message || res?.error);
      }
    });
  };

  const handleSetData = (res) => {
    const { smallBarButtons } = res;
    setCanSave(smallBarButtons.canSave);
    setCanSubmit(smallBarButtons.canSubmit);
    setCanAudit(smallBarButtons.canAudit);

    setUrlYear(res?.year);
    setOrgName(res?.orgName);
    setSmallMediumAmount(res?.smallMediumAmount);
    setSmallMicroAmount(res?.smallMicroAmount);
    setSmallMediumProportion(res?.smallMediumProportion);
    setSmallDetailList(res?.smallAnnouncementInfoDTOS);
    setTotalData(res);
    setSaveData(res?.smallAnnouncementInfoDTOS);
  };

  useEffect(() => {
    setFormGridItem(
      [
        {
          label: <span>年度</span>,
          colSpan,
          render: () => {
            return urlYear + '';
          },
        },
        {
          label: <span>采购单位</span>,
          colSpan,
          render: () => {
            return orgName;
          },
        },
        {
          label: <span>面向中小企业采购金额（万元）</span>,
          colSpan,
          render: () => {
            return (
              <Space>
                <InputAmount 
                  style={{ width: 200 }} 
                  allowFillZero 
                  readOnly 
                  sourceUnit="分"
                  targetUnit="万元"
                  color="orange" 
                  includeThousandsSeparator={false}
                  value={smallMediumAmount}
                />
              </Space>
            );
          },
        },
        {
          label: <span>面向小微企业采购金额（万元）</span>,
          colSpan,
          render: () => {
            return (
              <Space>
                <InputAmount 
                  style={{ width: 200 }} 
                  readOnly 
                  allowFillZero
                  sourceUnit="分"
                  targetUnit="万元"
                  color="orange" 
                  includeThousandsSeparator={false}
                  value={smallMicroAmount}
                />
              </Space>
            );
          },
        },
        {
          label: <span>面向小微企业采购金额占比（%）<Tooltip title="面向小微企业采购总额占比=面向小微企业采购总额÷面向中小企业采购总额×100%"><Icon style={{ marginLeft: 2 }} dora type="doraicon-question-o" /></Tooltip></span>,
          colSpan,
          render: () => {
            return (smallMediumProportion ? parseFloat(smallMediumProportion).toFixed(2) : '-');
          },
        },
      ]);
  }, [smallMediumAmount, smallMicroAmount, smallMediumProportion, colSpan]);

  // 点击自动更新
  const updateData = () => {
    confirm({
      title: '重新拉取自动读取数据',
      content: '该操作将清除当前自动读取数据，并重新进行拉取，请谨慎操作。',
      okText: '重新拉取',
      onOk: async () => {
        try {
          const res = await onSave();
          if (res?.success) {
            onUpdate(res?.result);
          } else {
            message.error(res.message || res.error);
          }
        } catch (error) {
          message.error('读取数据失败，请重试');
        }
      },
    });
  };

  const handleSave = async () => {
    const res = await onSave();
    if (res?.success) {
      dispatch({ 
        type: 'flow/getSmallYearAnnDetail',
        payload: { id: res?.result },
      }).then((result) => {
        handleSetData(result?.result);
        setAnnouncementDetail({ ...announcementDetail, createdAt: result?.result.addTime });
      });
    }
  };

  // 单据保存
  const onSave = () => {
    return new Promise((resolve) => {
      dispatch({
        type: 'flow/saveSmallYearAnn',
        payload: {
          ...totalData,
          smallAnnouncementInfoDTOS: saveData,
          id,
        },
      }).then((res) => {
        if (res?.success) {
          setId(res?.result);
          message.success('保存成功');
          resolve(res);
        } else {
          message.error(res.message || res.error);
          resolve(null);
        }
      });
    });
  };
    
  // 更新数据
  const onUpdate = (curId) => {
    setLoading(true);
    dispatch({
      type: 'flow/updateSmallYearAnn',
      payload: { id: curId },
    }).then((res) => {
      if (res?.success) {
        message.success('拉取成功');
        // 重新调取获取数据接口
        dispatch({ 
          type: 'flow/getSmallYearAnnDetail',
          payload: { id: curId },
        }).then((result) => {
          handleSetData(result?.result);
          setLoading(false);
        });
      } else {
        message.error(res.message || res.error);
      }
    });
  };

  // 单据提交
  const onSubmit = async () => {
    setShowFinalModal(false);
    setColSpan(1);
    const res = await onSave();
    // 审核人弹窗
    openAuditModal(() => {
      setShowSubmitModal(true);
    }, res?.result);
  };

  // 面包屑按钮
  const globalBtn = [];
  if (getQueryParamByName('pageType') !== 'detail') {
    globalBtn.push({
      label: '返回',
      onClick: () => {
        Modal.warning({
          title: '即将离开，是否保存更改',
          okCancel: true,
          okText: '保存并离开',
          cancelText: '直接离开',
          closable: true,
          onOk: async () => {     
            const res = await onSave();
            if (res?.success) {
              dispatch(routerRedux.goBack());
            }
          },
          onClose: () => {
            dispatch(routerRedux.goBack());
          },
          onCancel: () => {},
        });
      },
    });
  } else {
    globalBtn.push({
      label: '返回',
      onClick: () => { dispatch(routerRedux.goBack()); },
    });
  }
  if (getQueryParamByName('pageType') !== 'detail' && canSave) {
    globalBtn.push({
      label: '保存',
      onClick: handleSave,
    });
  }
  if (getQueryParamByName('pageType') !== 'detail' && canSubmit) {
    globalBtn.push({ 
      label: '提交',
      onClick: () => { setShowFinalModal(true); setColSpan(2); },
      type: 'primary',
    });
  }
  if (canAudit) {
    globalBtn.push({ 
      label: '审核',
      onClick: () => { openAuditModal(() => { setShowAuditModal(true); }, id); },
      type: 'primary',
    });
  }

  const handleChildDataSource = (data) => {
    setSaveData(data);
  };

  useEffect(() => {
    if (saveData.length > 0) {
      const countSmallMediumAmount = saveData.reduce(
        (acc, item) => acc + (parseFloat(item.smallMediumAmount) || 0), 0
      );
      const countSmallMicroAmount = saveData.reduce(
        (acc, item) => acc + (parseFloat(item.smallMicroAmount) || 0), 0
      );
      setSmallMediumAmount(countSmallMediumAmount + '');
      setSmallMicroAmount(countSmallMicroAmount + '');
      setSmallMediumProportion((countSmallMicroAmount / countSmallMediumAmount) * 100);
    }
  }, [saveData]);


  const formGrid = () => {
    return (
      <FormGrid
        bordered
        formGridItem={formGridItem}
      />
    );
  };

  // 打开审核人弹窗，获取工作流审核弹窗参数
  const openAuditModal = async (callback, announcementId) => {
    // 先获取审核流弹窗参数
    dispatch({
      type: 'dynamicCreate/fetchauditParams',
      payload: {
        announcementId,
        isSmall: true,
      },
    }).then((res = {}) => {
      if (res.success) {
        setAuditParams(res?.result);
        callback();
        getAuditConfig(announcementDetail?.district);
      }
    });
  };

  const getAuditConfig = async (districtCode) => { // 审核提交是否多选
    const res = await request('/announcement/config/getAnnouncementCheckConfig', {
      params: {
        districtCode,
        announcementType: id,
        isSmall: true,
      },
    });
    setSupportCheckerMultiSelect(_Get(res, 'result.supportCheckerMultiSelect', false) || false);   
  };

  // 选完审核人后提交
  const onFinalSubmit = (processFrontParam) => {
    dispatch({
      type: 'flow/submitSmallYearAnn',
      payload: {
        id,
        processFrontParam: JSON.stringify(processFrontParam),
      },
    }).then((res) => {
      if (res?.success) {
        dispatch(
          routerRedux.push('/announcement/list/14001')
        );
      } else {
        message.error(res?.message || res.error);
      }
    });
  };

  const onAudit = (processFrontParam) => {
    dispatch({
      type: 'flow/checkSmallAnnouncement',
      payload: {
        id,
        processFrontParam: JSON.stringify(processFrontParam),
      },
    }).then((res) => {
      if (res?.success) {
        dispatch(
          routerRedux.push('/announcement/list/14001')
        );
      } else {
        message.error(res?.message || res.error);
      }
    });
  };

  return (
    loading ? null : (
      <div className="small-year-ann-create">
        <ZcyBreadcrumb
          routes={[{
            label: '中小企业预留执行情况',
          }, {
            label: '详情',
          }]
          }
          globalBtn={globalBtn}
        />
        <DetailSteps
          announcementDetail={announcementDetail}
        />
        {/* )} */}
        <Panel
          title="中小企业预留执行情况统计"
          style={{ marginBottom: 10 }}
        >
          {formGrid(1)}
        </Panel>
        <SmallDetailTable 
          smallDetailList={smallDetailList}
          reservedProjectUpdateDate={totalData.reservedProjectUpdateDate}
          onDataSource={handleChildDataSource} 
          updateData={updateData}
        />
        {showFinalModal && (
          <Modal 
            title="提交中小企业预留执行情况"
            visible={showFinalModal}
            onOk={onSubmit}
            onCancel={() => { setShowFinalModal(false); setColSpan(1); }}
          >
            {formGrid(2)}
          </Modal>
        )}
        {/* 提交弹窗 */}
        {
          showSubmitModal && (
            <AuditModal
              visible={showSubmitModal}
              config={auditParams}
              onCancel={() => { setShowSubmitModal(false); }}
              onOk={onFinalSubmit}
              showUpload={false}
              ModalProps={{
                confirmLoading: false,
                title: '提交',
              }}
              isUserRadio={!supportCheckerMultiSelect}
            />
          )
        }
        {/* 审核弹窗 */}
        {
          showAuditModal && (
            <AuditModal
              visible={showAuditModal}
              config={auditParams}
              onCancel={() => { setShowAuditModal(false); }}
              onOk={onAudit}
              showUpload={false}
              isUserRadio={!supportCheckerMultiSelect}
              ModalProps={{
                confirmLoading: false,
                title: '审批',
              }}
            />
          )
        }
      </div>
    )
  );
}

const mapStateToProps = (state) => {
  return {
    flowData: state.flow,
  };
};
export default connect(mapStateToProps)(AnnouncementInfo);
