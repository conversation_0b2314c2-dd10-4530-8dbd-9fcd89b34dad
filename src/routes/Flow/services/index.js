import { request } from 'doraemon';

export async function queryList(params) {
  return request('/api/test', {
    params: {
      ...params,
    },
  }).then((res) => {
    if (!res) {
      return [];
    }
    return res.map((item, index) => {
      return {
        ...item,
        key: index,
      };
    });
  });
}

export async function getAnnouncementType(params) {
  return request('/announcement/config/getAnnTypeConfig', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    params,
  });
}

export async function queryAnnTypeByAnnBigType(params) {
  return request('/announcement/api/queryAnnTypeByAnnBigType', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取创建公告表单接口
export async function getDynamicForm(params) {
  return request('/announcement/api/obtainCreateAnnouncementForm', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 采购人
export async function obtainOrganizations(params) {
  return request('/announcement/api/obtainOrganizations', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 根据采购人获取行政区域
export async function getDistrictByCodeWithoutDate(params) {
  return request('/api/getDistrictByCodeWithoutDate', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}
// 行政区划-旧-异步接口
export async function getDistrictTreeWithPrivilegeInJson(params) {
  return request('/api/district/getDistrictTreeWithPrivilegeInJson', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 行政区划-新接口
export async function getDistTree(params) {
  params = {
    filterByOperatorDist: true,
    ...params,
  };
  return request('/announcement/api/getDistTree', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 采购方式和、采购类型
export async function obtainCGLXANDCGFS(params) {
  return request('/announcement/api/obtainCGLXANDCGFS', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 采购目录
export async function getGpcatalog(params) {
  return request('/api/getGpcatalog/tree', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 根据公告表单生成富文本

export async function getAnnouncementContent(data) {
  return request('/announcement/api/getAnnouncementContent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 获取公告工作流下一步审核人
export async function getNextTaskUsers(params) {
  return request('/announcement/api/getNextTaskUsers', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取监管信息
export async function getEnvByDistrictAndType(params) {
  return request('/announcement/api/getEnvByDistrictAndType', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取上传文件参数

export async function getSTSToken(params) {
  return request('/api/zoss/getSTSToken', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 公告创建
export async function submitAnnouncement(data) {
  return request('/announcement/api/submitAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 公告暂存
export async function tempSaveAnnouncement(data) {
  return request('/announcement/api/tempSaveAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 公告提交（编辑）
export async function modifyAnnouncement(data) {
  return request('/announcement/api/modifyAnnouncement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}


// 获取公告详情
export async function obtainAnnouncementDetail(params) {
  return request('/announcement/api/obtainAnnouncementDetail', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 根据code获取区划
export async function getTreeNameByCode(params) {
  return request('/api/zcy/common/getTreeNameByCode', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 获取下载地址
export async function getDownLoadUrl(params) {
  return request('/zcy/obs/file/getDownLoadUrl', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 当前时间是否为工作日
export async function checkWorkdayByDate(params) {
  return request('/announcement/api/checkWorkdayByDate', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 关联公告查询
export async function obtainAnnouncementByProCode(params) {
  return request('/announcement/api/obtainAnnouncementByProCode', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}
// 更正公告（澄清）
export async function listProjectAnnouncements(params) {
  return request('/announcement/api/listProjectAnnouncements', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 公告内容敏感词校验
export async function announcementCensor(data) {
  return request('/announcement/api/announcementCensor', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 获取公告标识
export async function getIdentifications(params) {
  return request('/announcement/api/getIdentifications', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 项目所在行政区划编码-下拉区划树数据（不带本级的区划树）
export async function getDistTreeFilterCorrespondingLevel(params) {
  return request('/announcement/api/getDistTreeFilterCorrespondingLevel', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

export async function fetchCurrentEnvApi() {
  return request('/announcement/api/currentEnvConfig', {
  });
}

// 社会统一信用代码 字段校验
export async function checkSocialCreditCode(data) {
  return request('/announcement/api/checkSocialCreditCode', {
    method: 'POST',
    data,
  });
}

export async function handleNonGovernmentProcurement(data) {
  return request('/announcement/api/nonGovernmentProcurement', {
    method: 'POST',
    data,
  });
}

export async function saveSmallYearAnn(data) {
  return request('/announcement/api/small/saveSmallAnnouncement', {
    method: 'POST',
    data,
  });
}

export async function submitSmallYearAnn(data) {
  return request('/announcement/api/small/submitSmallAnnouncement', {
    method: 'POST',
    data,
  });
}

export async function getSmallYearAnnData(params) {
  return request('/announcement/api/small/reservedProjectDetailsForShangHai', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}


export async function getSmallYearAnnDetail(params) {
  return request('/announcement/api/small/getSmallAnnouncementDetail', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

export async function updateSmallYearAnn(params) {
  return request('/announcement/api/small/updateReservedProjectDetail', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

export async function checkSmallAnnouncement(data) {
  return request('/announcement/api/small/checkSmallAnnouncement', {
    method: 'POST',
    data,
  });
}
