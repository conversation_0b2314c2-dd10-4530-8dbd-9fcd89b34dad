/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-08-12 16:47:27
 * @FilePath: /zcy-announcement-v2-front/src/routes/Flow/configs/utils.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { canSubmitAnnouncementGet } from 'src/api/announcement/api';
import { handleBtnByUiRule } from 'utils/utils';

export const getNameByValue = (data = [], matchCode) => {
  let match = null;
  data.forEach((item) => {
    if (item.value === matchCode) {
      match = item.label;
    } else if (item.children) {
      match = getNameByValue(item.children, matchCode) || match;
    }
  });
  return match;
};

export const getGlobalBtn = ({
  districtTree,
  submiting,
  submit,
  announcementCensor,
  preview,
  handelBack,
  titleSensitiveWords,
  handleCreateAnn,
}) => {
  let globalBtn = [];
  if (!preview) {
    globalBtn = [{
      label: '返回',
      onClick: handelBack,
    }, {
      label: '生成公告',
      type: 'primary',
      loading: Number(submiting) === 1 || districtTree.length === 0,
      onClick: () => {
        handleCreateAnn();
      },
    }];
  } else {
    globalBtn = [{
      label: '返回',
      onClick: handelBack,
    }, {
      label: '暂存',
      type: 'primary',
      onClick: () => {
        titleSensitiveWords(() => submit(2));
      },
    }, {
      label: '提交',
      type: 'primary',
      onClick: () => {
        titleSensitiveWords(announcementCensor);
      },
    }];
  }


  return handleBtnByUiRule(globalBtn);
};

const openTextAreaMaxLength = [7003, 7004];

// http://corp.cai-inc.com/zentao/story-view-31061.html
export const getTextAreaMaxLength = ({
  typeId,
}) => (openTextAreaMaxLength.includes(typeId) ? 10000 : 3000);

export const isEmptyValue = i => ['', undefined, null].includes(i);


export const checkOpenAudit = async (announcementId) => {
  const canSubmitRes = await canSubmitAnnouncementGet({
    announcementId,
  });
  if (!canSubmitRes?.success) return;
  return true;
};
