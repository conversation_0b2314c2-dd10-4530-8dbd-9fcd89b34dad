const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 14 },
};

const formItemLayoutForColSpan2 = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const hideTitleAnn = {
  3016: projectName => `${projectName}验收结果公告`, // 履约验收公告
  6003: projectName => `${projectName}验收结果公告`, // 其他履约验收公告
  7006: projectName => `关于${projectName}监督检查处理结果公告`, // 监督检查处理结果公告
};

const isProxyUnit = window.currentUserIdentity.categoryName.startsWith('0302');

// 50号文需求中，针对下述特殊公告类型需要展示提示文案
const NO50ANN = [
  '10016',
  '3008',
  '2001',
  '3001',
  '3020',
  '3002',
  '3011',
  '3003',
  '3004',
  '3012',
  '3005',
  '3017',
  '3015',
  '3010',
  '3016',
  '7003',
  '7006',
  '7007',
];

const noticeKeysMap = {
  3010: {
    // 合同
    key: 'purchaseContractContentInfo',
    col: 'acceptancePrices',
  },
  3004: {
    // 中标成交
    key: 'winningBidItemInfo',
    col: 'summaryPrice',
  },
  4005: {
    // 中标
    key: 'winningBidItemInfo',
    col: 'summaryPrice',
  },
  4006: {
    // 成交
    key: 'winningBidItemInfo',
    col: 'summaryPrice',
  },
  3005: {
    // 更正
    key: 'correctionItem',
    col: 'budgetPrice',
  },
  3003: {
    // 询价
    key: 'biddingProject',
    col: 'budgetPrice',
  },
  3001: {
    // 公开招标
    key: 'biddingProject',
    col: 'budgetPrice',
  },
  3011: {
    // 竞争性蹉商
    key: 'biddingProject',
    col: 'budgetPrice',
  },
  3012: {
    // 单一来源
    key: 'biddingProject',
    col: 'budgetPrice',
  },
  3002: {
    // 竞争性谈判
    key: 'biddingProject',
    col: 'budgetPrice',
  },
  3008: {
    // 邀请资格预审公告
    key: 'biddingProject',
    col: 'budgetPrice',
  },
};

const annBigTypeName = ['意见征询或公示', '采购项目公告', '更正公告', '采购结果公告', '采购合同公告', '履约验收公告', '政府采购监管公告', '电子卖场公告', '非政府采购公告'];

const specialAttachmentSize = [
  7003,
  7004,
  7005,
  7006,
  7007,
  7008,
  7009,
];

export {
  formItemLayout,
  formItemLayoutForColSpan2,
  formItemLayout2,
  hideTitleAnn,
  isProxyUnit,
  NO50ANN,
  noticeKeysMap,
  annBigTypeName,
  specialAttachmentSize,
};
