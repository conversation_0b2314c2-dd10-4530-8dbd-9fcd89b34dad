/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-10 15:51:57
 * @FilePath: /zcy-announcement-v2-front/src/routes/Flow/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
module.exports = [{
  url: '/flow/create',
  view: 'create',
  pageId: 'create',
  models: ['flow'],
}, {
  url: '/flow/adminCreate',
  view: 'adminCreate',
  pageId: 'create',
  models: ['flow'],
}, {
  url: '/edit/:annBigType/:announcementId',
  view: 'edit',
  pageId: 'edit',
  models: ['flow'],
}, {
  url: '/adminEdit/:annBigType/:announcementId',
  view: 'adminEdit',
  pageId: 'edit',
  models: ['flow'],
}, {
  url: '/smallYearAnn',
  view: 'smallYearAnnCreate',
  pageId: 'create',
  models: ['flow'],
}];
