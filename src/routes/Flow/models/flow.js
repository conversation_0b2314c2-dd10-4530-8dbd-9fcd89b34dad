
import { guid } from 'src/utils/utils';
import _ from 'lodash';
import {
  getDynamicForm,
  getAnnouncementContent,
  getDistTree,
  announcementCensor,
  tempSaveAnnouncement,
  modifyAnnouncement,
  getIdentifications,
  getDistTreeFilterCorrespondingLevel,
  obtainCGLXANDCGFS,
  getGpcatalog,
  getAnnouncementType,
  handleNonGovernmentProcurement,
  getSmallYearAnnData,
  getSmallYearAnnDetail,
  saveSmallYearAnn,
  submitSmallYearAnn,
  checkSmallAnnouncement,
  updateSmallYearAnn,
} from '../services';
import { resolveTree } from '../../../utils/tree';
import { FORM_GZSX, FORM_HYMC, FORM_ZGSCFS } from 'src/constants';
// 根据key找到对应某条数据
function findItemByKey(list, key) {
  return list.find(item => item.key === key);
}

// 按给定key值排序
function sortByGavingKeys(array, keys) {
  const sortArray = [];
  let othersArray = [];
  keys.forEach((key) => {
    if (findItemByKey(array, key)) {
      sortArray.push(findItemByKey(array, key));
    }
  });
  othersArray = array.filter(item => !keys.includes(item.key));
  return [...sortArray, ...othersArray];
}


export default {
  namespace: 'flow',

  state: {
    dynamicForm: [], // 动态表单数据
    richTextModalVisible: false,
    reviewModalVisible: false,
    pathName: undefined,
    shouldSubmitForm: null,
    dynamicTable: {},
    routerParams: {}, // 路由信息
    tableData: {}, // table数据
    preview: false, // 预览
    fileInfo: {
      defaultFileList: [], // 带过来的附件
      fileList: [], // 上传的附件
    }, // 文件列表
    fileInfo1: {
      defaultFileList1: [], // 带过来的附件
      fileList1: [], // 上传的附件
    }, // 文件列表
    fileInfo2: {
      defaultFileList2: [], // 带过来的附件
      fileList2: [], // 上传的附件
    }, // 文件列表
    fileInfo3: {
      defaultFileList3: [], // 带过来的附件
      fileList3: [], // 上传的附件
    }, // 文件列表
    disDataForPro: [],
    districtTree: [],
    cgfs: [],
    cglx: [],
    cgml: [],
    // 行业名称
    hymc: FORM_HYMC,
    // 更正事项
    gzsx: FORM_GZSX,
    // 资格审查方式
    zgscfs: FORM_ZGSCFS,
  },

  effects: {
    * setPreview({ payload }, { put }) {
      yield put({
        type: 'savePreview',
        payload,
      });
    },
    // 更新table表单信息
    * setTableData({ payload }, { put }) {
      yield put({
        type: 'saveTableData',
        payload,
      });
    },
    // 路由参数
    * setRouterParams({ payload }, { put }) {
      yield put({
        type: 'saveRouterParams',
        payload,
      });
    },
    // 同步路由变化信息
    * watchRouter({ payload }, { put }) {
      yield put({
        type: 'saveRouter',
        payload,
      });
    },
    // modal 显示隐藏
    * setModalVisible({ payload }, { put }) {
      yield put({
        type: 'saveModalVisible',
        payload,
      });
    },
    * getDynamicForm({ payload }, { call, put }) {
      const response = yield call(getDynamicForm, payload);
      if (response.success) {
        const oldArray = response.result;
        /** start 手动排序:【是否涉密、采购方式、采购类型、采购目录、项目名称、项目编号、项目所在行政区划、是否关联采购计划]排在最前面，其余字段按照传过来的顺序排序 */
        const newArray = sortByGavingKeys(
          oldArray,
          ['isSecret', 'procurementMethod', 'procurementType', 'gpCatalogName', 'projectName', 'projectCode', 'purchaseDate', 'punishedObject', 'districtCode', 'isBindProcurementPlan', 'ifCentralizedProcurement']
        );
        /** end 手动排序 */
        /** start 项目所在行政区划编码、名称，处理为一个字段，提交时还是原来字段 */
        const disCodeItem = findItemByKey(newArray, 'districtCode');
        const disNameItem = findItemByKey(newArray, 'districtName');
        if (disCodeItem) {
          disCodeItem.key = 'disForPro';
          disCodeItem.name = '项目所在行政区划';
          // 获取表单区划数据
          yield put({
            type: 'getDistTreeFilterCorrespondingLevel',
          });
        }
        if (disNameItem) {
          disNameItem.type = null;
        }
        /** end */
        response.result = newArray;
        yield put({
          type: 'updateData',
          payload: {
            dynamicForm: newArray,
          },
        });
      }
      return response;
    },
    * resetDynamicForm(p, { put }) {
      return yield put({
        type: 'setDynamicForm',
        payload: {
          result: [],
        },
      });
    },
    * shouldSubmitForm({ payload }, { put }) {
      return yield put({
        type: 'setSubmitFormState',
        payload,
      });
    },
    * setDynamicTable({ payload }, { put }) {
      return yield put({
        type: 'saveDynamicTable',
        payload,
      });
    },
    * getAnnouncementContent({ payload }, action) {
      const response = yield action.call(getAnnouncementContent, payload);
      return response;
    },
    * getDistTree({ payload }, { put, call }) {
      const response = yield call(getDistTree, payload);
      const districtTree = resolveTree(response?.result ?? []);
      yield put({
        type: 'setDistTree',
        payload: { districtTree },
      });
    },
    * setFileList({ payload }, { put }) {
      return yield put({
        type: 'updateFileList',
        payload,
      });
    },
    * tempSaveAnnouncement({ payload }, action) {
      const response = yield action.call(tempSaveAnnouncement, payload);
      return response;
    },
    * modifyAnnouncement({ payload }, action) {
      const response = yield action.call(modifyAnnouncement, payload);
      return response;
    },
    * announcementCensor({ payload }, action) {
      const response = yield action.call(announcementCensor, payload);
      return response;
    },
    * getIdentifications({ payload }, action) {
      const response = yield action.call(getIdentifications, payload);
      return response;
    },
    * getDistTreeFilterCorrespondingLevel({ payload }, { put, call }) {
      const response = yield call(getDistTreeFilterCorrespondingLevel, payload);
      const disDataForPro = resolveTree(response.result[0].children);
      yield put({
        type: 'setDisDataForPro',
        payload: { disDataForPro },
      });
    },
    // 采购区域、采购类型
    * obtainCGLXANDCGFS({ payload }, { put, call }) {
      const response = yield call(obtainCGLXANDCGFS, payload);
      let cgfs = [];
      let cglx = [];
      function getNodes(tree) {
        const newTree = [];
        if (!tree) {
          return null;
        }
        tree.forEach((item) => {
          newTree.push({
            ...item,
            label: item.codeName,
            title: item.codeName,
            value: item.code,
            key: item.code,
            disabled: item.children !== null && item.children !== undefined,
            children: (item.children ? getNodes(item.children) : undefined),
          });
        });
        return newTree;
      }
      cgfs = getNodes(_.get(response, 'result.cgfs') || []);
      cglx = getNodes(_.get(response, 'result.cglx') || []);
      yield put({
        type: 'updateData',
        payload: {
          cgfs,
          cglx,
        },
      });
    },
    // 采购目录
    * getGpcatalog({ payload }, { put, call }) {
      const response = yield call(getGpcatalog, payload);
      function getNodes(tree) {
        const newTree = [];
        tree.forEach((item) => {
          newTree.push({
            ...item,
            label: item.node.code + item.node.name,
            title: item.node.code + item.node.name,
            value: item.node.code,
            key: guid(),
            isLeaf: item.data === undefined,
            // disabled: item.data !== undefined,
            children: (item.data ? getNodes(item.data) : undefined),
          });
        });
        return newTree;
      }
      let cgml;
      if (response.data) {
        cgml = getNodes(response.data.data);
      } else {
        cgml = [];
      }
      yield put({
        type: 'updateData',
        payload: {
          cgml,
        },
      });
    },
    // 获取配置信息
    * getAnnouncementType({ payload }, { call }) {
      const response = yield call(getAnnouncementType, payload);
      return response;
    },
    // 清空缓存
    * clearStore(...params) {
      yield params[1].put({
        type: 'updateData',
        payload: {
          dynamicForm: [], // 动态表单数据
          richTextModalVisible: false,
          reviewModalVisible: false,
          pathName: undefined,
          shouldSubmitForm: null,
          dynamicTable: {},
          routerParams: {},
          tableData: {},
          preview: false,
          fileInfo: {
            defaultFileList: [],
            fileList: [],
          },
          fileInfo1: {
            defaultFileList1: [], // 带过来的附件
            fileList1: [], // 上传的附件
          }, // 文件列表
          fileInfo2: {
            defaultFileList2: [], // 带过来的附件
            fileList2: [], // 上传的附件
          }, // 文件列表
          fileInfo3: {
            defaultFileList3: [], // 带过来的附件
            fileList3: [], // 上传的附件
          }, // 文件列表
          disDataForPro: [],
          districtTree: [],
          cgfs: [],
          cglx: [],
        },
      });
    },
    * handleNonGovernmentProcurement({ payload }, { call }) {
      const response = yield call(handleNonGovernmentProcurement, payload);
      return response;
    },
    // 中小企业-生成年度数据
    * getSmallYearAnnData({ payload }, { call }) {
      const response = yield call(getSmallYearAnnData, payload);
      return response;
    },
    // 中小企业-汇中列表-查看
    * getSmallYearAnnDetail({ payload }, { call }) {
      const response = yield call(getSmallYearAnnDetail, payload);
      return response;
    },
    // 中小企业-保存年度数据
    * saveSmallYearAnn({ payload }, { call }) {
      const response = yield call(saveSmallYearAnn, payload);
      return response;
    },
    // 中小企业-更新年度提交数据-项目明细
    * updateSmallYearAnn({ payload }, { call }) {
      const response = yield call(updateSmallYearAnn, payload);
      return response;
    },
    // 中小企业-提交年度数据
    * submitSmallYearAnn({ payload }, { call }) {
      const response = yield call(submitSmallYearAnn, payload);
      return response;
    },
    // 中小企业-审批年度数据
    * checkSmallAnnouncement({ payload }, { call }) {
      const response = yield call(checkSmallAnnouncement, payload);
      return response;
    },
  },

  reducers: {
    savePreview(state, action) {
      return {
        ...state,
        preview: action.payload.preview,
      };
    },
    saveTableData(state, action) {
      return {
        ...state,
        tableData: action.payload.tableData,
      };
    },
    saveRouterParams(state, action) {
      return {
        ...state,
        routerParams: action.payload,
      };
    },
    saveRouter(state, action) {
      return {
        ...state,
        pathName: action.payload.pathName,
      };
    },
    saveHtml(state, action) {
      return {
        ...state,
        create: Object.assign(state.create, action.payload),
      };
    },
    saveModalVisible(state, action) {
      return {
        ...state,
        [action.payload.type]: action.payload.visible,
      };
    },
    setSubmitFormState(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    saveDynamicTable(state, action) {
      return {
        ...state,
        dynamicTable: {
          ...state.dynamicTable,
          ...action.payload.dynamicTable,
        },
      };
    },
    updateFileList(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    setDisDataForPro(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    setDistTree(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    updateData(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen((location) => {
        const paths = location.pathname.split('/');
        if (paths.length >= 3) {
          const pathName = paths[2];
          dispatch({
            type: 'watchRouter',
            payload: {
              pathName,
            },
          });
        }
      });
    },
  },
};
