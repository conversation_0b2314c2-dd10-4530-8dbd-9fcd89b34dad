.short-for-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}


.config-single-source-minWidth {
  min-width: 250px;
}

.config-purchase-amount-modal {
  &-alert {
    margin-bottom: 20px;
  }
}

.config-label {
  color: #666;
}

.config-content {
  color: #333;
  &-text {
    &::after {
      content: '';
      margin-right: 8px;
    }
  }
}

.config-purchase-intention-minWidth {
  min-width: 120px;
  display: inline-block;
}

.refEngAnnModal {
  width: 680px !important;
  .doraemon-modal-body {
    text-align: center !important;
    line-height: 6;
  }
}

.btn-throttle {
  animation: btnThrottle 1s step-end forwards;
  &:active {
    animation: none;
  }
}
