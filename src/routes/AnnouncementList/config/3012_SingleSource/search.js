import React from 'react';
import {
  Select,
  Input,
  DatePicker,
} from 'doraemon';
import moment from 'moment';

const { Option } = Select;
const { RangePicker } = DatePicker;
const statusArr = [
  {
    label: '待完善',
    value: 0,
  },
  {
    label: '审核中',
    value: 1,
  },
  {
    label: '待发布',
    value: 2,
  },
  {
    label: '被回退',
    value: 3,
  },
  {
    label: '已发布',
    value: 4,
  },
  {
    label: '已结束',
    value: 5,
  },
  {
    label: '取消审核中',
    value: 8,
  },
  {
    label: '已取消',
    value: 9,
  },
];

// 获得搜索项配置
export const searchItemConfig = () => {
  const baseItem = [{
    label: '公告标题',
    id: 'title',
    render: () => <Input placeholder="请输入" />,
  }, {
    label: '采购编号名称',
    id: 'projectName',
    render: () => <Input placeholder="请输入" />,
  }, {
    label: '发布时间',
    id: 'date',
    render: () => {
      return (
        <RangePicker
          showTime={{
            hideDisabledOptions: true,
            defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('00:00:00', 'HH:mm:ss')],
          }}
        />
      );
    },
  }, {
    label: '状态',
    id: 'queryStatus',
    render: () => {
      return (
        <Select
          filterOption={
            (input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
          allowClear
          showSearch
        >
          {
            statusArr
              .map((org = {}) => <Option value={org.value} key={org.value}>{org.label}</Option>)
          }
        </Select>
      );
    },
  },
  ];
  baseItem.push({
    label: '关键字',
    id: 'contentKeyWord',
    render: () => <Input placeholder="请输入" />,
  });
  return baseItem;
};
