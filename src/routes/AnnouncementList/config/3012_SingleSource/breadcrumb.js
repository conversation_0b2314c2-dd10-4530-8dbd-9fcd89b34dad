import React from 'react';
import { Button } from 'doraemon';
import { routerRedux } from 'dva/router';
import PurchaseAmountModal from './PurchaseAmountModal';
import PurchasePlanModal from './PurchasePlanModal';
import ReformationEnglishAnnModal from './ReformationEnglishAnnModal';

export const breadcrumbConfig = (component) => {
  const {
    publishModal = false,
    visiblePurchasePlanModal = false,
    isShowRefEngAnnModal = false,
    pushSingleSourcePublicityWithProjectBtn = false,
    pushSingleSourcePublicityWithMoneyBtn = false,
    singleForProjectBtn,
    singleForPlanBtn,
    singleForManualBtn,
    singleForManualForCPTPP,
    selectedRows,
    selectedIds,
    purchasePlanYear,
  } = component.state;

  const temp = [];
  pushSingleSourcePublicityWithProjectBtn && singleForProjectBtn && temp.push(       
    <React.Fragment>
      <Button
        style={{ marginRight: 12 }}
        onClick={() => {
          component.setState({
            visiblePurchasePlanModal: true,
          });
        }}
        type="primary"
      >新增单一来源公示(关联项目)
      </Button>
      {visiblePurchasePlanModal && (
        <PurchasePlanModal
          component={component}
          show={visiblePurchasePlanModal}
          hideModal={() => {
            component.setState({
              visiblePurchasePlanModal: false,
            });
          }}
        />
      )}
      <ReformationEnglishAnnModal
        show={isShowRefEngAnnModal}
        selectedRows={selectedRows}
        selectedIds={selectedIds}
        purchasePlanYear={purchasePlanYear}
        dispatch={component.props.dispatch}
        hideRefEngAnnModal={() => {
          component.setState({
            isShowRefEngAnnModal: false,
          });
        }}
      />

    </React.Fragment>);
  pushSingleSourcePublicityWithMoneyBtn && singleForPlanBtn && temp.push(
    <React.Fragment>
      <Button
        onClick={() => {
          component.setState({
            publishModal: true,
          });
        }}
        type="primary"
      >
        新增单一来源公示(关联资金)
      </Button>
      {publishModal && (
        <PurchaseAmountModal
          show={publishModal}
          hideModal={() => {
            component.setState({
              publishModal: false,
            });
          }}
        />
      )}
    </React.Fragment>
  );
  if (singleForManualBtn) {
    if (singleForManualForCPTPP) {
      temp.push(
        <React.Fragment>
          <Button  
            onClick={() => {
              component.setState({
                isShowRefEngAnnModal: true,
              });
            }}
            type="primary"
          >发布单一来源公示
          </Button>
          <ReformationEnglishAnnModal
            show={isShowRefEngAnnModal}
            singleForManualForCPTPP
            isShangHaiManual
            dispatch={component.props.dispatch}
            hideRefEngAnnModal={() => {
              component.setState({
                isShowRefEngAnnModal: false,
              });
            }}
          />
        </React.Fragment>
      );
    } else {
      temp.push(
        <Button  
          onClick={() => {
            component.props.dispatch(
              routerRedux.push(
                `/dynamic/create?formPageCode=singleSourcePublicity&announcementTypeName=${escape('单一来源公示')}&announcementType=3012&annBigType=1`
              )
            );
          }}
          type="primary"
        >发布单一来源公示
        </Button>);
    }
  }
  let buttons;
  if (temp.length) {
    buttons = (
      <span className="config-breadcrumb-button">
        {temp.map(ele => ele)}
      </span>
    );
  }
  return {
    routes: [{
      label: '单一来源公示',
    }],
    customContent: buttons,
  };
};
