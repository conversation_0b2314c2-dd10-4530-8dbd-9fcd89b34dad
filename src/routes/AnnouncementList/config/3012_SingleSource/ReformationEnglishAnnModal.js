/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-09-19 15:26:19
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-11-27 10:03:53
 * @FilePath: /zcy-announcement-v2-front/src/routes/AnnouncementList/config/3012_SingleSource/ReformationEnglishAnnModal.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState } from 'react';
import { Modal, Radio, request } from 'doraemon';
import { routerRedux } from 'dva/router';

const RadioGroup = Radio.Group;
const SINGLE_PAGE_TYPE = 11;
export default function ReformationEnglishAnnModal({ 
  show, 
  selectedRows, 
  selectedIds, 
  purchasePlanYear, 
  hideRefEngAnnModal, 
  dispatch, 
  isShangHaiManual = false,
  singleForManualForCPTPP = false,
}) {
  const [isReformationEnglishAnnouncement, setIsReformationEnglishAnnouncement] = useState(0);
  const onOk = async () => {
    if (isShangHaiManual) {
      dispatch(
        routerRedux.push(
          `/dynamic/create?formPageCode=singleSourcePublicity&announcementTypeName=${escape('单一来源公示')}&announcementType=3012&annBigType=1&isReformation=1&isReformationEnglishAnnouncement=${isReformationEnglishAnnouncement}&singleForManualForCPTPP=${singleForManualForCPTPP}`
        )
      );
    } else {
      const res = await request('/api/purchaseplan/intention/announcement/buildPurchaseSignleSource', {
        method: 'post',
        data: {
          ids: selectedRows[0].ids,
          pageType: SINGLE_PAGE_TYPE,
        },
      });
        
      const data = res.result;
      const url = '/dynamic/create';
      const params = [
        'formPageCode=singleSourcePublicity',
        'annBigType=1',
        'disabledProjectNo=1',
        `purchaseProjectCode=${selectedIds?.[0]}`,
        `announcementType=${data.announcementType}`,
        `orderId=${data.serialNum}`,
        `districtId=${data.districtCode}`,
        `appCode=${data.appCode}`,
        `title=${data.title ? escape(data.title) : ''}`,
        `year=${purchasePlanYear}`,
        'isReformation=1',
        `annSmallMediumFlag=${data.annSmallMediumFlag}`,
        `isReformationEnglishAnnouncement=${isReformationEnglishAnnouncement}`,
      ];
      dispatch(
        routerRedux.push(`${url}?${params.join('&')}`)
      );
    }
  };

  const onChange = (e) => {
    setIsReformationEnglishAnnouncement(e.target.value);
  };
  return (
    <Modal
      className="refEngAnnModal"
      title="选择对标改革公告语言类型"
      visible={show}
      width={980}
      onCancel={hideRefEngAnnModal}
      onOk={onOk}
    >
      <span>公告语言类型：</span>
      <RadioGroup onChange={onChange} value={isReformationEnglishAnnouncement}>
        <Radio value={0}>中文公告</Radio>
        <Radio value={1}>中英文公告</Radio>
      </RadioGroup>
    </Modal>
  );
}
