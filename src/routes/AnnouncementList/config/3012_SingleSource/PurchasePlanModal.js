import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, But<PERSON>, message, Input, Alert, Select, request, Tag } from 'doraemon';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { moneyFormat } from 'utils/utils.js';
import '../../../Manage/views/components/PlanModal.less';
import { pagingPurchaseProjectData } from '../../services';
import moment from 'moment';

const DATE_OPTION = [
  moment().subtract(3, 'y').format('YYYY'),
  moment().subtract(2, 'y').format('YYYY'),
  moment().subtract(1, 'y').format('YYYY'),
  moment().format('YYYY'),
  moment().add(1, 'y').format('YYYY'),
  moment().add(2, 'y').format('YYYY'),
];

const PAGE_SIZE = 6;
const SINGLE_PAGE_TYPE = 11;

const columns = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    render: (text, record) => {
      return (
        <div>
          <span title={text}>{text}</span>
          {record.isReformation ? (<Tag color="#CCA97D" style={{ marginLeft: '10px' }}>{record.isReformationDesc}</Tag>) : null}
        </div>
      );
    },
  },
  {
    title: '资金总额（元）',
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    width: 140,
    align: 'right',
    render: (text) => {
      return (
        <div className="colPlanInfo">
          <div>
            <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
          </div>
        </div>
      );
    },
  },
];


@connect()
export default class PurchasePlanModal extends Component {
  state = {
    list: [],
    listTotal: 0,
    selectedIds: [], // 选中项对象ids
    selectedRows: [], // 选中项对象
    purchasePlanYear: moment().format('YYYY'), //  选择项目采购实施计划 对应的年限时间
  };

  componentWillReceiveProps(nextProps) {
    if (nextProps.show && !this.props.show) {
      this.init();
    }
  }
  componentDidMount() {
    this.init();
  }
  init = () => {
    const { purchasePlanYear } = this.state;
    this.onSearch({
      years: purchasePlanYear,
    }); 
  }
  onSearch = (params = {}) => {
    if (!params.pageSize) {
      params.pageSize = PAGE_SIZE;
    }
    if (!params.pageNo) {
      params.pageNo = 1;
    }
    if (params.years) {
      params.years = [params.years];
      this.setState({
        purchasePlanYear: params.years,
      });
    }
    pagingPurchaseProjectData(params).then(({ result, success }) => {
      if (success) {
        const info = {
          list: result.data,
          listTotal: result.total,
          pageNo: params ? params.pageNo : 1,
        };
        this.setState(info);
      }
    });
  }
  onOk = async () => {
    const { selectedIds = [], selectedRows = [], purchasePlanYear } = this.state;
    if (!selectedIds.length || !selectedRows.length) {
      message.error('请选择');
      return;
    }
    
    if (!selectedRows[0].isCanPurchase) {
      message.error('该项目不支持创建单一来源公示');
      return;
    }
    // 如果不是对标改革公告
    if (!selectedRows[0].isReformation) {
      const res = await request('/api/purchaseplan/intention/announcement/buildPurchaseSignleSource', {
        method: 'post',
        data: {
          ids: selectedRows[0].ids,
          pageType: SINGLE_PAGE_TYPE,
        },
      });
  
      const data = res.result;
      const url = '/dynamic/create';
      const params = [
        'formPageCode=singleSourcePublicity',
        'annBigType=1',
        'disabledProjectNo=1',
        `purchaseProjectCode=${selectedIds?.[0]}`,
        `announcementType=${data.announcementType}`,
        `orderId=${data.serialNum}`,
        `districtId=${data.districtCode}`,
        `appCode=${data.appCode}`,
        `title=${data.title ? escape(data.title) : ''}`,
        `year=${purchasePlanYear}`,
        `annSmallMediumFlag=${data.annSmallMediumFlag}`,
        'isReformation=0',
        'isReformationEnglishAnnouncement=0',
      ];
  
      this.props.dispatch(
        routerRedux.push(`${url}?${params.join('&')}`)
      );
      return;
    }
    // 如果是对标改革公告，更新父组件的状态，传递数据
    this.props.component.setState({
      selectedRows,
      selectedIds,
      purchasePlanYear,
      visiblePurchasePlanModal: false,
      isShowRefEngAnnModal: true,
    });
  }

  onSelectChange = (ids = [], rows = []) => {
    this.setState({
      selectedIds: ids,
      selectedRows: rows,
    });
  }

  render() {
    const { listTotal, list, selectedIds } = this.state;
    const { hideModal } = this.props;
    return (
      <Modal
        title="选择项目采购实施计划"
        visible={this.props.show}
        width={980}
        onCancel={hideModal}
        onOk={this.onOk}
        className="tableModal modalList"
        footer={[
          <Button key="back" onClick={hideModal}>取消</Button>,
          <Button key="submit" onClick={this.onOk} type="primary" >
            确定
          </Button>,
        ]}
      >
        <Alert
          message="本列表仅展示未发布过单一来源公示且存在剩余资金的采购实施计划，每条项目支持发布多次单一来源公示"
          type="firstinfo"
          style={{
            margin: '-5px -18px 20px',
          }}
          showIcon
          iconType="exclamation-circle-o"
        />
        <div className="PlanModal">
          <ZcyList
            customItem={[
              {
                label: '项目名称',
                id: 'projectNameLike',
                render: () => {
                  return <Input placeholder="请输入" />;
                },
              },
              {
                label: '年度',
                id: 'years',
                decoratorOptions: {
                  // 默认当前年
                  initialValue: moment().format('YYYY'),
                },
                render: () => (
                  <Select getPopupContainer={e => e} placeholder="请选择">
                    {
                      DATE_OPTION.map(ele => (
                        <Select.Option key={ele} value={ele}>
                          {ele}
                        </Select.Option>
                      ))
                    }
                  </Select>
                ),
              },
            ]}
            column={2}
            onSearch={this.onSearch}
            table={{
              rowKey: 'projectNo',
              columns,
              dataSource: list,
              pagination: {
                total: listTotal,
                defaultPageSize: PAGE_SIZE,
              },
              rowSelection: {
                selectedRowKeys: selectedIds,
                type: 'radio',
                onChange: this.onSelectChange,
              },
            }}
          />
        </div>
      </Modal>
    );
  }
}

