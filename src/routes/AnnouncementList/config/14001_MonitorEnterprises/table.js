import React from 'react';
import {
  bizStringFormat,
} from '@zcy/utils';
import {
  Table,
  Badge,
  Tooltip,
  Icon,
} from 'doraemon';

import '../config.less';

const Actions = Table.Actions;
export const getTabs = (tab, isResponsibleBudgetUnit) => {
  const tabs = {
    activeKey: tab || '1',
    defaultActiveKey: tab || '1',
  };
  
  const tabLists = [
    {
      label: '全部',
      value: '1',
      key: '1',
    },
    {
      label: '本单位',
      value: '2',
      key: '2',
    },
    {
      label: '下级单位',
      value: '3',
      key: '3',
    },
    {
      label: '待审批',
      value: '4',
      key: '4',
    },
  ];

  if (isResponsibleBudgetUnit) {
    tabs.tabList = tabLists;
    tabs.defaultActiveKey = '1';
  } 
  return tabs;
};
  
// 操作权限
const getOperations = (component, record) => {
  const {
    handleEdit,
    handleDelete,
    handleCheck,
    handleRevoke,
    handleView,
  } = component;
  const operationList = {
    canEdit: {
      label: '编辑',
      sort: 1,
      onRowClick: handleEdit,
    },
    canDelete: {
      label: '删除',
      sort: 2,
      onRowClick: () => handleDelete(record, '删除单据', '删除后，单据信息将无法找回', '删除', 'announcementList/smallDelete'),
    },
    canCheck: {
      label: '审核',
      sort: 3,
      onRowClick: handleCheck,
    },
    canRevoke: {
      label: '撤回',
      sort: 4,
      onRowClick: handleRevoke,
    },
    canDetail: {
      label: '查看',
      sort: 5,
      onRowClick: handleView,
    },
  };
  const actions = [];
  const operationKeys = Object.keys(operationList);
  operationKeys?.forEach((item) => {
    if (record[item]) {
      actions.push(operationList[item]);
    }
  });
  return actions;
};
// 获得表格配置项
export const getTableConfig = (component) => {
  const { getParams, announcementList } = component.props;
  const { isResponsibleBudgetUnit } = component.state;
  const tableQueryParams = getParams({
    pageNo: 1,
    pageSize: 10,
  });
  const { pageSize, pageNo } = tableQueryParams;
  
  const getColumns = () => {
    const columns = [
      {
        title: '年度',
        dataIndex: 'year',
        key: 'year',
        width: 70,
        render: text => (
          <span title={text}>{bizStringFormat(text)}</span>
        ),
      }, {
        title: '面向中小企业采购金额 (万元)',
        dataIndex: 'smallMediumAmount',
        key: 'smallMediumAmount',
        align: 'right',
        render: text => (
          <span 
            title={text} 
            className="config-purchase-intention-minWidth"
            style={{ color: 'orange', fontWeight: 'bold' }}
          >{bizStringFormat(text)}
          </span>
        ),
      }, {
        title: '面向小微企业采购金额 (万元)',
        dataIndex: 'smallMicroAmount',
        key: 'smallMicroAmount',
        align: 'right',
        render: text => (
          <span 
            title={text}
            style={{ color: 'orange', fontWeight: 'bold' }}
          >{bizStringFormat(text)}
          </span>
        ),
      }, {
        title: (
          <span>
            面向小微企业采购金额占比 (%)
            <Tooltip title="面向小微企业采购总额占比=面向小微企业采购总额÷面向中小企业采购总额×100%">
              <Icon type="doraicon-question-o" style={{ marginLeft: 5 }} dora />
            </Tooltip>
          </span>
        ),
        dataIndex: 'smallMediumProportion',
        key: 'smallMediumProportion',
        render: text => (
          <span title={text}>{bizStringFormat(text)}</span>
        ),
      }, {
        title: '状态',
        dataIndex: 'statusDesc',
        key: 'statusDesc',
        width: 108,
        render: (text, record) => {
          let status;
          switch (record.status) {
          case 0:
          case 1:
          case 3:
            status = 'processing';
            break;
          case 4:
            status = 'warning';
            break;
          case 2:
            status = 'success';
            break;
          default:
            status = 'processing';
          }
          return (
            <div>
              <Badge status={status} text={text} />
            </div>
          );
        },
      }, {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 110,
        render: (text, record) => {
          const actions = getOperations(component, record);
          return (
            <React.Fragment>
              <Actions actions={actions} record={record} />
            </React.Fragment>
          );
        },
      },
    ];
    if (isResponsibleBudgetUnit) {
      columns.splice(1, 0, {
        title: '采购单位名称',
        dataIndex: 'orgName',
        key: 'orgName',
        render: text => (
          <span title={text}>{bizStringFormat(text)}</span>
        ),
      });
    }
    return columns;
  };
  
  const columns = getColumns();
  
  const { data = [], total } = announcementList?.data || {};
  return {
    columns,
    dataSource: data,
    pagination: {
      pageSize,
      current: pageNo,
      showSizeChanger: true,
      total,
    },
  };
};
