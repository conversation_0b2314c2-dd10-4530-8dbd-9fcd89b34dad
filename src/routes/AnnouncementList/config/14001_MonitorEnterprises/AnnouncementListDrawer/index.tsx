import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>, message } from '@zcy/doraemon';
import { bizDatetimeFormat, bizStringFormat, getQueryParamObj } from '@zcy/utils';
import React, { useEffect, useMemo, useState } from 'react';                                                                                                                                                                                                                                                                                
import { getPubType } from 'src/utils/utils';
import { getAnnouncementList } from 'src/routes/AnnouncementList/services';
import { deleteFormAnnouncement } from 'src/routes/Manage/services';
import { customRender } from 'src/utils/customRender';
import OutUrlContent from 'src/components/OutUrlContent';
import RevokeModal from './RevokeModal';
import './index.less';

const confirm = Modal.confirm;

const AnnouncementListDrawer = (props) => {
  const { onCancel } = props;
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    getListData();
  }, []);

  const getListData = async (params = { pageNo: 1, pageSize: 10 }) => {
    const res = await getAnnouncementList(params);
    setDataSource(res?.result?.data);
    setTotal(res?.result?.total);
  };

  const goDetail = (record) => {
    const {
      formPageCode = '',
      annId = '',
      district = '',
      annBigType = '',
    } = record;
    formPageCode && window.open(
      `/announcement-front/#/dynamic/detail?formPageCode=${formPageCode}&annId=${annId}&districtId=${district}&annBigType=${annBigType}`,
      '_blank'
    );
  };

  const goEdit = (record) => {
    record?.formPageCode && window.open(
      `/announcement-front/#/dynamic/edit?announcementTypeName=${escape(record?.announcementTypeName)}&announcementType=${record?.announcementType}&formPageCode=${record?.formPageCode}&annId=${record?.annId}&districtId=${record?.district}&annBigType=${record?.annBigType}`,
      '_blank'
    );
  };

  const columns = useMemo(() => [
    {
      title: '年份',
      dataIndex: 'year',
      key: 'year',
      width: 70,
      render: text => (
        <span title={text}>{bizStringFormat(text)}</span>
      ),
    },
    {
      title: '公告标题',
      dataIndex: 'title',
      key: 'title',
      render: text => (
        <span title={text}>{bizStringFormat(text)}</span>
      ),
    },
    {
      title: '发布方式',
      dataIndex: 'pubType',
      width: 118,
      render: (text) => {
        return getPubType(text);
      },
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      width: 180,
      render: (text, record) => {
        return (
          <React.Fragment>
            <div>
              <div className="config-label">公告发布时间：</div>
              <span
                title={bizDatetimeFormat(record?.releasedAt)}
              >
                {bizDatetimeFormat(record?.releasedAt)}
              </span>
            </div>
            <div>
              <div className="config-label">公告截止时间：</div>
              <span
                title={bizDatetimeFormat(record?.expiredAt)}
              >
                {bizDatetimeFormat(record?.expiredAt)}
              </span>
            </div>
          </React.Fragment>
        );
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 90,
      render: text => (
        <span title={text}>{bizStringFormat(text)}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 90,
      className: 'nowrap',
      render: (text, record, i) => {
        let status;
        switch (record?.showStatus) {
          case 0:
          case 1:
          case 2:
            status = 'processing';
            break;
          case 3:
            status = 'warning';
            break;
          case 4:
          case 5:
            status = 'success';
            break;
          case 9:
            status = 'default';
            break;
          default:
            status = 'processing';
        }
  
        return (
          <div>
            <Badge status={status} text={record?.showStatusName} />
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      key: 'options',
      width: 117,
      render: (options, item) => {
        // 待完善：删除、编辑
        // 审核中：审核、撤回
        // 已发布：查看、查看公告链接
        const operation = [{
          key: 2,
          value: '审核',
        }, {
          key: 4,
          value: '编辑',
        }, {
          key: 8,
          value: '删除',
        }];
        const itemList: any[] = [];
        const hasOurUrlList = !!item.announcementOutUrlDtoList?.length;
        operation.forEach((op) => {
          if ((op.key & item.dealCode) === op.key) {
            if (op.key === 2) { // 审核
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  goDetail(item);
                },
              });
            }
            if (op.key === 4) { // 编辑
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  goEdit(item);
                },
              });
            }
            if (op.key === 8) { // 删除
              itemList.push({
                label: op.value,
                onRowClick: () => {
                  confirm({
                    title: '提示',
                    content: '确认删除此公告？',
                    onOk: () => {
                      if (item?.formPageCode) {
                        deleteFormAnnouncement({
                          id: item.annId,
                          pageCode: item?.formPageCode,
                          processDefineKey: item?.processDefineKey,
                        }).then((res = {}) => {
                          if (res.success) {
                            message.success('删除成功！');
                            setTimeout(() => {
                              getListData();
                            }, 1000); // 等待后端 ES 执行结果
                          }
                        });
                      }
                    },
                  });
                },
              });
            }
          }
        });
        if (options?.canRevoke) {
          itemList.push({
            label: '撤回',
            onRowClick: () => {
              // @ts-ignore
              customRender(<RevokeModal currentRecord={item} reload={getListData} />);
            },
          });
        }

        if (itemList.length === 0 && !hasOurUrlList) {
          return (
            <a
              className="customLinkBlue"
              onClick={() => goDetail(item)}
            >
              查看
            </a>
          );
        }
        return (
          <div>
            {hasOurUrlList 
              ? (<div style={{ marginBottom: '8px' }}><OutUrlContent hideDivider list={item?.announcementOutUrlDtoList} /></div>)
              : null}
            {
              itemList?.length > 0
                ? itemList?.map((i, index) => {
                  return (
                    <div key={index} style={{ marginBottom: '8px' }}>
                      <Button
                        text
                        type="primary"
                        onClick={i?.onRowClick}
                      >
                        {i?.label}
                      </Button>
                    </div>
                  );
                })
                : null
            }
          </div>
        );
      },
    },
  ], []);

  return (
    <Drawer
      title="公告列表"
      width={880}
      visible
      onClose={onCancel}
    >
      <Alert showIcon
        message={
          <span>
            当前仅展示本账号创建的公告
            <Button
              onClick={() => {
                const params = getQueryParamObj(location.search, { isDecode: false });
                const queryString = Object.keys(params)
                  .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params?.[key])}`)
                  .join('&');
                const url = Object.keys(params)?.length 
                  ? `/announcement-front/#/manage/list/14?${queryString}`
                  : '/announcement-front/#/manage/list/14';
                // 点击新开页跳转到【其他政府采购公告】页面
                window.open(url, '_blank');
              }}  
              style={{ marginLeft: '8px' }}
              type="link"
            >查看本单位公告
            </Button>
          </span>}
      />
      <ZcyList
        className="sme-announcement-list-drawer"
        table={{
          columns,
          dataSource,
          pagination: {
            total,
          },
        }}
        customItem={[]}
        onSearch={getListData}
      />
    </Drawer>
  );
};

export default AnnouncementListDrawer;
