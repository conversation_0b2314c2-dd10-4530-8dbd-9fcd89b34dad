import { Icon, Modal, Form, Input, message } from '@zcy/doraemon';
import React from 'react';
import { recallFormAnnouncement, revokeAnnouncement } from 'src/routes/Manage/services';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};

const RevokeModal = ({ visible, form, onCancel, close, currentRecord, reload }) => {
  const { getFieldDecorator } = form;

  const onOk = () => {
    const {
      annId = '',
      formPageCode = '',
      processDefineKey = '',
      taskModelId = '',
    } = currentRecord;
    form.validateFields((err, values) => {
      if (!err) {
        if (formPageCode) {
          recallFormAnnouncement({
            id: annId,
            pageCode: formPageCode,
            processDefineKey,
            taskId: taskModelId,
            reason: values.revokeRes || '',
          }).then((res) => {
            if (res && res.success) {
              message.success('撤回成功！');
              setTimeout(() => {
                reload();
              }, 1000); // 等待后端 ES 执行结果
              close();
            }
          });
        } else {
          revokeAnnouncement({
            ...values,
            announcementId: annId,
          }).then((res) => {
            if (res.success) {
              message.success('撤回成功！');
              setTimeout(() => {
                reload();
              }, 1000); // 等待后端 ES 执行结果
              close();
            }
          });
        }
      }
    });
  };

  return (
    <Modal
      title="公告撤回"
      visible={visible}
      onOk={onOk}
      width={540}
      onCancel={onCancel}
    >
      <Form layout="horizontal">
        <FormItem label="撤回理由" {...formItemLayout}>
          {getFieldDecorator('revokeRes', {
            rules: [{ required: true, message: '请输入' }],
          })(
            <Input.TextArea maxLength={255} placeholder="请输入" />
          )}
        </FormItem>
      </Form>
      <div>
        <Icon type="exclamation-circle" style={{ color: 'orange' }} /> 提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作。
      </div>
    </Modal>
  );
};

export default Form.create()(RevokeModal);
