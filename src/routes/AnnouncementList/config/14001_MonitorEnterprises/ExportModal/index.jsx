import { Form, Modal, Select, Alert, message, Progress } from '@zcy/doraemon';
import React, { useEffect, useState } from 'react';
import { customRender } from 'src/utils/customRender';
import { getSmallListYear } from 'src/routes/Overview/services';
import { getAnnualInfoCount, exportAnnualInfo, pollingAnnualInfo } from '../../../services';
import './index.less';

const Option = Select.Option;

const ExportModal = Form.create()(({ visible, form, onCancel, close, showExtraTip }) => {
  const { getFieldDecorator } = form;
  const [yearArr, setYearArr] = useState([]);

  const defaultYear = yearArr.find(org => org?.isDefaultValue)?.year;

  useEffect(() => {
    getSmallListYear({ automaticExclusion: false }).then((res) => {
      setYearArr(res?.result);
    });
  }, []);

  // 检查指定年度下中小企业预留执行情况的条目数是否为 0，如果为 0 则报错
  const checkInfoCount = async (year) => {
    const res = await getAnnualInfoCount({ year });
    if (res?.success && res?.result) {
      return res?.result?.count > 0;
    }
    return false;
  };

  // 导出指定年度下中小企业预留执行情况的汇总数据，获取轮询任务 id
  const getPollingTaskId = async (year) => {
    const res = await exportAnnualInfo({ year });
    if (res?.success && res?.result) {
      return res?.result?.taskUuid;
    }
    return null;
  };
  
  // 导出指定年度下中小企业预留执行情况的汇总数据（轮询）
  const pollingExportTask = async (taskUuid, modal, timeInterval = 500) => {
    const res = await pollingAnnualInfo({ taskUuid });
    if (res?.success && res?.result) {
      const { currentProgress, exportFileUrl } = res?.result || {};
      modal?.update({ content: <Progress size="small" percent={currentProgress} /> });
      if (currentProgress === 100 && exportFileUrl) {
        modal?.destroy();
        return Modal.success({ 
          title: '数据导出成功，请下载',
          okText: '下载',
          closable: true,
          onOk: () => {
            window.open(exportFileUrl);
          },
        });
      }

      setTimeout(() => {
        pollingExportTask(taskUuid, modal);
      }, timeInterval);
    } else {
      modal?.destroy();
      Modal.error({
        title: '数据导出失败，请重试',
        okCancel: true,
        closable: true,
        okText: '重试',
        cancelText: '暂不重试',
        onOk: () => {
          customRender(<ExportModal showExtraTip={showExtraTip} />);
        },
      });
    }
  };

  const handleOnExport = () => {
    form?.validateFields(async (err, values) => {
      if (err) return;

      const { year } = values;
      const checkRes = await checkInfoCount(year);

      if (!checkRes) {
        return message.info('无可导出数据');
      }
      
      const taskId = await getPollingTaskId(year);
      if (taskId) {
        const modal = Modal.info({
          className: 'sme-export-modal',
          title: '正在导出数据，请勿关闭当前页面',
          content: <Progress size="small" percent={0} />,
        });
        await pollingExportTask(taskId, modal);
      }

      return close?.();
    });
  };

  return (
    <Modal
      title="导出"
      extra={showExtraTip && <Alert banner showIcon type="info" message="仅支持导出本单位全部数据及下级单位已审核完成数据" />}
      visible={visible}
      onCancel={onCancel}
      onOk={handleOnExport}
    >
      <Form
        style={{ marginTop: '24px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
      >
        <Form.Item label="年度">
          {
            getFieldDecorator('year', {
              initialValue: defaultYear,
              rules: [{
                required: true, message: '请选择年度',
              }],
            })(
              <Select placeholder="请选择">
                {yearArr.map((org = {}) => (
                  <Option 
                    value={org.year} 
                    key={org.year}
                    disabled={!org.isEnable}
                  >
                    <div>{org.year}</div>
                  </Option>
                ))}
              </Select>
            )}
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default ExportModal;
