import React from 'react';
import {
  Select,
  Input,
} from 'doraemon';

const { Option } = Select;
// 获得搜索项配置
export const searchItemConfig = (component) => {
  const { canDisplayAnnualSearch, canDisplayOrgNameSearch, yearArr } = component.state;
  const baseItem = [];
  if (canDisplayAnnualSearch) {
    baseItem.push({
      label: '年度',
      id: 'year',
      render: () => {
        return (
          <Select
            allowClear
            showSearch
          >
            {yearArr.map((org = {}) => (
              <Option value={org.year} key={org.year}>{org.year}</Option>
            ))}
          </Select>
        );
      },
    });
  }
  if (canDisplayOrgNameSearch) {
    baseItem.push(
      {
        label: '采购单位名称',
        id: 'orgName',
        render: () => <Input placeholder="请输入" />,
      }
    );
  }
  return baseItem;
};

