import React from 'react';
import { Button, Dropdown, Icon, Menu } from '@zcy/doraemon';
import { customRender } from 'src/utils/customRender';
import ExportModal from './ExportModal';
import '../config.less';

export const breadcrumbConfig = (component) => {
  const { 
    canExport,
    canCreate,
    canGenerateAnnualData,
    canShowAnnouncementList,
  } = component.state;

  const temp = [];

  // 导出按钮
  canExport && temp.push(
    <Button
      key="export"
      className="btn-throttle"
      onClick={() => {
        customRender(<ExportModal showExtraTip={component?.state?.isResponsibleBudgetUnit} />);
      }}
    >
      导出
    </Button>
  );

  // 如果同时可创建公告和展示公告列表，展示为公告操作（菜单下拉）
  if (canCreate && canShowAnnouncementList) {
    temp.push(
      <Dropdown key="operation"
        overlay={
          <Menu>
            <Menu.Item key="create" onClick={() => component.handelAddAnnouncement()}>创建公告</Menu.Item>
            <Menu.Item key="list" onClick={() => component.handleShowAnnouncementList()}>公告列表</Menu.Item>
          </Menu>
        }
      >
        <Button type="primary">
          公告操作<Icon dora type="doraicon-down-o" />
        </Button>
      </Dropdown>
    );
  } else {
    // 根据 canCreate 添加创建公告按钮
    if (canCreate) {
      temp.push(
        <Button key="create" type="primary" onClick={() => component.handelAddAnnouncement()}>
          创建公告
        </Button>
      );
    }

    if (canShowAnnouncementList) {
      temp.push(
        <Button key="list" type="primary" onClick={() => component.handleShowAnnouncementList()}>
          公告列表
        </Button>
      );
    }
  }

  // 根据 canGenerateAnnualData 添加生成年度数据按钮
  if (canGenerateAnnualData) {
    temp.push(
      <Button 
        type="primary"
        key="generate"
        onClick={() => component.handleGenerateAnnualData()}
      >
        生成年度数据
      </Button>
    );
  }
    
  let buttons;
  if (temp.length) {
    buttons = (
      <span className="config-breadcrumb-button">
        {temp.map(ele => ele)}
      </span>
    );
  }
    

  return {
    routes: [{
      label: '中小企业预留执行情况',
    }],
    customContent: buttons,
  };
};
