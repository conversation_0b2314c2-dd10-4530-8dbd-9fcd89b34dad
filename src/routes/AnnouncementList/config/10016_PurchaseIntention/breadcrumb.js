import React from 'react';
import { Button } from 'doraemon';
import { routerRedux } from 'dva/router';
import PlanModal from './PlanMultipleModal';
import BudgetModal from './BudgetMultipleModal';

export const breadcrumbConfig = (component) => {
  const { annModal = false, config = {}, env,  
    pushPurchaseIntentionWithBtn, purchaseIntentionBtn, purchaseIntentionForPlanBtn, 
    purchaseIntentionManualBtn } = component.state;

  const customButtonPlan = (
    <React.Fragment>
      <Button
        onClick={() => {
          component.setState({
            annModal: true,
          });
        }}
        type="primary"
      >采购意向发布
      </Button>
      {annModal && (
        <PlanModal
          show={annModal}
          hideModal={() => {
            component.setState({
              annModal: false,
            });
          }}
        />
      )}
    </React.Fragment>
  );
  
  const temp = [];
  if (purchaseIntentionManualBtn) {
    const buttonProps = {
      onClick: () => {
        component.props.dispatch(
          routerRedux.push(
            `/dynamic/create?formPageCode=publicNoticeOfPurchaseIntention&announcementTypeName=${escape('政府采购意向公开')}&announcementType=10016&annBigType=1`
          )
        );
      },
      type: 'primary',
      style: {
        marginLeft: '8px',
      },
      children: '手工发布采购意向',
    };
    if (env === 'gz') {
      pushPurchaseIntentionWithBtn && temp.push(
        <Button {...buttonProps}>手工发布采购意向</Button>
      );
    } else {
      temp.push(
        <Button {...buttonProps}>手工发布采购意向</Button>
      );
    }
  }
  if (purchaseIntentionBtn && ['1', '2'].includes(config.allowPlanOrBudgetIntentionManage)) {
    temp.push(
      customButtonPlan
    );
  } 
  if (purchaseIntentionForPlanBtn) {
    temp.push(
      <React.Fragment>
        <Button
          onClick={() => {
            component.setState({
              annModal: true,
            });
          }}
          type="primary"
        >选择采购指标发布采购意向
        </Button>
        {annModal && (
          <BudgetModal
            show={annModal}
            env={env}
            hideModal={() => {
              component.setState({
                annModal: false,
              });
            }}
          />
        )}
      </React.Fragment>);
  }
  let buttons;
  if (temp.length) {
    buttons = (
      <span className="config-breadcrumb-button">
        {temp.map(ele => ele)}
      </span>
    );
  }

  return {
    routes: [{
      label: '采购意向管理',
    }],
    // 1: 'purchase', // 采购单位
    // 2: 'ministry', // 主管单位
    // 3: 'finance', // 财政监管只可看本区划
    // 4: 'financeOnly', // 财政监管可以看下级区划
    customContent: buttons,
  };
};
