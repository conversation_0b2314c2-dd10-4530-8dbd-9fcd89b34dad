import React from 'react';
import {
  Select,
  Input,
  DatePicker,
  Cascader,
  request,
} from 'doraemon';
import moment from 'moment';
import _ from 'lodash';

const { Option } = Select;
const { RangePicker } = DatePicker;
const statusArr = [
  {
    label: '待完善',
    value: 0,
  },
  {
    label: '审核中',
    value: 1,
  },
  {
    label: '待发布',
    value: 2,
  },
  {
    label: '被回退',
    value: 3,
  },
  {
    label: '已发布',
    value: 4,
  },
  {
    label: '已结束',
    value: 5,
  },
  {
    label: '取消审核中',
    value: 8,
  },
  {
    label: '已取消',
    value: 9,
  },
];

const getDistricts = async (component) => {
  const res = await request('/announcement/api/subDist', {
    params: {
      districtCode: window.currentUserDistrict.code,
    },
  });
  const dealDistrict = (list) => {
    list?.map((item = {}) => {
      item.value = item.districtCode;
      item.label = item.districtName;
      if (Array.isArray(item.children)) {
        dealDistrict(item.children);
      }
    });
    return list;
  };
  const districts = dealDistrict(res.result);
  component.setState({
    districts,
  });
};
const onDistrictChange = async (component, v) => {
  const res = await request('/announcement/api/orgList', {
    params: {
      districtCode: (Array.isArray(v) && v.length)
        ? v[v.length - 1]
        : window.currentUserDistrict.code,
    },
  }) || {};
  const orgs = res.result;
  component.setState({
    orgs,
    districtCode: v,
  });
};

const onSelectChange = async (component, v) => {
  const districtCode = component.state.districtCode;
  const res = await request('/announcement/api/orgList', {
    params: {
      districtCode: Array.isArray(districtCode)
        ? [...districtCode].pop() // 取最后一个即可
        : window.currentUserDistrict.code,
      keyWord: v,
    },
  }) || {};
  const orgs = res.result;
  component.setState({
    orgs,
  });
};
const getPlanConfig = async (component) => {
  const res = await request('/api/purchaseplan/config/detail', {
    params: {
      districtCode: window.currentUserDistrict.code,
    },
  });
  const config = res.result || {};
  component.setState({
    config,
  });
};


/**
 *
 * @param {
    ...(['financeOnly'].includes(identity) ? [{
      label: '区划',
      id: 'district',
      decoratorOptions: {
        onChange: (v) => {
          onDistrictChange(component, v);
        },
      },
      render: () => <Cascader options={districts} placeholder="请选择区划" />,
    }] : []),} component
 */
// 获得搜索项配置
export const searchItemConfig = (component) => {
  const { districts = [], orgs = [], identity = '' } = component.state;
  const baseItem = [
    {
      label: '发布单位',
      id: 'orgIds',
      render: () => {
        return (
          <Select
            mode="multiple"
            filterOption={
              (input, option) => {
                return option.props.children
                  ? (option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0)
                  : false;
              }
            }
            optionFilterProp="children"
            allowClear
            showSearch
            onSearch={_.debounce(v => onSelectChange(component, v), 100)}
            // 若未选中数据则重新拉取数据
            onBlur={v => !v && onSelectChange(component, v)}
          >
            {
              !_.isEmpty(orgs) && orgs.map(org => (
                <Option value={org.id} key={org.id}>{org.name}</Option>
              ))
            }
          </Select>
        );
      },
    }, {
      label: '发布时间',
      id: 'date',
      render: () => {
        return (
          <RangePicker
            showTime={{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('00:00:00', 'HH:mm:ss')],
            }}
          />
        );
      },
    }, {
      label: '公告标题',
      id: 'title',
      render: () => <Input placeholder="请输入" />,
    }, {
      label: '状态',
      id: 'queryStatus',
      render: () => {
        return (
          <Select
            filterOption={
              (input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            allowClear
            showSearch
          >
            {statusArr.map((org = {}) => (
              <Option value={org.value} key={org.value}>{org.label}</Option>
            ))}
          </Select>
        );
      },
    },
  ];
  if (identity === 'financeOnly') {
    baseItem.unshift({
      label: '区划',
      id: 'district',
      decoratorOptions: {
        onChange: (v) => {
          onDistrictChange(component, v);
        },
      },
      render: () => <Cascader options={districts} placeholder="请选择区划" />,
    });
  }
  baseItem.push({
    label: '关键字',
    id: 'contentKeyWord',
    render: () => <Input placeholder="请输入" />,
  });
  baseItem.push({
    label: '项目名称',
    id: 'metaDataSearchKeys',
    render: () => <Input placeholder="请输入" />,
  });
  return baseItem;
};

// 初始化查询
export const iniLoad = (component) => {
  getDistricts(component);
  onDistrictChange(component);
  getPlanConfig(component);
};
