import React, { Component } from 'react';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import { <PERSON><PERSON>, <PERSON>cyL<PERSON>, Button, Tag, message, Tooltip, Select, request, Input } from 'doraemon';
import LargeContent from 'components/LargeShow';
import { moneyFormat } from 'utils/utils';

const Option = Select.Option;
const confirm = Modal.confirm;

const columns = [
  {
    title: '采购编号',
    dataIndex: 'purchaseNo',
    key: 'purchaseNo',
    width: 140,
    render: (text, record) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >
          <a
            href={`${window.envHref.settlement}/purchaseplan_front/#/plan/list/view?id=${record.id}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <LargeContent width={105} content={text} />
          </a>
          {!!record.isTemporary && <span className="temporary">临</span>}
          {record.intentionPublished === 1 && '(已发布)'}
        </Tooltip>
      );
    },
  },
  {
    title: '资金总额(元)',
    dataIndex: 'totalMoney',
    key: 'totalMoney',
    width: 140,
    align: 'right',
    render: (text, record) => {
      return (
        <div className="colPlanInfo">
          <div>
            <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
          </div>
          <div>
            <span style={{ fontSize: 12 }} className="colLabel">(可用: {moneyFormat(record.availMoney)})</span>
          </div>
        </div>
      );
    },
  },
  {
    title: '采购编号名称',
    dataIndex: 'projectName',
    key: 'projectName',
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >
          <LargeContent width={237} content={text} />
        </Tooltip>
      );
    },
  },
  {
    title: '组织形式',
    dataIndex: 'procurementTypeName',
    key: 'procurementTypeName',
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >
          <LargeContent width={237} content={text} />
        </Tooltip>
      );
    },
  },
  {
    title: '采购方式',
    dataIndex: 'procurementMethodName',
    key: 'procurementMethodName',
    width: 100,
    render: (text) => {
      return (
        <Tooltip
          title={text}
          placement="topLeft"
          getPopupContainer={n => n}
        >

          <LargeContent width={85} content={text} />
        </Tooltip>
      );
    },
  },
];

@connect(({ appCommon, loading }) => ({
  appCommon,
  loading,
}))
export default class PlanModal extends Component {
  state = {
    plans: [],
    plansTotal: 0,
    pageNo: 1, // 当前页
    result: null, // 选中项的id
    selected: [], // 选中项对象
    pageSelected: {}, // 每页选中项对象{page1: []}
  };
  componentWillUnmount() {
    this.setState = () => {
      // This is intentional
    };
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.show && !this.props.show) {
      this.onSearch();
    }
  }
  componentDidMount() {
    this.onSearch();
  }
  onSearch = (params) => {
    const { dispatch } = this.props;
    const url = '/api/purchaseplan/subOrganList';
    const myparams = params ? {
      ...params,
      url,
    } : {
      pageNo: 1,
      pageSize: 6,
      url,
    };
    if (myparams.intentionPublished === undefined) {
      myparams.intentionPublished = 2;
    }
    dispatch({
      type: 'appCommon/fetchData',
      payload: myparams,
    }).then((res) => {
      if (res) {
        this.setState({
          plans: res.data,
          plansTotal: res.total,
          pageNo: params ? params.pageNo : 1,
        });
      }
    });
  }
  onOk = async () => {
    const { result } = this.state;
    if (!result || !result.length) {
      message.destroy();
      message.error('请选择采购资金');
      return;
    }
    if (result.length > 20) {
      message.destroy();
      message.error('总数不可超过20条。');
      return;
    }
    const res = await request('/api/purchaseplan/intention/announcement/checkPurchaseIntention', {
      params: {
        pageType: 1,
        ids: result.join(','),
        announcementType: 10016,
      },
    });
    if (!res.success) {
      confirm({
        content: res.error,
        closable: true,
        onOk: () => {
          this.onAdd(result);
        },
      });
    } else {
      this.onAdd(result);
    }
  }
  onAdd = async (ids) => {
    const res = await request('/api/purchaseplan/intention/announcement/buildPurchaseIntention', {
      method: 'post',
      data: {
        ids,
        pageType: 1,
      },
    });
    const data = res.result;
    // const url = '/flow/create';
    const url = '/dynamic/create';
    const params = [
      // `announcementTypeName=${}`,
      'formPageCode=publicNoticeOfPurchaseIntention',
      `announcementType=${data.announcementType}`,
      `orderId=${data.serialNum}`,
      // `projectCode=${}`,
      // `projectName=${}`,
      `districtId=${data.districtCode}`,
      `isRelationProject=${true}`,
      `appCode=${data.appCode}`,
      `title=${escape(data.title)}`,
      'annBigType=1',
      // `ids=${}`,
    ];
    this.props.dispatch(
      routerRedux.push(`${url}?${params.join('&')}`)
    );
  }
  del = (id) => {
    const { pageSelected } = this.state;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          if (pageSelected[item][j].id !== id) {
            result.push(pageSelected[item][j].id);
            selected.push(pageSelected[item][j]);
          } else {
            pageSelected[item].splice(j, 1);
            j -= 1;
          }
        }
      }
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  onSelectChange = (ids, rows) => {
    const { pageNo } = this.state;
    const pageSelected = {
      ...this.state.pageSelected,
    };
    pageSelected[`page${pageNo}`] = rows;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          result.push(pageSelected[item][j].id);
          selected.push(pageSelected[item][j]);
        }
      }
    }
    if (result.length > 20) {
      message.destroy();
      message.error('批量变更总数不可超过20条！');
      return;
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  render() {
    const { plansTotal, plans, result, selected } = this.state;
    const { hideModal, loading } = this.props;
    return (
      <Modal
        title="采购资金选择"
        visible={this.props.show}
        width={980}
        onCancel={hideModal}
        onOk={this.onOk}
        className="tableModal modalList"
        footer={[
          <Button key="back" onClick={hideModal}>取消</Button>,
          <Button
            loading={loading.effects['planList/openAnn']}
            key="submit"
            onClick={this.onOk}
            type="primary"
          >
            确定
          </Button>,
        ]}
      >
        <ZcyList
          customItem={[
            {
              label: '采购编号',
              id: 'purchaseNo',
              render: () => {
                return <Input placeholder="请输入" />;
              },
            },
            {
              label: '是否已发布',
              id: 'intentionPublished',
              decoratorOptions: {
                initialValue: '2',
              },
              render: () => {
                return (
                  <Select getPopupContainer={node => node}>
                    <Option value="2">全部</Option>
                    <Option value="0">未发布</Option>
                    <Option value="1">已发布</Option>
                  </Select>
                );
              },
            },
            {
              label: '采购编号名称',
              id: 'projectName',
              render: () => {
                return <Input placeholder="请输入" />;
              },
            },
          ]}
          onSearch={this.onSearch}
          column={2}
          tabs={{
            tabList: [{
              label: '计划列表',
              value: 'all',
              key: 'all',
            }],
          }}
          preTabelContent={(
            <div style={{
              borderLeft: '1px solid #eaeaea',
              borderRight: '1px solid #eaeaea',
              borderTop: '1px solid #eaeaea',
              padding: '4px 10px 0',
            }}
            >
              {selected.map(item => (
                <Tag
                  closable
                  color="orange"
                  key={item.id}
                  onClose={() => {
                    this.del(item.id);
                  }}
                  style={{ marginBottom: '4px' }}
                >
                  {item.purchaseNo}
                </Tag>
              ))}
            </div>
          )}
          table={{
            rowKey: 'id',
            columns,
            dataSource: plans,
            pagination: {
              total: plansTotal,
              defaultPageSize: 6,
            },
            rowSelection: {
              selectedRowKeys: result,
              type: 'checkbox',
              onChange: this.onSelectChange,
              getCheckboxProps: record => ({
                disabled: record.canReturn === 0,
              }),
            },
            loading: loading.effects['appCommon/fetchData'],
          }}
        />
      </Modal>
    );
  }
}
