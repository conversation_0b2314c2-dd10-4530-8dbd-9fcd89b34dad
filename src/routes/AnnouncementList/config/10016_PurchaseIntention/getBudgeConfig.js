import React from 'react';
import { Tooltip } from 'doraemon';
import LargeContent from 'components/LargeShow';
import { moneyFormat } from 'utils/utils';

export const getColumns = (env) => {
  switch (env) {
  case 'ah':
    return [
      {
        title: '指标编号',
        key: 'purchaseNo',
        dataIndex: 'purchaseNo',
        width: 120,
        render: (text, rec) => {
          if (text) {
            return rec.intentionPublished === 1 ? `${text}(已发布)` : text;
          }
        },
      },
      {
        key: 'totalAmount',
        dataIndex: 'totalAmount',
        title: '指标金额(元)',
        width: 140,
        align: 'right',
        render: (text) => {
          return (
            <div className="colPlanInfo">
              <div>
                <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
              </div>
            </div>
          );
        },
      },
      {
        title: '项目名称',
        dataIndex: 'projectName',
        key: 'projectName',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={200} content={text} />
            </Tooltip>
          );
        },
      },
      {
        title: '项目编号',
        dataIndex: 'projectNo',
        key: 'projectNo',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={200} content={text} />
            </Tooltip>
          );
        },
      },
      {
        title: '组织形式',
        dataIndex: 'procurementType',
        key: 'procurementType',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={90} content={text} />
            </Tooltip>
          );
        },
      },
    ];
  case 'sx':
    return [
      {
        title: '预算编号',
        key: 'budgetNo',
        dataIndex: 'budgetNo',
        width: 120,
        render: (text, rec) => {
          if (text) {
            return rec.intentionPublished === 1 ? `${text}(已发布)` : text;
          }
        },
      },
      {
        key: 'totalMoney',
        dataIndex: 'totalMoney',
        title: '预算总额(元)',
        width: 140,
        align: 'right',
        render: (text) => {
          return (
            <div className="colPlanInfo">
              <div>
                <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
              </div>
            </div>
          );
        },
      },
      {
        title: '项目信息',
        key: 'projectInfo',
        width: 180,
        render: (text, rec) => {
          if (text) {
            return (
              <div>
                <p>指标类型：{rec.targetTypeName}</p>
                <p>采购编号名称：{rec.projectName}</p>
                <p>项目编号：{rec.projectNo}</p>
                <p>部门经济分类：{rec.economicSubjectCode}{rec.economicSubjectName}</p>
                <p>预算单位：{rec.orgName}</p>
              </div>
            );
          }
        },
      },
    ];
  default:
    return [
      {
        title: '预算编号',
        key: 'budgetNo',
        dataIndex: 'budgetNo',
        width: 120,
        render: (text, rec) => {
          if (text) {
            return rec.intentionPublished === 1 ? `${text}(已发布)` : text;
          }
        },
      },
      {
        key: 'totalMoney',
        dataIndex: 'totalMoney',
        title: '预算总额(元)',
        width: 140,
        align: 'right',
        render: (text) => {
          return (
            <div className="colPlanInfo">
              <div>
                <span style={{ color: '#ff9900' }}>{moneyFormat(text)}</span>
              </div>
            </div>
          );
        },
      },
      {
        title: '采购编号名称',
        dataIndex: 'projectName',
        key: 'projectName',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={237} content={text} />
            </Tooltip>
          );
        },
      },
      {
        title: '项目编号',
        dataIndex: 'projectNo',
        key: 'projectNo',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={237} content={text} />
            </Tooltip>
          );
        },
      },
      {
        title: '组织形式',
        dataIndex: 'procurementTypeName',
        key: 'procurementTypeName',
        render: (text) => {
          return (
            <Tooltip
              title={text}
              placement="topLeft"
              getPopupContainer={n => n}
            >
              <LargeContent width={237} content={text} />
            </Tooltip>
          );
        },
      },
    ];
  }
};

export const getBudgetAPI = (env) => {
  if (env === 'ah') {
    return {
      listURL: '/announcement/api/purchasePlan/pagingPurchaseIntentionData',
      checkURL: '/announcement/api/purchasePlan/checkPurchaseIntention',
      buildURL: '/announcement/api/purchasePlan/buildPurchaseIntentionAnnouncement',
    };
  }
  return {
    listURL: '/api/budget/quota/paging',
    checkURL: '/api/purchaseplan/intention/announcement/checkPurchaseIntention',
    buildURL: '/api/purchaseplan/intention/announcement/buildPurchaseIntention',
  };
};


export const getBudgetModalTitle = (env) => {
  if (env === 'ah') {
    return '采购指标选择';
  }
  return '采购预算选择';
};
