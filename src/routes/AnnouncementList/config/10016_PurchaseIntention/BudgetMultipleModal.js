import React, { Component } from 'react';
import { connect } from 'dva';
import debounce from 'lodash/debounce';
import { Modal, ZcyList, Button, Tag, message, Input, Select, request } from 'doraemon';
import { fetchPublishOrgListApi } from 'src/routes/Manage/services';
import { routerRedux } from 'dva/router';
import { getColumns, getBudgetAPI, getBudgetModalTitle } from './getBudgeConfig';

const Option = Select.Option;
const { pageType } = window;
const confirm = Modal.confirm;

@connect(({ appCommon, loading }) => ({
  appCommon,
  loading,
}))
export default class PlanModal extends Component {
  state = {
    plans: [],
    plansTotal: 0,
    pageNo: 1, // 当前页
    result: null, // 选中项的id
    selected: [], // 选中项对象
    pageSelected: {}, // 每页选中项对象{page1: []}
    selectData: [], // 预算单位数据
  };
  componentWillUnmount() {
    this.setState = () => {
      // This is intentional
    };
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.show && !this.props.show) {
      this.onSearch();
    }
  }
  componentDidMount() {
    this.onSearch();
    this.getSelectData();
  }

  getSelectData = (orgName) => {
    fetchPublishOrgListApi({
      userTypes: '01', // 只获取采购单位
      filterByPrivilege: false,
      orgName,
    }).then(({ result = [] }) => {
      this.setState({
        selectData: result,
      });
    });
  };


  onSearch = (params) => {
    const { dispatch, env } = this.props;
    const { listURL: url } = getBudgetAPI(env);
    if (params?.orgIds) {
      params.orgIds = params?.orgIds?.toString() || '';
    }
    const myparams = params ? {
      intentionPublished: 2,
      ...params,
      url,
    } : {
      intentionPublished: 2,
      pageNo: 1,
      pageSize: 6,
      url,
    };

    // 兼容不同的入参
    if (env === 'ah') {
      myparams.purchaseIntentionPublished = myparams.intentionPublished;
      delete myparams.intentionPublished;
    }

    dispatch({
      type: 'appCommon/fetchData',
      payload: myparams,
    }).then((res) => {
      if (res) {
        this.setState({
          plans: res.data,
          plansTotal: res.total,
          pageNo: params ? params.pageNo : 1,
        });
      }
    });
  }
  onOk = async () => {
    const { result } = this.state;
    if (!result || !result.length) {
      message.destroy();
      message.error('请选择采购预算');
      return;
    }
    if (result.length > 20) {
      message.destroy();
      message.error('总数不可超过20条。');
      return;
    }
    const { checkURL } = getBudgetAPI(this.props.env);
    const res = await request(checkURL, {
      params: {
        pageType: 2,
        ids: result.join(','),
        announcementType: 10016,
      },
    });
    if (!res.success) {
      confirm({
        content: res.error || res?.message,
        closable: true,
        onOk: () => {
          this.onAdd(result);
        },
      });
    } else {
      this.onAdd(result);
    }
  }
  del = (id) => {
    const { pageSelected } = this.state;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          if (pageSelected[item][j].id !== id) {
            result.push(pageSelected[item][j].id);
            selected.push(pageSelected[item][j]);
          } else {
            pageSelected[item].splice(j, 1);
            j -= 1;
          }
        }
      }
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  onSelectChange = (ids, rows) => {
    const { pageNo } = this.state;
    const pageSelected = {
      ...this.state.pageSelected,
    };
    pageSelected[`page${pageNo}`] = rows;
    const result = [];
    const selected = [];
    for (const item in pageSelected) {
      if ({}.hasOwnProperty.call(pageSelected, item)) {
        for (let j = 0; j < pageSelected[item].length; j += 1) {
          result.push(pageSelected[item][j].id);
          selected.push(pageSelected[item][j]);
        }
      }
    }
    if (result.length > 20) {
      message.destroy();
      message.error('批量变更总数不可超过20条！');
      return;
    }
    this.setState({
      result,
      pageSelected,
      selected,
    });
  }
  onAdd = async (ids) => {
    const { buildURL } = getBudgetAPI(this.props.env);
    const res = await request(buildURL, {
      method: 'post',
      data: {
        ids,
        pageType: 2,
      },
    });
    const data = res.result;
    const url = '/dynamic/create';
    const params = [
      'formPageCode=publicNoticeOfPurchaseIntention',
      `announcementType=${data.announcementType}`,
      `orderId=${data.serialNum}`,
      `districtId=${data.districtCode}`,
      `isRelationProject=${true}`,
      `appCode=${data.appCode}`,
      `title=${escape(data.title)}`,
      'annBigType=1',
    ];
    this.props.dispatch(
      routerRedux.push(`${url}?${params.join('&')}`)
    );
  }

  // 获取下拉框选项公共方法
  getSelectOptions = () => {
    const { selectData } = this.state;
    const selectOptions = {
      allowClear: true,
      showSearch: true,
      mode: 'multiple',
      getPopupContainer: node => node,
      filterOption: (input, option) =>
        option.props?.children?.toLowerCase()?.indexOf(input.toLowerCase()) >= 0,
      placeholder: '请输入关键字搜索',
      onBlur: (e) => {
        // 失去焦点时 判断是否选中 若无则更新一把
        if (!e?.length) {
          this.getSelectData();
        }
      },
      onSearch: debounce(this.getSelectData, 200),
    };
    return {
      label: '预算单位',
      id: 'orgIds',
      render: () => (
        <Select {...selectOptions}>
          {
            selectData?.map((item = {}) => (
              <Select.Option
                key={item.code}
                value={item.code}
                title={item.name}
              >
                {item.name}
              </Select.Option>
            ))
          }
        </Select>
      ),
    };
  }

  render() {
    const { plansTotal, plans, result, selected } = this.state;
    const { hideModal, loading, env } = this.props;
    return (
      <Modal
        title={getBudgetModalTitle(env)}
        visible={this.props.show}
        width={980}
        onCancel={hideModal}
        onOk={this.onOk}
        className="modalList"
        footer={[
          <Button key="back" onClick={hideModal}>取消</Button>,
          <Button
            loading={loading.effects['purplan/openAnn']}
            key="submit"
            onClick={this.onOk}
            type="primary"
          >
            确定
          </Button>,
        ]}
      >
        <ZcyList
          customItem={[
            {
              label: '项目名称',
              id: 'projectName',
              render: () => {
                return (
                  <Input placeholder="请输入" />
                );
              },
            },
            {
              label: '是否已发布',
              id: 'intentionPublished',
              decoratorOptions: {
                initialValue: '2',
              },
              render: () => {
                return (
                  <Select getPopupContainer={node => node}>
                    <Option value="2">全部</Option>
                    <Option value="0">未发布</Option>
                    <Option value="1">已发布</Option>
                  </Select>
                );
              },
            },
            {
              label: '预算编号',
              id: 'budgetNo',
              render: () => {
                return (
                  <Input placeholder="请输入" />
                );
              },
            },
            ...(pageType === 'hunan' ? [] : [{
              label: '项目编号',
              id: 'projectNo',
              render: () => {
                return (
                  <Input placeholder="请输入" />
                );
              },
            }]),
            env === 'sx' && this.getSelectOptions(),
          ].filter(Boolean)}
          onSearch={this.onSearch}
          column={2}
          tabs={{
            tabList: [{
              label: '计划列表',
              value: 'all',
              key: 'all',
            }],
          }}
          preTabelContent={(
            <div style={{
              borderLeft: '1px solid #eaeaea',
              borderRight: '1px solid #eaeaea',
              borderTop: '1px solid #eaeaea',
              padding: '4px 10px 0',
            }}
            >
              {selected.map(item => (
                <Tag
                  closable
                  color="orange"
                  key={item.id}
                  onClose={() => {
                    this.del(item.id);
                  }}
                  style={{ marginBottom: '4px' }}
                >
                  {item.budgetNo || item.purchaseNo}
                </Tag>
              ))}
            </div>
          )}
          table={{
            rowKey: 'id',
            columns: getColumns(env),
            dataSource: plans,
            pagination: {
              total: plansTotal,
              defaultPageSize: 6,
            },
            rowSelection: {
              selectedRowKeys: result,
              type: 'checkbox',
              onChange: this.onSelectChange,
              getCheckboxProps: record => ({
                disabled: record.canReturn === 0,
              }),
            },
            loading: loading.effects['appCommon/fetchData'],
          }}
        />
      </Modal>
    );
  }
}
