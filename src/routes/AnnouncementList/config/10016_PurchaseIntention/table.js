import React from 'react';
import { Link } from 'dva/router';
import {
  bizStringFormat,
  bizDatetimeFormat,
} from '@zcy/utils';
import {
  Table,
  Badge,
  Tag,
} from 'doraemon';
import TimeCountDown from 'components/TimeCountDown';
import OutUrlContent from 'components/OutUrlContent';
import { renderTags, TAG_SOURCE_TYPE_MAP } from '../../../Manage/config/custom-tag';
import { getPubType } from 'utils/utils';

import '../config.less';

const Actions = Table.Actions;
// tab 信息
export const getTabs = (tab, identity) => {
  const tabs = {
    activeKey: tab || '0',
    defaultActiveKey: tab || '0',
  };

  const tabLists = {
    myTodo: {
      label: '我的待办',
      value: '1',
      key: '1',
    },
    all: {
      label: '全部',
      value: '0',
      key: '0',
    },
  };

  switch (identity) {
  case 'purchase':
  case 'ministry':
    tabs.tabList = [tabLists.myTodo, tabLists.all];
    tabs.defaultActiveKey = '1';
    break;
  case 'financeOnly':
  case 'finance':
    tabs.tabList = [tabLists.all];
    tabs.defaultActiveKey = '0';
    break;
  default:
    tabs.tabList = [];
    tabs.defaultActiveKey = '0';
    break;
  }
  return tabs;
};

// 操作权限
const getOperations = (component, record, i) => {
  const { options: operation, objectionState, canRevokePublishSeconds } = record;
  const {
    handleEdit,
    handleDelete,
    handleCheck,
    handleRevoke,
    handleRevokePublish,
    handlePublish,
    handleView,
    objectAction,
    handleNotify,
  } = component;
  const { hideRevertBtn = {} } = component.state;
  const operationList = {
    canPublish: {
      label: '发布',
      sort: 1,
      onRowClick: handlePublish,
    },
    canEdit: {
      label: '编辑',
      sort: 2,
      onRowClick: handleEdit,
    },
    canCheck: {
      label: '审核',
      sort: 3,
      onRowClick: handleCheck,
    },
    canRevoke: {
      label: '撤回',
      sort: 4,
      onRowClick: handleRevoke,
    },
    canView: {
      label: '查看',
      sort: 5,
      onRowClick: handleView,
    },
    canNotify: {
      label: '短信通知',
      sort: 6,
      onRowClick: handleNotify,
    },
    canDelete: {
      label: '删除',
      sort: 7,
      onRowClick: () => handleDelete(record),
    },
  };
  const actions = [];
  const operationKeys = Object.keys(operation);
  operationKeys?.forEach((item) => {
    if (operation[item] && operationList[item]) {
      actions.push(operationList[item]);
    }
  });
  if (objectionState === -1) {
    actions.push({
      label: '异议确认',
      onRowClick: objectAction,
    });
  }
  if (!actions.length) {
    actions.push(operationList.canView);
  }
  // 撤回发布
  if (canRevokePublishSeconds > 0 && !hideRevertBtn[`revertBtn${i}`]) {
    actions.push({
      label: '撤回发布',
      sort: 4,
      onRowClick: handleRevokePublish,
    });
  }
  return actions.sort((item, itemNext) => item.sort - itemNext.sort);
};

// 获得表格配置项
export const getTableConfig = (component) => {
  const { getParams, announcementList } = component.props;
  const tableQueryParams = getParams({
    pageNo: 1,
    pageSize: 10,
  });
  const { pageSize, pageNo } = tableQueryParams;

  const getColumns = () => {
    return [
      {
        title: '公告标题',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        render: (text, record) => {
          const { objectionState } = record;
          let objStr;
          if (objectionState === 0) {
            objStr = '无异议';
          }
          if (objectionState === 1) {
            objStr = '有异议';
          }
          let identiNames = [];
          if (record.metaData) {
            const metaData = JSON.parse(record.metaData);
            identiNames = metaData.identiNames || [];
          }
          identiNames = identiNames.join('，');
          return (
            <React.Fragment>
              <span className="short-for-ellipsis">
                {
                  identiNames.length > 0 && <Tag color="green" title={identiNames}>{identiNames}</Tag>
                }
                {
                  objStr ? <span>[{objStr}]&nbsp;</span> : ''
                }
                {
                  record.formPageCode ? (
                    <Link
                      to={`/dynamic/detail?formPageCode=${record.formPageCode}&annId=${record.id}&districtId=${record.district}&annBigType=${record.annBigType}`}
                      className="public-query-table-ann-title"
                    >
                      {bizStringFormat(text)}
                    </Link>
                  ) : (
                    <Link title={text} to={`/detail/${record.annBigType}/${record.id}`}>{bizStringFormat(text)}</Link>
                  )
                }
              </span>
              <React.Fragment>
                <br />
                {renderTags(TAG_SOURCE_TYPE_MAP.business, record.announcementTagDtoList,
                  { maxWidth: 180 })}
                {record.isReformation ? (
                  <Tag color="#CCA97D"
                    style={{
                      maxWidth: 180,
                      verticalAlign: 'baseline',
                      marginRight: 4,
                      cursor: 'default',
                      textOverflow: 'ellipsis',
                      overflow: 'hidden',
                    }}
                  >{record.isReformationDesc}
                  </Tag>
                ) : null}
              </React.Fragment>
            </React.Fragment>
          );
        },
      }, {
        title: '项目数量',
        dataIndex: 'extendFirst',
        key: 'extendFirst',
        width: 120,
        render: text => (
          <span title={text}>{bizStringFormat(text)}</span>
        ),
      }, {
        title: '采购总金额（元）',
        dataIndex: 'extendSecond',
        key: 'extendSecond',
        render: text => (
          <span title={text} className="config-purchase-intention-minWidth">{bizStringFormat(text)}</span>
        ),
      }, {
        title: '发布单位',
        dataIndex: 'orgName',
        key: 'orgName',
        width: 120,
        render: text => (
          <span title={text}>{bizStringFormat(text)}</span>
        ),
      }, {
        title: '发布方式',
        dataIndex: 'pubType',
        width: 120,
        render: (text) => {
          return getPubType(text);
        },
      }, {
        title: '发布时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: 180,
        render: text => (
          bizDatetimeFormat(text)
        ),
      }, {
        title: '状态',
        dataIndex: 'showStatusName',
        key: 'showStatusName',
        width: 120,
        className: 'nowrap',
        render: (text, record, i) => {
          let status;
          const { hideRevertBtn = {} } = component.state;
          switch (record.showStatus) {
          case 0:
          case 1:
          case 2:
            status = 'processing';
            break;
          case 3:
            status = 'warning';
            break;
          case 4:
          case 5:
            status = 'success';
            break;
          case 9:
            status = 'default';
            break;
          default:
            status = 'processing';
          }
          return (
            <div>
              <Badge status={status} text={text} />
              {(record.canRevokePublishSeconds > 0 && !hideRevertBtn[`revertBtn${i}`]) && (
                <TimeCountDown
                  time={(record.canRevokePublishSeconds || 0) * 1000}
                  cb={() => {
                    component.setState((state) => {
                      const { hideRevertBtn: btns } = state;
                      btns[`revertBtn${i}`] = true;
                      return {
                        hideRevertBtn: btns,
                      };
                    });
                  }}
                />
              )}
              <React.Fragment>
                <br />
                {renderTags(TAG_SOURCE_TYPE_MAP.system, record.announcementTagDtoList)}
              </React.Fragment>
            </div>
          );
        },
      }, {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        fixed: 'right',
        width: 190,
        render: (text, record, i) => {
          const hasOurUrlList = !!record.announcementOutUrlDtoList?.length;
          const actions = getOperations(component, record, i);
          return (
            <React.Fragment>
              {hasOurUrlList ? (
                <OutUrlContent 
                  hideDivider={!actions.length} 
                  list={record.announcementOutUrlDtoList}
                />
              ) 
                : null
              }
              <Actions actions={actions} record={record} />
            </React.Fragment>
          );
        },
      },
    ];
  };

  const columns = getColumns();

  const { data = [], total } = announcementList?.data || {};
  return {
    columns,
    dataSource: data,
    pagination: {
      pageSize,
      current: pageNo,
      showSizeChanger: true,
      total,
    },
  };
};
