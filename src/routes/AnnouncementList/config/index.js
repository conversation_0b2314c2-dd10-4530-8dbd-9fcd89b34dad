/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-02-13 11:26:20
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-02-14 11:07:32
 * @FilePath: /zcy-announcement-v2-front/src/routes/AnnouncementList/config/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import PurchaseIntentionParams from './10016_PurchaseIntention/index';
import SingleSourceParams from './3012_SingleSource/index';
import MonitorEnterprisesParams from './14001_MonitorEnterprises/index';

export const getListParams = (annType) => {
  if (annType === '10016') {
    // 采购意向公告
    return PurchaseIntentionParams;
  }
  if (annType === '3012') {
    // 单一来源公告
    return SingleSourceParams;
  }
  if (annType === '14001') {
    // 中小企业年度数据
    return MonitorEnterprisesParams;
  }
};
