/* eslint-disable react/no-unused-state */
/* eslint-disable no-extra-boolean-cast */
import React, { Component } from 'react';
import {
  ZcyBreadcrumb,
  ZcySpin,
  ZcyList,
  request,
  Modal,
  message,
  Input,
  Alert,
  Select,
  Form,
  Tooltip,
} from 'doraemon';
import { routerRedux } from 'dva/router';
import { connect } from 'dva';
import _ from 'lodash';
import { fetchCurrentEnvApi, checkPagePermission } from 'src/common/services/app';
import { getBreadcrumbButton, getSmallBreadcrumbButton, getSmallListYear } from 'src/routes/Overview/services';
import ListParamsHoc from 'components/ListParamsHoc';
import { getListParams } from '../config/index';
import { revokeAnnouncement } from '../services/index';
import { recallFormAnnouncement } from '../../Manage/services';
import ObjectModal from 'components/ObjectModal';
import SMSModal from 'components/SMS/SMS-Modal';
import './list.less';
import { customRender } from 'src/utils/customRender';
import AnnouncementListDrawer from '../config/14001_MonitorEnterprises/AnnouncementListDrawer';

const userIdentityMap = {
  1: 'purchase', // 采购单位
  2: 'ministry', // 主管单位
  3: 'finance', // 财政监管只可看本区划
  4: 'financeOnly', // 财政监管可以看下级区划
};
const { Option } = Select;
const FormItem = Form.Item;
const confirm = Modal.confirm;
@Form.create()
@connect(({ loading, announcementList }) => ({
  announcementList,
  loading: loading.effects['announcementList/getCollect'],
}))
@ListParamsHoc
export default class AnnouncementList extends Component {
  constructor(props) {
    super(props);
    const { match } = props;
    this.annType = _.get(match, 'params.annType');
    this.state = {
      identity: '',
      objectModal: false,
      objectAnnId: undefined,
      SMSVisible: false, // 短信通知弹窗
      createYearAnnVisible: false, // 中小企业预留生成年度公告弹窗
      // announcementId: undefined,
      currentRecord: {}, // 当前选中行
      hideRevertBtn: {},
      // eslint-disable-next-line react/no-unused-state
      env: '', // 当前环境
      // eslint-disable-next-line react/no-unused-state
      districtCode: window.currentUserDistrict.code, // 当前选择的区划
      // eslint-disable-next-line react/no-unused-state
      orgs: [], // 发布单位数据
      // eslint-disable-next-line react/no-unused-state
      createPermission: false,
      // eslint-disable-next-line react/no-unused-state
      isShowCreatePurchaseIntentionButton: false,
      // eslint-disable-next-line react/no-unused-state
      singleForProjectBtn: false,
      // eslint-disable-next-line react/no-unused-state
      singleForPlanBtn: false,
      // eslint-disable-next-line react/no-unused-state
      singleForManualBtn: false,
      // eslint-disable-next-line react/no-unused-state
      purchaseIntentionBtn: false,
      // eslint-disable-next-line react/no-unused-state
      purchaseIntentionForPlanBtn: false,
      // eslint-disable-next-line react/no-unused-state
      purchaseIntentionManualBtn: false,
      // eslint-disable-next-line react/no-unused-state
      singleForManualForCPTPP: false,
      canCreate: false,
      canExport: false,
      canGenerateAnnualData: false,
      canDisplayAnnualSearch: false, // 是否展示年度搜索
      canDisplayOrgNameSearch: false, // 是否展示采购单位名称搜索
      isResponsibleBudgetUnit: false, // 是否为主管预算单位
      yearArr: [],
      currentAction: '',
    };
  }
  // 载入页面时,请求数据
  async componentDidMount() {
    this.getEnv();
    const identity = await this.getIdentity();
    const { getTabs, iniLoad } = getListParams(this.annType);
    const { tabList } = getTabs('', identity);
    const params = this.props.getParams({
      pageNo: 1,
      pageSize: 10,
      type: tabList[0].value,
      // tab: tabList[0].value,
    });
    this.loadData(params);
    iniLoad && iniLoad(this);
    this.getBreadcrumbButton();
    checkPagePermission({
      pageId: this.props?.location?.pathname,
    }).then((result = {}) => {
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        pushSingleSourcePublicityWithProjectBtn:
          result?.result?.pushSingleSourcePublicityWithProjectBtn ?? false,
        // eslint-disable-next-line react/no-unused-state
        pushSingleSourcePublicityWithMoneyBtn:
          result?.result?.pushSingleSourcePublicityWithMoneyBtn ?? false,
        // eslint-disable-next-line react/no-unused-state
        pushPurchaseIntentionWithBtn: 
        result?.result?.pushPurchaseIntentionWithBtn ?? false,
      });
    });
  }

  getBreadcrumbButton = () => {
    // 面向中小企业预留项目公告面包屑按钮
    if (String(this.annType) === '14001') {
      getSmallBreadcrumbButton().then((res) => {
        const {
          canCreate,
          canExport,
          canGenerateAnnualData,
          canDisplayAnnualSearch,
          canDisplayOrgNameSearch,
          isResponsibleBudgetUnit,
          canShowAnnouncementList, // 是否展示公告列表
        } = res.result;
        this.setState({
          canCreate,
          canExport,
          canGenerateAnnualData,
          canDisplayAnnualSearch,
          canDisplayOrgNameSearch,
          isResponsibleBudgetUnit,
          canShowAnnouncementList,
        });
      });
      getSmallListYear({ automaticExclusion: false }).then((res) => {
        this.setState({
          // eslint-disable-next-line react/no-unused-state
          yearArr: res?.result,
        });
      });
    } else { // 采购意向&单一来源公示面包屑按钮
      getBreadcrumbButton({ announcementType: this.annType }).then((res) => {
        const { 
          singleForProject, 
          singleForPlan, 
          singleForManual, 
          purchaseIntention,
          purchaseIntentionForPlan,
          purchaseIntentionManual,
          singleForManualForCPTPP,
        } = res.result;
        this.setState({
          // eslint-disable-next-line react/no-unused-state
          singleForProjectBtn: singleForProject,
          // eslint-disable-next-line react/no-unused-state
          singleForPlanBtn: singleForPlan,
          // eslint-disable-next-line react/no-unused-state
          singleForManualBtn: singleForManual,
          // eslint-disable-next-line react/no-unused-state
          purchaseIntentionBtn: purchaseIntention,
          // eslint-disable-next-line react/no-unused-state
          purchaseIntentionForPlanBtn: purchaseIntentionForPlan,
          // eslint-disable-next-line react/no-unused-state
          purchaseIntentionManualBtn: purchaseIntentionManual,
          // eslint-disable-next-line react/no-unused-state
          singleForManualForCPTPP,
        });
      });
    }
  }

  // 获取环境信息（政采云平台 or 上海环境）
  getEnv = () => {
    fetchCurrentEnvApi().then((envRes) => {
      if (envRes && envRes.success) {
        this.setState({
          // eslint-disable-next-line react/no-unused-state
          env: envRes?.result?.tenant,
        });
      }
    });
  }

  getIdentity = async () => {
    const res = await request('/announcement/api/userIdentity');
    const identity = userIdentityMap[res.result];
    this.setState({
      identity,
    });
    return identity;
  }

  onSelectChange = async (districtCode) => {
    const { result } = await request('/announcement/api/orgList', {
      params: {
        districtCode: Array.isArray(districtCode)
          ? [...districtCode].pop() // 取最后一个即可
          : window.currentUserDistrict.code,
      },
    }) || {};
    this.setState({
      // eslint-disable-next-line react/no-unused-state
      orgs: result,
    });
  };

  loadData = (params, isReset) => {
    this.props.setParams(params);
    const { metaDataSearchKeys } = params;
    if (String(this.annType) === '10016' && typeof metaDataSearchKeys === 'string') {
      if (metaDataSearchKeys === '') {
        delete params.metaDataSearchKeys;
      } else {
        params.metaDataSearchKeys = [
          {
            key: 'purchaseProjectName',
            value: [metaDataSearchKeys],
          },
        ];
      }
    }
    this.props.dispatch({
      type: 'announcementList/getCollect',
      payload: {
        ...params,
        announcementTypes: this.annType,
      },
    });

    // 仅单一来源公告列表页面需要 districtCode orgs 的变化
    if (String(this.annType) === '10016') {
      this.setState({
        // eslint-disable-next-line react/no-unused-state
        districtCode: params.district,
      });

      if (isReset) {
        this.onSelectChange(params.district);
      }
    }
  }
  onSearch = (params, t, isReset) => {
    this.loadData(params, isReset);
  }
  // 删除
  handleDelete = (item, title = '提示', content = '确认删除此公告？', okText = '确定', path = 'announcementList/delete') => {
    confirm({
      title,
      content,
      okText,
      onOk: () => {
        this.props.dispatch({
          type: path,
          payload: {
            id: item.id,
          },
        }).then((res) => {
          if (res?.success) {
            message.success('删除成功！');
            this.reload();
          } else {
            message.error(res?.message || res?.error);
          }
        });
      },
    });
  }
  // 短信通知
  handleNotify = (item) => {
    if (!_.isEmpty(item)) {
      this.setState({
        // announcementId: item.id,
        currentRecord: item,
      });
    }
    this.setState({
      SMSVisible: !this.state.SMSVisible,
    });
  }
  // 编辑
  handleEdit = (item) => {
    // 中小企业跳转路径
    if (String(this.annType) === '14001') {
      this.props.dispatch(
        routerRedux.push(
          `/smallYearAnn?pageType=edit&id=${item.id}`
        )
      );
      return;
    }
    // 采购意向&单一来源跳转路径
    if (!!item.formPageCode) {
      this.props.dispatch(
        routerRedux.push(
          `/dynamic/edit?announcementType=${item.announcementType}&formPageCode=${item.formPageCode}&annId=${item.id}&districtId=${item.district}&annBigType=${item.annBigType}&isReformationEnglishAnnouncement=${item.isReformationEnglishAnnouncement}&isReformation=${item.isReformation}`
        )
      );
    } else {
      this.props.dispatch(routerRedux.push(`/edit/${item.annBigType}/${item.id}`));
    }
  }
  // 审核
  handleCheck = (item) => {
    // 中小企业跳转路径
    if (String(this.annType) === '14001') {
      this.props.dispatch(
        routerRedux.push(
          `/smallYearAnn?pageType=detail&id=${item.id}`
        )
      );
      return;
    }
    // 采购意向&单一来源跳转路径
    if (!!item.formPageCode) {
      this.props.dispatch(
        routerRedux.push(
          `/dynamic/detail?formPageCode=${item.formPageCode}&annId=${item.id}&districtId=${item.district}&annBigType=${item.annBigType}`
        )
      );
    } else {
      this.props.dispatch(routerRedux.push(`/review/${item.annBigType}/${item.id}`));
    }
    // this.props.dispatch(routerRedux.push(`/review/${item.annBigType}/${item.id}`));
  }
  // 查看
  handleView = (item) => {
    // 中小企业跳转路径
    if (String(this.annType) === '14001') {
      this.props.dispatch(
        routerRedux.push(
          `/smallYearAnn?pageType=detail&id=${item.id}`
        )
      );
      return;
    }
    // 采购意向&单一来源跳转路径
    if (!!item.formPageCode) {
      this.props.dispatch(
        routerRedux.push(
          `/dynamic/detail?formPageCode=${item.formPageCode}&annId=${item.id}&districtId=${item.district}&annBigType=${item.annBigType}`
        )
      );
    } else {
      this.props.dispatch(routerRedux.push(`/detail/${item.annBigType}/${item.id}`));
    }
  }
  // 发布
  handlePublish = (item) => {
    confirm({
      title: '提示',
      content: '确认发布此公告？',
      onOk: () => {
        this.props.dispatch({
          type: 'announcementList/publish',
          payload: {
            announcementId: item.id,
          },
        }).then((res) => {
          if (res && res.success) {
            message.success('公告发布中');
            this.reload();
          } else {
            message.error(res.error);
          }
        });
      },
    });
  }
  // 撤回
  handleRevoke = (item) => {
    if (item.revokedLogicDelete) {
      confirm({
        title: '是否继续撤回?',
        content: `撤回后公告列表将不再展示该公告，编辑操作请至${item.appName}公告发起模块`,
        closable: true,
        onOk: () => {
          this.doRevoke(item);
        },
      });
    } else if (String(this.annType) === '14001') {
      // 中小企业预留撤回
      confirm({
        title: '撤回单据',
        content: '撤回后，该单据需重新申请审批',
        okText: '撤回',
        closable: true,
        onOk: () => {
          const { id = '' } = item || {};
          this.props.dispatch({
            type: 'announcementList/smallRevoke',
            payload: {
              id,
            },
          }).then((res) => {
            if (res?.success) {
              message.success('撤回成功！');
              this.reload();
            } else {
              message.error(res.error || res.message);
            }
          });
        },
      });
    } else {
      this.doRevoke(item);
    }
  }
  doRevoke = (item) => {
    this.revokeRes = undefined;
    confirm({
      title: '撤回理由',
      className: 'revokeReasonModal',
      content: (
        <div>
          <Alert
            message="提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作。"
            type="warning"
            style={{ margin: '0 auto 10px auto' }}
          />
          <Input.TextArea
            maxLength={255}
            onChange={(e) => {
              this.revokeRes = e.target.value;
            }}
            placeholder="请输入撤回理由"
          />
        </div>
      ),
      onOk: () => {
        if (!this.revokeRes) {
          return message.error('请输入撤回理由');
        }
        const { id = '', formPageCode = '', processDefineKey = '', taskModelId = '' } = item || {};
        // 采购意向&单一来源撤回
        if (formPageCode) {
          recallFormAnnouncement({
            id,
            pageCode: formPageCode,
            processDefineKey,
            taskId: taskModelId,
            reason: this.revokeRes || '',
          }).then((res = {}) => {
            if (res?.success) {
              message.success('撤回成功！');
              this.reload();
            } else {
              message.error(res.error || res.message);
            }
          });
        } else {
          this.props.dispatch({
            type: 'announcementList/revoke',
            payload: {
              revokeRes: this.revokeRes,
              announcementId: id,
            },
          }).then((res = {}) => {
            if (res?.success) {
              message.success('撤回成功！');
              this.reload();
            } else {
              message.error(res.error || res.message);
            }
          });
        }
      },
    });
  }
  // 异议确认
  objectAction = (record) => {
    this.setState({
      objectModal: true,
      objectAnnId: record.id,
    });
  }
  hideModal = () => {
    this.setState({
      objectModal: false,
    });
  }
  reload = () => {
    const params = this.props.getParams();
    setTimeout(() => {
      this.loadData(params, true);
    }, 100);
  }
  // 撤回发布操作
  handleRevokePublish = (record) => {
    Modal.confirm({
      title: '是否确认撤回发布该公告？',
      onOk: () => {
        revokeAnnouncement({
          announcementId: record.id,
        }).then((res) => {
          // es查询延迟需要延迟一秒进行查询状态才会变更
          setTimeout(() => {
            this.reload();
          }, 1000);
          if (res && res.success) {
            message.success('撤回发布成功！');
          } else {
            message.error(res.error);
          }
        });
      },
    });
  }
  handleSmallYearCancel = () => {
    this.setState({ createYearAnnVisible: false });
  }

  // 中小企业-选择年度弹窗
  onCreate = () => {
    const { form, dispatch } = this.props;
    const { currentAction } = this.state;
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      form.resetFields();
      this.setState({ visible: false });
      if (currentAction === 'generateAnnualData') {
        dispatch(
          routerRedux.push(
            `/smallYearAnn?pageType=create&year=${values.year}`
          )
        );
      } else if (currentAction === 'addAnnouncement') {
        dispatch(
          routerRedux.push(
            `/dynamic/create?formPageCode=monitorEnterprisesReservedItemsNotice&announcementTypeName=%u9762%u5411%u4E2D%u5C0F%u4F01%u4E1A%u9884%u7559%u9879%u76EE%u6267%u884C%u60C5%u51B5%u516C%u544A&announcementType=14001&annBigType=14&ids=10178&smallAnnouncementYear=${values.year}`
          )
        );
      }
    });
  }

  // 中小企业-生成年度数据回调
  handleGenerateAnnualData = () => {
    getSmallListYear({ automaticExclusion: true }).then((res) => {
      this.setState({
        yearArr: res?.result,
        createYearAnnVisible: true, 
        currentAction: 'generateAnnualData',
      });
    });
  }

  // 中小企业-生成公告
  handelAddAnnouncement= () => {
    getSmallListYear({ automaticExclusion: false }).then((res) => {
      this.setState({
        yearArr: res?.result,
        createYearAnnVisible: true, 
        currentAction: 'addAnnouncement',
      });
    });
  }

  // 中小企业-展示公告列表
  handleShowAnnouncementList = () => {
    customRender(<AnnouncementListDrawer />);
  }

  render() {
    const {
      loading,
      getParams,
    } = this.props;
    const {
      objectModal,
      objectAnnId,
      identity,
      isResponsibleBudgetUnit,
      SMSVisible,
      createYearAnnVisible,
      currentRecord = {},
      hideRevertBtn = {},
      yearArr = [],
      currentAction,
    } = this.state;
    const params = getParams({
      pageNo: 1,
      pageSize: 10,
    });
    const { getFieldDecorator } = this.props.form;
    // initSearchParams 搜索项默认回填参数
    const { pageNo, pageSize, type, ...initSearchParams } = params;
    const currentInitSearchParams = {
      ...initSearchParams, 
      metaDataSearchKeys: initSearchParams.metaDataSearchKeys?.[0]?.value[0],
    };
    const {
      breadcrumbConfig,
      searchItemConfig,
      getTabs,
      getTableConfig,
    } = getListParams(this.annType);
    // 中小企业预算金额身份
    const curIdentity = String(this.annType) === '14001' ? isResponsibleBudgetUnit : identity;
    const tabs = getTabs ? getTabs(type, curIdentity) : [];
    const table = getTableConfig ? getTableConfig(this, hideRevertBtn) : {};
    const customItem = searchItemConfig ? searchItemConfig(this) : [];
    const defaultYear = yearArr.find(org => org.isDefaultValue)?.year;
    return (
      <div className="config-management-list">
        <ZcySpin spinning={!!loading}>
          <ZcyBreadcrumb
            {...breadcrumbConfig(this)}
          />
          <ZcyList
            tabs={tabs}
            tabKey="type"
            table={table}
            onSearch={this.onSearch}
            customItem={customItem}
            initSearchParams={currentInitSearchParams}
            openSearchCache
          />
          {objectModal && (
            <ObjectModal
              show={objectModal}
              hideMe={this.hideModal}
              annId={objectAnnId}
              reload={this.reload}
            />
          )}
          {
            SMSVisible ? (
              <SMSModal
                visible={SMSVisible}
                onCancel={this.handleNotify}
                announcementId={currentRecord.id}
                distCode={currentRecord.district}
              />
            ) : null
          }
          {createYearAnnVisible && (
            <Modal
              title={currentAction === 'generateAnnualData' ? '生成年度中小企业预留执行情况' : '生成公告'} 
              visible={createYearAnnVisible}
              onOk={this.onCreate}
              onCancel={this.handleSmallYearCancel}
              extra={
                currentAction === 'generateAnnualData' ? (
                  <Alert
                    showIcon
                    message="每年度仅可生成一条数据，已生成的年度将不可选择，如未提交可继续修改。"
                    banner
                    type="info"
                  />
                ) : null
              }
            >
              <Form labelCol={{ span: 6 }}
                wrapperCol={{ span: 14 }}
              >
                <FormItem 
                  label="年度"
                  style={{ marginTop: 20 }}
                >
                  {getFieldDecorator('year', {
                    initialValue: defaultYear,
                    rules: [{
                      required: true, message: '请选择年度',
                    }],
                  })(
                    <Select
                      placeholder="请选择"
                      style={{ width: 300 }}
                    >
                      {yearArr.map((org = {}) => (
                        <Option 
                          value={org.year} 
                          key={org.year}
                          disabled={!org.isEnable}
                        >
                          {org.isEnable ? <div>{org.year}</div> : (
                            <Tooltip title="已生成数据">
                              <span>{org.year}</span>
                            </Tooltip>
                          )}
                        </Option>
                      ))}
                    </Select>
                  )}
                </FormItem>
              </Form>
            </Modal> 
          )
          }
        </ZcySpin>
      </div>
    );
  }
}
