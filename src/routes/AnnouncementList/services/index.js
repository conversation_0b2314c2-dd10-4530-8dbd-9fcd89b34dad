/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-17 11:27:22
 * @FilePath: /zcy-announcement-v2-front/src/routes/AnnouncementList/services/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from 'doraemon';
import moment from 'moment';

const urls = {
  list: '/announcement/api/announcementList', // 单一来源&采购意向列表
  moinitorEnterprisiesList: '/announcement/api/small/listSmallAnnouncement', // 面向中小企业列表
  del: '/announcement/api/deleteAnnouncement', // 删除
  smallDel: '/announcement/api/small/deleteSmallAnnouncement', // 删除中小
  publish: '/announcement/api/releaseAnnouncement', // 发布
  revork: '/announcement/api/revokeAnnouncement', // 撤回
  canCreatePurchaseIntention: '/announcement/api/canCreatePurchaseIntention', // 采购意向按钮权限
  revokeSmall: '/announcement/api/small/revokeSmallAnnouncement',
};

// 查询公告
export async function reqList(params) {
  const { date, district, ...rest } = params;
  return request(urls.list, {
    method: 'post',
    data: {
      ...rest,
      district: (Array.isArray(district) && district.length) ?
        district[district.length - 1] : undefined,
      releaseStartAt: (Array.isArray(date) && date.length) ?
        moment(date[0]).startOf('day').format('YYYY-MM-DD') : undefined,
      releaseEndAt: (Array.isArray(date) && date.length) ?
        moment(date[1]).endOf('day').format('YYYY-MM-DD') : undefined,
    },
  });
}

export async function reqSmallList(params) {
  const { announcementTypes, ...rest } = params;
  return request(urls.moinitorEnterprisiesList, {
    method: 'post',
    data: {
      ...rest,
    },
  });
}

// 删除公告
export async function deleteAnnouncement(data) {
  return request(urls.del, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}
// 删除中小公告
export async function deleteSmallDocument(params) {
  return request(urls.smallDel, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}
// 发布公告
export async function publishAnnouncement(data) {
  return request(urls.publish, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 撤回公告
export async function revokeAnnouncement(params) {
  return request(urls.revork, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}

// 撤回中小企业年度数据
export async function revokeSmall(data) {
  return request(urls.revokeSmall, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export async function pagingPurchaseProjectData(data) {
  return request('/announcement/api/purchasePlan/pagingPurchaseProjectData', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export async function getCanCreatePurchaseIntentionButton() {
  return request(urls.canCreatePurchaseIntention, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 获取指定年度下中小企业预留执行情况的条目数
export async function getAnnualInfoCount(params) {
  return request('/announcement/api/small/getAnnualInfoCount', {
    method: 'GET',
    params,
  });
}

// 执行导出指定年度下中小企业预留执行情况的汇总数据任务
export async function exportAnnualInfo(params) {
  return request('/announcement/api/small/exportAnnualInfo', {
    method: 'GET',
    params,
  });
}

// 导出指定年度下中小企业预留执行情况的汇总数据
export async function pollingAnnualInfo(params) {
  return request('/announcement/api/small/pollingAnnualInfo', {
    method: 'GET',
    params,
  });
}

// 中小企业-公告列表
export async function getAnnouncementList(data) {
  return request('/announcement/api/small/listSelfAnnouncement', {
    method: 'POST',
    data,
  });
}
