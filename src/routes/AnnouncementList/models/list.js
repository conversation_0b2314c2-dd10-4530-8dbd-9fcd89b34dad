/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-12-17 20:41:09
 * @FilePath: /zcy-announcement-v2-front/src/routes/AnnouncementList/models/list.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Modal } from 'doraemon';
import { 
  reqList, 
  reqSmallList, 
  deleteAnnouncement, 
  deleteSmallDocument,
  publishAnnouncement, 
  revokeAnnouncement, 
  revokeSmall,
  getCanCreatePurchaseIntentionButton,
} from '../services';


/**
 * 请求错误信息统一处理
 * @param res
 */
const responseErrorHandle = (res) => {
  const { success, error, message } = res;
  if (!success) {
    Modal.error({
      content: error || message,
    });
  }
};

export default {
  namespace: 'announcementList',

  state: {
    data: {},
  },

  effects: {
    * getCollect({ payload }, { call, put }) {
      const { announcementTypes } = payload;
      let response;
      if (announcementTypes === '14001') {
        response = yield call(reqSmallList, payload);
      } else {
        response = yield call(reqList, payload);
      }
      responseErrorHandle(response);
      const result = response.result;
      result?.data?.forEach((item) => {
        try {
          item.metaDataJson = item.metaData ? JSON.parse(item.metaData) : {};
        } catch (error) {
          item.metaDataJson = {};
        }
      });
      yield put({
        type: 'queryList',
        payload: result,
      });
    },
    * delete({ payload }, { call }) {
      const response = yield call(deleteAnnouncement, payload);
      return response;
    },
    * smallDelete({ payload }, { call }) {
      const response = yield call(deleteSmallDocument, payload);
      return response;
    },
    * publish({ payload }, { call }) {
      const response = yield call(publishAnnouncement, payload);
      return response;
    },
    * revoke({ payload }, { call }) {
      const response = yield call(revokeAnnouncement, payload);
      return response;
    },
    
    // 撤回中小企业年度数据
    * smallRevoke({ payload }, { call }) {
      const response = yield call(revokeSmall, payload);
      return response;
    },
    * getCanCreatePurchaseIntention(_, { call }) {
      const response = yield call(getCanCreatePurchaseIntentionButton);
      return response.result;
    },
  },

  reducers: {
    queryList(state, action) {
      return {
        ...state,
        data: action.payload,
      };
    },
  },
};
