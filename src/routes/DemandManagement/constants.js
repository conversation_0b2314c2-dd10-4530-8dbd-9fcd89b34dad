export const STATUS_MAP = {
  /** 待提交 */
  waitSubmit: 0,
  /** 审核中 */
  audit: 1,
  /** 待发布 */
  waitPublish: 2,
  /** 被退回 */
  failed: 3,
  /** 发布中 */
  publishing: 4,
  /** 已结束 */
  end: 5,
  /** 已作废 */
  abolished: 6,
  /** 异议中 */
  objection: 7,
  /** 已发布 */
  published: 100,
};

export const CHECK_SUBMIT_CODE = {
  // 1:全部发布可结束下一节点  2:存在公示未发布  3:未关联任何采购意向  4: 存在预算未发布采购意向
  ok: 1,
  hasNoPublish: 2,
  hasNoAssociate: 3,
  hasNoPublishBudget: 4,
};

// 意向公开列表page_code
export const PAGE_CODE = 'procurementIntentionNotice';
// 查看编辑表单code 采购意向公告
export const FORM_PAGE_CODE = 'publicNoticeOfPurchaseIntention';
export const ANNOUNCEMENT_TYPE = 10016;
export const ANNBIG_TYPE = 1;
export const LAYOUTMODE = 'project';

export const NODE_STATUS = {
  /** 
   * 未开始
  */
  UN_START: -1,
  /** 
   * 初始化
  */
  INIT: 0,
  /**
   * 初始化完成
   */
  INIT_DONE: 1,
  /**
   * 已完成
   */
  DONE: 2,
};
