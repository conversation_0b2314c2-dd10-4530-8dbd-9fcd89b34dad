import hecateRequest from 'src/utils/request';

// 生成采购意向公开
export const buildProcurementIntention = data => hecateRequest('/api/procurement/project/purchaseIntention/buildProcurementIntention', {
  method: 'POST',
  data,
});

// 关联采购意向列表页
export const listProcurementIntentionNotice = data => hecateRequest('/api/procurement/project/purchaseIntention/listProcurementIntentionNotice', {
  method: 'POST',
  data,
});

// 采购意向公开列表页按钮集合
export const getButtons = params => hecateRequest('/api/procurement/project/purchaseIntention/getButtons', {
  params,
});

// 关联采购意向
export const relationProcurementIntentionNotice = data => hecateRequest('/api/procurement/project/purchaseIntention/relationProcurementIntentionNotice', {
  method: 'POST',
  data,
});

// 采购意向公开-结束节点(弹窗提示时的确定按钮)
export const endProcurementIntentionNode = params => hecateRequest('/api/procurement/project/purchaseIntention/endProcurementIntentionNode', {
  params,
});

// 撤回-出弹窗
export const revokeWorkFlow = params => hecateRequest('/api/procurement/project/purchaseIntention/revokeWorkFlow', {
  params,
});

// 撤回发布/撤回公告
export const revokeAnnouncement = params => hecateRequest('/api/procurement/project/purchaseIntention/revokeAnnouncement', {
  params,
});

// 采购意向公开列表页-取消关联
export const cancelProcurementIntentionRelation = params => hecateRequest('/api/procurement/project/purchaseIntention/cancelProcurementIntentionRelation', {
  params,
});

// 采购意向公开-校验是否可以结束节点
export const checkEndProcurementIntentionNode = params => hecateRequest('/api/procurement/project/purchaseIntention/checkEndProcurementIntentionNode', {
  params,
});


// 采购意向公开-公告发布
export const releaseAnnouncement = params => hecateRequest('/api/procurement/project/purchaseIntention/releaseAnnouncement', {
  params,
});

// 采购意向节点状态
export const procurementIntentionNodeStatus = params => hecateRequest('/api/procurement/project/purchaseIntention/procurementIntentionNodeStatus', {
  params,
});


// 预算下拉数据源
export const budgetList = data => hecateRequest('/api/procurement/project/purchaseIntention/budgetList', {
  method: 'POST',
  data,
});
