import React, { useEffect, useState } from 'react';
import { Drawer, ZcyList, Tooltip, message, Tag, Input } from 'doraemon';
import { budgetList } from '../services';
import { bizCentToYuanFormat } from '@zcy/utils';

const BudgetDrawer = (props = {}) => {
  const { visible, onCancel, onSubmit, projectId = '' } = props;
  const [total, setTotal] = useState(0);
  const [list, setList] = useState([]);
  const [selectRows, setSelectRows] = useState([]);

  useEffect(() => {
    if (visible) {
      init();
      handleSearch();
    }
  }, [visible]);

  const init = () => {
    setTotal(0);
    setList([]);
    setSelectRows([]);
  };

  const goDetail = (record) => {
    const url = `${window.envHref.settlement}/purchaseplan_front/#/purchase/plan/details/${record.budgetId}`;
    window.open(url);
  };

  // 获取表格列
  const getBudgetTableColumn = () => {
    return (
      [
        {
          title: '序号',
          dataIndex: 'index',
          render: (_, __, index) => {
            return index + 1;
          },
        },
        {
          title: '预算编号',
          dataIndex: 'budgetCode',
          render: (text, record) => {
            const { intentionPublished } = record;
            return (
              <>
                <a onClick={() => goDetail(record)}>{text}</a>
                {
                  intentionPublished ? 
                    <Tag style={{ background: '#fff', color: '#00B044', borderColor: '#CFF1DC', marginLeft: 5 }}>已公示</Tag> : null
                }
              </>
            );
          },
        },
        {
          title: '指标类型',
          dataIndex: 'targetTypeName',
          width: 90,
          render: (text) => {
            return text || '未知';
          },
        },
        {
          title: '预算使用金额',
          dataIndex: 'budgetMoney',
          align: 'right',
          render: (_, { budgetMoney, budgetMoneyForHover }) => {
            const val = bizCentToYuanFormat(+budgetMoney * 100);
            return (
              <Tooltip
                getPopupContainer={n => n}
                title={`${budgetMoneyForHover}`}
                overlayClassName="zcy-com-tooltip-theme-dark"
                placement="bottom"
              >
                <span style={{ color: '#FF9700' }}>{val}</span>
              </Tooltip>
            );
          },
          width: 150,
        },
        {
          title: '预算信息',
          dataIndex: 'info',
          render: (_, { procurementDirectory, economicSubject }) => {
            return (
              <span>
                <div><span style={{ color: 'rgba(153,153,153,0.70)' }}>采购目录：</span><span>{procurementDirectory.name}</span></div>
                <div><span style={{ color: 'rgba(153,153,153,0.70)' }}>部门经济分类: </span><span>{economicSubject.name}</span></div>
              </span>
            );
          },
          width: 184,
        },
      ]
    );
  };

  const handleSearch = (params = {}) => {
    const {
      budgetCode = '',
      pageNo = 1,
      pageSize = 10,
    } = params;
    return budgetList({
      budgetCode,
      projectId,
      pageNo,
      pageSize,
    }).then((res) => {
      if (!res.success) return;
      setTotal(res.result.total);
      setList(res.result.data);
    });
  };

  const onSelectChange = (selectKeys, selectedRows = []) => {
    const tempRows = [...selectRows, ...selectedRows];
    setSelectRows(selectKeys.map(id => tempRows.find(ele => ele.budgetId === id)));
  };

  const del = (budgetId) => {
    const index = selectRows.findIndex(ele => ele.budgetId === budgetId);
    selectRows.splice(index, 1);
    setSelectRows([...selectRows]);
  };

  const onSave = () => {
    if (!selectRows.length) {
      message.error('请选择一条预算');
      return;
    }
    return onSubmit(selectRows);
  };
  return (
    <Drawer
      title="关联预算"
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={1100}
      className="procurementIntentionNotice-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <ZcyList
        customItem={[
          {
            label: '预算编号',
            id: 'budgetCode',
            render: () => <Input placeholder="请输入" />,
          },
        ]}
        preTabelContent={(
          <div style={{
            marginTop: -24,
            paddingBottom: 12,
          }}
          >
            {selectRows.map(item => (
              <Tag
                closable
                color="orange"
                key={item.budgetId}
                onClose={() => del(item.budgetId)}
                style={{ marginBottom: '4px' }}
              >
                {item.budgetCode}
              </Tag>
            ))}
          </div>
        )}
        table={{
          rowKey: 'budgetId',
          dataSource: list,
          columns: getBudgetTableColumn(),
          pagination: {
            showQuickJumper: {
              goButton: true,
            },
            showSizeChanger: true,
            total,
            pageSizeOptions: ['10', '20', '50', '100'],
          },
          rowSelection: {
            selectedRowKeys: selectRows.map(ele => ele.budgetId),
            onChange: onSelectChange,
          },
        }}
        onSearch={handleSearch}
      />
    </Drawer>
  );
};

export default BudgetDrawer;
