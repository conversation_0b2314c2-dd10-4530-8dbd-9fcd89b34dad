import React, { useEffect, useState } from 'react';
import { <PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Badge, Tooltip, message, Tag, Modal } from 'doraemon';
import { searchItemConfig } from '../config';
import { listProcurementIntentionNotice } from '../services';
import moment from 'moment';
import { STATUS_MAP, FORM_PAGE_CODE, ANNOUNCEMENT_TYPE, ANNBIG_TYPE, NODE_STATUS } from '../constants';
import { getUrlByParams } from '../utils';
import { bizCentToYuanFormat } from '@zcy/utils';

const AssociatedDrawer = (props = {}) => {
  const { visible, onCancel, onSubmit, projectId = '', nodeStatus } = props;
  const [total, setTotal] = useState(0);
  const [list, setList] = useState([]);
  const [selectRows, setSelectRows] = useState([]);

  useEffect(() => {
    if (visible) {
      init();
      handleSearch();
    }
  }, [visible]);

  const init = () => {
    setTotal(0);
    setList([]);
    setSelectRows([]);
  };

  const goDetail = (record) => {
    const searchUrl = getUrlByParams('/dynamic/detail', {
      announcementType: ANNOUNCEMENT_TYPE,
      formPageCode: FORM_PAGE_CODE,
      annId: record.announcementId,
      districtId: record.districtCode,
      annBigType: ANNBIG_TYPE,
    });
    const url = `${window.location.origin}${window.location.pathname}#${searchUrl}`;
    window.open(url);
  };

  // 获取表格列
  const getAssociatedTableColumn = () => {
    return (
      [
        {
          title: '采购意向公告标题',
          dataIndex: 'announcementTitle',
          render: (text, record) => {
            return <a onClick={() => goDetail(record)}>{text}</a>;
          },
        },
        {
          title: '预算数量',
          dataIndex: 'budgetNum',
          width: 90,
        },
        {
          title: '采购金额（元）',
          dataIndex: 'budgetMoney',
          align: 'right',
          render: (_, { budgetMoney, budgetMoneyForWy }) => {
            const val = bizCentToYuanFormat(+budgetMoney * 100);
            return (
              <Tooltip 
                getPopupContainer={n => n}
                title={`${budgetMoneyForWy}`}
                overlayClassName="zcy-com-tooltip-theme-dark"
                placement="bottom"
              >
                <span style={{ color: '#FF9700' }}>{val}</span>
              </Tooltip>
            );
          },
          width: 150,
        },
        {
          title: '发布时间',
          dataIndex: 'pushTime',
          render: (val) => {
            if (!val) return '-';
            return moment(val).format('YYYY-MM-DD HH:mm:ss');
          },
        },
        {
          title: '发布天数（天）',
          dataIndex: 'pushDay',
          render: (val) => {
            return val ?? '-';
          },
          width: 132,
        },
        {
          title: '状态',
          dataIndex: 'statusDesc',
          render: (_, record) => {
            const { status, statusDesc } = record;
            let badgeStatus = 'default';
            // 被退回 异议中
            if ([STATUS_MAP.failed, STATUS_MAP.objection].includes(status)) {
              badgeStatus = 'error';
            } 

            // 已作废
            if ([STATUS_MAP.abolished].includes(status)) {
              badgeStatus = 'default';
            }
            // 已结束 已发布
            if ([STATUS_MAP.end, STATUS_MAP.published].includes(status)) {
              badgeStatus = 'success';
            }
            // 待提交 审核中 待发布 发布中
            if ([STATUS_MAP.waitSubmit, STATUS_MAP.audit,
              STATUS_MAP.waitPublish, STATUS_MAP.publishing].includes(status)) {
              badgeStatus = 'processing';
            }
            if (status !== null) {
              return (
                <div>
                  <Badge status={badgeStatus} text={statusDesc} />
                </div>
              );
            }
            return '-';
          },
          width: 85,
        },
        {
          title: '发布单位',
          dataIndex: 'purchaseOrgName',
        },
      ]
    );
  };

  const handleSearch = (params = {}) => {
    const { 
      announcementTitle = '', 
      rangeDate = [],
      pageNo = 1,
      pageSize = 10,
    } = params;
    let minTime = '';
    let maxTime = '';
    if (rangeDate?.length) {
      minTime = rangeDate[0];
      maxTime = rangeDate[1];
    }
    return listProcurementIntentionNotice({
      announcementTitle,
      projectId,
      minTime,
      maxTime,
      pageNo,
      pageSize,
    }).then((res) => {
      if (!res.success) return;
      setTotal(res.result.total);
      setList(res.result.data);
    });
  };

  const onSelectChange = (selectKeys, selectedRows = []) => {
    const tempRows = [...selectRows, ...selectedRows];
    setSelectRows(selectKeys.map(id => tempRows.find(ele => ele.announcementId === id)));
  };

  const del = (announcementId) => {
    const index = selectRows.findIndex(ele => ele.announcementId === announcementId);
    selectRows.splice(index, 1);
    setSelectRows([...selectRows]);
  };
  
  const onSave = () => {
    if (!selectRows.length) {
      message.error('请选择一条采购意向公告');
      return;
    }
    if (nodeStatus === NODE_STATUS.DONE) {
      Modal.confirm({
        title: '确认关联采购意向',
        okText: '确认关联',
        cancelText: '暂不关联',
        content: (
          <>
            此环节已确认完成<br />
            再次关联的采购意向数据，
            <span style={{ color: 'red' }}>不可取消关联</span>
          </>
        ),
        onOk: () => onSubmit(selectRows),
      });
      return;
    }
    if (!selectRows.length) {
      message.error('请选择一条采购意向公告');
      return;
    }
    return onSubmit(selectRows);
  };

  const TitleNode = () => {
    return (
      <div className="drawer-titleNode-wrapper">
        <span>关联采购意向公示</span>
      </div>
    );
  };
  return (
    <Drawer
      title={<TitleNode />}
      openConfirmBtn
      onOk={onSave}
      onCancel={onCancel}
      visible={visible}
      width={1100}
      className="procurementIntentionNotice-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <ZcyList 
        customItem={searchItemConfig()}
        preTabelContent={(
          <div style={{
            marginTop: -24,
            paddingBottom: 12,
          }}
          >
            {selectRows.map(item => (
              <Tag
                closable
                color="orange"
                key={item.announcementId}
                onClose={() => del(item.announcementId)}
                style={{ marginBottom: '4px' }}
              >
                {item.announcementTitle}
              </Tag>
            ))}
          </div>
        )}
        table={{
          rowKey: 'announcementId',
          dataSource: list,
          columns: getAssociatedTableColumn(),
          pagination: {
            showQuickJumper: {
              goButton: true,
            },
            showSizeChanger: true,
            total,
            pageSizeOptions: ['10', '20', '50', '100'],
          },
          rowSelection: {
            selectedRowKeys: selectRows.map(ele => ele.announcementId),
            onChange: onSelectChange,
          },
        }}
        onSearch={handleSearch}
      />
    </Drawer>
  );
};

export default AssociatedDrawer;
