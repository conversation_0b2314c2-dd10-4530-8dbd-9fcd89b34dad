/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-07-01 16:07:36
 * @FilePath: /zcy-announcement-v2-front/src/routes/DemandManagement/views/revokeModal.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect } from 'react';
import { Modal, Form, Alert, Input, message } from 'doraemon';
import { revokeWorkFlow } from '../services';


const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const RevokeModal = (props = {}) => {
  const { visible, onCancel, onSubmit, form, id } = props;
  const { getFieldDecorator, validateFields, resetFields } = form;


  useEffect(() => {
    if (visible) {
      resetFields();
    }
  }, [visible]);

  const onOk = () => {
    validateFields((err, values) => {
      if (err) return;
      const { reason } = values;
      revokeWorkFlow({
        reason, id,
      }).then((res) => {
        if (!res.success) return;
        message.success('回撤成功');
        onSubmit();
      });
    });
  };


  return (
    <Modal
      title="公告撤回"
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Alert
        message="提醒：撤回后可在公告管理模块对该撤回公告进行删除、编辑或修改操作"
        type="firstinfo"
        showIcon
        iconType="exclamation-circle-o"
      />
      <Form style={{ marginTop: 24 }} layout="inline">
        <Form.Item 
          {...formItemLayout}
          label="撤回理由"
          style={{ width: '100%' }}
        >
          {getFieldDecorator('reason', {
            rules: [{ required: true, message: '请输入' }],
          })(
            <Input.TextArea />
          )}
        </Form.Item>
      </Form>

    </Modal>
  );
};

export default Form.create()(RevokeModal);
