import React from 'react';
import {
  Input,
  DatePicker,
} from 'doraemon';
import moment from 'moment';

const { RangePicker } = DatePicker;
// 获得搜索项配置
export const searchItemConfig = () => {
  const baseItem = [
    {
      label: '公告标题',
      id: 'announcementTitle',
      render: () => <Input placeholder="请输入" />,
    }, {
      label: '发布时间',
      id: 'rangeDate',
      render: () => {
        return (
          <RangePicker
            getCalendarContainer={e => e?.parentNode}
            showTime={{
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
            }}
          />
        );
      },
    },
  ];
  return baseItem;
};
