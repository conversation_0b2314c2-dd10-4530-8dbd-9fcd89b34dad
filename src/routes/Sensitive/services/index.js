/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-03-21 16:46:42
 * @FilePath: /zcy-announcement-v2-front/src/routes/Sensitive/services/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from 'doraemon';

const urls = {
  rePushAnnouncement: '/announcement/sensitive/rePushAnnouncement', // 敏感词拦截公告重推
  replaceAnnouncementSensitive: '/announcement/sensitive/replaceAnnouncementSensitive', // 敏感词拦截替换
  logDetail: '/announcement/sensitive/log/detail', // 敏感词拦截记录操作日志查询
};

// 敏感词拦截公告重推
export async function rePushAnnouncement(data) {
  return request(urls.rePushAnnouncement, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export async function replaceAnnouncementSensitive(data) {
  return request(urls.replaceAnnouncementSensitive, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export async function logDetail(params) {
  return request(urls.logDetail, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
  });
}
