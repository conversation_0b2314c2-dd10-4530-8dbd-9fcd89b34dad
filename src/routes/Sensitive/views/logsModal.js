/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-03-28 16:49:56
 * @FilePath: /zcy-announcement-v2-front/src/routes/Sensitive/views/logsModal.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Table, Modal, message } from '@zcy/doraemon';
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { bizStringFormat } from '@zcy/utils';
import { logDetail } from '../services';


const TABLE_COLUMN = [{
  title: '操作时间',
  dataIndex: 'raddTime',
  width: 160,
  render: (text) => {
    return moment(text).format('YYYY-MM-DD HH:mm:ss');
  },
}, {
  title: '操作类型',
  dataIndex: 'actionName',
  width: 90,
}, {
  title: '操作内容',
  dataIndex: 'actionContent',
  width: 160,
  render: text => (bizStringFormat(text)),
}, {
  title: '操作人',
  dataIndex: 'operatorName',
  width: 90,
}];

const LogsModal = ({ recordId, visible, onCancel }) => {
  const [list, setList] = useState([]);

  useEffect(() => {
    if (visible) {
      logDetail({
        recordId,
      }).then((res) => {
        if (!res.success) {
          message.error(res.error);
        }
        setList(res.result);
      });
    }
  }, [visible]);
  return (
    <Modal
      title="查看处理日志"
      visible={visible}
      onCancel={onCancel}
      onOk={onCancel}
    >
      <Table 
        columns={TABLE_COLUMN}
        dataSource={list}
        rowKey="id"
        pagination={false}
      />
    </Modal>
  );
};

export default LogsModal;
