import React, { Component } from 'react';
import { ZcyBreadcrumb, message } from 'doraemon';
import { ListPage, ListPageWrap } from '@zcy/zcy-list-component-back';
import { rePushAnnouncement, replaceAnnouncementSensitive } from '../services';
import { connect } from 'dva';
import { routerRedux } from 'dva/router';
import ReplaceSensitiveModal from './replaceSensitiveModal';
import LogsModal from './logsModal';
import InterceptDetailDrawer from './interceptDetailDrawer';

const routes = [{
  label: '拦截数据查询',
}];

@connect()
@ListPageWrap({
  getSchemaParams: () => {
    return {
      pageCode: 'sensitiveAnnouncementList',
      alias: [{
        code: 'a',
        value: '1',
      }],
    };
  },
})
export default class Sensitive extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visibleReplaceSensitiveModal: false,
      visibleLogsModal: false,
      visibleInterceptDetailDrawer: false,
      curId: '',
      curSensitiveWords: '',
      curAnnouncementTitle: '',
      curRecord: {},
    };
  }
  onRePush = async (record, reRender) => {
    const res = await rePushAnnouncement({ recordId: record.id });
    if (!res.success) {
      message.error(res.error);
      return;
    }
    message.success('推送成功');
    // reRender 接收一个参数，传 false 时等同于搜索，传 true 时等同于重置
    reRender(false);
  }
  onAction = (code, record, reRender) => {
    const { dispatch } = this.props;
    const { id, announcementId, annBigType } = record;
    this.setState({
      curId: id,
    });
    switch (code) {
    case 'DETAIL':
      dispatch(routerRedux.push(`/detail/${annBigType}/${announcementId}`));
      break;
    case 'INTERCEPTDETAIL':
      // 控制抽屉
      this.setState({
        visibleInterceptDetailDrawer: true,
        curRecord: record,
      });
      break;
    case 'REPLACE':
      this.handleReplace(record);
      break;
    case 'REPUSH':
      this.onRePush(record, reRender);
      break;
    case 'LOG':
      this.setState({
        visibleLogsModal: true,
      });
      break;
    default:
      //
    }
  }

  onCancelLogsModal = () => {
    this.setState({
      visibleLogsModal: false,
    });
  }

  onCancel = () => {
    this.setState({
      visibleReplaceSensitiveModal: false,
    });
  }
  onCloseInterceptDetail = () => {
    this.setState({
      visibleInterceptDetailDrawer: false,
    });
  }
  onSubmit = ({
    replaceContentWord = '',
    replaceTitle = '',
  }) => {
    const { curId } = this.state;
    replaceAnnouncementSensitive({
      replaceContentWord,
      replaceTitle,
      recordId: curId,
    }).then((res) => {
      if (!res.success) {
        message.error(res.error);
        return;
      }
      message.success('替换敏感词成功');
      this.onCancel();
    });
  }
  handleReplace = (record = this.state.curRecord) => {
    const { sensitiveWords, announcementTitle } = record;
    this.setState({
      visibleReplaceSensitiveModal: true,
      curSensitiveWords: sensitiveWords,
      curAnnouncementTitle: announcementTitle,
    });
  }
  render() {
    const { 
      visibleReplaceSensitiveModal,
      visibleLogsModal,
      curSensitiveWords,
      curAnnouncementTitle,
      curId,
      visibleInterceptDetailDrawer,
    } = this.state;
    return (
      <React.Fragment>
        <ZcyBreadcrumb
          routes={routes}
        />
        {/* 此处引入设计的业务组件 */}
        <ListPage
          pageComponent={this}
          searchCallback={(res) => {
            window.console.log('searchCallback', res);
          }}
          beforeSearch={(searchParams, isReset, search) => {
            window.console.log('beforeSearch', searchParams, isReset);
            search();
          }}
        />
        <ReplaceSensitiveModal 
          visible={visibleReplaceSensitiveModal}
          sensitiveWords={curSensitiveWords}
          announcementTitle={curAnnouncementTitle}
          onCancel={this.onCancel}
          onSubmit={this.onSubmit}
        />
        <LogsModal 
          visible={visibleLogsModal}
          recordId={curId}
          onCancel={this.onCancelLogsModal}
        />
        <InterceptDetailDrawer
          visible={visibleInterceptDetailDrawer}
          sensitiveRecordId={curId}
          onClose={this.onCloseInterceptDetail}
          handleReplace={() => { this.handleReplace(); }}
        />
      </React.Fragment>
    );
  }
}
