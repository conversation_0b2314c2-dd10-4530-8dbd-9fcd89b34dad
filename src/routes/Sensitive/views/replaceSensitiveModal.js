import { Modal, Form, Select, Input } from '@zcy/doraemon';
import React, { useEffect } from 'react';

const FormItem = Form.Item;
const TextArea = Input.TextArea;

const SELECT_OPTIONS = [{
  label: '星号（***）',
  value: '***',
}, {
  label: '井号（##）',
  value: '##',
}, {
  label: '叉号A（XX）',
  value: 'XX',
}, {
  label: '叉号B（xx）',
  value: 'xx',
}];

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};

const ReplaceSensitiveModal = ({ visible, onCancel, onSubmit, 
  sensitiveWords = '',
  announcementTitle = '',
  form }) => {
  const { getFieldDecorator, getFieldsValue } = form;
  
  useEffect(() => {
    form.resetFields('replaceTitle', announcementTitle);
  }, [visible]);

  const onOk = () => {
    const res = getFieldsValue();
    onSubmit(res);
  };

  return (
    <Modal
      title="快速替换敏感词"
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form>
        <FormItem {...formItemLayout} label="命中敏感词" >
          <span>{sensitiveWords || '暂无敏感词'}</span>
        </FormItem>
        <FormItem {...formItemLayout} label="正文敏感词替换为" >
          {
            getFieldDecorator('replaceContentWord', {
              initialValue: SELECT_OPTIONS[0].value,
            })(
              <Select
                style={{ width: 300 }}
                getPopupContainer={triggerNode => triggerNode.parentNode}
              >
                {
                  SELECT_OPTIONS.map(
                    ({ label, value }) =>
                      <Select.Option key={value} value={value}>{label}</Select.Option>
                  )
                }
              </Select>
            )
          }
        </FormItem>
        <FormItem {...formItemLayout} label="标题替换为" >
          {
            getFieldDecorator('replaceTitle', {
              initialValue: announcementTitle,
            })(
              <TextArea autosize={{ minRows: 2 }} />
            )
          }
        </FormItem>
      </Form>
    </Modal>
  );
};

export default Form.create()(ReplaceSensitiveModal);
