/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-03-07 17:34:38
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-03-21 16:43:16
 * @FilePath: /zcy-announcement-v2-front/src/routes/Sensitive/views/interceptDetailDrawer.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Drawer, Table, message, Popover } from 'doraemon';
import React, { useEffect, useState } from 'react';
import { Link } from 'dva/router';
import { detailAnnouncementSensitiveGet } from 'src/api/announcement/sensitive';
import './interceptDetailDrawer.less';

interface Attachment {
  id: number;
  fileName: string;
  fileSensitiveStatus: number;
  sensitiveWords: string;
  fileUrl: string;
  actionName: string;
}

interface Props {
  sensitiveRecordId: number;
  visible: boolean;
  onClose: () => void;
  handleReplace: () => void;
}

enum FileStatus {
  PendingVerification = 0,
  VerificationPassed = 1,
  VerificationFailed = 2,
  VerificationError = 3,
}

const fileStatus: { [key: number]: string } = {
  [FileStatus.PendingVerification]: '待校验',
  [FileStatus.VerificationPassed]: '校验通过',
  [FileStatus.VerificationFailed]: '校验不通过',
  [FileStatus.VerificationError]: '校验失败',
};

const InterceptDetailDrawer: React.FC<Props> = ({ 
  sensitiveRecordId, visible, onClose, handleReplace,
}) => {
  const [sensitiveWords, setSensitiveWords] = useState<string>('');
  const [exitSensitiveTitleAndContent, setExitSensitiveTitleAndContent] = useState<string>('');
  const [announcementId, setAnnouncementId] = useState<number>();
  const [attachmentList, setAttachmentList] = useState<Attachment[]>([]);

  const colunms = [{
    title: '序号',
    dataIndex: 'index',
    width: 65,
    render: (_, __, index) => index + 1,
  }, {
    title: '附件名称',
    dataIndex: 'fileName',
  }, {
    title: '校验结果',
    dataIndex: 'fileSensitiveStatus',
    width: 100,
    render: (result, row) => {
      return (
        result === FileStatus.VerificationFailed ? (
          <Popover content={row.sensitiveWords} getPopupContainer={n => n} >
            <span>{fileStatus[result]}</span>
          </Popover>
        ) : <span> {fileStatus[result]} </span>
      );
    },
  }, {
    title: '操作',
    dataIndex: 'actionName',
    width: 140,
    render: (_, row) => {
      return (
        row.fileSensitiveStatus === FileStatus.VerificationFailed ? (
          <div>
            <a onClick={() => {
              window.open(row.fileUrl);
            }}
            >下载
            </a> &nbsp;
            <Link to={`/manage/reEdit/${announcementId}`}>
              手动订正
            </Link>
          </div>
        ) : ''
      );
    },
  }];
  useEffect(() => {
    if (visible) {
      detailAnnouncementSensitiveGet({
        sensitiveRecordId,
      }).then((res) => {
        if (!res.success) {
          message.error(res.error);
        }
        const detail = res.result;
        setExitSensitiveTitleAndContent(detail.exitSensitiveTitleAndContent); // 公告内容检验结果
        setSensitiveWords(detail.sensitiveWords);
        setAnnouncementId(detail.announcementId);
        setAttachmentList(detail.attachmentResultList);
      });
    }
  }, [visible]);

  return (
    <div>
      <Drawer
        title="公告敏感词拦截明细"
        width={580}
        onClose={onClose}
        visible={visible}
      >
        <div className="drawer_title">
          公告内容
        </div>
        <div className="ann_content">
          <span className={exitSensitiveTitleAndContent ? 'check_un_pass' : ''}>
            检验{ exitSensitiveTitleAndContent ? '不通过' : '通过' } &nbsp;   
            {exitSensitiveTitleAndContent && <span className="batch_replace" onClick={handleReplace}>批量替换</span>}
          </span>
          <div>
            命中敏感词：{sensitiveWords}
          </div>
        </div>
        <div className="drawer_title">公告附件</div>
        <Table 
          columns={colunms}
          dataSource={attachmentList}
          rowKey="id"
          pagination={false}
        />
      </Drawer>
    </div>
  );
};

export default InterceptDetailDrawer;
