/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_24 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_24 = '' as any
const prodUrl_0_0_0_24 = '' as any
const dataKey_0_0_0_24 = 'data' as any

/**
 * 接口 [分页查询我的公告列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425138) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/pagingAnnouncement`
 * @项目ID 2024
 */
export interface PagingAnnouncementGetRequest {
  /**
   * 大类型,多个类型中间用逗号隔开
   */
  annBigTypes: string
  /**
   * 是否是待我处理,默认否
   */
  myTodo?: string
  /**
   * 查询状态:0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束
   */
  queryStatus?: string
  pageNo?: string
  pageSize?: string
}

/**
 * 接口 [分页查询我的公告列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425138) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/pagingAnnouncement`
 * @项目ID 2024
 */
export interface PagingAnnouncementGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 主键id
       */
      id?: number
      /**
       * 标题
       */
      title?: string
      /**
       * 公告名称
       */
      announcementTypeName?: string
      /**
       * 外网链接
       */
      outUrl?: string
      /**
       * 处理码: @see{@link AnnouncementDealEnum}
       */
      dealCode?: number
      /**
       * 显示用状态(0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束)
       */
      showStatus?: number
      /**
       * 项目名称
       */
      projectName?: string
      /**
       * 项目编号
       */
      projectCode?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [分页查询我的公告列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425138) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/pagingAnnouncement`
 * @项目ID 2024
 */
type PagingAnnouncementGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/pagingAnnouncement',
    'data',
    string,
    'annBigTypes' | 'myTodo' | 'queryStatus' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [分页查询我的公告列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425138) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/pagingAnnouncement`
 * @项目ID 2024
 */
const pagingAnnouncementGetRequestConfig: PagingAnnouncementGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/pagingAnnouncement',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: ['annBigTypes', 'myTodo', 'queryStatus', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pagingAnnouncementGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [分页查询我的公告列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425138) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/pagingAnnouncement`
 * @项目ID 2024
 */
export const pagingAnnouncementGet = /*#__PURE__*/ (
  requestData: PagingAnnouncementGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PagingAnnouncementGetResponse>(prepare(pagingAnnouncementGetRequestConfig, requestData), ...args)
}

pagingAnnouncementGet.requestConfig = pagingAnnouncementGetRequestConfig

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425146) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnouncement`
 * @项目ID 2024
 */
export interface GetAnnouncementGetRequest {
  /**
   * 公告id
   */
  announcementId: string
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425146) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnouncement`
 * @项目ID 2024
 */
export interface GetAnnouncementGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键id
     */
    id?: number
    /**
     * 标题
     */
    title?: string
    /**
     * 创建时间
     */
    createdAt?: string
    /**
     * 创建人名称
     */
    creatorName?: string
    /**
     * 附件列表
     */
    attachments?: {
      /**
       * 文件url,OSS id
       */
      fileId?: string
      /**
       * 文件名称
       */
      name?: string
      /**
       * 文件大小
       */
      size?: number
      /**
       * 是否在外网显示
       */
      isShow?: boolean
      /**
       * 文件编码
       */
      fileCode?: string
      /**
       * 文件描述
       */
      fileDesc?: string
      /**
       * 原始FileID
       */
      sourceFileId?: string
    }[]
    /**
     * 显示用状态(0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束)
     */
    showStatus?: number
    /**
     * 公告内容 html
     */
    content?: string
    /**
     * 处理码
     * 计算方法:AnnouncementDealEnum.getAnnouncementDealEnumList(dealCode)可以得出具体的处理集合
     */
    dealCode?: number
    /**
     * 公告类型
     */
    announcementType?: number
  }
  error?: string
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425146) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnouncement`
 * @项目ID 2024
 */
type GetAnnouncementGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/getAnnouncement',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425146) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnouncement`
 * @项目ID 2024
 */
const getAnnouncementGetRequestConfig: GetAnnouncementGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/getAnnouncement',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425146) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnouncement`
 * @项目ID 2024
 */
export const getAnnouncementGet = /*#__PURE__*/ (
  requestData: GetAnnouncementGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementGetResponse>(prepare(getAnnouncementGetRequestConfig, requestData), ...args)
}

getAnnouncementGet.requestConfig = getAnnouncementGetRequestConfig

/**
 * 接口 [获取我的公告大类型对应的待办数↗](http://yapi.cai-inc.com/project/2024/interface/api/425154) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnBigTypeBacklogCounts`
 * @项目ID 2024
 */
export interface GetAnnBigTypeBacklogCountsGetRequest {}

/**
 * 接口 [获取我的公告大类型对应的待办数↗](http://yapi.cai-inc.com/project/2024/interface/api/425154) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnBigTypeBacklogCounts`
 * @项目ID 2024
 */
export interface GetAnnBigTypeBacklogCountsGetResponse {
  success?: boolean
  result?: {
    '0'?: number
  }
  error?: string
}

/**
 * 接口 [获取我的公告大类型对应的待办数↗](http://yapi.cai-inc.com/project/2024/interface/api/425154) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnBigTypeBacklogCounts`
 * @项目ID 2024
 */
type GetAnnBigTypeBacklogCountsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/getAnnBigTypeBacklogCounts',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取我的公告大类型对应的待办数↗](http://yapi.cai-inc.com/project/2024/interface/api/425154) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnBigTypeBacklogCounts`
 * @项目ID 2024
 */
const getAnnBigTypeBacklogCountsGetRequestConfig: GetAnnBigTypeBacklogCountsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/getAnnBigTypeBacklogCounts',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnBigTypeBacklogCountsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取我的公告大类型对应的待办数↗](http://yapi.cai-inc.com/project/2024/interface/api/425154) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnBigTypeBacklogCounts`
 * @项目ID 2024
 */
export const getAnnBigTypeBacklogCountsGet = /*#__PURE__*/ (
  requestData?: GetAnnBigTypeBacklogCountsGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnBigTypeBacklogCountsGetResponse>(
    prepare(getAnnBigTypeBacklogCountsGetRequestConfig, requestData),
    ...args,
  )
}

getAnnBigTypeBacklogCountsGet.requestConfig = getAnnBigTypeBacklogCountsGetRequestConfig

/**
 * 接口 [获取公告流转日志↗](http://yapi.cai-inc.com/project/2024/interface/api/425162) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnFlowlogs`
 * @项目ID 2024
 */
export interface GetAnnFlowlogsGetRequest {
  announcementId: string
}

/**
 * 接口 [获取公告流转日志↗](http://yapi.cai-inc.com/project/2024/interface/api/425162) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnFlowlogs`
 * @项目ID 2024
 */
export interface GetAnnFlowlogsGetResponse {
  success?: boolean
  result?: {
    /**
     * 操作者姓名
     */
    operatorName?: string
    /**
     * 执行时间
     */
    createAt?: string
    /**
     * 审批意见
     */
    mark?: string
    /**
     * 操作人机构名称
     */
    operatorOrgName?: string
    /**
     * 节点信息
     */
    nodeName?: string
  }[]
  error?: string
}

/**
 * 接口 [获取公告流转日志↗](http://yapi.cai-inc.com/project/2024/interface/api/425162) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnFlowlogs`
 * @项目ID 2024
 */
type GetAnnFlowlogsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/getAnnFlowlogs',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [获取公告流转日志↗](http://yapi.cai-inc.com/project/2024/interface/api/425162) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnFlowlogs`
 * @项目ID 2024
 */
const getAnnFlowlogsGetRequestConfig: GetAnnFlowlogsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/getAnnFlowlogs',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnFlowlogsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告流转日志↗](http://yapi.cai-inc.com/project/2024/interface/api/425162) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getAnnFlowlogs`
 * @项目ID 2024
 */
export const getAnnFlowlogsGet = /*#__PURE__*/ (
  requestData: GetAnnFlowlogsGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnFlowlogsGetResponse>(prepare(getAnnFlowlogsGetRequestConfig, requestData), ...args)
}

getAnnFlowlogsGet.requestConfig = getAnnFlowlogsGetRequestConfig

/**
 * 接口 [获取公告审核人详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425170) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getCheckerDetail`
 * @项目ID 2024
 */
export interface GetCheckerDetailGetRequest {
  announcementId: string
  announcementType: string
}

/**
 * 接口 [获取公告审核人详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425170) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getCheckerDetail`
 * @项目ID 2024
 */
export interface GetCheckerDetailGetResponse {
  success?: boolean
  result?: {
    checkerName?: string
    nextTaskUserName?: string[]
  }
  error?: string
}

/**
 * 接口 [获取公告审核人详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425170) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getCheckerDetail`
 * @项目ID 2024
 */
type GetCheckerDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/getCheckerDetail',
    'data',
    string,
    'announcementId' | 'announcementType',
    false
  >
>

/**
 * 接口 [获取公告审核人详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425170) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getCheckerDetail`
 * @项目ID 2024
 */
const getCheckerDetailGetRequestConfig: GetCheckerDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/getCheckerDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: ['announcementId', 'announcementType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getCheckerDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告审核人详情↗](http://yapi.cai-inc.com/project/2024/interface/api/425170) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getCheckerDetail`
 * @项目ID 2024
 */
export const getCheckerDetailGet = /*#__PURE__*/ (
  requestData: GetCheckerDetailGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetCheckerDetailGetResponse>(prepare(getCheckerDetailGetRequestConfig, requestData), ...args)
}

getCheckerDetailGet.requestConfig = getCheckerDetailGetRequestConfig

/**
 * 接口 [审核公告↗](http://yapi.cai-inc.com/project/2024/interface/api/425178) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `POST /announcement/mapp/checkAnnouncement`
 * @项目ID 2024
 */
export interface CheckAnnouncementPostRequest {
  /**
   * 公告id
   */
  id?: number
  /**
   * 是否通过
   */
  pass?: boolean
  /**
   * 审批意见
   */
  checkOpinion?: string
  nextTaskUserId?: number
  nextTaskUserIds?: number[]
  identificationIds?: number[]
  /**
   * 工作流参数
   */
  processFrontParam?: string
}

/**
 * 接口 [审核公告↗](http://yapi.cai-inc.com/project/2024/interface/api/425178) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `POST /announcement/mapp/checkAnnouncement`
 * @项目ID 2024
 */
export interface CheckAnnouncementPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [审核公告↗](http://yapi.cai-inc.com/project/2024/interface/api/425178) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `POST /announcement/mapp/checkAnnouncement`
 * @项目ID 2024
 */
type CheckAnnouncementPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/checkAnnouncement',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [审核公告↗](http://yapi.cai-inc.com/project/2024/interface/api/425178) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `POST /announcement/mapp/checkAnnouncement`
 * @项目ID 2024
 */
const checkAnnouncementPostRequestConfig: CheckAnnouncementPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/checkAnnouncement',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'checkAnnouncementPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [审核公告↗](http://yapi.cai-inc.com/project/2024/interface/api/425178) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `POST /announcement/mapp/checkAnnouncement`
 * @项目ID 2024
 */
export const checkAnnouncementPost = /*#__PURE__*/ (
  requestData: CheckAnnouncementPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CheckAnnouncementPostResponse>(prepare(checkAnnouncementPostRequestConfig, requestData), ...args)
}

checkAnnouncementPost.requestConfig = checkAnnouncementPostRequestConfig

/**
 * 接口 [查询并返下个节点的审核人列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425186) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getNextTaskUsers`
 * @项目ID 2024
 */
export interface GetNextTaskUsersGetRequest {
  announcementId: string
  announcementType: string
}

/**
 * 接口 [查询并返下个节点的审核人列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425186) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getNextTaskUsers`
 * @项目ID 2024
 */
export interface GetNextTaskUsersGetResponse {
  success?: boolean
  result?: {
    existNextNode?: boolean
    taskName?: string
    taskUser?: {
      userId?: number
      userName?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [查询并返下个节点的审核人列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425186) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getNextTaskUsers`
 * @项目ID 2024
 */
type GetNextTaskUsersGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/mapp/getNextTaskUsers',
    'data',
    string,
    'announcementId' | 'announcementType',
    false
  >
>

/**
 * 接口 [查询并返下个节点的审核人列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425186) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getNextTaskUsers`
 * @项目ID 2024
 */
const getNextTaskUsersGetRequestConfig: GetNextTaskUsersGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_24,
  devUrl: devUrl_0_0_0_24,
  prodUrl: prodUrl_0_0_0_24,
  path: '/announcement/mapp/getNextTaskUsers',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_24,
  paramNames: [],
  queryNames: ['announcementId', 'announcementType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getNextTaskUsersGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询并返下个节点的审核人列表↗](http://yapi.cai-inc.com/project/2024/interface/api/425186) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [MAppController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51068)
 * @请求头 `GET /announcement/mapp/getNextTaskUsers`
 * @项目ID 2024
 */
export const getNextTaskUsersGet = /*#__PURE__*/ (
  requestData: GetNextTaskUsersGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetNextTaskUsersGetResponse>(prepare(getNextTaskUsersGetRequestConfig, requestData), ...args)
}

getNextTaskUsersGet.requestConfig = getNextTaskUsersGetRequestConfig

/* prettier-ignore-end */
