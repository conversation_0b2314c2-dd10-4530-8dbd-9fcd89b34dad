/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_9 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_9 = '' as any
const prodUrl_0_0_0_9 = '' as any
const dataKey_0_0_0_9 = 'data' as any

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423802) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncement`
 * @项目ID 2024
 */
export interface GetAnnouncementGetRequest {
  /**
   * 公告ID
   */
  announcementId: string
  /**
   * 是否包含附件信息（默认不包含）
   */
  containsAttachment?: string
  /**
   * 是否隐藏不可见附件（默认隐藏）
   */
  hideInvisibleAttachment?: string
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423802) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncement`
 * @项目ID 2024
 */
export interface GetAnnouncementGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键id
     */
    id?: number
    /**
     * 标题
     */
    title?: string
    /**
     * 行政区划code
     */
    district?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 项目名称
     */
    projectName?: string
    /**
     * 项目编码
     */
    projectCode?: string
    /**
     * 公告元数据内容
     */
    metaData?: string
    /**
     * 公告内容 html
     */
    content?: string
    /**
     * 状态 初始(1) 待审核(2) 待发布(3) 审核不通过(4) 已发布(5)
     */
    status?: number
    /**
     * 创建时间
     */
    createdAt?: string
    /**
     * 发布时间
     */
    releasedAt?: string
    /**
     * 截止时间
     */
    expiredAt?: string
    /**
     * 创建人名称
     */
    creatorName?: string
    /**
     * 创建人id
     */
    creatorId?: number
    /**
     * 创建人orgId
     */
    createOrgId?: number
    /**
     * 附件列表
     */
    attachments?: {
      /**
       * 文件url,OSS id
       */
      fileId?: string
      /**
       * 文件名称
       */
      name?: string
      /**
       * 文件大小
       */
      size?: number
      /**
       * 是否在外网显示
       */
      isShow?: boolean
      /**
       * 文件编码
       */
      fileCode?: string
      /**
       * 文件描述
       */
      fileDesc?: string
      /**
       * 原始FileID
       */
      sourceFileId?: string
    }[]
    /**
     * 业务链接
     */
    url?: string
    /**
     * 业务id
     */
    serialNum?: string
    /**
     * 公告发布类型@1:定时发布@2
     */
    pubType?: number
    /**
     * 记录公告展示天数
     */
    showDuration?: number
    /**
     * 外网链接
     */
    outUrl?: string
    /**
     * 机构名称
     */
    orgName?: string
    /**
     * 大类型
     */
    annBigType?: number
    /**
     * 业务应用code
     */
    appCode?: string
    /**
     * 业务名称
     */
    appName?: string
    /**
     * 公告发布人信息
     */
    creatorInfo?: string
    /**
     * 公告更新时间
     */
    updateTime?: string
  }
  code?: string
  message?: string
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423802) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncement`
 * @项目ID 2024
 */
type GetAnnouncementGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/open/getAnnouncement',
    'data',
    string,
    'announcementId' | 'containsAttachment' | 'hideInvisibleAttachment',
    false
  >
>

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423802) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncement`
 * @项目ID 2024
 */
const getAnnouncementGetRequestConfig: GetAnnouncementGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_9,
  devUrl: devUrl_0_0_0_9,
  prodUrl: prodUrl_0_0_0_9,
  path: '/announcement/open/getAnnouncement',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_9,
  paramNames: [],
  queryNames: ['announcementId', 'containsAttachment', 'hideInvisibleAttachment'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423802) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncement`
 * @项目ID 2024
 */
export const getAnnouncementGet = /*#__PURE__*/ (
  requestData: GetAnnouncementGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementGetResponse>(prepare(getAnnouncementGetRequestConfig, requestData), ...args)
}

getAnnouncementGet.requestConfig = getAnnouncementGetRequestConfig

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423810) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncementEncrypt`
 * @项目ID 2024
 */
export interface GetAnnouncementEncryptGetRequest {
  /**
   * 公告ID
   */
  announcementEncryptId?: string
  /**
   * 是否包含附件信息（默认不包含）
   */
  containsAttachment?: string
  /**
   * 是否隐藏不可见附件（默认隐藏）
   */
  hideInvisibleAttachment?: string
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423810) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncementEncrypt`
 * @项目ID 2024
 */
export interface GetAnnouncementEncryptGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键id
     */
    id?: number
    /**
     * 标题
     */
    title?: string
    /**
     * 行政区划code
     */
    district?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 项目名称
     */
    projectName?: string
    /**
     * 项目编码
     */
    projectCode?: string
    /**
     * 公告元数据内容
     */
    metaData?: string
    /**
     * 公告内容 html
     */
    content?: string
    /**
     * 状态 初始(1) 待审核(2) 待发布(3) 审核不通过(4) 已发布(5)
     */
    status?: number
    /**
     * 创建时间
     */
    createdAt?: string
    /**
     * 发布时间
     */
    releasedAt?: string
    /**
     * 截止时间
     */
    expiredAt?: string
    /**
     * 创建人名称
     */
    creatorName?: string
    /**
     * 创建人id
     */
    creatorId?: number
    /**
     * 创建人orgId
     */
    createOrgId?: number
    /**
     * 附件列表
     */
    attachments?: {
      /**
       * 文件url,OSS id
       */
      fileId?: string
      /**
       * 文件名称
       */
      name?: string
      /**
       * 文件大小
       */
      size?: number
      /**
       * 是否在外网显示
       */
      isShow?: boolean
      /**
       * 文件编码
       */
      fileCode?: string
      /**
       * 文件描述
       */
      fileDesc?: string
      /**
       * 原始FileID
       */
      sourceFileId?: string
    }[]
    /**
     * 业务链接
     */
    url?: string
    /**
     * 业务id
     */
    serialNum?: string
    /**
     * 公告发布类型@1:定时发布@2
     */
    pubType?: number
    /**
     * 记录公告展示天数
     */
    showDuration?: number
    /**
     * 外网链接
     */
    outUrl?: string
    /**
     * 机构名称
     */
    orgName?: string
    /**
     * 大类型
     */
    annBigType?: number
    /**
     * 业务应用code
     */
    appCode?: string
    /**
     * 业务名称
     */
    appName?: string
    /**
     * 公告发布人信息
     */
    creatorInfo?: string
    /**
     * 公告更新时间
     */
    updateTime?: string
  }
  code?: string
  message?: string
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423810) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncementEncrypt`
 * @项目ID 2024
 */
type GetAnnouncementEncryptGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/open/getAnnouncementEncrypt',
    'data',
    string,
    'announcementEncryptId' | 'containsAttachment' | 'hideInvisibleAttachment',
    false
  >
>

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423810) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncementEncrypt`
 * @项目ID 2024
 */
const getAnnouncementEncryptGetRequestConfig: GetAnnouncementEncryptGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_9,
  devUrl: devUrl_0_0_0_9,
  prodUrl: prodUrl_0_0_0_9,
  path: '/announcement/open/getAnnouncementEncrypt',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_9,
  paramNames: [],
  queryNames: ['announcementEncryptId', 'containsAttachment', 'hideInvisibleAttachment'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementEncryptGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getAnnouncement↗](http://yapi.cai-inc.com/project/2024/interface/api/423810) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getAnnouncementEncrypt`
 * @项目ID 2024
 */
export const getAnnouncementEncryptGet = /*#__PURE__*/ (
  requestData: GetAnnouncementEncryptGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementEncryptGetResponse>(
    prepare(getAnnouncementEncryptGetRequestConfig, requestData),
    ...args,
  )
}

getAnnouncementEncryptGet.requestConfig = getAnnouncementEncryptGetRequestConfig

/**
 * 接口 [fixedTimeConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/423818) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getFixedTimeFlag`
 * @项目ID 2024
 */
export interface GetFixedTimeFlagGetRequest {
  districtCode?: string
}

/**
 * 接口 [fixedTimeConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/423818) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getFixedTimeFlag`
 * @项目ID 2024
 */
export interface GetFixedTimeFlagGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [fixedTimeConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/423818) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getFixedTimeFlag`
 * @项目ID 2024
 */
type GetFixedTimeFlagGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/open/getFixedTimeFlag',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [fixedTimeConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/423818) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getFixedTimeFlag`
 * @项目ID 2024
 */
const getFixedTimeFlagGetRequestConfig: GetFixedTimeFlagGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_9,
  devUrl: devUrl_0_0_0_9,
  prodUrl: prodUrl_0_0_0_9,
  path: '/announcement/open/getFixedTimeFlag',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_9,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getFixedTimeFlagGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [fixedTimeConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/423818) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementOpenController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51053)
 * @请求头 `GET /announcement/open/getFixedTimeFlag`
 * @项目ID 2024
 */
export const getFixedTimeFlagGet = /*#__PURE__*/ (
  requestData: GetFixedTimeFlagGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetFixedTimeFlagGetResponse>(prepare(getFixedTimeFlagGetRequestConfig, requestData), ...args)
}

getFixedTimeFlagGet.requestConfig = getFixedTimeFlagGetRequestConfig

/* prettier-ignore-end */
