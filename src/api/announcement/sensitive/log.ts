/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_0 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_0 = '' as any
const prodUrl_0_0_0_0 = '' as any
const dataKey_0_0_0_0 = 'data' as any

/**
 * 接口 [敏感词拦截记录操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423890) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/log/detail`
 * @项目ID 2024
 */
export interface DetailGetRequest {
  recordId: string
}

/**
 * 接口 [敏感词拦截记录操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423890) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/log/detail`
 * @项目ID 2024
 */
export interface DetailGetResponse {
  success?: boolean
  result?: {
    /**
     * 操作日志主键id
     */
    id?: number
    /**
     * 操作动作
     */
    actionName?: string
    /**
     * 操作人id
     */
    operatorId?: number
    /**
     * 操作人姓名
     */
    operatorName?: string
    /**
     * 操作日志创建时间
     */
    rAddTime?: string
    /**
     * 操作内容详情
     */
    actionContent?: string
  }[]
  error?: string
}

/**
 * 接口 [敏感词拦截记录操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423890) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/log/detail`
 * @项目ID 2024
 */
type DetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/sensitive/log/detail',
    'data',
    string,
    'recordId',
    false
  >
>

/**
 * 接口 [敏感词拦截记录操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423890) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/log/detail`
 * @项目ID 2024
 */
const detailGetRequestConfig: DetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/sensitive/log/detail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['recordId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'detailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [敏感词拦截记录操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423890) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/log/detail`
 * @项目ID 2024
 */
export const detailGet = /*#__PURE__*/ (requestData: DetailGetRequest, ...args: UserRequestRestArgs) => {
  return request<DetailGetResponse>(prepare(detailGetRequestConfig, requestData), ...args)
}

detailGet.requestConfig = detailGetRequestConfig

/* prettier-ignore-end */
