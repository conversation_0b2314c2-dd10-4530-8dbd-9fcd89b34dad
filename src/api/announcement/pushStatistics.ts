/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_23 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_23 = '' as any
const prodUrl_0_0_0_23 = '' as any
const dataKey_0_0_0_23 = 'data' as any

/**
 * 接口 [listTarget↗](http://yapi.cai-inc.com/project/2024/interface/api/425090) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listTarget`
 * @项目ID 2024
 */
export interface ListTargetGetRequest {
  name?: string
}

/**
 * 接口 [listTarget↗](http://yapi.cai-inc.com/project/2024/interface/api/425090) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listTarget`
 * @项目ID 2024
 */
export interface ListTargetGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [listTarget↗](http://yapi.cai-inc.com/project/2024/interface/api/425090) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listTarget`
 * @项目ID 2024
 */
type ListTargetGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/listTarget',
    'data',
    string,
    'name',
    false
  >
>

/**
 * 接口 [listTarget↗](http://yapi.cai-inc.com/project/2024/interface/api/425090) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listTarget`
 * @项目ID 2024
 */
const listTargetGetRequestConfig: ListTargetGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/listTarget',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['name'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listTargetGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [listTarget↗](http://yapi.cai-inc.com/project/2024/interface/api/425090) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listTarget`
 * @项目ID 2024
 */
export const listTargetGet = /*#__PURE__*/ (requestData: ListTargetGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListTargetGetResponse>(prepare(listTargetGetRequestConfig, requestData), ...args)
}

listTargetGet.requestConfig = listTargetGetRequestConfig

/**
 * 接口 [listProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425098) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listProvince`
 * @项目ID 2024
 */
export interface ListProvinceGetRequest {
  name?: string
}

/**
 * 接口 [listProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425098) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listProvince`
 * @项目ID 2024
 */
export interface ListProvinceGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [listProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425098) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listProvince`
 * @项目ID 2024
 */
type ListProvinceGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/listProvince',
    'data',
    string,
    'name',
    false
  >
>

/**
 * 接口 [listProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425098) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listProvince`
 * @项目ID 2024
 */
const listProvinceGetRequestConfig: ListProvinceGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/listProvince',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['name'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listProvinceGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [listProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425098) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/listProvince`
 * @项目ID 2024
 */
export const listProvinceGet = /*#__PURE__*/ (requestData: ListProvinceGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListProvinceGetResponse>(prepare(listProvinceGetRequestConfig, requestData), ...args)
}

listProvinceGet.requestConfig = listProvinceGetRequestConfig

/**
 * 接口 [pushStatisticsBase↗](http://yapi.cai-inc.com/project/2024/interface/api/425106) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsBase`
 * @项目ID 2024
 */
export interface PushStatisticsBaseGetRequest {
  targetKey?: string
  startDate?: string
  endDate?: string
}

/**
 * 接口 [pushStatisticsBase↗](http://yapi.cai-inc.com/project/2024/interface/api/425106) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsBase`
 * @项目ID 2024
 */
export interface PushStatisticsBaseGetResponse {
  success?: boolean
  result?: {
    /**
     * 日期
     */
    date?: string
    /**
     * 总数
     */
    totalNum?: number
    /**
     * 成功数
     */
    successNum?: number
  }[]
  error?: string
}

/**
 * 接口 [pushStatisticsBase↗](http://yapi.cai-inc.com/project/2024/interface/api/425106) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsBase`
 * @项目ID 2024
 */
type PushStatisticsBaseGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/pushStatisticsBase',
    'data',
    string,
    'targetKey' | 'startDate' | 'endDate',
    false
  >
>

/**
 * 接口 [pushStatisticsBase↗](http://yapi.cai-inc.com/project/2024/interface/api/425106) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsBase`
 * @项目ID 2024
 */
const pushStatisticsBaseGetRequestConfig: PushStatisticsBaseGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/pushStatisticsBase',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['targetKey', 'startDate', 'endDate'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pushStatisticsBaseGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [pushStatisticsBase↗](http://yapi.cai-inc.com/project/2024/interface/api/425106) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsBase`
 * @项目ID 2024
 */
export const pushStatisticsBaseGet = /*#__PURE__*/ (
  requestData: PushStatisticsBaseGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PushStatisticsBaseGetResponse>(prepare(pushStatisticsBaseGetRequestConfig, requestData), ...args)
}

pushStatisticsBaseGet.requestConfig = pushStatisticsBaseGetRequestConfig

/**
 * 接口 [pushStatisticsGroupByAnnType↗](http://yapi.cai-inc.com/project/2024/interface/api/425114) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByAnnType`
 * @项目ID 2024
 */
export interface PushStatisticsByAnnTypeGetRequest {
  targetKey?: string
  startDate?: string
  endDate?: string
}

/**
 * 接口 [pushStatisticsGroupByAnnType↗](http://yapi.cai-inc.com/project/2024/interface/api/425114) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByAnnType`
 * @项目ID 2024
 */
export interface PushStatisticsByAnnTypeGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 日期
     */
    date?: string
    /**
     * 总数
     */
    totalNum?: number
    /**
     * 成功数
     */
    successNum?: number
  }[]
  error?: string
}

/**
 * 接口 [pushStatisticsGroupByAnnType↗](http://yapi.cai-inc.com/project/2024/interface/api/425114) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByAnnType`
 * @项目ID 2024
 */
type PushStatisticsByAnnTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/pushStatisticsByAnnType',
    'data',
    string,
    'targetKey' | 'startDate' | 'endDate',
    false
  >
>

/**
 * 接口 [pushStatisticsGroupByAnnType↗](http://yapi.cai-inc.com/project/2024/interface/api/425114) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByAnnType`
 * @项目ID 2024
 */
const pushStatisticsByAnnTypeGetRequestConfig: PushStatisticsByAnnTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/pushStatisticsByAnnType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['targetKey', 'startDate', 'endDate'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pushStatisticsByAnnTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [pushStatisticsGroupByAnnType↗](http://yapi.cai-inc.com/project/2024/interface/api/425114) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByAnnType`
 * @项目ID 2024
 */
export const pushStatisticsByAnnTypeGet = /*#__PURE__*/ (
  requestData: PushStatisticsByAnnTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PushStatisticsByAnnTypeGetResponse>(
    prepare(pushStatisticsByAnnTypeGetRequestConfig, requestData),
    ...args,
  )
}

pushStatisticsByAnnTypeGet.requestConfig = pushStatisticsByAnnTypeGetRequestConfig

/**
 * 接口 [pushStatisticsGroupByProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425122) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByProvince`
 * @项目ID 2024
 */
export interface PushStatisticsByProvinceGetRequest {
  targetKey?: string
  startDate?: string
  endDate?: string
}

/**
 * 接口 [pushStatisticsGroupByProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425122) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByProvince`
 * @项目ID 2024
 */
export interface PushStatisticsByProvinceGetResponse {
  success?: boolean
  result?: {
    /**
     * 省分区划code
     */
    provinceCode?: string
    /**
     * 省分的名称
     */
    provinceName?: string
    /**
     * 日期
     */
    date?: string
    /**
     * 总数
     */
    totalNum?: number
    /**
     * 成功数
     */
    successNum?: number
  }[]
  error?: string
}

/**
 * 接口 [pushStatisticsGroupByProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425122) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByProvince`
 * @项目ID 2024
 */
type PushStatisticsByProvinceGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/pushStatisticsByProvince',
    'data',
    string,
    'targetKey' | 'startDate' | 'endDate',
    false
  >
>

/**
 * 接口 [pushStatisticsGroupByProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425122) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByProvince`
 * @项目ID 2024
 */
const pushStatisticsByProvinceGetRequestConfig: PushStatisticsByProvinceGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/pushStatisticsByProvince',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['targetKey', 'startDate', 'endDate'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pushStatisticsByProvinceGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [pushStatisticsGroupByProvince↗](http://yapi.cai-inc.com/project/2024/interface/api/425122) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pushStatisticsByProvince`
 * @项目ID 2024
 */
export const pushStatisticsByProvinceGet = /*#__PURE__*/ (
  requestData: PushStatisticsByProvinceGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PushStatisticsByProvinceGetResponse>(
    prepare(pushStatisticsByProvinceGetRequestConfig, requestData),
    ...args,
  )
}

pushStatisticsByProvinceGet.requestConfig = pushStatisticsByProvinceGetRequestConfig

/**
 * 接口 [pagePushErrorAnn↗](http://yapi.cai-inc.com/project/2024/interface/api/425130) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pagePushErrorAnn`
 * @项目ID 2024
 */
export interface PagePushErrorAnnGetRequest {
  targetKey?: string
  /**
   * 区划前缀
   */
  districtPrefix?: string
  /**
   * 公告类型
   */
  announcementType?: string
  startDate?: string
  endDate?: string
  /**
   * pageNo 页码(null的时候默认1)(非必传)
   */
  pageNo?: string
  /**
   * pageSize (null的时候默认20)(非必传)
   */
  pageSize?: string
}

/**
 * 接口 [pagePushErrorAnn↗](http://yapi.cai-inc.com/project/2024/interface/api/425130) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pagePushErrorAnn`
 * @项目ID 2024
 */
export interface PagePushErrorAnnGetResponse {
  success?: boolean
  result?: {
    pageNo?: number
    pageSize?: number
    totalPage?: number
    total?: number
    data?: {
      /**
       * 公告ID
       */
      annId?: number
      /**
       * 公告区划CODE
       */
      district?: string
      /**
       * 公告区划名称
       */
      districtName?: string
      /**
       * 公告大类型
       */
      annBigType?: number
      /**
       * 公告类型
       */
      announcementType?: number
      /**
       * 公告类型名称
       */
      announcementTypeName?: string
      /**
       * 公告标题
       */
      title?: string
      /**
       * 错误原因
       */
      error?: string
      /**
       * 创建时间
       */
      createAt?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [pagePushErrorAnn↗](http://yapi.cai-inc.com/project/2024/interface/api/425130) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pagePushErrorAnn`
 * @项目ID 2024
 */
type PagePushErrorAnnGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/pushStatistics/pagePushErrorAnn',
    'data',
    string,
    'targetKey' | 'districtPrefix' | 'announcementType' | 'startDate' | 'endDate' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [pagePushErrorAnn↗](http://yapi.cai-inc.com/project/2024/interface/api/425130) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pagePushErrorAnn`
 * @项目ID 2024
 */
const pagePushErrorAnnGetRequestConfig: PagePushErrorAnnGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_23,
  devUrl: devUrl_0_0_0_23,
  prodUrl: prodUrl_0_0_0_23,
  path: '/announcement/pushStatistics/pagePushErrorAnn',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_23,
  paramNames: [],
  queryNames: ['targetKey', 'districtPrefix', 'announcementType', 'startDate', 'endDate', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pagePushErrorAnnGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [pagePushErrorAnn↗](http://yapi.cai-inc.com/project/2024/interface/api/425130) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnPushStatisticsController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51067)
 * @请求头 `GET /announcement/pushStatistics/pagePushErrorAnn`
 * @项目ID 2024
 */
export const pagePushErrorAnnGet = /*#__PURE__*/ (
  requestData: PagePushErrorAnnGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PagePushErrorAnnGetResponse>(prepare(pagePushErrorAnnGetRequestConfig, requestData), ...args)
}

pagePushErrorAnnGet.requestConfig = pagePushErrorAnnGetRequestConfig

/* prettier-ignore-end */
