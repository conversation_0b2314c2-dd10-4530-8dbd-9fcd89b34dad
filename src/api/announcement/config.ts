/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_1 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_1 = '' as any
const prodUrl_0_0_0_1 = '' as any
const dataKey_0_0_0_1 = 'data' as any

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423370) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @项目ID 2024
 */
export interface PageSiteInfoGetRequest {
  /**
   * 站点名称
   */
  siteName: string
  districtCode: string
  pageNo: string
  pageSize: string
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423370) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @项目ID 2024
 */
export interface PageSiteInfoGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 公告站点主键ID
       */
      announcementSiteId?: number
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点域名
       */
      siteDomain?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423370) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @项目ID 2024
 */
type PageSiteInfoGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/pageSiteInfo',
    'data',
    string,
    'siteName' | 'districtCode' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423370) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @项目ID 2024
 */
const pageSiteInfoGetRequestConfig: PageSiteInfoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/pageSiteInfo',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: ['siteName', 'districtCode', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pageSiteInfoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423370) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @项目ID 2024
 */
export const pageSiteInfoGet = /*#__PURE__*/ (requestData: PageSiteInfoGetRequest, ...args: UserRequestRestArgs) => {
  return request<PageSiteInfoGetResponse>(prepare(pageSiteInfoGetRequestConfig, requestData), ...args)
}

pageSiteInfoGet.requestConfig = pageSiteInfoGetRequestConfig

/**
 * 接口 [关联公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/423378) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSite`
 * @项目ID 2024
 */
export interface RelationSitePostRequest {
  /**
   * 区划编码
   */
  districtCode?: string
  /**
   * 公告站点表主键ID
   */
  announcementSiteIds?: number[]
}

/**
 * 接口 [关联公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/423378) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSite`
 * @项目ID 2024
 */
export interface RelationSitePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [关联公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/423378) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSite`
 * @项目ID 2024
 */
type RelationSitePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/relationSite',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [关联公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/423378) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSite`
 * @项目ID 2024
 */
const relationSitePostRequestConfig: RelationSitePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/relationSite',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'relationSitePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [关联公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/423378) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSite`
 * @项目ID 2024
 */
export const relationSitePost = /*#__PURE__*/ (requestData: RelationSitePostRequest, ...args: UserRequestRestArgs) => {
  return request<RelationSitePostResponse>(prepare(relationSitePostRequestConfig, requestData), ...args)
}

relationSitePost.requestConfig = relationSitePostRequestConfig

/**
 * 接口 [公告站点配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423386) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listSiteDistrict`
 * @项目ID 2024
 */
export interface ListSiteDistrictGetRequest {
  /**
   * 区划code
   */
  districtCode: string
}

/**
 * 接口 [公告站点配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423386) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listSiteDistrict`
 * @项目ID 2024
 */
export interface ListSiteDistrictGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否继承
     */
    isExtend?: boolean
    /**
     * 继承区划
     */
    extendDistrict?: string
    /**
     * 继承区划名称
     */
    extendDistrictName?: string
    /**
     * 省级区划code
     */
    parentDistrictCode?: string
    /**
     * 省级区划name
     */
    parentDistrictName?: string
    /**
     * 列表数据
     */
    announcementConfigSiteVoList?: {
      /**
       * 主键ID
       */
      id?: number
      /**
       * 公告站点主键ID
       */
      announcementSiteId?: number
      /**
       * 站点ID
       */
      siteId?: number
      /**
       * 站点环境
       */
      siteEnv?: string
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点域名
       */
      siteDomain?: string
      /**
       * 是否前台展示
       */
      isDisplay?: boolean
      /**
       * 排序
       */
      sort?: number
      /**
       * 站点管理后台链接
       */
      siteManagerUrl?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [公告站点配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423386) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listSiteDistrict`
 * @项目ID 2024
 */
type ListSiteDistrictGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listSiteDistrict',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [公告站点配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423386) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listSiteDistrict`
 * @项目ID 2024
 */
const listSiteDistrictGetRequestConfig: ListSiteDistrictGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/listSiteDistrict',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listSiteDistrictGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告站点配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423386) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listSiteDistrict`
 * @项目ID 2024
 */
export const listSiteDistrictGet = /*#__PURE__*/ (
  requestData: ListSiteDistrictGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListSiteDistrictGetResponse>(prepare(listSiteDistrictGetRequestConfig, requestData), ...args)
}

listSiteDistrictGet.requestConfig = listSiteDistrictGetRequestConfig

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423394) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @项目ID 2024
 */
export interface GetSiteDetailGetRequest {
  /**
   * 主键ID
   */
  id: string
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423394) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @项目ID 2024
 */
export interface GetSiteDetailGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键ID
     */
    id?: number
    /**
     * 站点ID
     */
    siteId?: number
    /**
     * 站点环境
     */
    siteEnv?: string
    /**
     * 站点名称
     */
    siteName?: string
    /**
     * 站点域名
     */
    siteDomain?: string
    /**
     * 是否前台展示
     */
    isDisplay?: boolean
    /**
     * 排序
     */
    sort?: number
    /**
     * 站点管理后台链接
     */
    siteManagerUrl?: string
  }
  error?: string
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423394) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @项目ID 2024
 */
type GetSiteDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getSiteDetail',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423394) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @项目ID 2024
 */
const getSiteDetailGetRequestConfig: GetSiteDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/getSiteDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getSiteDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423394) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @项目ID 2024
 */
export const getSiteDetailGet = /*#__PURE__*/ (requestData: GetSiteDetailGetRequest, ...args: UserRequestRestArgs) => {
  return request<GetSiteDetailGetResponse>(prepare(getSiteDetailGetRequestConfig, requestData), ...args)
}

getSiteDetailGet.requestConfig = getSiteDetailGetRequestConfig

/**
 * 接口 [保存公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423402) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/saveSite`
 * @项目ID 2024
 */
export interface SaveSitePostRequest {
  /**
   * 区划code
   */
  districtCode?: string
  /**
   * id
   */
  id?: number
  /**
   * 是否前台展示
   */
  isDisplay?: boolean
}

/**
 * 接口 [保存公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423402) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/saveSite`
 * @项目ID 2024
 */
export interface SaveSitePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [保存公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423402) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/saveSite`
 * @项目ID 2024
 */
type SaveSitePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveSite',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423402) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/saveSite`
 * @项目ID 2024
 */
const saveSitePostRequestConfig: SaveSitePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/saveSite',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveSitePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423402) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/saveSite`
 * @项目ID 2024
 */
export const saveSitePost = /*#__PURE__*/ (requestData: SaveSitePostRequest, ...args: UserRequestRestArgs) => {
  return request<SaveSitePostResponse>(prepare(saveSitePostRequestConfig, requestData), ...args)
}

saveSitePost.requestConfig = saveSitePostRequestConfig

/**
 * 接口 [删除公告站点关联↗](http://yapi.cai-inc.com/project/2024/interface/api/423410) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/deleteSite`
 * @项目ID 2024
 */
export interface DeleteSitePostRequest {
  /**
   * 区划code
   */
  districtCode?: string
  /**
   * id
   */
  id?: number
}

/**
 * 接口 [删除公告站点关联↗](http://yapi.cai-inc.com/project/2024/interface/api/423410) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/deleteSite`
 * @项目ID 2024
 */
export interface DeleteSitePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [删除公告站点关联↗](http://yapi.cai-inc.com/project/2024/interface/api/423410) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/deleteSite`
 * @项目ID 2024
 */
type DeleteSitePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/deleteSite',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [删除公告站点关联↗](http://yapi.cai-inc.com/project/2024/interface/api/423410) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/deleteSite`
 * @项目ID 2024
 */
const deleteSitePostRequestConfig: DeleteSitePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/deleteSite',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deleteSitePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除公告站点关联↗](http://yapi.cai-inc.com/project/2024/interface/api/423410) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/deleteSite`
 * @项目ID 2024
 */
export const deleteSitePost = /*#__PURE__*/ (requestData: DeleteSitePostRequest, ...args: UserRequestRestArgs) => {
  return request<DeleteSitePostResponse>(prepare(deleteSitePostRequestConfig, requestData), ...args)
}

deleteSitePost.requestConfig = deleteSitePostRequestConfig

/**
 * 接口 [排序公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423418) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/sortSiteDisplay`
 * @项目ID 2024
 */
export interface SortSiteDisplayPostRequest {
  /**
   * 区划code
   */
  districtCode?: string
  /**
   * 排序信息
   */
  announcementConfigSiteSortParamList?: {
    /**
     * 主键ID
     */
    id?: number
    /**
     * 排序
     */
    sort?: number
  }[]
}

/**
 * 接口 [排序公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423418) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/sortSiteDisplay`
 * @项目ID 2024
 */
export interface SortSiteDisplayPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [排序公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423418) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/sortSiteDisplay`
 * @项目ID 2024
 */
type SortSiteDisplayPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/sortSiteDisplay',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [排序公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423418) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/sortSiteDisplay`
 * @项目ID 2024
 */
const sortSiteDisplayPostRequestConfig: SortSiteDisplayPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/sortSiteDisplay',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'sortSiteDisplayPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [排序公告站点配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423418) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/sortSiteDisplay`
 * @项目ID 2024
 */
export const sortSiteDisplayPost = /*#__PURE__*/ (
  requestData: SortSiteDisplayPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SortSiteDisplayPostResponse>(prepare(sortSiteDisplayPostRequestConfig, requestData), ...args)
}

sortSiteDisplayPost.requestConfig = sortSiteDisplayPostRequestConfig

/**
 * 接口 [查询设置公告站点推送公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477146) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listAnnouncementTypesInfo`
 * @项目ID 2024
 */
export interface ListAnnouncementTypesInfoGetRequest {
  /**
   * 区划code
   */
  districtCode: string
  /**
   * 公告站点ID
   */
  announcementSiteId: string
}

/**
 * 接口 [查询设置公告站点推送公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477146) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listAnnouncementTypesInfo`
 * @项目ID 2024
 */
export interface ListAnnouncementTypesInfoGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告类型编码
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 公告类型分组
     */
    announcementTypeGroup?: string
    /**
     * 是否关联
     */
    isRelation?: boolean
  }[]
  error?: string
}

/**
 * 接口 [查询设置公告站点推送公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477146) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listAnnouncementTypesInfo`
 * @项目ID 2024
 */
type ListAnnouncementTypesInfoGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listAnnouncementTypesInfo',
    'data',
    string,
    'districtCode' | 'announcementSiteId',
    false
  >
>

/**
 * 接口 [查询设置公告站点推送公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477146) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listAnnouncementTypesInfo`
 * @项目ID 2024
 */
const listAnnouncementTypesInfoGetRequestConfig: ListAnnouncementTypesInfoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/listAnnouncementTypesInfo',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: ['districtCode', 'announcementSiteId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAnnouncementTypesInfoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询设置公告站点推送公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477146) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/listAnnouncementTypesInfo`
 * @项目ID 2024
 */
export const listAnnouncementTypesInfoGet = /*#__PURE__*/ (
  requestData: ListAnnouncementTypesInfoGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAnnouncementTypesInfoGetResponse>(
    prepare(listAnnouncementTypesInfoGetRequestConfig, requestData),
    ...args,
  )
}

listAnnouncementTypesInfoGet.requestConfig = listAnnouncementTypesInfoGetRequestConfig

/**
 * 接口 [关联公告推送的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477154) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSiteAnnouncementType`
 * @项目ID 2024
 */
export interface RelationSiteAnnouncementTypePostRequest {
  /**
   * 区划编码
   */
  districtCode: string
  /**
   * relation主键ID
   */
  relationId: number
  /**
   * 公告类型code
   */
  announcementTypes?: number[]
}

/**
 * 接口 [关联公告推送的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477154) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSiteAnnouncementType`
 * @项目ID 2024
 */
export interface RelationSiteAnnouncementTypePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [关联公告推送的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477154) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSiteAnnouncementType`
 * @项目ID 2024
 */
type RelationSiteAnnouncementTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/relationSiteAnnouncementType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [关联公告推送的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477154) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSiteAnnouncementType`
 * @项目ID 2024
 */
const relationSiteAnnouncementTypePostRequestConfig: RelationSiteAnnouncementTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/relationSiteAnnouncementType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'relationSiteAnnouncementTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [关联公告推送的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/477154) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/relationSiteAnnouncementType`
 * @项目ID 2024
 */
export const relationSiteAnnouncementTypePost = /*#__PURE__*/ (
  requestData: RelationSiteAnnouncementTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<RelationSiteAnnouncementTypePostResponse>(
    prepare(relationSiteAnnouncementTypePostRequestConfig, requestData),
    ...args,
  )
}

relationSiteAnnouncementTypePost.requestConfig = relationSiteAnnouncementTypePostRequestConfig

/**
 * 接口 [ceshi↗](http://yapi.cai-inc.com/project/2024/interface/api/479330) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/test1`
 * @项目ID 2024
 */
export interface Test1GetRequest {
  announcementId: string
}

/**
 * 接口 [ceshi↗](http://yapi.cai-inc.com/project/2024/interface/api/479330) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/test1`
 * @项目ID 2024
 */
export interface Test1GetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [ceshi↗](http://yapi.cai-inc.com/project/2024/interface/api/479330) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/test1`
 * @项目ID 2024
 */
type Test1GetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/test1',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [ceshi↗](http://yapi.cai-inc.com/project/2024/interface/api/479330) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/test1`
 * @项目ID 2024
 */
const test1GetRequestConfig: Test1GetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/test1',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'test1Get',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [ceshi↗](http://yapi.cai-inc.com/project/2024/interface/api/479330) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `GET /announcement/config/test1`
 * @项目ID 2024
 */
export const test1Get = /*#__PURE__*/ (requestData: Test1GetRequest, ...args: UserRequestRestArgs) => {
  return request<Test1GetResponse>(prepare(test1GetRequestConfig, requestData), ...args)
}

test1Get.requestConfig = test1GetRequestConfig

/**
 * 接口 [更新主站点设置↗](http://yapi.cai-inc.com/project/2024/interface/api/553354) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/updateMainSite`
 * @项目ID 2024
 */
export interface UpdateMainSitePostRequest {
  districtCode?: string
  extendDistrict?: string
  announcementSiteId?: number
}

/**
 * 接口 [更新主站点设置↗](http://yapi.cai-inc.com/project/2024/interface/api/553354) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/updateMainSite`
 * @项目ID 2024
 */
export interface UpdateMainSitePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [更新主站点设置↗](http://yapi.cai-inc.com/project/2024/interface/api/553354) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/updateMainSite`
 * @项目ID 2024
 */
type UpdateMainSitePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/updateMainSite',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [更新主站点设置↗](http://yapi.cai-inc.com/project/2024/interface/api/553354) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/updateMainSite`
 * @项目ID 2024
 */
const updateMainSitePostRequestConfig: UpdateMainSitePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_1,
  devUrl: devUrl_0_0_0_1,
  prodUrl: prodUrl_0_0_0_1,
  path: '/announcement/config/updateMainSite',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_1,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updateMainSitePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [更新主站点设置↗](http://yapi.cai-inc.com/project/2024/interface/api/553354) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51045)
 * @请求头 `POST /announcement/config/updateMainSite`
 * @项目ID 2024
 */
export const updateMainSitePost = /*#__PURE__*/ (
  requestData: UpdateMainSitePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<UpdateMainSitePostResponse>(prepare(updateMainSitePostRequestConfig, requestData), ...args)
}

updateMainSitePost.requestConfig = updateMainSitePostRequestConfig

const mockUrl_0_0_0_3 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_3 = '' as any
const prodUrl_0_0_0_3 = '' as any
const dataKey_0_0_0_3 = 'data' as any

/**
 * 接口 [获取已开通的区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/424050) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getDistTree`
 * @项目ID 2024
 */
export interface GetDistTreeGetRequest {
  /**
   * 区划
   */
  distCode?: string
}

/**
 * 接口 [获取已开通的区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/424050) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getDistTree`
 * @项目ID 2024
 */
export interface GetDistTreeGetResponse {
  success?: boolean
  result?: {
    /**
     * 区划ID
     */
    id?: string
    /**
     * 父区划ID
     */
    pid?: string
    /**
     * 区划编码
     */
    code?: string
    /**
     * ？？？
     */
    text?: string
    /**
     * 区划的旧类型划分方式
     *
     * 「已废弃」replaced by districtType
     * 「已废弃」
     */
    type?: string
    /**
     * 新区划类型
     */
    districtType?: string
    /**
     * 是否为叶子区划
     */
    isLeaf?: boolean
    editPrivilege?: boolean
    addPrivilege?: boolean
    children?: {
      /**
       * 区划ID
       */
      id?: string
      /**
       * 父区划ID
       */
      pid?: string
      /**
       * 区划编码
       */
      code?: string
      /**
       * ？？？
       */
      text?: string
      /**
       * 区划的旧类型划分方式
       *
       * 「已废弃」replaced by districtType
       * 「已废弃」
       */
      type?: string
      /**
       * 新区划类型
       */
      districtType?: string
      /**
       * 是否为叶子区划
       */
      isLeaf?: boolean
      editPrivilege?: boolean
      addPrivilege?: boolean
      children?: {}[]
      /**
       * 区划名称
       */
      name?: string
      /**
       * 区划全称
       */
      fullName?: string
      /**
       * 区划简称
       */
      nameBone?: string
      /**
       * 状态
       */
      status?: string
    }[]
    /**
     * 区划名称
     */
    name?: string
    /**
     * 区划全称
     */
    fullName?: string
    /**
     * 区划简称
     */
    nameBone?: string
    /**
     * 状态
     */
    status?: string
  }[]
  error?: string
}

/**
 * 接口 [获取已开通的区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/424050) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getDistTree`
 * @项目ID 2024
 */
type GetDistTreeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getDistTree',
    'data',
    string,
    'distCode',
    false
  >
>

/**
 * 接口 [获取已开通的区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/424050) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getDistTree`
 * @项目ID 2024
 */
const getDistTreeGetRequestConfig: GetDistTreeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getDistTree',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getDistTreeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取已开通的区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/424050) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getDistTree`
 * @项目ID 2024
 */
export const getDistTreeGet = /*#__PURE__*/ (requestData: GetDistTreeGetRequest, ...args: UserRequestRestArgs) => {
  return request<GetDistTreeGetResponse>(prepare(getDistTreeGetRequestConfig, requestData), ...args)
}

getDistTreeGet.requestConfig = getDistTreeGetRequestConfig

/**
 * 接口 [公告关联配置-获取全部公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424058) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAllAnnTypeList`
 * @项目ID 2024
 */
export interface GetAllAnnTypeListGetRequest {}

/**
 * 接口 [公告关联配置-获取全部公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424058) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAllAnnTypeList`
 * @项目ID 2024
 */
export interface GetAllAnnTypeListGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告类型名称 如 单一来源公示
     */
    label?: string
    /**
     * 公告类型id，如 3012
     */
    value?: string
    /**
     * 公告类型id，如 3012
     */
    key?: string
    /**
     * 公告大类型下的子类型，如"采购项目公告"大类别下的 公开招标公告，询价公告等等
     */
    children?: {
      /**
       * 公告类型名称 如 单一来源公示
       */
      label?: string
      /**
       * 公告类型id，如 3012
       */
      value?: string
      /**
       * 公告类型id，如 3012
       */
      key?: string
      /**
       * 公告大类型下的子类型，如"采购项目公告"大类别下的 公开招标公告，询价公告等等
       */
      children?: {}[]
    }[]
  }[]
  error?: string
}

/**
 * 接口 [公告关联配置-获取全部公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424058) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAllAnnTypeList`
 * @项目ID 2024
 */
type GetAllAnnTypeListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAllAnnTypeList',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [公告关联配置-获取全部公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424058) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAllAnnTypeList`
 * @项目ID 2024
 */
const getAllAnnTypeListGetRequestConfig: GetAllAnnTypeListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAllAnnTypeList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAllAnnTypeListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告关联配置-获取全部公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424058) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAllAnnTypeList`
 * @项目ID 2024
 */
export const getAllAnnTypeListGet = /*#__PURE__*/ (
  requestData?: GetAllAnnTypeListGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAllAnnTypeListGetResponse>(prepare(getAllAnnTypeListGetRequestConfig, requestData), ...args)
}

getAllAnnTypeListGet.requestConfig = getAllAnnTypeListGetRequestConfig

/**
 * 接口 [公告关联配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424066) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRelationConfig`
 * @项目ID 2024
 */
export interface SaveRelationConfigPostRequest {
  /**
   * 区划
   */
  distCode: string
  /**
   * 选择的公告类型，多个逗号分隔
   */
  annTypes?: string
}

/**
 * 接口 [公告关联配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424066) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRelationConfig`
 * @项目ID 2024
 */
export interface SaveRelationConfigPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [公告关联配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424066) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRelationConfig`
 * @项目ID 2024
 */
type SaveRelationConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveRelationConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告关联配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424066) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRelationConfig`
 * @项目ID 2024
 */
const saveRelationConfigPostRequestConfig: SaveRelationConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/saveRelationConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveRelationConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告关联配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424066) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRelationConfig`
 * @项目ID 2024
 */
export const saveRelationConfigPost = /*#__PURE__*/ (
  requestData: SaveRelationConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveRelationConfigPostResponse>(prepare(saveRelationConfigPostRequestConfig, requestData), ...args)
}

saveRelationConfigPost.requestConfig = saveRelationConfigPostRequestConfig

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424074) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/listRelatedAnnTypes`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypesGetRequest {
  /**
   * 区划编码
   */
  distCode?: string
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424074) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/listRelatedAnnTypes`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypesGetResponse {
  success?: boolean
  result?: {
    /**
     * 父公告类型id
     */
    typeId?: number
    /**
     * 父公告类型名称
     */
    name?: string
    /**
     * 子公告列表
     */
    children?: {
      /**
       * 自增主键id
       */
      id?: number
      /**
       * 公告类型名称
       */
      name?: string
      /**
       * 区划编码
       */
      distCode?: string
      /**
       * 公告类型id
       */
      typeId?: number
      /**
       * 当前类型父类型id， 0表示顶层节点，无父类型
       */
      parentId?: number
      /**
       * 公告有效期,单位天
       */
      expiryPeriod?: number
      /**
       * 系统类别：1-政府采购；2-企业购
       */
      systemType?: number
      /**
       * 是否开放到外网：0-不开放； 1-开放
       */
      isOut?: number
      /**
       * 如果is_out=0，为不开放原因
       */
      unOpenReason?: string
      /**
       * 当前数据是否有效：0-无效； 1-有效
       */
      isValid?: boolean
      /**
       * 是否强控，若强控，创建公告时，强制关联采购方式
       */
      isForcedControl?: number
      /**
       * 是否公告发布后撤回
       */
      canRevoke?: number
      /**
       * 可撤回时间，单位秒
       */
      revokeTime?: number
      /**
       * 附件是否必填
       */
      hasAttachment?: number
      /**
       * 采购方式代码(内外网同步映射代码)
       */
      procurementMethodCode?: string
      /**
       * 采购方式名称
       */
      procurementMethodName?: string
      /**
       * 是否可以发起异议，0：不可异议，1：可异议
       */
      canObject?: boolean
      /**
       * 被质疑对象信息字段，多个逗号分隔
       */
      objectionKeys?: string
      /**
       * 质疑对象信息字段，多个逗号分隔
       */
      beObjectionKeys?: string
      /**
       * 需要校验代理机构类型，多个逗号分隔
       */
      checkAgencyTypes?: string
      /**
       * 是否允许二次编辑，0：不可编辑， 1：可编辑
       */
      secondEdit?: boolean
      /**
       * 是否自定义标题
       */
      customizedTitle?: boolean
      /**
       * 标题前缀
       */
      titlePrefix?: string
      /**
       * 标题后缀
       */
      titleSuffix?: string
      /**
       * 短信推送   如果为true，则列表页会有发送短信按钮
       */
      isNotify?: boolean
      /**
       * 是否接入表单
       */
      isFormPage?: boolean
      /**
       * 表单code
       */
      formPageCode?: string
      /**
       * 是否开启敏感词校验
       */
      canContentCensor?: number
      /**
       * 是否展示附件
       */
      attachmentShow?: number
      /**
       * 附件对外展示规则
       * {@link AttachmentExternalDisplayRulesEnums}
       */
      attachmentExternalDisplayRules?: number
      /**
       * 附件上传提示文案
       */
      attachmentUploadDesc?: string
      /**
       * 是否有水印
       */
      isWater?: boolean
      /**
       * 水印内容  长度20
       */
      waterContent?: string
      /**
       * 页眉内容  长度 100
       */
      waterHeaderContent?: string
      /**
       * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
       */
      enableSummaryCalculation?: boolean
      /**
       * 期限类型 0-工作日，1-自然日
       */
      termType?: number
      /**
       * 是否可取消公告 0-否，1-是
       */
      isRevocable?: number
      /**
       * 取消按钮名称
       */
      revocableButtonName?: string
      /**
       * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
       * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
       */
      publishTypes?: string
      /**
       * 白名单区划-查看
       */
      whiteListDistrictList?: {
        code?: string
        codeDesc?: string
      }[]
      /**
       * 白名单区划-保存
       */
      whiteListDistrictCode?: string[]
    }[]
  }[]
  error?: string
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424074) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/listRelatedAnnTypes`
 * @项目ID 2024
 */
type ListRelatedAnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listRelatedAnnTypes',
    'data',
    string,
    'distCode',
    false
  >
>

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424074) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/listRelatedAnnTypes`
 * @项目ID 2024
 */
const listRelatedAnnTypesGetRequestConfig: ListRelatedAnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listRelatedAnnTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listRelatedAnnTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424074) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/listRelatedAnnTypes`
 * @项目ID 2024
 */
export const listRelatedAnnTypesGet = /*#__PURE__*/ (
  requestData: ListRelatedAnnTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListRelatedAnnTypesGetResponse>(prepare(listRelatedAnnTypesGetRequestConfig, requestData), ...args)
}

listRelatedAnnTypesGet.requestConfig = listRelatedAnnTypesGetRequestConfig

/**
 * 接口 [按区划获取已关联类型id列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424082) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listRelatedAnnTypeIds`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypeIdsGetRequest {
  distCode?: string
}

/**
 * 接口 [按区划获取已关联类型id列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424082) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listRelatedAnnTypeIds`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypeIdsGetResponse {
  success?: boolean
  result?: number[]
  error?: string
}

/**
 * 接口 [按区划获取已关联类型id列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424082) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listRelatedAnnTypeIds`
 * @项目ID 2024
 */
type ListRelatedAnnTypeIdsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listRelatedAnnTypeIds',
    'data',
    string,
    'distCode',
    false
  >
>

/**
 * 接口 [按区划获取已关联类型id列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424082) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listRelatedAnnTypeIds`
 * @项目ID 2024
 */
const listRelatedAnnTypeIdsGetRequestConfig: ListRelatedAnnTypeIdsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listRelatedAnnTypeIds',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listRelatedAnnTypeIdsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [按区划获取已关联类型id列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424082) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listRelatedAnnTypeIds`
 * @项目ID 2024
 */
export const listRelatedAnnTypeIdsGet = /*#__PURE__*/ (
  requestData: ListRelatedAnnTypeIdsGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListRelatedAnnTypeIdsGetResponse>(prepare(listRelatedAnnTypeIdsGetRequestConfig, requestData), ...args)
}

listRelatedAnnTypeIdsGet.requestConfig = listRelatedAnnTypeIdsGetRequestConfig

/**
 * 接口 [公告关联配置-同步关联配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424090) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncRelationConfig`
 * @项目ID 2024
 */
export interface SyncRelationConfigPostRequest {
  /**
   * 待同步来源区划
   */
  sourceDist: string
  /**
   * 待同步目标区划
   */
  targetDist: string[]
  /**
   * 需要同步的公告类型
   */
  typeIdList?: number[]
}

/**
 * 接口 [公告关联配置-同步关联配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424090) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncRelationConfig`
 * @项目ID 2024
 */
export interface SyncRelationConfigPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [公告关联配置-同步关联配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424090) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncRelationConfig`
 * @项目ID 2024
 */
type SyncRelationConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/syncRelationConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告关联配置-同步关联配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424090) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncRelationConfig`
 * @项目ID 2024
 */
const syncRelationConfigPostRequestConfig: SyncRelationConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/syncRelationConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncRelationConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告关联配置-同步关联配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424090) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncRelationConfig`
 * @项目ID 2024
 */
export const syncRelationConfigPost = /*#__PURE__*/ (
  requestData: SyncRelationConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SyncRelationConfigPostResponse>(prepare(syncRelationConfigPostRequestConfig, requestData), ...args)
}

syncRelationConfigPost.requestConfig = syncRelationConfigPostRequestConfig

/**
 * 接口 [公告开放配置-获取设置↗](http://yapi.cai-inc.com/project/2024/interface/api/424098) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeConfig`
 * @项目ID 2024
 */
export interface GetAnnTypeConfigGetRequest {
  /**
   * 主键id
   */
  id?: string
  typeId?: string
}

/**
 * 接口 [公告开放配置-获取设置↗](http://yapi.cai-inc.com/project/2024/interface/api/424098) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeConfig`
 * @项目ID 2024
 */
export interface GetAnnTypeConfigGetResponse {
  success?: boolean
  result?: {
    /**
     * 自增主键id
     */
    id?: number
    /**
     * 公告类型名称
     */
    name?: string
    /**
     * 区划编码
     */
    distCode?: string
    /**
     * 公告类型id
     */
    typeId?: number
    /**
     * 当前类型父类型id， 0表示顶层节点，无父类型
     */
    parentId?: number
    /**
     * 公告有效期,单位天
     */
    expiryPeriod?: number
    /**
     * 系统类别：1-政府采购；2-企业购
     */
    systemType?: number
    /**
     * 是否开放到外网：0-不开放； 1-开放
     */
    isOut?: number
    /**
     * 如果is_out=0，为不开放原因
     */
    unOpenReason?: string
    /**
     * 当前数据是否有效：0-无效； 1-有效
     */
    isValid?: boolean
    /**
     * 是否强控，若强控，创建公告时，强制关联采购方式
     */
    isForcedControl?: number
    /**
     * 是否公告发布后撤回
     */
    canRevoke?: number
    /**
     * 可撤回时间，单位秒
     */
    revokeTime?: number
    /**
     * 附件是否必填
     */
    hasAttachment?: number
    /**
     * 采购方式代码(内外网同步映射代码)
     */
    procurementMethodCode?: string
    /**
     * 采购方式名称
     */
    procurementMethodName?: string
    /**
     * 是否可以发起异议，0：不可异议，1：可异议
     */
    canObject?: boolean
    /**
     * 被质疑对象信息字段，多个逗号分隔
     */
    objectionKeys?: string
    /**
     * 质疑对象信息字段，多个逗号分隔
     */
    beObjectionKeys?: string
    /**
     * 需要校验代理机构类型，多个逗号分隔
     */
    checkAgencyTypes?: string
    /**
     * 是否允许二次编辑，0：不可编辑， 1：可编辑
     */
    secondEdit?: boolean
    /**
     * 是否自定义标题
     */
    customizedTitle?: boolean
    /**
     * 标题前缀
     */
    titlePrefix?: string
    /**
     * 标题后缀
     */
    titleSuffix?: string
    /**
     * 短信推送   如果为true，则列表页会有发送短信按钮
     */
    isNotify?: boolean
    /**
     * 是否接入表单
     */
    isFormPage?: boolean
    /**
     * 表单code
     */
    formPageCode?: string
    /**
     * 是否开启敏感词校验
     */
    canContentCensor?: number
    /**
     * 是否展示附件
     */
    attachmentShow?: number
    /**
     * 附件对外展示规则
     * {@link AttachmentExternalDisplayRulesEnums}
     */
    attachmentExternalDisplayRules?: number
    /**
     * 附件上传提示文案
     */
    attachmentUploadDesc?: string
    /**
     * 是否有水印
     */
    isWater?: boolean
    /**
     * 水印内容  长度20
     */
    waterContent?: string
    /**
     * 页眉内容  长度 100
     */
    waterHeaderContent?: string
    /**
     * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
     */
    enableSummaryCalculation?: boolean
    /**
     * 期限类型 0-工作日，1-自然日
     */
    termType?: number
    /**
     * 是否可取消公告 0-否，1-是
     */
    isRevocable?: number
    /**
     * 取消按钮名称
     */
    revocableButtonName?: string
    /**
     * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
     * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
     */
    publishTypes?: string
    /**
     * 白名单区划-查看
     */
    whiteListDistrictList?: {
      code?: string
      codeDesc?: string
    }[]
    /**
     * 白名单区划-保存
     */
    whiteListDistrictCode?: string[]
  }
  error?: string
}

/**
 * 接口 [公告开放配置-获取设置↗](http://yapi.cai-inc.com/project/2024/interface/api/424098) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeConfig`
 * @项目ID 2024
 */
type GetAnnTypeConfigGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnTypeConfig',
    'data',
    string,
    'id' | 'typeId',
    false
  >
>

/**
 * 接口 [公告开放配置-获取设置↗](http://yapi.cai-inc.com/project/2024/interface/api/424098) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeConfig`
 * @项目ID 2024
 */
const getAnnTypeConfigGetRequestConfig: GetAnnTypeConfigGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnTypeConfig',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['id', 'typeId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnTypeConfigGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告开放配置-获取设置↗](http://yapi.cai-inc.com/project/2024/interface/api/424098) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeConfig`
 * @项目ID 2024
 */
export const getAnnTypeConfigGet = /*#__PURE__*/ (
  requestData: GetAnnTypeConfigGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnTypeConfigGetResponse>(prepare(getAnnTypeConfigGetRequestConfig, requestData), ...args)
}

getAnnTypeConfigGet.requestConfig = getAnnTypeConfigGetRequestConfig

/**
 * 接口 [公告开放配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424106) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnTypeConfig`
 * @项目ID 2024
 */
export interface SaveAnnTypeConfigPostRequest {
  /**
   * 自增主键id
   */
  id?: number
  /**
   * 公告类型名称
   */
  name?: string
  /**
   * 区划编码
   */
  distCode?: string
  /**
   * 公告类型id
   */
  typeId?: number
  /**
   * 当前类型父类型id， 0表示顶层节点，无父类型
   */
  parentId?: number
  /**
   * 公告有效期,单位天
   */
  expiryPeriod?: number
  /**
   * 系统类别：1-政府采购；2-企业购
   */
  systemType?: number
  /**
   * 是否开放到外网：0-不开放； 1-开放
   */
  isOut?: number
  /**
   * 如果is_out=0，为不开放原因
   */
  unOpenReason?: string
  /**
   * 当前数据是否有效：0-无效； 1-有效
   */
  isValid?: boolean
  /**
   * 是否强控，若强控，创建公告时，强制关联采购方式
   */
  isForcedControl?: number
  /**
   * 是否公告发布后撤回
   */
  canRevoke?: number
  /**
   * 可撤回时间，单位秒
   */
  revokeTime?: number
  /**
   * 附件是否必填
   */
  hasAttachment?: number
  /**
   * 采购方式代码(内外网同步映射代码)
   */
  procurementMethodCode?: string
  /**
   * 采购方式名称
   */
  procurementMethodName?: string
  /**
   * 是否可以发起异议，0：不可异议，1：可异议
   */
  canObject?: boolean
  /**
   * 被质疑对象信息字段，多个逗号分隔
   */
  objectionKeys?: string
  /**
   * 质疑对象信息字段，多个逗号分隔
   */
  beObjectionKeys?: string
  /**
   * 需要校验代理机构类型，多个逗号分隔
   */
  checkAgencyTypes?: string
  /**
   * 是否允许二次编辑，0：不可编辑， 1：可编辑
   */
  secondEdit?: boolean
  /**
   * 是否自定义标题
   */
  customizedTitle?: boolean
  /**
   * 标题前缀
   */
  titlePrefix?: string
  /**
   * 标题后缀
   */
  titleSuffix?: string
  /**
   * 短信推送   如果为true，则列表页会有发送短信按钮
   */
  isNotify?: boolean
  /**
   * 是否接入表单
   */
  isFormPage?: boolean
  /**
   * 表单code
   */
  formPageCode?: string
  /**
   * 是否开启敏感词校验
   */
  canContentCensor?: number
  /**
   * 是否展示附件
   */
  attachmentShow?: number
  /**
   * 附件对外展示规则
   * {@link AttachmentExternalDisplayRulesEnums}
   */
  attachmentExternalDisplayRules?: number
  /**
   * 附件上传提示文案
   */
  attachmentUploadDesc?: string
  /**
   * 是否有水印
   */
  isWater?: boolean
  /**
   * 水印内容  长度20
   */
  waterContent?: string
  /**
   * 页眉内容  长度 100
   */
  waterHeaderContent?: string
  /**
   * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
   */
  enableSummaryCalculation?: boolean
  /**
   * 期限类型 0-工作日，1-自然日
   */
  termType?: number
  /**
   * 是否可取消公告 0-否，1-是
   */
  isRevocable?: number
  /**
   * 取消按钮名称
   */
  revocableButtonName?: string
  /**
   * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
   * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
   */
  publishTypes?: string
  /**
   * 白名单区划-查看
   */
  whiteListDistrictList?: {
    code?: string
    codeDesc?: string
  }[]
  /**
   * 白名单区划-保存
   */
  whiteListDistrictCode?: string[]
}

/**
 * 接口 [公告开放配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424106) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnTypeConfig`
 * @项目ID 2024
 */
export interface SaveAnnTypeConfigPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [公告开放配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424106) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnTypeConfig`
 * @项目ID 2024
 */
type SaveAnnTypeConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveAnnTypeConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告开放配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424106) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnTypeConfig`
 * @项目ID 2024
 */
const saveAnnTypeConfigPostRequestConfig: SaveAnnTypeConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/saveAnnTypeConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnTypeConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告开放配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424106) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnTypeConfig`
 * @项目ID 2024
 */
export const saveAnnTypeConfigPost = /*#__PURE__*/ (
  requestData: SaveAnnTypeConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnTypeConfigPostResponse>(prepare(saveAnnTypeConfigPostRequestConfig, requestData), ...args)
}

saveAnnTypeConfigPost.requestConfig = saveAnnTypeConfigPostRequestConfig

/**
 * 接口 [公告开放配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424114) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnTypeConfig`
 * @项目ID 2024
 */
export interface SyncAnnTypeConfigPostRequest {
  /**
   * 同步来源区划
   */
  sourceDist: string
  /**
   * 同步目标区划
   */
  targetDist: string[]
  /**
   * 待同步公告大类型
   */
  parentId?: number
  /**
   * 待同步公告类型列表
   */
  typeIdList: number[]
}

/**
 * 接口 [公告开放配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424114) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnTypeConfig`
 * @项目ID 2024
 */
export interface SyncAnnTypeConfigPostResponse {
  success?: boolean
  result?: number
  error?: string
}

/**
 * 接口 [公告开放配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424114) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnTypeConfig`
 * @项目ID 2024
 */
type SyncAnnTypeConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/syncAnnTypeConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告开放配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424114) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnTypeConfig`
 * @项目ID 2024
 */
const syncAnnTypeConfigPostRequestConfig: SyncAnnTypeConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/syncAnnTypeConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncAnnTypeConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告开放配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424114) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnTypeConfig`
 * @项目ID 2024
 */
export const syncAnnTypeConfigPost = /*#__PURE__*/ (
  requestData: SyncAnnTypeConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SyncAnnTypeConfigPostResponse>(prepare(syncAnnTypeConfigPostRequestConfig, requestData), ...args)
}

syncAnnTypeConfigPost.requestConfig = syncAnnTypeConfigPostRequestConfig

/**
 * 接口 [获取公告模版预览↗](http://yapi.cai-inc.com/project/2024/interface/api/424122) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/templatePreview`
 * @项目ID 2024
 */
export interface TemplatePreviewGetRequest {
  typeId: string
  distCode: string
}

/**
 * 接口 [获取公告模版预览↗](http://yapi.cai-inc.com/project/2024/interface/api/424122) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/templatePreview`
 * @项目ID 2024
 */
export interface TemplatePreviewGetResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [获取公告模版预览↗](http://yapi.cai-inc.com/project/2024/interface/api/424122) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/templatePreview`
 * @项目ID 2024
 */
type TemplatePreviewGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/templatePreview',
    'data',
    string,
    'typeId' | 'distCode',
    false
  >
>

/**
 * 接口 [获取公告模版预览↗](http://yapi.cai-inc.com/project/2024/interface/api/424122) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/templatePreview`
 * @项目ID 2024
 */
const templatePreviewGetRequestConfig: TemplatePreviewGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/templatePreview',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['typeId', 'distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'templatePreviewGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告模版预览↗](http://yapi.cai-inc.com/project/2024/interface/api/424122) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/templatePreview`
 * @项目ID 2024
 */
export const templatePreviewGet = /*#__PURE__*/ (
  requestData: TemplatePreviewGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<TemplatePreviewGetResponse>(prepare(templatePreviewGetRequestConfig, requestData), ...args)
}

templatePreviewGet.requestConfig = templatePreviewGetRequestConfig

/**
 * 接口 [公告发布配置-保存发布配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424130) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnOriginator`
 * @项目ID 2024
 */
export interface SaveAnnOriginatorPostRequest {
  id?: number
  /**
   * 用户区划，从登陆账户获取
   */
  distCode?: string
  /**
   * 发起人类型，1-采购单位，2-采购代理机构，3-采购监管
   */
  originatorType: string
  /**
   * 发起人编码，由用户区划与发起人类型拼接，格式如distCode_originator_1
   */
  originatorCode?: string
  /**
   * 已选择的公告类型
   */
  announcementTypes?: string
}

/**
 * 接口 [公告发布配置-保存发布配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424130) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnOriginator`
 * @项目ID 2024
 */
export interface SaveAnnOriginatorPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [公告发布配置-保存发布配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424130) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnOriginator`
 * @项目ID 2024
 */
type SaveAnnOriginatorPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveAnnOriginator',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告发布配置-保存发布配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424130) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnOriginator`
 * @项目ID 2024
 */
const saveAnnOriginatorPostRequestConfig: SaveAnnOriginatorPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/saveAnnOriginator',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnOriginatorPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告发布配置-保存发布配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424130) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveAnnOriginator`
 * @项目ID 2024
 */
export const saveAnnOriginatorPost = /*#__PURE__*/ (
  requestData: SaveAnnOriginatorPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnOriginatorPostResponse>(prepare(saveAnnOriginatorPostRequestConfig, requestData), ...args)
}

saveAnnOriginatorPost.requestConfig = saveAnnOriginatorPostRequestConfig

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424138) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/getSelectedAnnTypes`
 * @项目ID 2024
 */
export interface GetSelectedAnnTypesGetRequest {
  /**
   * 区划
   */
  distCode?: string
  /**
   * 用户类型
   */
  userType: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424138) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/getSelectedAnnTypes`
 * @项目ID 2024
 */
export interface GetSelectedAnnTypesGetResponse {
  success?: boolean
  result?: {
    /**
     * 父公告类型id
     */
    typeId?: number
    /**
     * 父公告类型名称
     */
    name?: string
    /**
     * 子公告列表
     */
    children?: {
      /**
       * 自增主键id
       */
      id?: number
      /**
       * 公告类型名称
       */
      name?: string
      /**
       * 区划编码
       */
      distCode?: string
      /**
       * 公告类型id
       */
      typeId?: number
      /**
       * 当前类型父类型id， 0表示顶层节点，无父类型
       */
      parentId?: number
      /**
       * 公告有效期,单位天
       */
      expiryPeriod?: number
      /**
       * 系统类别：1-政府采购；2-企业购
       */
      systemType?: number
      /**
       * 是否开放到外网：0-不开放； 1-开放
       */
      isOut?: number
      /**
       * 如果is_out=0，为不开放原因
       */
      unOpenReason?: string
      /**
       * 当前数据是否有效：0-无效； 1-有效
       */
      isValid?: boolean
      /**
       * 是否强控，若强控，创建公告时，强制关联采购方式
       */
      isForcedControl?: number
      /**
       * 是否公告发布后撤回
       */
      canRevoke?: number
      /**
       * 可撤回时间，单位秒
       */
      revokeTime?: number
      /**
       * 附件是否必填
       */
      hasAttachment?: number
      /**
       * 采购方式代码(内外网同步映射代码)
       */
      procurementMethodCode?: string
      /**
       * 采购方式名称
       */
      procurementMethodName?: string
      /**
       * 是否可以发起异议，0：不可异议，1：可异议
       */
      canObject?: boolean
      /**
       * 被质疑对象信息字段，多个逗号分隔
       */
      objectionKeys?: string
      /**
       * 质疑对象信息字段，多个逗号分隔
       */
      beObjectionKeys?: string
      /**
       * 需要校验代理机构类型，多个逗号分隔
       */
      checkAgencyTypes?: string
      /**
       * 是否允许二次编辑，0：不可编辑， 1：可编辑
       */
      secondEdit?: boolean
      /**
       * 是否自定义标题
       */
      customizedTitle?: boolean
      /**
       * 标题前缀
       */
      titlePrefix?: string
      /**
       * 标题后缀
       */
      titleSuffix?: string
      /**
       * 短信推送   如果为true，则列表页会有发送短信按钮
       */
      isNotify?: boolean
      /**
       * 是否接入表单
       */
      isFormPage?: boolean
      /**
       * 表单code
       */
      formPageCode?: string
      /**
       * 是否开启敏感词校验
       */
      canContentCensor?: number
      /**
       * 是否展示附件
       */
      attachmentShow?: number
      /**
       * 附件对外展示规则
       * {@link AttachmentExternalDisplayRulesEnums}
       */
      attachmentExternalDisplayRules?: number
      /**
       * 附件上传提示文案
       */
      attachmentUploadDesc?: string
      /**
       * 是否有水印
       */
      isWater?: boolean
      /**
       * 水印内容  长度20
       */
      waterContent?: string
      /**
       * 页眉内容  长度 100
       */
      waterHeaderContent?: string
      /**
       * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
       */
      enableSummaryCalculation?: boolean
      /**
       * 期限类型 0-工作日，1-自然日
       */
      termType?: number
      /**
       * 是否可取消公告 0-否，1-是
       */
      isRevocable?: number
      /**
       * 取消按钮名称
       */
      revocableButtonName?: string
      /**
       * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
       * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
       */
      publishTypes?: string
      /**
       * 白名单区划-查看
       */
      whiteListDistrictList?: {
        code?: string
        codeDesc?: string
      }[]
      /**
       * 白名单区划-保存
       */
      whiteListDistrictCode?: string[]
    }[]
  }[]
  error?: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424138) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/getSelectedAnnTypes`
 * @项目ID 2024
 */
type GetSelectedAnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getSelectedAnnTypes',
    'data',
    string,
    'distCode' | 'userType',
    false
  >
>

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424138) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/getSelectedAnnTypes`
 * @项目ID 2024
 */
const getSelectedAnnTypesGetRequestConfig: GetSelectedAnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getSelectedAnnTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode', 'userType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getSelectedAnnTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424138) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @标签 `deprecated`
 * @请求头 `GET /announcement/config/getSelectedAnnTypes`
 * @项目ID 2024
 */
export const getSelectedAnnTypesGet = /*#__PURE__*/ (
  requestData: GetSelectedAnnTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetSelectedAnnTypesGetResponse>(prepare(getSelectedAnnTypesGetRequestConfig, requestData), ...args)
}

getSelectedAnnTypesGet.requestConfig = getSelectedAnnTypesGetRequestConfig

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端勾选数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424146) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnOriginator`
 * @项目ID 2024
 */
export interface GetAnnOriginatorGetRequest {
  /**
   * 区划
   */
  distCode?: string
  /**
   * 用户类型
   */
  userType: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端勾选数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424146) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnOriginator`
 * @项目ID 2024
 */
export interface GetAnnOriginatorGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 用户区划，从登陆账户获取
     */
    distCode?: string
    /**
     * 发起人类型，1-采购单位，2-采购代理机构，3-采购监管
     */
    originatorType: string
    /**
     * 发起人编码，由用户区划与发起人类型拼接，格式如distCode_originator_1
     */
    originatorCode?: string
    /**
     * 已选择的公告类型
     */
    announcementTypes?: string
  }
  error?: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端勾选数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424146) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnOriginator`
 * @项目ID 2024
 */
type GetAnnOriginatorGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnOriginator',
    'data',
    string,
    'distCode' | 'userType',
    false
  >
>

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端勾选数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424146) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnOriginator`
 * @项目ID 2024
 */
const getAnnOriginatorGetRequestConfig: GetAnnOriginatorGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnOriginator',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode', 'userType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnOriginatorGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端勾选数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/424146) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnOriginator`
 * @项目ID 2024
 */
export const getAnnOriginatorGet = /*#__PURE__*/ (
  requestData: GetAnnOriginatorGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnOriginatorGetResponse>(prepare(getAnnOriginatorGetRequestConfig, requestData), ...args)
}

getAnnOriginatorGet.requestConfig = getAnnOriginatorGetRequestConfig

/**
 * 接口 [公告发布配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424154) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnOriginatorConfig`
 * @项目ID 2024
 */
export interface SyncAnnOriginatorConfigPostRequest {
  /**
   * 待同步来源区划
   */
  sourceDist: string
  /**
   * 待同步用户类别
   */
  userTypeList: string[]
  /**
   * 待同步目标区划
   */
  targetDist: string[]
}

/**
 * 接口 [公告发布配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424154) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnOriginatorConfig`
 * @项目ID 2024
 */
export interface SyncAnnOriginatorConfigPostResponse {
  success?: boolean
  result?: number
  error?: string
}

/**
 * 接口 [公告发布配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424154) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnOriginatorConfig`
 * @项目ID 2024
 */
type SyncAnnOriginatorConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/syncAnnOriginatorConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告发布配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424154) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnOriginatorConfig`
 * @项目ID 2024
 */
const syncAnnOriginatorConfigPostRequestConfig: SyncAnnOriginatorConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/syncAnnOriginatorConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncAnnOriginatorConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告发布配置-同步配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424154) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/syncAnnOriginatorConfig`
 * @项目ID 2024
 */
export const syncAnnOriginatorConfigPost = /*#__PURE__*/ (
  requestData: SyncAnnOriginatorConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SyncAnnOriginatorConfigPostResponse>(
    prepare(syncAnnOriginatorConfigPostRequestConfig, requestData),
    ...args,
  )
}

syncAnnOriginatorConfigPost.requestConfig = syncAnnOriginatorConfigPostRequestConfig

/**
 * 接口 [监管参数配置-获取配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424162) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getRegularList`
 * @项目ID 2024
 */
export interface GetRegularListGetRequest {
  /**
   * 区划
   */
  distCode?: string
}

/**
 * 接口 [监管参数配置-获取配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424162) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getRegularList`
 * @项目ID 2024
 */
export interface GetRegularListGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 行政区划编码
     */
    districtCode?: string
    /**
     * 公告类型type
     */
    announcementTypes: string
    /**
     * 组名
     */
    groupName: string
    /**
     * 系统参数
     */
    envs: {
      key?: {}
    }
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
  }[]
  error?: string
}

/**
 * 接口 [监管参数配置-获取配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424162) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getRegularList`
 * @项目ID 2024
 */
type GetRegularListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getRegularList',
    'data',
    string,
    'distCode',
    false
  >
>

/**
 * 接口 [监管参数配置-获取配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424162) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getRegularList`
 * @项目ID 2024
 */
const getRegularListGetRequestConfig: GetRegularListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getRegularList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getRegularListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [监管参数配置-获取配置列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424162) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getRegularList`
 * @项目ID 2024
 */
export const getRegularListGet = /*#__PURE__*/ (
  requestData: GetRegularListGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetRegularListGetResponse>(prepare(getRegularListGetRequestConfig, requestData), ...args)
}

getRegularListGet.requestConfig = getRegularListGetRequestConfig

/**
 * 接口 [监管参数配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424170) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRegularConfig`
 * @项目ID 2024
 */
export interface SaveRegularConfigPostRequest {
  id?: number
  /**
   * 行政区划编码
   */
  districtCode?: string
  /**
   * 公告类型type
   */
  announcementTypes: string
  /**
   * 组名
   */
  groupName: string
  /**
   * 系统参数
   */
  envs: {
    key?: {}
  }
  /**
   * 公告类型名称
   */
  announcementTypeName?: string
}

/**
 * 接口 [监管参数配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424170) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRegularConfig`
 * @项目ID 2024
 */
export interface SaveRegularConfigPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [监管参数配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424170) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRegularConfig`
 * @项目ID 2024
 */
type SaveRegularConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveRegularConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [监管参数配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424170) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRegularConfig`
 * @项目ID 2024
 */
const saveRegularConfigPostRequestConfig: SaveRegularConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/saveRegularConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveRegularConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [监管参数配置-保存配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424170) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveRegularConfig`
 * @项目ID 2024
 */
export const saveRegularConfigPost = /*#__PURE__*/ (
  requestData: SaveRegularConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveRegularConfigPostResponse>(prepare(saveRegularConfigPostRequestConfig, requestData), ...args)
}

saveRegularConfigPost.requestConfig = saveRegularConfigPostRequestConfig

/**
 * 接口 [监管参数配置-删除配置组↗](http://yapi.cai-inc.com/project/2024/interface/api/424178) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/deleteRegular`
 * @项目ID 2024
 */
export type DeleteRegularPostRequest = number

/**
 * 接口 [监管参数配置-删除配置组↗](http://yapi.cai-inc.com/project/2024/interface/api/424178) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/deleteRegular`
 * @项目ID 2024
 */
export interface DeleteRegularPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [监管参数配置-删除配置组↗](http://yapi.cai-inc.com/project/2024/interface/api/424178) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/deleteRegular`
 * @项目ID 2024
 */
type DeleteRegularPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/deleteRegular',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [监管参数配置-删除配置组↗](http://yapi.cai-inc.com/project/2024/interface/api/424178) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/deleteRegular`
 * @项目ID 2024
 */
const deleteRegularPostRequestConfig: DeleteRegularPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/deleteRegular',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deleteRegularPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [监管参数配置-删除配置组↗](http://yapi.cai-inc.com/project/2024/interface/api/424178) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/deleteRegular`
 * @项目ID 2024
 */
export const deleteRegularPost = /*#__PURE__*/ (
  requestData: DeleteRegularPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<DeleteRegularPostResponse>(prepare(deleteRegularPostRequestConfig, requestData), ...args)
}

deleteRegularPost.requestConfig = deleteRegularPostRequestConfig

/**
 * 接口 [listAgencyTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424186) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAgencyTypes`
 * @项目ID 2024
 */
export interface ListAgencyTypesGetRequest {}

/**
 * 接口 [listAgencyTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424186) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAgencyTypes`
 * @项目ID 2024
 */
export interface ListAgencyTypesGetResponse {
  success?: boolean
  result?: {
    key?: string
  }[]
  error?: string
}

/**
 * 接口 [listAgencyTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424186) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAgencyTypes`
 * @项目ID 2024
 */
type ListAgencyTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listAgencyTypes',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [listAgencyTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424186) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAgencyTypes`
 * @项目ID 2024
 */
const listAgencyTypesGetRequestConfig: ListAgencyTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listAgencyTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAgencyTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [listAgencyTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424186) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAgencyTypes`
 * @项目ID 2024
 */
export const listAgencyTypesGet = /*#__PURE__*/ (
  requestData?: ListAgencyTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAgencyTypesGetResponse>(prepare(listAgencyTypesGetRequestConfig, requestData), ...args)
}

listAgencyTypesGet.requestConfig = listAgencyTypesGetRequestConfig

/**
 * 接口 [listUserTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424194) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listUserTypes`
 * @项目ID 2024
 */
export interface ListUserTypesGetRequest {}

/**
 * 接口 [listUserTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424194) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listUserTypes`
 * @项目ID 2024
 */
export interface ListUserTypesGetResponse {
  success?: boolean
  result?: {
    key?: string
  }[]
  error?: string
}

/**
 * 接口 [listUserTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424194) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listUserTypes`
 * @项目ID 2024
 */
type ListUserTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listUserTypes',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [listUserTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424194) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listUserTypes`
 * @项目ID 2024
 */
const listUserTypesGetRequestConfig: ListUserTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listUserTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listUserTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [listUserTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424194) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listUserTypes`
 * @项目ID 2024
 */
export const listUserTypesGet = /*#__PURE__*/ (requestData?: ListUserTypesGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListUserTypesGetResponse>(prepare(listUserTypesGetRequestConfig, requestData), ...args)
}

listUserTypesGet.requestConfig = listUserTypesGetRequestConfig

/**
 * 接口 [annTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424202) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annTypes`
 * @项目ID 2024
 */
export interface AnnTypesGetRequest {
  name?: string
}

/**
 * 接口 [annTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424202) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annTypes`
 * @项目ID 2024
 */
export interface AnnTypesGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [annTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424202) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annTypes`
 * @项目ID 2024
 */
type AnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/annTypes',
    'data',
    string,
    'name',
    false
  >
>

/**
 * 接口 [annTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424202) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annTypes`
 * @项目ID 2024
 */
const annTypesGetRequestConfig: AnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/annTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['name'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'annTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [annTypes↗](http://yapi.cai-inc.com/project/2024/interface/api/424202) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annTypes`
 * @项目ID 2024
 */
export const annTypesGet = /*#__PURE__*/ (requestData: AnnTypesGetRequest, ...args: UserRequestRestArgs) => {
  return request<AnnTypesGetResponse>(prepare(annTypesGetRequestConfig, requestData), ...args)
}

annTypesGet.requestConfig = annTypesGetRequestConfig

/**
 * 接口 [listOrg↗](http://yapi.cai-inc.com/project/2024/interface/api/424210) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listOrg`
 * @项目ID 2024
 */
export interface ListOrgGetRequest {
  /**
   * 模糊机构name
   */
  orgName?: string
  /**
   * 用户类型
   */
  userTypes?: string
  /**
   * 是否权限过滤
   */
  filterByPrivilege?: string
}

/**
 * 接口 [listOrg↗](http://yapi.cai-inc.com/project/2024/interface/api/424210) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listOrg`
 * @项目ID 2024
 */
export interface ListOrgGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [listOrg↗](http://yapi.cai-inc.com/project/2024/interface/api/424210) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listOrg`
 * @项目ID 2024
 */
type ListOrgGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listOrg',
    'data',
    string,
    'orgName' | 'userTypes' | 'filterByPrivilege',
    false
  >
>

/**
 * 接口 [listOrg↗](http://yapi.cai-inc.com/project/2024/interface/api/424210) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listOrg`
 * @项目ID 2024
 */
const listOrgGetRequestConfig: ListOrgGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listOrg',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['orgName', 'userTypes', 'filterByPrivilege'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listOrgGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [listOrg↗](http://yapi.cai-inc.com/project/2024/interface/api/424210) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listOrg`
 * @项目ID 2024
 */
export const listOrgGet = /*#__PURE__*/ (requestData: ListOrgGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListOrgGetResponse>(prepare(listOrgGetRequestConfig, requestData), ...args)
}

listOrgGet.requestConfig = listOrgGetRequestConfig

/**
 * 接口 [公告规则信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424218) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/allConfig`
 * @项目ID 2024
 */
export interface AllConfigGetRequest {
  /**
   * 区划code
   */
  districtCode: string
  /**
   * 公告id
   */
  announcementId?: string
}

/**
 * 接口 [公告规则信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424218) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/allConfig`
 * @项目ID 2024
 */
export interface AllConfigGetResponse {
  success?: boolean
  result?: {
    key?: null
  }
  error?: string
}

/**
 * 接口 [公告规则信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424218) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/allConfig`
 * @项目ID 2024
 */
type AllConfigGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/allConfig',
    'data',
    string,
    'districtCode' | 'announcementId',
    false
  >
>

/**
 * 接口 [公告规则信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424218) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/allConfig`
 * @项目ID 2024
 */
const allConfigGetRequestConfig: AllConfigGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/allConfig',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['districtCode', 'announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'allConfigGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告规则信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424218) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/allConfig`
 * @项目ID 2024
 */
export const allConfigGet = /*#__PURE__*/ (requestData: AllConfigGetRequest, ...args: UserRequestRestArgs) => {
  return request<AllConfigGetResponse>(prepare(allConfigGetRequestConfig, requestData), ...args)
}

allConfigGet.requestConfig = allConfigGetRequestConfig

/**
 * 接口 [getAnnouncementConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424226) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/getAnnouncementConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementConfigPostRequest {
  /**
   * 公告类型
   */
  announcementType?: number
  /**
   * 公告区划
   */
  districtCode?: string
  /**
   * 公告元数据
   */
  metadata?: {
    key?: {}
  }
}

/**
 * 接口 [getAnnouncementConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424226) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/getAnnouncementConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementConfigPostResponse {
  success?: boolean
  result?: {
    /**
     * 公告有效期,单位天
     */
    expiryPeriod?: number
    /**
     * 是否允许二次编辑，0：不可编辑， 1：可编辑
     */
    secondEdit?: boolean
    /**
     * 是否自定义标题
     */
    customizedTitle?: boolean
    /**
     * 标题前缀
     */
    titlePrefix?: string
    /**
     * 标题后缀
     */
    titleSuffix?: string
    /**
     * 短信推送
     */
    isNotify?: boolean
    /**
     * 是否公告发布后可撤回 为空业务方自行判断
     */
    canRevoke?: number
    /**
     * N分钟内可撤回 单位：秒 如果可撤回，取配置的N分钟内撤回，如果可撤回但没有指定几分钟，则默认10分钟；返回空代表未配置不可撤回
     */
    revokeTime?: number
    /**
     * 期限类型 0-工作日，1-自然日
     */
    termType?: number
    /**
     * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
     * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
     */
    publishTypes?: string
    /**
     * 附件是否必填
     */
    hasAttachment?: boolean
    /**
     * 附件是否展示
     */
    attachmentShow?: boolean
    /**
     * 附件对外展示规则
     * {@link AttachmentExternalDisplayRulesEnums}
     */
    attachmentExternalDisplayRules?: number
    /**
     * 附件上传提示文案
     */
    attachmentUploadDesc?: string
  }
  error?: string
}

/**
 * 接口 [getAnnouncementConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424226) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/getAnnouncementConfig`
 * @项目ID 2024
 */
type GetAnnouncementConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnouncementConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [getAnnouncementConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424226) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/getAnnouncementConfig`
 * @项目ID 2024
 */
const getAnnouncementConfigPostRequestConfig: GetAnnouncementConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnouncementConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getAnnouncementConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424226) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/getAnnouncementConfig`
 * @项目ID 2024
 */
export const getAnnouncementConfigPost = /*#__PURE__*/ (
  requestData: GetAnnouncementConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementConfigPostResponse>(
    prepare(getAnnouncementConfigPostRequestConfig, requestData),
    ...args,
  )
}

getAnnouncementConfigPost.requestConfig = getAnnouncementConfigPostRequestConfig

/**
 * 接口 [announcementCheckConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424234) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnouncementCheckConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementCheckConfigGetRequest {
  districtCode: string
  announcementType: string
}

/**
 * 接口 [announcementCheckConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424234) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnouncementCheckConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementCheckConfigGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }
  error?: string
}

/**
 * 接口 [announcementCheckConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424234) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnouncementCheckConfig`
 * @项目ID 2024
 */
type GetAnnouncementCheckConfigGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnouncementCheckConfig',
    'data',
    string,
    'districtCode' | 'announcementType',
    false
  >
>

/**
 * 接口 [announcementCheckConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424234) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnouncementCheckConfig`
 * @项目ID 2024
 */
const getAnnouncementCheckConfigGetRequestConfig: GetAnnouncementCheckConfigGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnouncementCheckConfig',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['districtCode', 'announcementType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementCheckConfigGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [announcementCheckConfig↗](http://yapi.cai-inc.com/project/2024/interface/api/424234) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnouncementCheckConfig`
 * @项目ID 2024
 */
export const getAnnouncementCheckConfigGet = /*#__PURE__*/ (
  requestData: GetAnnouncementCheckConfigGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementCheckConfigGetResponse>(
    prepare(getAnnouncementCheckConfigGetRequestConfig, requestData),
    ...args,
  )
}

getAnnouncementCheckConfigGet.requestConfig = getAnnouncementCheckConfigGetRequestConfig

/**
 * 接口 [根据公告类型+区划查询公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424242) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getPublishTypeConfig`
 * @项目ID 2024
 */
export interface GetPublishTypeConfigGetRequest {
  /**
   * 区划编码
   */
  districtCode: string
  /**
   * 公告类型
   */
  announcementType: string
}

/**
 * 接口 [根据公告类型+区划查询公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424242) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getPublishTypeConfig`
 * @项目ID 2024
 */
export interface GetPublishTypeConfigGetResponse {
  success?: boolean
  result?: {
    /**
     * 描述 "k": "审核完成发布",
     */
    k?: string
    /**
     * 值 "v": "2"
     */
    v?: string
  }[]
  error?: string
}

/**
 * 接口 [根据公告类型+区划查询公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424242) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getPublishTypeConfig`
 * @项目ID 2024
 */
type GetPublishTypeConfigGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getPublishTypeConfig',
    'data',
    string,
    'districtCode' | 'announcementType',
    false
  >
>

/**
 * 接口 [根据公告类型+区划查询公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424242) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getPublishTypeConfig`
 * @项目ID 2024
 */
const getPublishTypeConfigGetRequestConfig: GetPublishTypeConfigGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getPublishTypeConfig',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['districtCode', 'announcementType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getPublishTypeConfigGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [根据公告类型+区划查询公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424242) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getPublishTypeConfig`
 * @项目ID 2024
 */
export const getPublishTypeConfigGet = /*#__PURE__*/ (
  requestData: GetPublishTypeConfigGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetPublishTypeConfigGetResponse>(prepare(getPublishTypeConfigGetRequestConfig, requestData), ...args)
}

getPublishTypeConfigGet.requestConfig = getPublishTypeConfigGetRequestConfig

/**
 * 接口 [getAnnTypeId↗](http://yapi.cai-inc.com/project/2024/interface/api/424258) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeId`
 * @项目ID 2024
 */
export interface GetAnnTypeIdGetRequest {
  parentId: string
}

/**
 * 接口 [getAnnTypeId↗](http://yapi.cai-inc.com/project/2024/interface/api/424258) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeId`
 * @项目ID 2024
 */
export interface GetAnnTypeIdGetResponse {
  success?: boolean
  result?: number
  error?: string
}

/**
 * 接口 [getAnnTypeId↗](http://yapi.cai-inc.com/project/2024/interface/api/424258) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeId`
 * @项目ID 2024
 */
type GetAnnTypeIdGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnTypeId',
    'data',
    string,
    'parentId',
    false
  >
>

/**
 * 接口 [getAnnTypeId↗](http://yapi.cai-inc.com/project/2024/interface/api/424258) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeId`
 * @项目ID 2024
 */
const getAnnTypeIdGetRequestConfig: GetAnnTypeIdGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnTypeId',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['parentId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnTypeIdGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getAnnTypeId↗](http://yapi.cai-inc.com/project/2024/interface/api/424258) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeId`
 * @项目ID 2024
 */
export const getAnnTypeIdGet = /*#__PURE__*/ (requestData: GetAnnTypeIdGetRequest, ...args: UserRequestRestArgs) => {
  return request<GetAnnTypeIdGetResponse>(prepare(getAnnTypeIdGetRequestConfig, requestData), ...args)
}

getAnnTypeIdGet.requestConfig = getAnnTypeIdGetRequestConfig

/**
 * 接口 [保存公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424266) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveBigAnnType`
 * @项目ID 2024
 */
export interface SaveBigAnnTypePostRequest {
  /**
   * 公告类型code
   */
  typeCode: number
  /**
   * 公告类型名称
   */
  typeName: string
  /**
   * 公告类型表id
   */
  id?: number
}

/**
 * 接口 [保存公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424266) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveBigAnnType`
 * @项目ID 2024
 */
export interface SaveBigAnnTypePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [保存公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424266) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveBigAnnType`
 * @项目ID 2024
 */
type SaveBigAnnTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/saveBigAnnType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424266) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveBigAnnType`
 * @项目ID 2024
 */
const saveBigAnnTypePostRequestConfig: SaveBigAnnTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/saveBigAnnType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveBigAnnTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424266) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/saveBigAnnType`
 * @项目ID 2024
 */
export const saveBigAnnTypePost = /*#__PURE__*/ (
  requestData: SaveBigAnnTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveBigAnnTypePostResponse>(prepare(saveBigAnnTypePostRequestConfig, requestData), ...args)
}

saveBigAnnTypePost.requestConfig = saveBigAnnTypePostRequestConfig

/**
 * 接口 [编辑公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424274) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateBigAnnType`
 * @项目ID 2024
 */
export interface UpdateBigAnnTypePostRequest {
  /**
   * 公告类型code
   */
  typeCode: number
  /**
   * 公告类型名称
   */
  typeName: string
  /**
   * 公告类型表id
   */
  id?: number
}

/**
 * 接口 [编辑公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424274) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateBigAnnType`
 * @项目ID 2024
 */
export interface UpdateBigAnnTypePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [编辑公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424274) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateBigAnnType`
 * @项目ID 2024
 */
type UpdateBigAnnTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/updateBigAnnType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [编辑公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424274) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateBigAnnType`
 * @项目ID 2024
 */
const updateBigAnnTypePostRequestConfig: UpdateBigAnnTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/updateBigAnnType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updateBigAnnTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑公告大类↗](http://yapi.cai-inc.com/project/2024/interface/api/424274) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateBigAnnType`
 * @项目ID 2024
 */
export const updateBigAnnTypePost = /*#__PURE__*/ (
  requestData: UpdateBigAnnTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<UpdateBigAnnTypePostResponse>(prepare(updateBigAnnTypePostRequestConfig, requestData), ...args)
}

updateBigAnnTypePost.requestConfig = updateBigAnnTypePostRequestConfig

/**
 * 接口 [查询公告大类列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424282) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listBigAnnType`
 * @项目ID 2024
 */
export interface ListBigAnnTypeGetRequest {}

/**
 * 接口 [查询公告大类列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424282) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listBigAnnType`
 * @项目ID 2024
 */
export interface ListBigAnnTypeGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告类型code
     */
    typeCode: number
    /**
     * 公告类型名称
     */
    typeName: string
    /**
     * 公告类型表id
     */
    id?: number
  }[]
  error?: string
}

/**
 * 接口 [查询公告大类列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424282) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listBigAnnType`
 * @项目ID 2024
 */
type ListBigAnnTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listBigAnnType',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [查询公告大类列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424282) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listBigAnnType`
 * @项目ID 2024
 */
const listBigAnnTypeGetRequestConfig: ListBigAnnTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listBigAnnType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listBigAnnTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告大类列表↗](http://yapi.cai-inc.com/project/2024/interface/api/424282) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listBigAnnType`
 * @项目ID 2024
 */
export const listBigAnnTypeGet = /*#__PURE__*/ (
  requestData?: ListBigAnnTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListBigAnnTypeGetResponse>(prepare(listBigAnnTypeGetRequestConfig, requestData), ...args)
}

listBigAnnTypeGet.requestConfig = listBigAnnTypeGetRequestConfig

/**
 * 接口 [删除公告大类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424290) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delBigAnnType`
 * @项目ID 2024
 */
export interface DelBigAnnTypeGetRequest {
  /**
   * 公告类型code
   */
  typeCode: string
}

/**
 * 接口 [删除公告大类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424290) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delBigAnnType`
 * @项目ID 2024
 */
export interface DelBigAnnTypeGetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [删除公告大类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424290) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delBigAnnType`
 * @项目ID 2024
 */
type DelBigAnnTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/delBigAnnType',
    'data',
    string,
    'typeCode',
    false
  >
>

/**
 * 接口 [删除公告大类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424290) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delBigAnnType`
 * @项目ID 2024
 */
const delBigAnnTypeGetRequestConfig: DelBigAnnTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/delBigAnnType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['typeCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'delBigAnnTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除公告大类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424290) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delBigAnnType`
 * @项目ID 2024
 */
export const delBigAnnTypeGet = /*#__PURE__*/ (requestData: DelBigAnnTypeGetRequest, ...args: UserRequestRestArgs) => {
  return request<DelBigAnnTypeGetResponse>(prepare(delBigAnnTypeGetRequestConfig, requestData), ...args)
}

delBigAnnTypeGet.requestConfig = delBigAnnTypeGetRequestConfig

/**
 * 接口 [创建公告小类↗](http://yapi.cai-inc.com/project/2024/interface/api/424298) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/createAnnType`
 * @项目ID 2024
 */
export interface CreateAnnTypePostRequest {
  /**
   * 公告大类
   */
  parentType: number
  /**
   * 公告类型Code
   */
  typeCode: number
  /**
   * 公告类型名称
   */
  typeName: string
  /**
   * 公告特性值
   */
  characteristicValues: string
  /**
   * 是否启用0-禁用，1-启用
   */
  isEnable: number
  /**
   * 表单页面code
   */
  formPageCode?: string
  /**
   * 流程定义KEY
   */
  processDefineKey?: string
  /**
   * 新增公告权限码
   */
  addPrivilegeCode?: string
  /**
   * 是否关联表单页面
   */
  isFormPage?: number
  /**
   * 标题特性值
   */
  titleCharacteristicValues?: string
  /**
   * 类型id
   */
  id?: number
}

/**
 * 接口 [创建公告小类↗](http://yapi.cai-inc.com/project/2024/interface/api/424298) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/createAnnType`
 * @项目ID 2024
 */
export interface CreateAnnTypePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [创建公告小类↗](http://yapi.cai-inc.com/project/2024/interface/api/424298) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/createAnnType`
 * @项目ID 2024
 */
type CreateAnnTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/createAnnType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [创建公告小类↗](http://yapi.cai-inc.com/project/2024/interface/api/424298) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/createAnnType`
 * @项目ID 2024
 */
const createAnnTypePostRequestConfig: CreateAnnTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/createAnnType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'createAnnTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [创建公告小类↗](http://yapi.cai-inc.com/project/2024/interface/api/424298) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/createAnnType`
 * @项目ID 2024
 */
export const createAnnTypePost = /*#__PURE__*/ (
  requestData: CreateAnnTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CreateAnnTypePostResponse>(prepare(createAnnTypePostRequestConfig, requestData), ...args)
}

createAnnTypePost.requestConfig = createAnnTypePostRequestConfig

/**
 * 接口 [查询公告类型配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424306) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAnnTypesList`
 * @项目ID 2024
 */
export interface ListAnnTypesListGetRequest {
  /**
   * 公告类型code
   */
  typeCode?: string
  /**
   * 公告类型名称
   */
  typeName?: string
  /**
   * 公告特性值
   */
  characteristicValues?: string
  /**
   * 是否启用，1-启用，0禁用
   */
  isEnable?: string
  /**
   * 页码
   */
  pageNo: string
  /**
   * 每页条数
   */
  pageSize: string
  /**
   * 大类code
   */
  parentType?: string
}

/**
 * 接口 [查询公告类型配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424306) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAnnTypesList`
 * @项目ID 2024
 */
export interface ListAnnTypesListGetResponse {
  success?: boolean
  result?: {
    pageNo?: number
    pageSize?: number
    totalPage?: number
    total?: number
    data?: {
      /**
       * 类型名称
       */
      typeName?: string
      /**
       * 类型code
       */
      typeCode?: number
      /**
       * 特性值
       */
      characteristicValues?: string
      /**
       * 类型id
       */
      id?: number
      /**
       * 是否启用0-禁用，1-启用
       */
      isEnable?: number
      /**
       * 所属公告大类
       */
      parentTypeName?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [查询公告类型配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424306) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAnnTypesList`
 * @项目ID 2024
 */
type ListAnnTypesListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/listAnnTypesList',
    'data',
    string,
    'typeCode' | 'typeName' | 'characteristicValues' | 'isEnable' | 'pageNo' | 'pageSize' | 'parentType',
    false
  >
>

/**
 * 接口 [查询公告类型配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424306) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAnnTypesList`
 * @项目ID 2024
 */
const listAnnTypesListGetRequestConfig: ListAnnTypesListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/listAnnTypesList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['typeCode', 'typeName', 'characteristicValues', 'isEnable', 'pageNo', 'pageSize', 'parentType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAnnTypesListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告类型配置↗](http://yapi.cai-inc.com/project/2024/interface/api/424306) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/listAnnTypesList`
 * @项目ID 2024
 */
export const listAnnTypesListGet = /*#__PURE__*/ (
  requestData: ListAnnTypesListGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAnnTypesListGetResponse>(prepare(listAnnTypesListGetRequestConfig, requestData), ...args)
}

listAnnTypesListGet.requestConfig = listAnnTypesListGetRequestConfig

/**
 * 接口 [查询公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/424314) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeInfo`
 * @项目ID 2024
 */
export interface GetAnnTypeInfoGetRequest {
  /**
   * 类型id
   */
  id: string
}

/**
 * 接口 [查询公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/424314) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeInfo`
 * @项目ID 2024
 */
export interface GetAnnTypeInfoGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告大类
     */
    parentType?: number
    /**
     * 公告大类名称
     */
    parentTypeName?: string
    /**
     * 公告类型Code
     */
    typeCode?: number
    /**
     * 公告类型名称
     */
    typeName?: string
    /**
     * 公告特性值
     */
    characteristicValues?: string
    /**
     * 公告特性值跳转链接
     */
    characteristicValuesUrl?: string
    /**
     * 是否启用
     */
    isEnable?: number
    /**
     * 表单页面code
     */
    formPageCode?: string
    /**
     * 流程定义KEY
     */
    processDefineKey?: string
    /**
     * 新增公告权限码
     */
    addPrivilegeCode?: string
    /**
     * 标题特性值
     */
    titleCharacteristicValues?: string
    /**
     * 标题特性值跳转链接
     */
    titleCharacteristicValuesUrl?: string
    /**
     * 类型id
     */
    id?: number
  }
  error?: string
}

/**
 * 接口 [查询公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/424314) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeInfo`
 * @项目ID 2024
 */
type GetAnnTypeInfoGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getAnnTypeInfo',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [查询公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/424314) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeInfo`
 * @项目ID 2024
 */
const getAnnTypeInfoGetRequestConfig: GetAnnTypeInfoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/getAnnTypeInfo',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnTypeInfoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/424314) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/getAnnTypeInfo`
 * @项目ID 2024
 */
export const getAnnTypeInfoGet = /*#__PURE__*/ (
  requestData: GetAnnTypeInfoGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnTypeInfoGetResponse>(prepare(getAnnTypeInfoGetRequestConfig, requestData), ...args)
}

getAnnTypeInfoGet.requestConfig = getAnnTypeInfoGetRequestConfig

/**
 * 接口 [编辑公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424322) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateAnnType`
 * @项目ID 2024
 */
export interface UpdateAnnTypePostRequest {
  /**
   * 公告大类
   */
  parentType: number
  /**
   * 公告类型Code
   */
  typeCode: number
  /**
   * 公告类型名称
   */
  typeName: string
  /**
   * 公告特性值
   */
  characteristicValues: string
  /**
   * 是否启用0-禁用，1-启用
   */
  isEnable: number
  /**
   * 表单页面code
   */
  formPageCode?: string
  /**
   * 流程定义KEY
   */
  processDefineKey?: string
  /**
   * 新增公告权限码
   */
  addPrivilegeCode?: string
  /**
   * 是否关联表单页面
   */
  isFormPage?: number
  /**
   * 标题特性值
   */
  titleCharacteristicValues?: string
  /**
   * 类型id
   */
  id?: number
}

/**
 * 接口 [编辑公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424322) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateAnnType`
 * @项目ID 2024
 */
export interface UpdateAnnTypePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [编辑公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424322) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateAnnType`
 * @项目ID 2024
 */
type UpdateAnnTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/updateAnnType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [编辑公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424322) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateAnnType`
 * @项目ID 2024
 */
const updateAnnTypePostRequestConfig: UpdateAnnTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/updateAnnType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updateAnnTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424322) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `POST /announcement/config/updateAnnType`
 * @项目ID 2024
 */
export const updateAnnTypePost = /*#__PURE__*/ (
  requestData: UpdateAnnTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<UpdateAnnTypePostResponse>(prepare(updateAnnTypePostRequestConfig, requestData), ...args)
}

updateAnnTypePost.requestConfig = updateAnnTypePostRequestConfig

/**
 * 接口 [获取公告大类启停数量↗](http://yapi.cai-inc.com/project/2024/interface/api/424330) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/countStartStopSetting`
 * @项目ID 2024
 */
export interface CountStartStopSettingGetRequest {}

/**
 * 接口 [获取公告大类启停数量↗](http://yapi.cai-inc.com/project/2024/interface/api/424330) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/countStartStopSetting`
 * @项目ID 2024
 */
export interface CountStartStopSettingGetResponse {
  success?: boolean
  result?: {
    /**
     * 类型名称
     */
    typeName?: string
    /**
     * 类型code
     */
    typeCode?: number
    /**
     * 启用数量
     */
    enableNum?: number
    /**
     * 停用数量
     */
    disableNum?: number
  }[]
  error?: string
}

/**
 * 接口 [获取公告大类启停数量↗](http://yapi.cai-inc.com/project/2024/interface/api/424330) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/countStartStopSetting`
 * @项目ID 2024
 */
type CountStartStopSettingGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/countStartStopSetting',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取公告大类启停数量↗](http://yapi.cai-inc.com/project/2024/interface/api/424330) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/countStartStopSetting`
 * @项目ID 2024
 */
const countStartStopSettingGetRequestConfig: CountStartStopSettingGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/countStartStopSetting',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'countStartStopSettingGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告大类启停数量↗](http://yapi.cai-inc.com/project/2024/interface/api/424330) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/countStartStopSetting`
 * @项目ID 2024
 */
export const countStartStopSettingGet = /*#__PURE__*/ (
  requestData?: CountStartStopSettingGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CountStartStopSettingGetResponse>(prepare(countStartStopSettingGetRequestConfig, requestData), ...args)
}

countStartStopSettingGet.requestConfig = countStartStopSettingGetRequestConfig

/**
 * 接口 [删除公告小类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424338) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delLittleAnnType`
 * @项目ID 2024
 */
export interface DelLittleAnnTypeGetRequest {
  /**
   * 公告类型code
   */
  typeCode: string
}

/**
 * 接口 [删除公告小类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424338) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delLittleAnnType`
 * @项目ID 2024
 */
export interface DelLittleAnnTypeGetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [删除公告小类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424338) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delLittleAnnType`
 * @项目ID 2024
 */
type DelLittleAnnTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/delLittleAnnType',
    'data',
    string,
    'typeCode',
    false
  >
>

/**
 * 接口 [删除公告小类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424338) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delLittleAnnType`
 * @项目ID 2024
 */
const delLittleAnnTypeGetRequestConfig: DelLittleAnnTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/delLittleAnnType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: ['typeCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'delLittleAnnTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除公告小类型↗](http://yapi.cai-inc.com/project/2024/interface/api/424338) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/delLittleAnnType`
 * @项目ID 2024
 */
export const delLittleAnnTypeGet = /*#__PURE__*/ (
  requestData: DelLittleAnnTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<DelLittleAnnTypeGetResponse>(prepare(delLittleAnnTypeGetRequestConfig, requestData), ...args)
}

delLittleAnnTypeGet.requestConfig = delLittleAnnTypeGetRequestConfig

/**
 * 接口 [公告大类、小类新增权限校验。只允许云平台可以新增↗](http://yapi.cai-inc.com/project/2024/interface/api/424346) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/cloudRegion`
 * @项目ID 2024
 */
export interface CloudRegionGetRequest {}

/**
 * 接口 [公告大类、小类新增权限校验。只允许云平台可以新增↗](http://yapi.cai-inc.com/project/2024/interface/api/424346) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/cloudRegion`
 * @项目ID 2024
 */
export interface CloudRegionGetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [公告大类、小类新增权限校验。只允许云平台可以新增↗](http://yapi.cai-inc.com/project/2024/interface/api/424346) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/cloudRegion`
 * @项目ID 2024
 */
type CloudRegionGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/cloudRegion',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [公告大类、小类新增权限校验。只允许云平台可以新增↗](http://yapi.cai-inc.com/project/2024/interface/api/424346) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/cloudRegion`
 * @项目ID 2024
 */
const cloudRegionGetRequestConfig: CloudRegionGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/cloudRegion',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'cloudRegionGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告大类、小类新增权限校验。只允许云平台可以新增↗](http://yapi.cai-inc.com/project/2024/interface/api/424346) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/cloudRegion`
 * @项目ID 2024
 */
export const cloudRegionGet = /*#__PURE__*/ (requestData?: CloudRegionGetRequest, ...args: UserRequestRestArgs) => {
  return request<CloudRegionGetResponse>(prepare(cloudRegionGetRequestConfig, requestData), ...args)
}

cloudRegionGet.requestConfig = cloudRegionGetRequestConfig

/**
 * 接口 [获取所有公告状态枚举值↗](http://yapi.cai-inc.com/project/2024/interface/api/486954) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annStatuses`
 * @项目ID 2024
 */
export interface AnnStatusesGetRequest {}

/**
 * 接口 [获取所有公告状态枚举值↗](http://yapi.cai-inc.com/project/2024/interface/api/486954) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annStatuses`
 * @项目ID 2024
 */
export interface AnnStatusesGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [获取所有公告状态枚举值↗](http://yapi.cai-inc.com/project/2024/interface/api/486954) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annStatuses`
 * @项目ID 2024
 */
type AnnStatusesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/annStatuses',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取所有公告状态枚举值↗](http://yapi.cai-inc.com/project/2024/interface/api/486954) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annStatuses`
 * @项目ID 2024
 */
const annStatusesGetRequestConfig: AnnStatusesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/config/annStatuses',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'annStatusesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取所有公告状态枚举值↗](http://yapi.cai-inc.com/project/2024/interface/api/486954) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51061)
 * @请求头 `GET /announcement/config/annStatuses`
 * @项目ID 2024
 */
export const annStatusesGet = /*#__PURE__*/ (requestData?: AnnStatusesGetRequest, ...args: UserRequestRestArgs) => {
  return request<AnnStatusesGetResponse>(prepare(annStatusesGetRequestConfig, requestData), ...args)
}

annStatusesGet.requestConfig = annStatusesGetRequestConfig

/* prettier-ignore-end */
