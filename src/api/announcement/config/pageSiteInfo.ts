/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_0 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_0 = '' as any
const prodUrl_0_0_0_0 = '' as any
const dataKey_0_0_0_0 = 'data' as any

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/421914) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @更新时间 `2023-03-09 10:31:17`
 * @项目ID 2024
 */
export interface PageSiteInfoGetRequest {
  /**
   * 站点名称
   */
  siteName: string
  districtCode: string
  pageNo: string
  pageSize: string
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/421914) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @更新时间 `2023-03-09 10:31:17`
 * @项目ID 2024
 */
export interface PageSiteInfoGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 公告站点主键ID
       */
      announcementSiteId?: number
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点域名
       */
      siteDomain?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/421914) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @更新时间 `2023-03-09 10:31:17`
 * @项目ID 2024
 */
type PageSiteInfoGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/pageSiteInfo',
    'data',
    string,
    'siteName' | 'districtCode' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/421914) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @更新时间 `2023-03-09 10:31:17`
 * @项目ID 2024
 */
const pageSiteInfoGetRequestConfig: PageSiteInfoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/config/pageSiteInfo',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['siteName', 'districtCode', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pageSiteInfoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告站点配置可选站点分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/421914) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/pageSiteInfo`
 * @更新时间 `2023-03-09 10:31:17`
 * @项目ID 2024
 */
export const pageSiteInfoGet = /*#__PURE__*/ (requestData: PageSiteInfoGetRequest, ...args: UserRequestRestArgs) => {
  return request<PageSiteInfoGetResponse>(prepare(pageSiteInfoGetRequestConfig, requestData), ...args)
}

pageSiteInfoGet.requestConfig = pageSiteInfoGetRequestConfig

/* prettier-ignore-end */
