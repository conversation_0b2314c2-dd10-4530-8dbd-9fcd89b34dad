/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_0 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_0 = '' as any
const prodUrl_0_0_0_0 = '' as any
const dataKey_0_0_0_0 = 'data' as any

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/421882) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @更新时间 `2023-03-09 10:31:18`
 * @项目ID 2024
 */
export interface GetSiteDetailGetRequest {
  /**
   * 主键ID
   */
  id: string
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/421882) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @更新时间 `2023-03-09 10:31:18`
 * @项目ID 2024
 */
export interface GetSiteDetailGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键ID
     */
    id?: number
    /**
     * 站点ID
     */
    siteId?: number
    /**
     * 站点环境
     */
    siteEnv?: string
    /**
     * 站点名称
     */
    siteName?: string
    /**
     * 站点域名
     */
    siteDomain?: string
    /**
     * 是否前台展示
     */
    isDisplay?: boolean
    /**
     * 排序
     */
    sort?: number
    /**
     * 站点管理后台链接
     */
    siteManagerUrl?: string
  }
  error?: string
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/421882) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @更新时间 `2023-03-09 10:31:18`
 * @项目ID 2024
 */
type GetSiteDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/config/getSiteDetail',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/421882) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @更新时间 `2023-03-09 10:31:18`
 * @项目ID 2024
 */
const getSiteDetailGetRequestConfig: GetSiteDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/config/getSiteDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getSiteDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告站点配置详情↗](http://yapi.cai-inc.com/project/2024/interface/api/421882) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [公告站点配置 AnnouncementSiteConfigController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_50984)
 * @请求头 `GET /announcement/config/getSiteDetail`
 * @更新时间 `2023-03-09 10:31:18`
 * @项目ID 2024
 */
export const getSiteDetailGet = /*#__PURE__*/ (requestData: GetSiteDetailGetRequest, ...args: UserRequestRestArgs) => {
  return request<GetSiteDetailGetResponse>(prepare(getSiteDetailGetRequestConfig, requestData), ...args)
}

getSiteDetailGet.requestConfig = getSiteDetailGetRequestConfig

/* prettier-ignore-end */
