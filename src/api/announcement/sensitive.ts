/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_0 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_0 = '' as any
const prodUrl_0_0_0_0 = '' as any
const dataKey_0_0_0_0 = 'data' as any

/**
 * 接口 [公告敏感词拦截记录分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423866) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/paging`
 * @项目ID 2024
 */
export interface PagingGetRequest {
  /**
   * 公告发布时间-起始
   */
  announcementReleasedAtStart?: string
  /**
   * 公告发布时间-截止
   */
  announcementReleasedAtEnd?: string
  /**
   * 公告标题
   */
  announcementTitle?: string
  /**
   * 公告关联的业务单据编号
   */
  announcementSerialNum?: string
  /**
   * 拦截敏感词
   */
  sensitiveWords?: string
  /**
   * 单据状态
   */
  status?: string
  /**
   * 分页pageNo
   */
  pageNo: string
  /**
   * 分页pageSize
   */
  pageSize: string
}

/**
 * 接口 [公告敏感词拦截记录分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423866) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/paging`
 * @项目ID 2024
 */
export interface PagingGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 主键id
       */
      id?: number
      /**
       * 公告id
       */
      announcementId?: number
      /**
       * 公告标题
       */
      announcementTitle?: string
      /**
       * 记录处理状态
       */
      statusName?: string
      /**
       * 命中敏感词
       */
      sensitiveWords?: string
      /**
       * 公告状态，如：敏感词校验异常/已发布/已结束/已取消
       */
      announcementStatusName?: string
      /**
       * 公告状态Code，如：42/4/5/9
       */
      announcementStatusCode?: string
      /**
       * 公告关联的业务单据编号
       */
      announcementSerialNum?: string
      /**
       * 公告发布时间
       */
      announcementReleasedAt?: string
      /**
       * 公告大类型
       */
      annBigType?: number
    }[]
  }
  error?: string
}

/**
 * 接口 [公告敏感词拦截记录分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423866) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/paging`
 * @项目ID 2024
 */
type PagingGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/sensitive/paging',
    'data',
    string,
    | 'announcementReleasedAtStart'
    | 'announcementReleasedAtEnd'
    | 'announcementTitle'
    | 'announcementSerialNum'
    | 'sensitiveWords'
    | 'status'
    | 'pageNo'
    | 'pageSize',
    false
  >
>

/**
 * 接口 [公告敏感词拦截记录分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423866) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/paging`
 * @项目ID 2024
 */
const pagingGetRequestConfig: PagingGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/sensitive/paging',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: [
    'announcementReleasedAtStart',
    'announcementReleasedAtEnd',
    'announcementTitle',
    'announcementSerialNum',
    'sensitiveWords',
    'status',
    'pageNo',
    'pageSize',
  ],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pagingGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告敏感词拦截记录分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423866) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/paging`
 * @项目ID 2024
 */
export const pagingGet = /*#__PURE__*/ (requestData: PagingGetRequest, ...args: UserRequestRestArgs) => {
  return request<PagingGetResponse>(prepare(pagingGetRequestConfig, requestData), ...args)
}

pagingGet.requestConfig = pagingGetRequestConfig

/**
 * 接口 [敏感词拦截替换↗](http://yapi.cai-inc.com/project/2024/interface/api/423874) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/replaceAnnouncementSensitive`
 * @项目ID 2024
 */
export interface ReplaceAnnouncementSensitivePostRequest {
  /**
   * 敏感词拦截记录主键id
   */
  recordId: number
  /**
   * 公告正文敏感词替换为xxx
   */
  replaceContentWord: string
  /**
   * 公告标题指定替换的新标题
   */
  replaceTitle: string
}

/**
 * 接口 [敏感词拦截替换↗](http://yapi.cai-inc.com/project/2024/interface/api/423874) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/replaceAnnouncementSensitive`
 * @项目ID 2024
 */
export interface ReplaceAnnouncementSensitivePostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [敏感词拦截替换↗](http://yapi.cai-inc.com/project/2024/interface/api/423874) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/replaceAnnouncementSensitive`
 * @项目ID 2024
 */
type ReplaceAnnouncementSensitivePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/sensitive/replaceAnnouncementSensitive',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [敏感词拦截替换↗](http://yapi.cai-inc.com/project/2024/interface/api/423874) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/replaceAnnouncementSensitive`
 * @项目ID 2024
 */
const replaceAnnouncementSensitivePostRequestConfig: ReplaceAnnouncementSensitivePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/sensitive/replaceAnnouncementSensitive',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'replaceAnnouncementSensitivePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [敏感词拦截替换↗](http://yapi.cai-inc.com/project/2024/interface/api/423874) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/replaceAnnouncementSensitive`
 * @项目ID 2024
 */
export const replaceAnnouncementSensitivePost = /*#__PURE__*/ (
  requestData: ReplaceAnnouncementSensitivePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ReplaceAnnouncementSensitivePostResponse>(
    prepare(replaceAnnouncementSensitivePostRequestConfig, requestData),
    ...args,
  )
}

replaceAnnouncementSensitivePost.requestConfig = replaceAnnouncementSensitivePostRequestConfig

/**
 * 接口 [敏感词拦截公告重推↗](http://yapi.cai-inc.com/project/2024/interface/api/423882) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/rePushAnnouncement`
 * @项目ID 2024
 */
export interface RePushAnnouncementPostRequest {
  /**
   * 敏感词拦截记录主键id
   */
  recordId: number
}

/**
 * 接口 [敏感词拦截公告重推↗](http://yapi.cai-inc.com/project/2024/interface/api/423882) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/rePushAnnouncement`
 * @项目ID 2024
 */
export interface RePushAnnouncementPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [敏感词拦截公告重推↗](http://yapi.cai-inc.com/project/2024/interface/api/423882) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/rePushAnnouncement`
 * @项目ID 2024
 */
type RePushAnnouncementPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/sensitive/rePushAnnouncement',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [敏感词拦截公告重推↗](http://yapi.cai-inc.com/project/2024/interface/api/423882) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/rePushAnnouncement`
 * @项目ID 2024
 */
const rePushAnnouncementPostRequestConfig: RePushAnnouncementPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/sensitive/rePushAnnouncement',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'rePushAnnouncementPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [敏感词拦截公告重推↗](http://yapi.cai-inc.com/project/2024/interface/api/423882) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `POST /announcement/sensitive/rePushAnnouncement`
 * @项目ID 2024
 */
export const rePushAnnouncementPost = /*#__PURE__*/ (
  requestData: RePushAnnouncementPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<RePushAnnouncementPostResponse>(prepare(rePushAnnouncementPostRequestConfig, requestData), ...args)
}

rePushAnnouncementPost.requestConfig = rePushAnnouncementPostRequestConfig

/**
 * 接口 [查看敏感词拦截记录详情↗](http://yapi.cai-inc.com/project/2024/interface/api/623994) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/detailAnnouncementSensitive`
 * @项目ID 2024
 */
export interface DetailAnnouncementSensitiveGetRequest {
  sensitiveRecordId: string
}

/**
 * 接口 [查看敏感词拦截记录详情↗](http://yapi.cai-inc.com/project/2024/interface/api/623994) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/detailAnnouncementSensitive`
 * @项目ID 2024
 */
export interface DetailAnnouncementSensitiveGetResponse {
  success?: boolean
  result?: {
    /**
     * 存在标题和正文的敏感词
     */
    exitSensitiveTitleAndContent?: boolean
    /**
     * 附件校验结果列表
     */
    attachmentResultList?: {
      /**
       * 文件名称
       */
      fileName?: string
      /**
       * 文件URL
       */
      fileUrl?: string
      /**
       * 文件校验状态 0-待校验，1-校验通过，2-校验不通过，3-校验失败
       */
      fileSensitiveStatus?: number
      /**
       * 命中敏感词
       */
      sensitiveWords?: string
    }[]
    /**
     * 主键id
     */
    id?: number
    /**
     * 公告id
     */
    announcementId?: number
    /**
     * 公告标题
     */
    announcementTitle?: string
    /**
     * 记录处理状态
     */
    statusName?: string
    /**
     * 命中敏感词
     */
    sensitiveWords?: string
    /**
     * 公告状态，如：敏感词校验异常/已发布/已结束/已取消
     */
    announcementStatusName?: string
    /**
     * 公告状态Code，如：42/4/5/9
     */
    announcementStatusCode?: string
    /**
     * 公告关联的业务单据编号
     */
    announcementSerialNum?: string
    /**
     * 公告发布时间
     */
    announcementReleasedAt?: string
    /**
     * 公告大类型
     */
    annBigType?: number
  }
  error?: string
}

/**
 * 接口 [查看敏感词拦截记录详情↗](http://yapi.cai-inc.com/project/2024/interface/api/623994) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/detailAnnouncementSensitive`
 * @项目ID 2024
 */
type DetailAnnouncementSensitiveGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/sensitive/detailAnnouncementSensitive',
    'data',
    string,
    'sensitiveRecordId',
    false
  >
>

/**
 * 接口 [查看敏感词拦截记录详情↗](http://yapi.cai-inc.com/project/2024/interface/api/623994) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/detailAnnouncementSensitive`
 * @项目ID 2024
 */
const detailAnnouncementSensitiveGetRequestConfig: DetailAnnouncementSensitiveGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/sensitive/detailAnnouncementSensitive',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['sensitiveRecordId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'detailAnnouncementSensitiveGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查看敏感词拦截记录详情↗](http://yapi.cai-inc.com/project/2024/interface/api/623994) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSensitiveRecordController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51055)
 * @请求头 `GET /announcement/sensitive/detailAnnouncementSensitive`
 * @项目ID 2024
 */
export const detailAnnouncementSensitiveGet = /*#__PURE__*/ (
  requestData: DetailAnnouncementSensitiveGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<DetailAnnouncementSensitiveGetResponse>(
    prepare(detailAnnouncementSensitiveGetRequestConfig, requestData),
    ...args,
  )
}

detailAnnouncementSensitiveGet.requestConfig = detailAnnouncementSensitiveGetRequestConfig

/* prettier-ignore-end */
