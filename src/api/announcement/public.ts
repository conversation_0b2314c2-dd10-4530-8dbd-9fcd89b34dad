/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_1 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_1 = '' as any
const prodUrl_0_0_0_1 = '' as any
const dataKey_0_0_0_1 = 'data' as any

/**
 * 接口 [批量新增公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423354) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchSavePublicAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchSavePublicAnnouncementTypesPostRequest {
  /**
   * 用户类别
   */
  userTypes: string[]
  /**
   * 目标区划
   */
  targetDist: string[]
  /**
   * 公告类型
   */
  typeIdList: number[]
}

/**
 * 接口 [批量新增公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423354) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchSavePublicAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchSavePublicAnnouncementTypesPostResponse {
  success?: boolean
  result?: number
  error?: string
}

/**
 * 接口 [批量新增公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423354) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchSavePublicAnnouncementTypes`
 * @项目ID 2024
 */
type BatchSavePublicAnnouncementTypesPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/public/batchSavePublicAnnouncementTypes',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量新增公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423354) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchSavePublicAnnouncementTypes`
 * @项目ID 2024
 */
const batchSavePublicAnnouncementTypesPostRequestConfig: BatchSavePublicAnnouncementTypesPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_1,
    devUrl: devUrl_0_0_0_1,
    prodUrl: prodUrl_0_0_0_1,
    path: '/announcement/public/batchSavePublicAnnouncementTypes',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_1,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'batchSavePublicAnnouncementTypesPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [批量新增公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423354) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchSavePublicAnnouncementTypes`
 * @项目ID 2024
 */
export const batchSavePublicAnnouncementTypesPost = /*#__PURE__*/ (
  requestData: BatchSavePublicAnnouncementTypesPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<BatchSavePublicAnnouncementTypesPostResponse>(
    prepare(batchSavePublicAnnouncementTypesPostRequestConfig, requestData),
    ...args,
  )
}

batchSavePublicAnnouncementTypesPost.requestConfig = batchSavePublicAnnouncementTypesPostRequestConfig

/**
 * 接口 [批量取消公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423362) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchCancelPublicAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchCancelPublicAnnouncementTypesPostRequest {
  /**
   * 用户类别
   */
  userTypes: string[]
  /**
   * 目标区划
   */
  targetDist: string[]
  /**
   * 公告类型
   */
  typeIdList: number[]
}

/**
 * 接口 [批量取消公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423362) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchCancelPublicAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchCancelPublicAnnouncementTypesPostResponse {
  success?: boolean
  result?: number
  error?: string
}

/**
 * 接口 [批量取消公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423362) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchCancelPublicAnnouncementTypes`
 * @项目ID 2024
 */
type BatchCancelPublicAnnouncementTypesPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/public/batchCancelPublicAnnouncementTypes',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量取消公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423362) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchCancelPublicAnnouncementTypes`
 * @项目ID 2024
 */
const batchCancelPublicAnnouncementTypesPostRequestConfig: BatchCancelPublicAnnouncementTypesPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_1,
    devUrl: devUrl_0_0_0_1,
    prodUrl: prodUrl_0_0_0_1,
    path: '/announcement/public/batchCancelPublicAnnouncementTypes',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_1,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'batchCancelPublicAnnouncementTypesPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [批量取消公告发布类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423362) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementPublicController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51044)
 * @请求头 `POST /announcement/public/batchCancelPublicAnnouncementTypes`
 * @项目ID 2024
 */
export const batchCancelPublicAnnouncementTypesPost = /*#__PURE__*/ (
  requestData: BatchCancelPublicAnnouncementTypesPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<BatchCancelPublicAnnouncementTypesPostResponse>(
    prepare(batchCancelPublicAnnouncementTypesPostRequestConfig, requestData),
    ...args,
  )
}

batchCancelPublicAnnouncementTypesPost.requestConfig = batchCancelPublicAnnouncementTypesPostRequestConfig

/* prettier-ignore-end */
