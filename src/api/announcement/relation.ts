/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_0 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_0 = '' as any
const prodUrl_0_0_0_0 = '' as any
const dataKey_0_0_0_0 = 'data' as any

/**
 * 接口 [开放配置-拿到（区划，公告类型）对应配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423330) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/getAnnouncementConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementConfigPostRequest {
  /**
   * 公告类型
   */
  announcementType?: number
  /**
   * 公告区划
   */
  districtCode?: string
  /**
   * 公告元数据
   */
  metadata?: {
    key?: {}
  }
}

/**
 * 接口 [开放配置-拿到（区划，公告类型）对应配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423330) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/getAnnouncementConfig`
 * @项目ID 2024
 */
export interface GetAnnouncementConfigPostResponse {
  success?: boolean
  result?: {
    /**
     * 失效时间
     */
    expiryPeriod?: number
    /**
     * 是否二次编辑
     */
    secondEdit?: boolean
    /**
     * 是否自定义标题
     */
    customizedTitle?: boolean
    /**
     * 标题后缀
     */
    titlePrefix?: string
    /**
     * 标题前缀
     */
    titleSuffix?: string
    /**
     * 是否短信提醒
     */
    isNotify?: boolean
    /**
     * 附件是否必填
     */
    hasAttachment?: boolean
    /**
     * 是否强控
     */
    isForcedControl?: boolean
    /**
     * 附件是否展示
     */
    attachmentShow?: boolean
    /**
     * 附件对外展示规则
     * {@link AttachmentExternalDisplayRulesEnums}
     */
    attachmentExternalDisplayRules?: number
    /**
     * 附件上传提示文案
     */
    attachmentUploadDesc?: string
    /**
     * 是否有水印
     */
    isWater?: boolean
    /**
     * 水印内容  长度20
     */
    waterContent?: string
    /**
     * 页眉内容  长度 100
     */
    waterHeaderContent?: string
    /**
     * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
     */
    enableSummaryCalculation?: boolean
  }
  error?: string
}

/**
 * 接口 [开放配置-拿到（区划，公告类型）对应配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423330) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/getAnnouncementConfig`
 * @项目ID 2024
 */
type GetAnnouncementConfigPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/getAnnouncementConfig',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [开放配置-拿到（区划，公告类型）对应配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423330) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/getAnnouncementConfig`
 * @项目ID 2024
 */
const getAnnouncementConfigPostRequestConfig: GetAnnouncementConfigPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/relation/getAnnouncementConfig',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementConfigPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [开放配置-拿到（区划，公告类型）对应配置↗](http://yapi.cai-inc.com/project/2024/interface/api/423330) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/getAnnouncementConfig`
 * @项目ID 2024
 */
export const getAnnouncementConfigPost = /*#__PURE__*/ (
  requestData: GetAnnouncementConfigPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementConfigPostResponse>(
    prepare(getAnnouncementConfigPostRequestConfig, requestData),
    ...args,
  )
}

getAnnouncementConfigPost.requestConfig = getAnnouncementConfigPostRequestConfig

/**
 * 接口 [批量新增公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423338) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchSaveRelationAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchSaveRelationAnnouncementTypesPostRequest {
  /**
   * 目标区划
   */
  targetDist: string[]
  /**
   * 公告类型
   */
  typeIdList: number[]
}

/**
 * 接口 [批量新增公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423338) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchSaveRelationAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchSaveRelationAnnouncementTypesPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [批量新增公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423338) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchSaveRelationAnnouncementTypes`
 * @项目ID 2024
 */
type BatchSaveRelationAnnouncementTypesPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/batchSaveRelationAnnouncementTypes',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量新增公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423338) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchSaveRelationAnnouncementTypes`
 * @项目ID 2024
 */
const batchSaveRelationAnnouncementTypesPostRequestConfig: BatchSaveRelationAnnouncementTypesPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/announcement/relation/batchSaveRelationAnnouncementTypes',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'batchSaveRelationAnnouncementTypesPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [批量新增公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423338) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchSaveRelationAnnouncementTypes`
 * @项目ID 2024
 */
export const batchSaveRelationAnnouncementTypesPost = /*#__PURE__*/ (
  requestData: BatchSaveRelationAnnouncementTypesPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<BatchSaveRelationAnnouncementTypesPostResponse>(
    prepare(batchSaveRelationAnnouncementTypesPostRequestConfig, requestData),
    ...args,
  )
}

batchSaveRelationAnnouncementTypesPost.requestConfig = batchSaveRelationAnnouncementTypesPostRequestConfig

/**
 * 接口 [批量取消公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423346) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchCancelRelationAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchCancelRelationAnnouncementTypesPostRequest {
  /**
   * 目标区划
   */
  targetDist: string[]
  /**
   * 公告类型
   */
  typeIdList: number[]
}

/**
 * 接口 [批量取消公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423346) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchCancelRelationAnnouncementTypes`
 * @项目ID 2024
 */
export interface BatchCancelRelationAnnouncementTypesPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [批量取消公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423346) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchCancelRelationAnnouncementTypes`
 * @项目ID 2024
 */
type BatchCancelRelationAnnouncementTypesPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/batchCancelRelationAnnouncementTypes',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量取消公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423346) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchCancelRelationAnnouncementTypes`
 * @项目ID 2024
 */
const batchCancelRelationAnnouncementTypesPostRequestConfig: BatchCancelRelationAnnouncementTypesPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_0,
    devUrl: devUrl_0_0_0_0,
    prodUrl: prodUrl_0_0_0_0,
    path: '/announcement/relation/batchCancelRelationAnnouncementTypes',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_0,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'batchCancelRelationAnnouncementTypesPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [批量取消公告关联类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423346) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `POST /announcement/relation/batchCancelRelationAnnouncementTypes`
 * @项目ID 2024
 */
export const batchCancelRelationAnnouncementTypesPost = /*#__PURE__*/ (
  requestData: BatchCancelRelationAnnouncementTypesPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<BatchCancelRelationAnnouncementTypesPostResponse>(
    prepare(batchCancelRelationAnnouncementTypesPostRequestConfig, requestData),
    ...args,
  )
}

batchCancelRelationAnnouncementTypesPost.requestConfig = batchCancelRelationAnnouncementTypesPostRequestConfig

/**
 * 接口 [判断对应区划是否关联公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/442282) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/checkExistRelatedAnnTypes`
 * @项目ID 2024
 */
export interface CheckExistRelatedAnnTypesGetRequest {
  /**
   * 区划编码
   */
  districtCode?: string
}

/**
 * 接口 [判断对应区划是否关联公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/442282) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/checkExistRelatedAnnTypes`
 * @项目ID 2024
 */
export interface CheckExistRelatedAnnTypesGetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [判断对应区划是否关联公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/442282) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/checkExistRelatedAnnTypes`
 * @项目ID 2024
 */
type CheckExistRelatedAnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/checkExistRelatedAnnTypes',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [判断对应区划是否关联公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/442282) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/checkExistRelatedAnnTypes`
 * @项目ID 2024
 */
const checkExistRelatedAnnTypesGetRequestConfig: CheckExistRelatedAnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/relation/checkExistRelatedAnnTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'checkExistRelatedAnnTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [判断对应区划是否关联公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/442282) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/checkExistRelatedAnnTypes`
 * @项目ID 2024
 */
export const checkExistRelatedAnnTypesGet = /*#__PURE__*/ (
  requestData: CheckExistRelatedAnnTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CheckExistRelatedAnnTypesGetResponse>(
    prepare(checkExistRelatedAnnTypesGetRequestConfig, requestData),
    ...args,
  )
}

checkExistRelatedAnnTypesGet.requestConfig = checkExistRelatedAnnTypesGetRequestConfig

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/470026) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/listRelatedAnnTypes`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypesGetRequest {
  /**
   * 区划编码
   */
  distCode?: string
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/470026) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/listRelatedAnnTypes`
 * @项目ID 2024
 */
export interface ListRelatedAnnTypesGetResponse {
  success?: boolean
  result?: {
    /**
     * 父公告类型id
     */
    typeId?: number
    /**
     * 父公告类型名称
     */
    name?: string
    /**
     * 子公告列表
     */
    children?: {
      id?: number
      /**
       * 区划code
       */
      distCode?: string
      /**
       * 公告类型coed
       */
      typeId?: number
      /**
       * 公告类型名称
       */
      name?: string
      /**
       * 是否启用
       */
      isValid?: boolean
    }[]
  }[]
  error?: string
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/470026) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/listRelatedAnnTypes`
 * @项目ID 2024
 */
type ListRelatedAnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/listRelatedAnnTypes',
    'data',
    string,
    'distCode',
    false
  >
>

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/470026) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/listRelatedAnnTypes`
 * @项目ID 2024
 */
const listRelatedAnnTypesGetRequestConfig: ListRelatedAnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/relation/listRelatedAnnTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['distCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listRelatedAnnTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [按区划获取已关联类型列表↗](http://yapi.cai-inc.com/project/2024/interface/api/470026) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/listRelatedAnnTypes`
 * @项目ID 2024
 */
export const listRelatedAnnTypesGet = /*#__PURE__*/ (
  requestData: ListRelatedAnnTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListRelatedAnnTypesGetResponse>(prepare(listRelatedAnnTypesGetRequestConfig, requestData), ...args)
}

listRelatedAnnTypesGet.requestConfig = listRelatedAnnTypesGetRequestConfig

/**
 * 接口 [查询开放配置具体信息↗](http://yapi.cai-inc.com/project/2024/interface/api/470034) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getRelationById`
 * @项目ID 2024
 */
export interface GetRelationByIdGetRequest {
  /**
   * 区划编码
   */
  relationId: string
}

/**
 * 接口 [查询开放配置具体信息↗](http://yapi.cai-inc.com/project/2024/interface/api/470034) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getRelationById`
 * @项目ID 2024
 */
export interface GetRelationByIdGetResponse {
  success?: boolean
  result?: {
    /**
     * 自增主键id
     */
    id?: number
    /**
     * 公告类型名称
     */
    name?: string
    /**
     * 区划编码
     */
    distCode?: string
    /**
     * 公告类型id
     */
    typeId?: number
    /**
     * 当前类型父类型id， 0表示顶层节点，无父类型
     */
    parentId?: number
    /**
     * 公告有效期,单位天
     */
    expiryPeriod?: number
    /**
     * 系统类别：1-政府采购；2-企业购
     */
    systemType?: number
    /**
     * 是否开放到外网：0-不开放； 1-开放
     */
    isOut?: number
    /**
     * 如果is_out=0，为不开放原因
     */
    unOpenReason?: string
    /**
     * 当前数据是否有效：0-无效； 1-有效
     */
    isValid?: boolean
    /**
     * 是否强控，若强控，创建公告时，强制关联采购方式
     */
    isForcedControl?: number
    /**
     * 是否公告发布后撤回
     */
    canRevoke?: number
    /**
     * 可撤回时间，单位秒
     */
    revokeTime?: number
    /**
     * 附件是否必填
     */
    hasAttachment?: number
    /**
     * 采购方式代码(内外网同步映射代码)
     */
    procurementMethodCode?: string
    /**
     * 采购方式名称
     */
    procurementMethodName?: string
    /**
     * 是否可以发起异议，0：不可异议，1：可异议
     */
    canObject?: boolean
    /**
     * 被质疑对象信息字段，多个逗号分隔
     */
    objectionKeys?: string
    /**
     * 质疑对象信息字段，多个逗号分隔
     */
    beObjectionKeys?: string
    /**
     * 需要校验代理机构类型，多个逗号分隔
     */
    checkAgencyTypes?: string
    /**
     * 是否允许二次编辑，0：不可编辑， 1：可编辑
     */
    secondEdit?: boolean
    /**
     * 是否自定义标题
     */
    customizedTitle?: boolean
    /**
     * 标题前缀
     */
    titlePrefix?: string
    /**
     * 标题后缀
     */
    titleSuffix?: string
    /**
     * 短信推送   如果为true，则列表页会有发送短信按钮
     */
    isNotify?: boolean
    /**
     * 是否接入表单
     */
    isFormPage?: boolean
    /**
     * 表单code
     */
    formPageCode?: string
    /**
     * 是否开启敏感词校验
     */
    canContentCensor?: number
    /**
     * 是否展示附件
     */
    attachmentShow?: number
    /**
     * 附件对外展示规则
     * {@link AttachmentExternalDisplayRulesEnums}
     */
    attachmentExternalDisplayRules?: number
    /**
     * 附件上传提示文案
     */
    attachmentUploadDesc?: string
    /**
     * 是否有水印
     */
    isWater?: boolean
    /**
     * 水印内容  长度20
     */
    waterContent?: string
    /**
     * 页眉内容  长度 100
     */
    waterHeaderContent?: string
    /**
     * 是否允许手动修改中小&小微企业预留金额及占比=允许/禁止
     */
    enableSummaryCalculation?: boolean
    /**
     * 是否展示允许手动修改中小&小微企业预留金额及占比=允许/禁止字段
     */
    enableSummaryCalculationShow?: boolean
    /**
     * 期限类型 0-工作日，1-自然日
     */
    termType?: number
    /**
     * 是否可取消公告 0-否，1-是
     */
    isRevocable?: number
    /**
     * 展示是否可取消公告 0-否，1-是
     */
    isRevocableShow?: boolean
    /**
     * 取消按钮名称
     */
    revocableButtonName?: string
    /**
     * 发布类型 2-审核完成发布 1-定时发布 3-手动发布 e.g. 2,1,3
     * cn.gov.zcy.announcement.constants.AnnouncementPubTypeEnum
     */
    publishTypes?: string
    /**
     * 白名单区划-查看
     */
    whiteListDistrictList?: {
      code?: string
      codeDesc?: string
    }[]
    /**
     * 白名单区划-保存
     */
    whiteListDistrictCode?: string[]
  }
  error?: string
}

/**
 * 接口 [查询开放配置具体信息↗](http://yapi.cai-inc.com/project/2024/interface/api/470034) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getRelationById`
 * @项目ID 2024
 */
type GetRelationByIdGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/getRelationById',
    'data',
    string,
    'relationId',
    false
  >
>

/**
 * 接口 [查询开放配置具体信息↗](http://yapi.cai-inc.com/project/2024/interface/api/470034) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getRelationById`
 * @项目ID 2024
 */
const getRelationByIdGetRequestConfig: GetRelationByIdGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/relation/getRelationById',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['relationId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getRelationByIdGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询开放配置具体信息↗](http://yapi.cai-inc.com/project/2024/interface/api/470034) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getRelationById`
 * @项目ID 2024
 */
export const getRelationByIdGet = /*#__PURE__*/ (
  requestData: GetRelationByIdGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetRelationByIdGetResponse>(prepare(getRelationByIdGetRequestConfig, requestData), ...args)
}

getRelationByIdGet.requestConfig = getRelationByIdGetRequestConfig

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/475146) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getSelectedAnnTypes`
 * @项目ID 2024
 */
export interface GetSelectedAnnTypesGetRequest {
  /**
   * 区划编码
   */
  distCode?: string
  /**
   * 公告类型
   */
  userType: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/475146) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getSelectedAnnTypes`
 * @项目ID 2024
 */
export interface GetSelectedAnnTypesGetResponse {
  success?: boolean
  result?: {
    /**
     * 父公告类型id
     */
    typeId?: number
    /**
     * 父公告类型名称
     */
    name?: string
    /**
     * 子公告列表
     */
    children?: {
      id?: number
      /**
       * 区划code
       */
      distCode?: string
      /**
       * 公告类型coed
       */
      typeId?: number
      /**
       * 公告类型名称
       */
      name?: string
      /**
       * 是否启用
       */
      isValid?: boolean
    }[]
  }[]
  error?: string
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/475146) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getSelectedAnnTypes`
 * @项目ID 2024
 */
type GetSelectedAnnTypesGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/relation/getSelectedAnnTypes',
    'data',
    string,
    'distCode' | 'userType',
    false
  >
>

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/475146) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getSelectedAnnTypes`
 * @项目ID 2024
 */
const getSelectedAnnTypesGetRequestConfig: GetSelectedAnnTypesGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_0,
  devUrl: devUrl_0_0_0_0,
  prodUrl: prodUrl_0_0_0_0,
  path: '/announcement/relation/getSelectedAnnTypes',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_0,
  paramNames: [],
  queryNames: ['distCode', 'userType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getSelectedAnnTypesGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告发布配置-获取已配置公告类型(前端展示数据)↗](http://yapi.cai-inc.com/project/2024/interface/api/475146) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementRelationController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51043)
 * @请求头 `GET /announcement/relation/getSelectedAnnTypes`
 * @项目ID 2024
 */
export const getSelectedAnnTypesGet = /*#__PURE__*/ (
  requestData: GetSelectedAnnTypesGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetSelectedAnnTypesGetResponse>(prepare(getSelectedAnnTypesGetRequestConfig, requestData), ...args)
}

getSelectedAnnTypesGet.requestConfig = getSelectedAnnTypesGetRequestConfig

/* prettier-ignore-end */
