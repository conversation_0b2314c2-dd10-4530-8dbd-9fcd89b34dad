/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_5 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_5 = '' as any
const prodUrl_0_0_0_5 = '' as any
const dataKey_0_0_0_5 = 'data' as any

/**
 * 接口 [公告审计日志分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423554) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/paging`
 * @项目ID 2024
 */
export interface PagingGetRequest {
  /**
   * 操作时间-起始
   */
  addTimeStart?: string
  /**
   * 操作时间-截止
   */
  addTimeEnd?: string
  /**
   * 配置菜单
   */
  source?: string
  /**
   * 配置类型
   */
  operation?: string
  /**
   * 操作人姓名
   */
  operatorName?: string
  pageNo: string
  pageSize: string
}

/**
 * 接口 [公告审计日志分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423554) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/paging`
 * @项目ID 2024
 */
export interface PagingGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 主键id
       */
      id?: number
      /**
       * 操作人姓名
       */
      operatorName?: string
      /**
       * 创建时间
       */
      addTime?: string
      /**
       * 操作来源
       */
      source?: string
      /**
       * 操作动作
       */
      operation?: string
      /**
       * 应答信息
       */
      responseMsg?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [公告审计日志分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423554) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/paging`
 * @项目ID 2024
 */
type PagingGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/audit/log/paging',
    'data',
    string,
    'addTimeStart' | 'addTimeEnd' | 'source' | 'operation' | 'operatorName' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [公告审计日志分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423554) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/paging`
 * @项目ID 2024
 */
const pagingGetRequestConfig: PagingGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_5,
  devUrl: devUrl_0_0_0_5,
  prodUrl: prodUrl_0_0_0_5,
  path: '/announcement/audit/log/paging',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_5,
  paramNames: [],
  queryNames: ['addTimeStart', 'addTimeEnd', 'source', 'operation', 'operatorName', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pagingGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告审计日志分页查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423554) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/paging`
 * @项目ID 2024
 */
export const pagingGet = /*#__PURE__*/ (requestData: PagingGetRequest, ...args: UserRequestRestArgs) => {
  return request<PagingGetResponse>(prepare(pagingGetRequestConfig, requestData), ...args)
}

pagingGet.requestConfig = pagingGetRequestConfig

/**
 * 接口 [公告类型管理操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423578) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/listBigAnnLog`
 * @项目ID 2024
 */
export interface ListBigAnnLogGetRequest {
  /**
   * 来源
   */
  source: string
  /**
   * 公告类型名称
   */
  typeName?: string
  /**
   * 公告类型code
   */
  typeCode?: string
  /**
   * 操作类型
   */
  operationType?: string
  /**
   * 操作人
   */
  operatorName?: string
  /**
   * 开始时间
   */
  addTimeStart?: string
  /**
   * 截止时间
   */
  addTimeEnd?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 页码
   */
  pageNo: string
  /**
   * 条数
   */
  pageSize: string
}

/**
 * 接口 [公告类型管理操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423578) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/listBigAnnLog`
 * @项目ID 2024
 */
export interface ListBigAnnLogGetResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 操作类型
       */
      operationType?: string
      /**
       * 公告类型
       */
      typeCode?: number
      /**
       * 公告类型名称
       */
      typeName?: string
      /**
       * 操作人
       */
      operatorName?: string
      /**
       * 操作时间
       */
      addTime?: string
      /**
       * 备注
       */
      remark?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [公告类型管理操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423578) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/listBigAnnLog`
 * @项目ID 2024
 */
type ListBigAnnLogGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/audit/log/listBigAnnLog',
    'data',
    string,
    | 'source'
    | 'typeName'
    | 'typeCode'
    | 'operationType'
    | 'operatorName'
    | 'addTimeStart'
    | 'addTimeEnd'
    | 'remark'
    | 'pageNo'
    | 'pageSize',
    false
  >
>

/**
 * 接口 [公告类型管理操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423578) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/listBigAnnLog`
 * @项目ID 2024
 */
const listBigAnnLogGetRequestConfig: ListBigAnnLogGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_5,
  devUrl: devUrl_0_0_0_5,
  prodUrl: prodUrl_0_0_0_5,
  path: '/announcement/audit/log/listBigAnnLog',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_5,
  paramNames: [],
  queryNames: [
    'source',
    'typeName',
    'typeCode',
    'operationType',
    'operatorName',
    'addTimeStart',
    'addTimeEnd',
    'remark',
    'pageNo',
    'pageSize',
  ],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listBigAnnLogGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告类型管理操作日志查询↗](http://yapi.cai-inc.com/project/2024/interface/api/423578) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/listBigAnnLog`
 * @项目ID 2024
 */
export const listBigAnnLogGet = /*#__PURE__*/ (requestData: ListBigAnnLogGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListBigAnnLogGetResponse>(prepare(listBigAnnLogGetRequestConfig, requestData), ...args)
}

listBigAnnLogGet.requestConfig = listBigAnnLogGetRequestConfig

/**
 * 接口 [获取所有操作类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423586) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/getOperationType`
 * @项目ID 2024
 */
export interface GetOperationTypeGetRequest {}

/**
 * 接口 [获取所有操作类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423586) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/getOperationType`
 * @项目ID 2024
 */
export interface GetOperationTypeGetResponse {
  success?: boolean
  result?: {
    code?: string
    name?: string
  }[]
  error?: string
}

/**
 * 接口 [获取所有操作类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423586) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/getOperationType`
 * @项目ID 2024
 */
type GetOperationTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/audit/log/getOperationType',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取所有操作类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423586) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/getOperationType`
 * @项目ID 2024
 */
const getOperationTypeGetRequestConfig: GetOperationTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_5,
  devUrl: devUrl_0_0_0_5,
  prodUrl: prodUrl_0_0_0_5,
  path: '/announcement/audit/log/getOperationType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_5,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getOperationTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取所有操作类型↗](http://yapi.cai-inc.com/project/2024/interface/api/423586) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/getOperationType`
 * @项目ID 2024
 */
export const getOperationTypeGet = /*#__PURE__*/ (
  requestData?: GetOperationTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetOperationTypeGetResponse>(prepare(getOperationTypeGetRequestConfig, requestData), ...args)
}

getOperationTypeGet.requestConfig = getOperationTypeGetRequestConfig

/* prettier-ignore-end */
