/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_5 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_5 = '' as any
const prodUrl_0_0_0_5 = '' as any
const dataKey_0_0_0_5 = 'data' as any

/**
 * 接口 [公告审计日志列表配置菜单下拉选项↗](http://yapi.cai-inc.com/project/2024/interface/api/423562) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/source/list`
 * @项目ID 2024
 */
export interface ListGetRequest {}

/**
 * 接口 [公告审计日志列表配置菜单下拉选项↗](http://yapi.cai-inc.com/project/2024/interface/api/423562) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/source/list`
 * @项目ID 2024
 */
export interface ListGetResponse {
  success?: boolean
  result?: {
    key?: {}
  }[]
  error?: string
}

/**
 * 接口 [公告审计日志列表配置菜单下拉选项↗](http://yapi.cai-inc.com/project/2024/interface/api/423562) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/source/list`
 * @项目ID 2024
 */
type ListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/audit/log/source/list',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [公告审计日志列表配置菜单下拉选项↗](http://yapi.cai-inc.com/project/2024/interface/api/423562) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/source/list`
 * @项目ID 2024
 */
const listGetRequestConfig: ListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_5,
  devUrl: devUrl_0_0_0_5,
  prodUrl: prodUrl_0_0_0_5,
  path: '/announcement/audit/log/source/list',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_5,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告审计日志列表配置菜单下拉选项↗](http://yapi.cai-inc.com/project/2024/interface/api/423562) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAuditLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51049)
 * @请求头 `GET /announcement/audit/log/source/list`
 * @项目ID 2024
 */
export const listGet = /*#__PURE__*/ (requestData?: ListGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListGetResponse>(prepare(listGetRequestConfig, requestData), ...args)
}

listGet.requestConfig = listGetRequestConfig

/* prettier-ignore-end */
