/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_7 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_7 = '' as any
const prodUrl_0_0_0_7 = '' as any
const dataKey_0_0_0_7 = 'data' as any

/**
 * 接口 [getBackCategory↗](http://yapi.cai-inc.com/project/2024/interface/api/423762) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementItemController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51051)
 * @请求头 `GET /announcement/api/item/backCategory`
 * @项目ID 2024
 */
export interface BackCategoryGetRequest {}

/**
 * 接口 [getBackCategory↗](http://yapi.cai-inc.com/project/2024/interface/api/423762) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementItemController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51051)
 * @请求头 `GET /announcement/api/item/backCategory`
 * @项目ID 2024
 */
export interface BackCategoryGetResponse {
  success?: boolean
  result?: {
    node?: {
      id?: number
      pid?: number
      name?: string
      code?: string
      level?: number
      hasQualificationRequirements?: boolean
    }
    children?: {
      node?: {
        id?: number
        pid?: number
        name?: string
        code?: string
        level?: number
        hasQualificationRequirements?: boolean
      }
      children?: {}[]
    }[]
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [getBackCategory↗](http://yapi.cai-inc.com/project/2024/interface/api/423762) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementItemController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51051)
 * @请求头 `GET /announcement/api/item/backCategory`
 * @项目ID 2024
 */
type BackCategoryGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/item/backCategory',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [getBackCategory↗](http://yapi.cai-inc.com/project/2024/interface/api/423762) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementItemController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51051)
 * @请求头 `GET /announcement/api/item/backCategory`
 * @项目ID 2024
 */
const backCategoryGetRequestConfig: BackCategoryGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_7,
  devUrl: devUrl_0_0_0_7,
  prodUrl: prodUrl_0_0_0_7,
  path: '/announcement/api/item/backCategory',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_7,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'backCategoryGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getBackCategory↗](http://yapi.cai-inc.com/project/2024/interface/api/423762) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementItemController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51051)
 * @请求头 `GET /announcement/api/item/backCategory`
 * @项目ID 2024
 */
export const backCategoryGet = /*#__PURE__*/ (requestData?: BackCategoryGetRequest, ...args: UserRequestRestArgs) => {
  return request<BackCategoryGetResponse>(prepare(backCategoryGetRequestConfig, requestData), ...args)
}

backCategoryGet.requestConfig = backCategoryGetRequestConfig

/* prettier-ignore-end */
