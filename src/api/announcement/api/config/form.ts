/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_30 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_30 = '' as any
const prodUrl_0_0_0_30 = '' as any
const dataKey_0_0_0_30 = 'data' as any

/**
 * 接口 [获取年份，如includeSelf=true,size=2,则返回值是2022,2023↗](http://yapi.cai-inc.com/project/2024/interface/api/484530) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `GET /announcement/api/config/form/listYear`
 * @项目ID 2024
 */
export interface ListYearGetRequest {
  /**
   * 是否含有当前年份
   */
  includeSelf: string
  /**
   * 返回数量
   */
  size: string
}

/**
 * 接口 [获取年份，如includeSelf=true,size=2,则返回值是2022,2023↗](http://yapi.cai-inc.com/project/2024/interface/api/484530) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `GET /announcement/api/config/form/listYear`
 * @项目ID 2024
 */
export interface ListYearGetResponse {
  success?: boolean
  result?: number[]
  code?: string
  message?: string
}

/**
 * 接口 [获取年份，如includeSelf=true,size=2,则返回值是2022,2023↗](http://yapi.cai-inc.com/project/2024/interface/api/484530) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `GET /announcement/api/config/form/listYear`
 * @项目ID 2024
 */
type ListYearGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/form/listYear',
    'data',
    string,
    'includeSelf' | 'size',
    false
  >
>

/**
 * 接口 [获取年份，如includeSelf=true,size=2,则返回值是2022,2023↗](http://yapi.cai-inc.com/project/2024/interface/api/484530) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `GET /announcement/api/config/form/listYear`
 * @项目ID 2024
 */
const listYearGetRequestConfig: ListYearGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_30,
  devUrl: devUrl_0_0_0_30,
  prodUrl: prodUrl_0_0_0_30,
  path: '/announcement/api/config/form/listYear',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_30,
  paramNames: [],
  queryNames: ['includeSelf', 'size'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listYearGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取年份，如includeSelf=true,size=2,则返回值是2022,2023↗](http://yapi.cai-inc.com/project/2024/interface/api/484530) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `GET /announcement/api/config/form/listYear`
 * @项目ID 2024
 */
export const listYearGet = /*#__PURE__*/ (requestData: ListYearGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListYearGetResponse>(prepare(listYearGetRequestConfig, requestData), ...args)
}

listYearGet.requestConfig = listYearGetRequestConfig

/**
 * 接口 [从大数据，获取中小企业预留相关数据↗](http://yapi.cai-inc.com/project/2024/interface/api/485578) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetails`
 * @项目ID 2024
 */
export interface ReservedProjectDetailsPostRequest {
  /**
   * 年度
   */
  year: number
  /**
   * 机构id
   */
  institutionId: number
  /**
   * 筛选口径:1--场景1，按计划年度；2--场景2，按合同年度',
   */
  type?: number
}

/**
 * 接口 [从大数据，获取中小企业预留相关数据↗](http://yapi.cai-inc.com/project/2024/interface/api/485578) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetails`
 * @项目ID 2024
 */
export interface ReservedProjectDetailsPostResponse {
  success?: boolean
  result?: {
    /**
     * 序号
     */
    sectionNo?: string
    /**
     * 项目名称
     */
    projectName?: string
    /**
     * 预留选项
     */
    reservedType?: {
      key?: string
      label?: string
    }
    /**
     * 中小预留比例  smesReservedRate
     */
    reservedPercent?: string
    /**
     * 面向中小企业预留  mediumPurchaseAmount
     */
    moitorItemAmount?: string
    /**
     * 面向小微企业预留  smallPurchaseAmount
     */
    smMonitorItemAmount?: string
    /**
     * 合同信息
     */
    contractInfo?: {
      /**
       * 合同名称
       */
      contractName?: string
      /**
       * 合同编号
       */
      contractNo?: string
      /**
       * 合同链接
       */
      contractUrl?: string
    }[]
    /**
     * 合同链接
     */
    contractUrl?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [从大数据，获取中小企业预留相关数据↗](http://yapi.cai-inc.com/project/2024/interface/api/485578) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetails`
 * @项目ID 2024
 */
type ReservedProjectDetailsPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/form/reservedProjectDetails',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [从大数据，获取中小企业预留相关数据↗](http://yapi.cai-inc.com/project/2024/interface/api/485578) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetails`
 * @项目ID 2024
 */
const reservedProjectDetailsPostRequestConfig: ReservedProjectDetailsPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_30,
  devUrl: devUrl_0_0_0_30,
  prodUrl: prodUrl_0_0_0_30,
  path: '/announcement/api/config/form/reservedProjectDetails',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_30,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'reservedProjectDetailsPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [从大数据，获取中小企业预留相关数据↗](http://yapi.cai-inc.com/project/2024/interface/api/485578) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetails`
 * @项目ID 2024
 */
export const reservedProjectDetailsPost = /*#__PURE__*/ (
  requestData: ReservedProjectDetailsPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ReservedProjectDetailsPostResponse>(
    prepare(reservedProjectDetailsPostRequestConfig, requestData),
    ...args,
  )
}

reservedProjectDetailsPost.requestConfig = reservedProjectDetailsPostRequestConfig

/**
 * 接口 [从大数据，获取中小企业预留相关数据--贵州↗](http://yapi.cai-inc.com/project/2024/interface/api/566154) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetailsForGuiZhou`
 * @项目ID 2024
 */
export interface ReservedProjectDetailsForGuiZhouPostRequest {
  /**
   * 年度
   */
  year: number
  /**
   * 机构id
   */
  institutionId: number
  /**
   * 筛选口径:1--场景1，按计划年度；2--场景2，按合同年度',
   */
  type?: number
}

/**
 * 接口 [从大数据，获取中小企业预留相关数据--贵州↗](http://yapi.cai-inc.com/project/2024/interface/api/566154) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetailsForGuiZhou`
 * @项目ID 2024
 */
export interface ReservedProjectDetailsForGuiZhouPostResponse {
  success?: boolean
  result?: {
    /**
     * 序号
     */
    sectionNo?: string
    /**
     * 项目名称
     */
    projectName?: string
    /**
     * 预留选项
     */
    reservedType?: {
      key?: string
      label?: string
    }
    /**
     * 中小预留比例  smesReservedRate
     */
    reservedPercent?: string
    /**
     * 面向中小企业预留  mediumPurchaseAmount
     */
    moitorItemAmount?: string
    /**
     * 面向小微企业预留  smallPurchaseAmount
     */
    smMonitorItemAmount?: string
    /**
     * 合同信息
     */
    contractInfo?: {
      /**
       * 合同名称
       */
      contractName?: string
      /**
       * 合同编号
       */
      contractNo?: string
      /**
       * 合同链接
       */
      contractUrl?: string
    }[]
    /**
     * 合同链接
     */
    contractUrl?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [从大数据，获取中小企业预留相关数据--贵州↗](http://yapi.cai-inc.com/project/2024/interface/api/566154) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetailsForGuiZhou`
 * @项目ID 2024
 */
type ReservedProjectDetailsForGuiZhouPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/form/reservedProjectDetailsForGuiZhou',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [从大数据，获取中小企业预留相关数据--贵州↗](http://yapi.cai-inc.com/project/2024/interface/api/566154) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetailsForGuiZhou`
 * @项目ID 2024
 */
const reservedProjectDetailsForGuiZhouPostRequestConfig: ReservedProjectDetailsForGuiZhouPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_30,
    devUrl: devUrl_0_0_0_30,
    prodUrl: prodUrl_0_0_0_30,
    path: '/announcement/api/config/form/reservedProjectDetailsForGuiZhou',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_30,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'reservedProjectDetailsForGuiZhouPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [从大数据，获取中小企业预留相关数据--贵州↗](http://yapi.cai-inc.com/project/2024/interface/api/566154) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementFormController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52238)
 * @请求头 `POST /announcement/api/config/form/reservedProjectDetailsForGuiZhou`
 * @项目ID 2024
 */
export const reservedProjectDetailsForGuiZhouPost = /*#__PURE__*/ (
  requestData: ReservedProjectDetailsForGuiZhouPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ReservedProjectDetailsForGuiZhouPostResponse>(
    prepare(reservedProjectDetailsForGuiZhouPostRequestConfig, requestData),
    ...args,
  )
}

reservedProjectDetailsForGuiZhouPost.requestConfig = reservedProjectDetailsForGuiZhouPostRequestConfig

/* prettier-ignore-end */
