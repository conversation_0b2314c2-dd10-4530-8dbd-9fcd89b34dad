/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_3 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_3 = '' as any
const prodUrl_0_0_0_3 = '' as any
const dataKey_0_0_0_3 = 'data' as any

/**
 * 接口 [公告类型环境同步校验↗](http://yapi.cai-inc.com/project/2024/interface/api/423426) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementTypeCheck`
 * @项目ID 2024
 */
export interface SyncAnnouncementTypeCheckPostRequest {
  /**
   * 公告类型
   */
  announcementTypeCodes: number[]
  /**
   * 目标环境
   */
  environments: string
}

/**
 * 接口 [公告类型环境同步校验↗](http://yapi.cai-inc.com/project/2024/interface/api/423426) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementTypeCheck`
 * @项目ID 2024
 */
export interface SyncAnnouncementTypeCheckPostResponse {
  success?: boolean
  result?: {
    code?: number
    codeDesc?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [公告类型环境同步校验↗](http://yapi.cai-inc.com/project/2024/interface/api/423426) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementTypeCheck`
 * @项目ID 2024
 */
type SyncAnnouncementTypeCheckPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/announcementType/syncAnnouncementTypeCheck',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告类型环境同步校验↗](http://yapi.cai-inc.com/project/2024/interface/api/423426) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementTypeCheck`
 * @项目ID 2024
 */
const syncAnnouncementTypeCheckPostRequestConfig: SyncAnnouncementTypeCheckPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/api/config/announcementType/syncAnnouncementTypeCheck',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncAnnouncementTypeCheckPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告类型环境同步校验↗](http://yapi.cai-inc.com/project/2024/interface/api/423426) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementTypeCheck`
 * @项目ID 2024
 */
export const syncAnnouncementTypeCheckPost = /*#__PURE__*/ (
  requestData: SyncAnnouncementTypeCheckPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SyncAnnouncementTypeCheckPostResponse>(
    prepare(syncAnnouncementTypeCheckPostRequestConfig, requestData),
    ...args,
  )
}

syncAnnouncementTypeCheckPost.requestConfig = syncAnnouncementTypeCheckPostRequestConfig

/**
 * 接口 [公告类型环境同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423434) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementType`
 * @项目ID 2024
 */
export interface SyncAnnouncementTypePostRequest {
  /**
   * 公告类型
   */
  announcementTypeCodes: number[]
  /**
   * 目标环境
   */
  environments: string
}

/**
 * 接口 [公告类型环境同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423434) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementType`
 * @项目ID 2024
 */
export interface SyncAnnouncementTypePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [公告类型环境同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423434) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementType`
 * @项目ID 2024
 */
type SyncAnnouncementTypePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/announcementType/syncAnnouncementType',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告类型环境同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423434) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementType`
 * @项目ID 2024
 */
const syncAnnouncementTypePostRequestConfig: SyncAnnouncementTypePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/api/config/announcementType/syncAnnouncementType',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncAnnouncementTypePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告类型环境同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423434) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `POST /announcement/api/config/announcementType/syncAnnouncementType`
 * @项目ID 2024
 */
export const syncAnnouncementTypePost = /*#__PURE__*/ (
  requestData: SyncAnnouncementTypePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SyncAnnouncementTypePostResponse>(prepare(syncAnnouncementTypePostRequestConfig, requestData), ...args)
}

syncAnnouncementTypePost.requestConfig = syncAnnouncementTypePostRequestConfig

/**
 * 接口 [获取所有环境信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423442) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `GET /announcement/api/config/announcementType/listAllEnvironments`
 * @项目ID 2024
 */
export interface ListAllEnvironmentsGetRequest {}

/**
 * 接口 [获取所有环境信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423442) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `GET /announcement/api/config/announcementType/listAllEnvironments`
 * @项目ID 2024
 */
export interface ListAllEnvironmentsGetResponse {
  success?: boolean
  result?: {
    /**
     * 环境evn唯一标识
     */
    env?: string
    /**
     * 环境名称
     */
    envName?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [获取所有环境信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423442) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `GET /announcement/api/config/announcementType/listAllEnvironments`
 * @项目ID 2024
 */
type ListAllEnvironmentsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/announcementType/listAllEnvironments',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取所有环境信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423442) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `GET /announcement/api/config/announcementType/listAllEnvironments`
 * @项目ID 2024
 */
const listAllEnvironmentsGetRequestConfig: ListAllEnvironmentsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_3,
  devUrl: devUrl_0_0_0_3,
  prodUrl: prodUrl_0_0_0_3,
  path: '/announcement/api/config/announcementType/listAllEnvironments',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_3,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAllEnvironmentsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取所有环境信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423442) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51046)
 * @请求头 `GET /announcement/api/config/announcementType/listAllEnvironments`
 * @项目ID 2024
 */
export const listAllEnvironmentsGet = /*#__PURE__*/ (
  requestData?: ListAllEnvironmentsGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAllEnvironmentsGetResponse>(prepare(listAllEnvironmentsGetRequestConfig, requestData), ...args)
}

listAllEnvironmentsGet.requestConfig = listAllEnvironmentsGetRequestConfig

/* prettier-ignore-end */
