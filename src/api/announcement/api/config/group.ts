/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_14 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_14 = '' as any
const prodUrl_0_0_0_14 = '' as any
const dataKey_0_0_0_14 = 'data' as any

/**
 * 接口 [查询所有分组↗](http://yapi.cai-inc.com/project/2024/interface/api/429986) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/listAnnouncementGroup`
 * @项目ID 2024
 */
export interface ListAnnouncementGroupGetRequest {
  id?: string
  districtCode?: string
}

/**
 * 接口 [查询所有分组↗](http://yapi.cai-inc.com/project/2024/interface/api/429986) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/listAnnouncementGroup`
 * @项目ID 2024
 */
export interface ListAnnouncementGroupGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键ID
     */
    id?: number
    /**
     * 分组名称
     */
    groupName?: string
    /**
     * 区划编码
     */
    districtCode?: string
    /**
     * 公告类型
     */
    announcementTypes?: string
    /**
     * 是否删除  1：删除  0未删除
     */
    isDelete?: boolean
    /**
     * 是否启用  1：启用  0：停用
     */
    status?: boolean
    /**
     * 创建人ID
     */
    createdId?: number
    /**
     * 创建人名称
     */
    createdName?: string
    /**
     * 更新人ID
     */
    updatedId?: number
    /**
     * 更新人名称
     */
    updatedName?: string
    /**
     * 创建时间
     */
    addTime?: string
    /**
     * 修改时间
     */
    modifiedTime?: string
    /**
     * 公告类型
     */
    announcementTypeDtoList?: {
      id?: number
      /**
       * 公告类型code
       */
      typeCode?: number
      /**
       * 公告类型名称
       */
      typeName?: string
      tradeType?: number
      bizAnnType?: number
      bizType?: number
      /**
       * 环境信息
       */
      env?: number
      /**
       * 父类型
       */
      parentType?: number
      createdAt?: string
      updatedAt?: string
      /**
       * 特性值
       */
      characteristicValues?: string
      /**
       * 排序号
       */
      orderNo?: number
      /**
       * 关联表单页面编号
       */
      formPageCode?: string
      /**
       * 流程key
       */
      processDefineKey?: string
      /**
       * 新增权限码
       */
      addPrivilegeCode?: string
      /**
       * 是否关联表单页面
       */
      isFormPage?: boolean
      /**
       * 公告标题模板特性值
       */
      titleCharacteristicValues?: string
      /**
       * 是否启用0-禁用，1-启用
       */
      isEnable?: boolean
      /**
       * 是否删除 0 - 正常 ; 1 - 删除
       */
      isDelete?: boolean
    }[]
    /**
     * 是否允许初始化
     */
    canInit?: boolean
    /**
     * 是否允许区划同步
     */
    canSyn?: boolean
    /**
     * 是否允许启动
     */
    canOpen?: boolean
    /**
     * 是否允许启动
     */
    currentOpenFlag?: boolean
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [查询所有分组↗](http://yapi.cai-inc.com/project/2024/interface/api/429986) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/listAnnouncementGroup`
 * @项目ID 2024
 */
type ListAnnouncementGroupGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/listAnnouncementGroup',
    'data',
    string,
    'id' | 'districtCode',
    false
  >
>

/**
 * 接口 [查询所有分组↗](http://yapi.cai-inc.com/project/2024/interface/api/429986) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/listAnnouncementGroup`
 * @项目ID 2024
 */
const listAnnouncementGroupGetRequestConfig: ListAnnouncementGroupGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/listAnnouncementGroup',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['id', 'districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAnnouncementGroupGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询所有分组↗](http://yapi.cai-inc.com/project/2024/interface/api/429986) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/listAnnouncementGroup`
 * @项目ID 2024
 */
export const listAnnouncementGroupGet = /*#__PURE__*/ (
  requestData: ListAnnouncementGroupGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAnnouncementGroupGetResponse>(prepare(listAnnouncementGroupGetRequestConfig, requestData), ...args)
}

listAnnouncementGroupGet.requestConfig = listAnnouncementGroupGetRequestConfig

/**
 * 接口 [启动\/停用↗](http://yapi.cai-inc.com/project/2024/interface/api/429994) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/openAnnouncementGroup`
 * @项目ID 2024
 */
export interface OpenAnnouncementGroupGetRequest {
  /**
   * 区划
   */
  districtCode: string
  /**
   * 状态
   */
  status: string
}

/**
 * 接口 [启动\/停用↗](http://yapi.cai-inc.com/project/2024/interface/api/429994) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/openAnnouncementGroup`
 * @项目ID 2024
 */
export interface OpenAnnouncementGroupGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [启动\/停用↗](http://yapi.cai-inc.com/project/2024/interface/api/429994) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/openAnnouncementGroup`
 * @项目ID 2024
 */
type OpenAnnouncementGroupGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/openAnnouncementGroup',
    'data',
    string,
    'districtCode' | 'status',
    false
  >
>

/**
 * 接口 [启动\/停用↗](http://yapi.cai-inc.com/project/2024/interface/api/429994) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/openAnnouncementGroup`
 * @项目ID 2024
 */
const openAnnouncementGroupGetRequestConfig: OpenAnnouncementGroupGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/openAnnouncementGroup',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['districtCode', 'status'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'openAnnouncementGroupGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [启动\/停用↗](http://yapi.cai-inc.com/project/2024/interface/api/429994) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/openAnnouncementGroup`
 * @项目ID 2024
 */
export const openAnnouncementGroupGet = /*#__PURE__*/ (
  requestData: OpenAnnouncementGroupGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<OpenAnnouncementGroupGetResponse>(prepare(openAnnouncementGroupGetRequestConfig, requestData), ...args)
}

openAnnouncementGroupGet.requestConfig = openAnnouncementGroupGetRequestConfig

/**
 * 接口 [分组初始化，初始化完成后是停用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430002) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/initAnnouncementGroup`
 * @项目ID 2024
 */
export interface InitAnnouncementGroupGetRequest {
  /**
   * 区划code
   */
  districtCode: string
}

/**
 * 接口 [分组初始化，初始化完成后是停用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430002) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/initAnnouncementGroup`
 * @项目ID 2024
 */
export interface InitAnnouncementGroupGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [分组初始化，初始化完成后是停用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430002) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/initAnnouncementGroup`
 * @项目ID 2024
 */
type InitAnnouncementGroupGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/initAnnouncementGroup',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [分组初始化，初始化完成后是停用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430002) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/initAnnouncementGroup`
 * @项目ID 2024
 */
const initAnnouncementGroupGetRequestConfig: InitAnnouncementGroupGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/initAnnouncementGroup',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'initAnnouncementGroupGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [分组初始化，初始化完成后是停用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430002) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/initAnnouncementGroup`
 * @项目ID 2024
 */
export const initAnnouncementGroupGet = /*#__PURE__*/ (
  requestData: InitAnnouncementGroupGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<InitAnnouncementGroupGetResponse>(prepare(initAnnouncementGroupGetRequestConfig, requestData), ...args)
}

initAnnouncementGroupGet.requestConfig = initAnnouncementGroupGetRequestConfig

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430018) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/announcementGroupSyn`
 * @项目ID 2024
 */
export interface AnnouncementGroupSynPostRequest {
  /**
   * 区划编码
   */
  districtCode: string
  /**
   * 目标区划区划编码
   */
  targetDistrictCode: string[]
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430018) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/announcementGroupSyn`
 * @项目ID 2024
 */
export interface AnnouncementGroupSynPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430018) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/announcementGroupSyn`
 * @项目ID 2024
 */
type AnnouncementGroupSynPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/announcementGroupSyn',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430018) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/announcementGroupSyn`
 * @项目ID 2024
 */
const announcementGroupSynPostRequestConfig: AnnouncementGroupSynPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/announcementGroupSyn',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementGroupSynPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/430018) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/announcementGroupSyn`
 * @项目ID 2024
 */
export const announcementGroupSynPost = /*#__PURE__*/ (
  requestData: AnnouncementGroupSynPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementGroupSynPostResponse>(prepare(announcementGroupSynPostRequestConfig, requestData), ...args)
}

announcementGroupSynPost.requestConfig = announcementGroupSynPostRequestConfig

/**
 * 接口 [查询分组配置页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/430026) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/checkButton`
 * @项目ID 2024
 */
export interface CheckButtonGetRequest {
  /**
   * 区划code
   */
  districtCode: string
}

/**
 * 接口 [查询分组配置页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/430026) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/checkButton`
 * @项目ID 2024
 */
export interface CheckButtonGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键ID
     */
    id?: number
    /**
     * 分组名称
     */
    groupName?: string
    /**
     * 区划编码
     */
    districtCode?: string
    /**
     * 公告类型
     */
    announcementTypes?: string
    /**
     * 是否删除  1：删除  0未删除
     */
    isDelete?: boolean
    /**
     * 是否启用  1：启用  0：停用
     */
    status?: boolean
    /**
     * 创建人ID
     */
    createdId?: number
    /**
     * 创建人名称
     */
    createdName?: string
    /**
     * 更新人ID
     */
    updatedId?: number
    /**
     * 更新人名称
     */
    updatedName?: string
    /**
     * 创建时间
     */
    addTime?: string
    /**
     * 修改时间
     */
    modifiedTime?: string
    /**
     * 公告类型
     */
    announcementTypeDtoList?: {
      id?: number
      /**
       * 公告类型code
       */
      typeCode?: number
      /**
       * 公告类型名称
       */
      typeName?: string
      tradeType?: number
      bizAnnType?: number
      bizType?: number
      /**
       * 环境信息
       */
      env?: number
      /**
       * 父类型
       */
      parentType?: number
      createdAt?: string
      updatedAt?: string
      /**
       * 特性值
       */
      characteristicValues?: string
      /**
       * 排序号
       */
      orderNo?: number
      /**
       * 关联表单页面编号
       */
      formPageCode?: string
      /**
       * 流程key
       */
      processDefineKey?: string
      /**
       * 新增权限码
       */
      addPrivilegeCode?: string
      /**
       * 是否关联表单页面
       */
      isFormPage?: boolean
      /**
       * 公告标题模板特性值
       */
      titleCharacteristicValues?: string
      /**
       * 是否启用0-禁用，1-启用
       */
      isEnable?: boolean
      /**
       * 是否删除 0 - 正常 ; 1 - 删除
       */
      isDelete?: boolean
    }[]
    /**
     * 是否允许初始化
     */
    canInit?: boolean
    /**
     * 是否允许区划同步
     */
    canSyn?: boolean
    /**
     * 是否允许启动
     */
    canOpen?: boolean
    /**
     * 是否允许启动
     */
    currentOpenFlag?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询分组配置页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/430026) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/checkButton`
 * @项目ID 2024
 */
type CheckButtonGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/checkButton',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [查询分组配置页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/430026) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/checkButton`
 * @项目ID 2024
 */
const checkButtonGetRequestConfig: CheckButtonGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/checkButton',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'checkButtonGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询分组配置页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/430026) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/checkButton`
 * @项目ID 2024
 */
export const checkButtonGet = /*#__PURE__*/ (requestData: CheckButtonGetRequest, ...args: UserRequestRestArgs) => {
  return request<CheckButtonGetResponse>(prepare(checkButtonGetRequestConfig, requestData), ...args)
}

checkButtonGet.requestConfig = checkButtonGetRequestConfig

/**
 * 接口 [新增公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430034) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/createAnnouncementGroup`
 * @项目ID 2024
 */
export interface CreateAnnouncementGroupPostRequest {
  /**
   * 主键ID
   */
  id?: number
  /**
   * 分组名称
   */
  groupName: string
  districtCode?: string
  /**
   * 公告类型
   */
  announcementTypes: number[]
}

/**
 * 接口 [新增公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430034) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/createAnnouncementGroup`
 * @项目ID 2024
 */
export interface CreateAnnouncementGroupPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [新增公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430034) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/createAnnouncementGroup`
 * @项目ID 2024
 */
type CreateAnnouncementGroupPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/createAnnouncementGroup',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [新增公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430034) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/createAnnouncementGroup`
 * @项目ID 2024
 */
const createAnnouncementGroupPostRequestConfig: CreateAnnouncementGroupPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/createAnnouncementGroup',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'createAnnouncementGroupPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [新增公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430034) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/createAnnouncementGroup`
 * @项目ID 2024
 */
export const createAnnouncementGroupPost = /*#__PURE__*/ (
  requestData: CreateAnnouncementGroupPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CreateAnnouncementGroupPostResponse>(
    prepare(createAnnouncementGroupPostRequestConfig, requestData),
    ...args,
  )
}

createAnnouncementGroupPost.requestConfig = createAnnouncementGroupPostRequestConfig

/**
 * 接口 [编辑公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430042) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/updateAnnouncementGroup`
 * @项目ID 2024
 */
export interface UpdateAnnouncementGroupPostRequest {
  /**
   * 主键ID
   */
  id?: number
  /**
   * 分组名称
   */
  groupName: string
  districtCode?: string
  /**
   * 公告类型
   */
  announcementTypes: number[]
}

/**
 * 接口 [编辑公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430042) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/updateAnnouncementGroup`
 * @项目ID 2024
 */
export interface UpdateAnnouncementGroupPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [编辑公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430042) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/updateAnnouncementGroup`
 * @项目ID 2024
 */
type UpdateAnnouncementGroupPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/updateAnnouncementGroup',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [编辑公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430042) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/updateAnnouncementGroup`
 * @项目ID 2024
 */
const updateAnnouncementGroupPostRequestConfig: UpdateAnnouncementGroupPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/updateAnnouncementGroup',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updateAnnouncementGroupPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑公告分组：↗](http://yapi.cai-inc.com/project/2024/interface/api/430042) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/updateAnnouncementGroup`
 * @项目ID 2024
 */
export const updateAnnouncementGroupPost = /*#__PURE__*/ (
  requestData: UpdateAnnouncementGroupPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<UpdateAnnouncementGroupPostResponse>(
    prepare(updateAnnouncementGroupPostRequestConfig, requestData),
    ...args,
  )
}

updateAnnouncementGroupPost.requestConfig = updateAnnouncementGroupPostRequestConfig

/**
 * 接口 [删除单条分组↗](http://yapi.cai-inc.com/project/2024/interface/api/430050) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/deleteAnnouncementGroup`
 * @项目ID 2024
 */
export interface DeleteAnnouncementGroupGetRequest {
  /**
   * 分组id
   */
  id: string
}

/**
 * 接口 [删除单条分组↗](http://yapi.cai-inc.com/project/2024/interface/api/430050) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/deleteAnnouncementGroup`
 * @项目ID 2024
 */
export interface DeleteAnnouncementGroupGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [删除单条分组↗](http://yapi.cai-inc.com/project/2024/interface/api/430050) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/deleteAnnouncementGroup`
 * @项目ID 2024
 */
type DeleteAnnouncementGroupGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/deleteAnnouncementGroup',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [删除单条分组↗](http://yapi.cai-inc.com/project/2024/interface/api/430050) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/deleteAnnouncementGroup`
 * @项目ID 2024
 */
const deleteAnnouncementGroupGetRequestConfig: DeleteAnnouncementGroupGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/deleteAnnouncementGroup',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deleteAnnouncementGroupGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除单条分组↗](http://yapi.cai-inc.com/project/2024/interface/api/430050) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/deleteAnnouncementGroup`
 * @项目ID 2024
 */
export const deleteAnnouncementGroupGet = /*#__PURE__*/ (
  requestData: DeleteAnnouncementGroupGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<DeleteAnnouncementGroupGetResponse>(
    prepare(deleteAnnouncementGroupGetRequestConfig, requestData),
    ...args,
  )
}

deleteAnnouncementGroupGet.requestConfig = deleteAnnouncementGroupGetRequestConfig

/**
 * 接口 [通过分组id查询分组下关联的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/430058) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementGroupById`
 * @项目ID 2024
 */
export interface SelectAnnouncementGroupByIdGetRequest {
  /**
   * 分组id
   */
  id: string
}

/**
 * 接口 [通过分组id查询分组下关联的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/430058) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementGroupById`
 * @项目ID 2024
 */
export interface SelectAnnouncementGroupByIdGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 公告类型code
     */
    typeCode?: number
    /**
     * 公告类型名称
     */
    typeName?: string
    tradeType?: number
    bizAnnType?: number
    bizType?: number
    /**
     * 环境信息
     */
    env?: number
    /**
     * 父类型
     */
    parentType?: number
    createdAt?: string
    updatedAt?: string
    /**
     * 特性值
     */
    characteristicValues?: string
    /**
     * 排序号
     */
    orderNo?: number
    /**
     * 关联表单页面编号
     */
    formPageCode?: string
    /**
     * 流程key
     */
    processDefineKey?: string
    /**
     * 新增权限码
     */
    addPrivilegeCode?: string
    /**
     * 是否关联表单页面
     */
    isFormPage?: boolean
    /**
     * 公告标题模板特性值
     */
    titleCharacteristicValues?: string
    /**
     * 是否启用0-禁用，1-启用
     */
    isEnable?: boolean
    /**
     * 是否删除 0 - 正常 ; 1 - 删除
     */
    isDelete?: boolean
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [通过分组id查询分组下关联的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/430058) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementGroupById`
 * @项目ID 2024
 */
type SelectAnnouncementGroupByIdGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/selectAnnouncementGroupById',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [通过分组id查询分组下关联的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/430058) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementGroupById`
 * @项目ID 2024
 */
const selectAnnouncementGroupByIdGetRequestConfig: SelectAnnouncementGroupByIdGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/selectAnnouncementGroupById',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectAnnouncementGroupByIdGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [通过分组id查询分组下关联的公告类型↗](http://yapi.cai-inc.com/project/2024/interface/api/430058) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementGroupById`
 * @项目ID 2024
 */
export const selectAnnouncementGroupByIdGet = /*#__PURE__*/ (
  requestData: SelectAnnouncementGroupByIdGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectAnnouncementGroupByIdGetResponse>(
    prepare(selectAnnouncementGroupByIdGetRequestConfig, requestData),
    ...args,
  )
}

selectAnnouncementGroupByIdGet.requestConfig = selectAnnouncementGroupByIdGetRequestConfig

/**
 * 接口 [通过区划查询可使用公告类型（剔除分组使用过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/430066) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementType`
 * @项目ID 2024
 */
export interface SelectAnnouncementTypeGetRequest {
  districtCode: string
  id?: string
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除分组使用过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/430066) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementType`
 * @项目ID 2024
 */
export interface SelectAnnouncementTypeGetResponse {
  success?: boolean
  result?: {
    id?: number
    distCode?: string
    name?: string
    typeId?: number
    parentId?: number
    expiryPeriod?: number
    systemType?: boolean
    isOut?: boolean
    isForcedControl?: boolean
    canRevoke?: boolean
    hasAttachment?: boolean
    procurementMethodCode?: string
    procurementMethodName?: string
    isValid?: boolean
    unopenReason?: string
    createTime?: string
    updateTime?: string
    termType?: boolean
    isRevocable?: boolean
    isObjection?: boolean
    objectionKeys?: string
    beObjectionKeys?: string
    checkAgencyTypes?: string
    env?: number
    secondEdit?: number
    customizedTitle?: number
    titlePrefix?: string
    titleSuffix?: string
    isNotify?: boolean
    orderNo?: number
    revokeTime?: number
    isAttachmentShow?: boolean
    canContentCensor?: boolean
    isFormPage?: boolean
    revocableButtonName?: string
    publishTypes?: string
    isDelete?: boolean
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除分组使用过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/430066) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementType`
 * @项目ID 2024
 */
type SelectAnnouncementTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/selectAnnouncementType',
    'data',
    string,
    'districtCode' | 'id',
    false
  >
>

/**
 * 接口 [通过区划查询可使用公告类型（剔除分组使用过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/430066) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementType`
 * @项目ID 2024
 */
const selectAnnouncementTypeGetRequestConfig: SelectAnnouncementTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/selectAnnouncementType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['districtCode', 'id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectAnnouncementTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除分组使用过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/430066) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `GET /announcement/api/config/group/selectAnnouncementType`
 * @项目ID 2024
 */
export const selectAnnouncementTypeGet = /*#__PURE__*/ (
  requestData: SelectAnnouncementTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectAnnouncementTypeGetResponse>(
    prepare(selectAnnouncementTypeGetRequestConfig, requestData),
    ...args,
  )
}

selectAnnouncementTypeGet.requestConfig = selectAnnouncementTypeGetRequestConfig

/**
 * 接口 [公告列表查询↗](http://yapi.cai-inc.com/project/2024/interface/api/430074) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/selectAnnouncement`
 * @项目ID 2024
 */
export interface SelectAnnouncementPostRequest {
  /**
   * 分组id
   */
  groupId?: number
  /**
   * 公告类型,如果不传则会根据isGovernAnnouncement字段去判断公告类型,如果传入则和isGovernAnnouncement取交集
   */
  announcementTypes?: string
  /**
   * 大类型,多个类型中间用逗号隔开
   */
  annBigTypes?: string
  /**
   * 公告标题(模糊查询)
   */
  title?: string
  /**
   * 项目编号
   */
  projectCode?: string
  /**
   * 项目名称(模糊查询)
   */
  projectName?: string
  /**
   * 发布时间--开始时间
   */
  releaseStartAt?: string
  /**
   * 发布时间--结束时间(如果不为空则会自动置为日期的23:59:59)
   */
  releaseEndAt?: string
  /**
   * 区划code
   */
  district?: string
  /**
   * 机构id
   */
  orgIds?: number[]
  /**
   * 0.我的全部
   * 1.待处理
   */
  type?: number
  /**
   * 是否政府采购公告:不传:全部, 0是，1否(该字段回去筛选公告类型)
   */
  isGovernAnnouncement?: number
  /**
   * 查询状态:0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束
   */
  queryStatus?: number
  /**
   * 关键词查询
   */
  contentKeyWord?: string
  pageNo?: number
  pageSize?: number
  operator?: any
}

/**
 * 接口 [公告列表查询↗](http://yapi.cai-inc.com/project/2024/interface/api/430074) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/selectAnnouncement`
 * @项目ID 2024
 */
export interface SelectAnnouncementPostResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      /**
       * 主键id
       */
      id?: number
      /**
       * 标题
       */
      title?: string
      /**
       * 行政区划code
       */
      district?: string
      /**
       * 行政区划名称
       */
      districtName?: string
      /**
       * 公告类型
       */
      announcementType?: number
      /**
       * 项目名称
       */
      projectName?: string
      /**
       * 项目编号
       */
      projectCode?: string
      /**
       * 公告元数据内容
       */
      metaData?: string
      /**
       * 公告内容 html
       */
      content?: string
      /**
       * 描述
       */
      description?: string
      /**
       * 状态 初始(1) 待审核(2) 待发布(3) 审核不通过(4) 已发布(5)
       */
      status?: number
      /**
       * 创建时间
       */
      createdAt?: string
      /**
       * 审核时间
       */
      checkedAt?: string
      /**
       * 发布时间
       */
      releasedAt?: string
      /**
       * 截止时间
       */
      expiredAt?: string
      /**
       * 审核意见
       */
      checkOpinion?: string
      /**
       * 创建人名称
       */
      creatorName?: string
      /**
       * 创建人id
       */
      creatorId?: number
      /**
       * 审核人名称
       */
      checkerName?: string
      /**
       * 审核人id
       */
      checkerId?: string
      /**
       * 创建人orgId
       */
      createOrgId?: number
      /**
       * 附件列表
       */
      attachments?: {
        /**
         * 文件url,OSS id
         */
        fileId?: string
        /**
         * 文件名称
         */
        name?: string
        /**
         * 文件大小
         */
        size?: number
        /**
         * 是否在外网显示
         */
        isShow?: boolean
        /**
         * 文件编码
         */
        fileCode?: string
        /**
         * 文件描述
         */
        fileDesc?: string
        /**
         * 原始FileID
         */
        sourceFileId?: string
      }[]
      /**
       * 历史记录
       */
      announcementFlowLogDtos?: {
        /**
         * 业务id
         */
        id?: number
        /**
         * 所属公告
         */
        announcementId?: number
        /**
         * 操作者id
         */
        operatorId?: number
        /**
         * 操作者姓名
         */
        operatorName?: string
        /**
         * 操作者动作
         */
        option?: string
        /**
         * 执行时间
         */
        createAt?: string
        /**
         * 审批意见
         */
        mark?: string
        /**
         * 操作人机构id
         */
        operatorOrgId?: number
        /**
         * 操作人机构名称
         */
        operatorOrgName?: string
        /**
         * 节点信息
         */
        nodeName?: string
        /**
         * 节点是否通过(默认true)
         */
        pass?: boolean
      }[]
      /**
       * 业务链接
       */
      url?: string
      /**
       * 业务id
       */
      serialNum?: string
      /**
       * 是否通过
       */
      pass?: boolean
      /**
       * 公告名称
       */
      announcementTypeName?: string
      /**
       * 公告发布类型@1:定时发布@2
       */
      pubType?: number
      /**
       * 记录公告展示天数
       */
      showDuration?: number
      /**
       * 是否存在其他同类公告
       */
      existOtherAnnouncement?: boolean
      checkFlag?: boolean
      /**
       * 外网链接
       */
      outUrl?: string
      /**
       * 是否显示修改、删除、撤回操作连接（true:显示;false:不显示）
       */
      modifyFlag?: boolean
      /**
       * 工作流ID
       */
      bizId?: string
      /**
       * 下个公告审核人(可以为空)
       */
      nextTaskUserId?: number
      /**
       * 当前审核人列表
       */
      checkUserList?: {
        /**
         * 公告id
         */
        announcementId?: number
        /**
         * 审核人employeeId,可能是employeeId,也可能是operatorId
         */
        checkUserId?: number
        /**
         * 审核人名称
         */
        checkUserName?: string
      }[]
      /**
       * 机构名称
       */
      orgName?: string
      /**
       * 处理码
       * 计算方法:AnnouncementDealEnum.getAnnouncementDealEnumList(dealCode)可以得出具体的处理集合
       */
      dealCode?: number
      options?: {
        canPublish?: boolean
        canDelete?: boolean
        canEdit?: boolean
        canRevoke?: boolean
        canCheck?: boolean
        /**
         * 是否发送短信通知
         */
        canNotify?: boolean
        /**
         * 是否可以撤回发布
         */
        canRevokePublish?: boolean
        canDownload?: boolean
        canPrint?: boolean
        /**
         * 是否可以重新推送
         */
        canRePush?: boolean
      }
      /**
       * 是否配置短信推送
       */
      isNotify?: boolean
      /**
       * 当前节点名称
       */
      currentTaskName?: string
      /**
       * 下个节点名称
       */
      nextTaskName?: string
      /**
       * 显示用状态(0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束)
       */
      showStatus?: number
      showStatusName?: string
      /**
       * 大类型
       */
      annBigType?: number
      /**
       * 系统类别：1-政府采购；2-企业购,参考枚举SystemTypeEnum
       */
      systemType?: number
      /**
       * 业务应用code
       */
      appCode?: string
      /**
       * 业务名称
       */
      appName?: string
      /**
       * 撤回公告是否需要逻辑删除
       */
      revokedLogicDelete?: boolean
      /**
       * 是否是涉密公告，false-否； true-是
       */
      secret?: boolean
      /**
       * 附件是否必填
       */
      hasAttachment?: boolean
      /**
       * 公告标识的id集合 announcement_identification
       */
      identificationIds?: number[]
      /**
       * 异议状态(0:无异议，1:有异议)
       */
      objectionState?: number
      /**
       * 公告发布人信息
       */
      creatorInfo?: string
      /**
       * 摘要信息
       */
      digest?: string
      /**
       * 公告更新时间
       */
      updateTime?: string
      /**
       * 扩展字段1， 用于各种公告填充扩展值
       */
      extendFirst?: string
      /**
       * 扩展字段2， 用于各种公告填充扩展值
       */
      extendSecond?: string
      /**
       * 扩展字段3， 用于各种公告填充扩展值
       */
      extendThird?: string
      /**
       * 扩展字段4， 用于各种公告填充扩展值
       */
      extendFour?: string
      /**
       * 扩展字段5， 用于各种公告填充扩展值
       */
      extendFive?: string
      /**
       * 表单页面编号
       */
      formPageCode?: string
      /**
       * 流程定义KEY
       */
      processDefineKey?: string
      /**
       * 节点ID
       */
      taskModelId?: string
      /**
       * 可以撤回发布的秒数
       */
      canRevokePublishSeconds?: number
      /**
       * 重推状态
       */
      rePushStatus?: number
      /**
       * 重推状态描述
       */
      rePushStatusName?: string
      /**
       * 公告标签信息
       */
      announcementTagDtoList?: {
        id?: number
        /**
         * 标签类型
         */
        tagType?: 1 | 2 | 3 | 4 | 5 | 6 | 7
        /**
         * 标签code
         */
        code?: string
        /**
         * 标签名称
         */
        name?: string
        /**
         * 标签来源类型 0-系统标签 1-业务标签
         * 当以下标签类型时有值
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#RULE_TAG
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#ACTION_TAG
         */
        tagSourceType?: number
        /**
         * 标签展示文案
         * 当以下标签类型时有值
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#RULE_TAG
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#ACTION_TAG
         */
        tagDisplay?: string
        /**
         * 标签颜色
         * 当以下标签类型时有值
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#RULE_TAG
         * cn.gov.zcy.announcement.dto.enums.AnnouncementTagEnum#ACTION_TAG
         */
        tagColor?: string
      }[]
      /**
       * 公告链接联系
       */
      announcementOutUrlDtoList?: {
        /**
         * 公告ID
         */
        announcementId?: number
        /**
         * 外网链接名称
         */
        outName?: string
        /**
         * 外网链接
         */
        outUrl?: string
      }[]
    }[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [公告列表查询↗](http://yapi.cai-inc.com/project/2024/interface/api/430074) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/selectAnnouncement`
 * @项目ID 2024
 */
type SelectAnnouncementPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/group/selectAnnouncement',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [公告列表查询↗](http://yapi.cai-inc.com/project/2024/interface/api/430074) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/selectAnnouncement`
 * @项目ID 2024
 */
const selectAnnouncementPostRequestConfig: SelectAnnouncementPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/config/group/selectAnnouncement',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectAnnouncementPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告列表查询↗](http://yapi.cai-inc.com/project/2024/interface/api/430074) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTypeGroupController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51179)
 * @请求头 `POST /announcement/api/config/group/selectAnnouncement`
 * @项目ID 2024
 */
export const selectAnnouncementPost = /*#__PURE__*/ (
  requestData: SelectAnnouncementPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectAnnouncementPostResponse>(prepare(selectAnnouncementPostRequestConfig, requestData), ...args)
}

selectAnnouncementPost.requestConfig = selectAnnouncementPostRequestConfig

/* prettier-ignore-end */
