/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_28 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_28 = '' as any
const prodUrl_0_0_0_28 = '' as any
const dataKey_0_0_0_28 = 'data' as any

/**
 * 接口 [查询配置向导列表页↗](http://yapi.cai-inc.com/project/2024/interface/api/464458) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listAnnouncementGuide`
 * @项目ID 2024
 */
export interface ListAnnouncementGuideGetRequest {
  /**
   * 区划
   */
  districtCode: string
  pageNo: string
  pageSize: string
}

/**
 * 接口 [查询配置向导列表页↗](http://yapi.cai-inc.com/project/2024/interface/api/464458) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listAnnouncementGuide`
 * @项目ID 2024
 */
export interface ListAnnouncementGuideGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否继承
     */
    isExtend?: boolean
    /**
     * 区划code
     */
    districtCode?: string
    /**
     * 区划名称
     */
    districtName?: string
    guideList?: {
      data?: {
        /**
         * 主键ID-关联表
         */
        id?: number
        /**
         * 公告类型
         */
        announcementType?: number
        /**
         * 公告类型名称
         */
        announcementTypeName?: string
        /**
         * 是否手工公告
         */
        manualAnnouncement?: boolean
        /**
         * 是否业务公告
         */
        businessAnnouncement?: boolean
        /**
         * 配置状态 1-已同步 0-未同步
         */
        status?: number
        /**
         * 节点信息
         */
        nodeList?: {
          id?: number
          /**
           * 节点code
           */
          nodeCode?: string
          /**
           * 节点名称
           */
          nodeName?: string
          /**
           * 节点状态  节点状态，0:待配置 1:已保存  2：已提交
           */
          status?: number
          /**
           * 节点序号
           */
          nodeOrder?: number
        }[]
        /**
         * 是否允许查看配置
         */
        canDetail?: boolean
        /**
         * 是否允许编辑配置
         */
        canEdit?: boolean
        /**
         * 是否删除配置
         */
        canDelete?: boolean
        /**
         * 是否可以同步
         */
        canEnable?: boolean
      }[]
      total?: number
      pageable?: {
        page?: number
        size?: number
        sort?: {
          field?: string
          direction?: 'ASC' | 'DESC'
        }[]
      }
    }
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询配置向导列表页↗](http://yapi.cai-inc.com/project/2024/interface/api/464458) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listAnnouncementGuide`
 * @项目ID 2024
 */
type ListAnnouncementGuideGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/listAnnouncementGuide',
    'data',
    string,
    'districtCode' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [查询配置向导列表页↗](http://yapi.cai-inc.com/project/2024/interface/api/464458) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listAnnouncementGuide`
 * @项目ID 2024
 */
const listAnnouncementGuideGetRequestConfig: ListAnnouncementGuideGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/listAnnouncementGuide',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['districtCode', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listAnnouncementGuideGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询配置向导列表页↗](http://yapi.cai-inc.com/project/2024/interface/api/464458) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listAnnouncementGuide`
 * @项目ID 2024
 */
export const listAnnouncementGuideGet = /*#__PURE__*/ (
  requestData: ListAnnouncementGuideGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ListAnnouncementGuideGetResponse>(prepare(listAnnouncementGuideGetRequestConfig, requestData), ...args)
}

listAnnouncementGuideGet.requestConfig = listAnnouncementGuideGetRequestConfig

/**
 * 接口 [删除↗](http://yapi.cai-inc.com/project/2024/interface/api/464466) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/deleteAnnouncementGuide`
 * @项目ID 2024
 */
export interface DeleteAnnouncementGuideGetRequest {
  /**
   * 关联关系表id
   */
  relationId: string
  /**
   * 区划
   */
  districtCode: string
}

/**
 * 接口 [删除↗](http://yapi.cai-inc.com/project/2024/interface/api/464466) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/deleteAnnouncementGuide`
 * @项目ID 2024
 */
export interface DeleteAnnouncementGuideGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [删除↗](http://yapi.cai-inc.com/project/2024/interface/api/464466) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/deleteAnnouncementGuide`
 * @项目ID 2024
 */
type DeleteAnnouncementGuideGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/deleteAnnouncementGuide',
    'data',
    string,
    'relationId' | 'districtCode',
    false
  >
>

/**
 * 接口 [删除↗](http://yapi.cai-inc.com/project/2024/interface/api/464466) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/deleteAnnouncementGuide`
 * @项目ID 2024
 */
const deleteAnnouncementGuideGetRequestConfig: DeleteAnnouncementGuideGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/deleteAnnouncementGuide',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['relationId', 'districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'deleteAnnouncementGuideGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [删除↗](http://yapi.cai-inc.com/project/2024/interface/api/464466) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/deleteAnnouncementGuide`
 * @项目ID 2024
 */
export const deleteAnnouncementGuideGet = /*#__PURE__*/ (
  requestData: DeleteAnnouncementGuideGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<DeleteAnnouncementGuideGetResponse>(
    prepare(deleteAnnouncementGuideGetRequestConfig, requestData),
    ...args,
  )
}

deleteAnnouncementGuideGet.requestConfig = deleteAnnouncementGuideGetRequestConfig

/**
 * 接口 [同步↗](http://yapi.cai-inc.com/project/2024/interface/api/464474) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/synchronous`
 * @项目ID 2024
 */
export interface SynchronousGetRequest {
  /**
   * 关联关系表id
   */
  relationId: string
  /**
   * 区划
   */
  districtCode: string
}

/**
 * 接口 [同步↗](http://yapi.cai-inc.com/project/2024/interface/api/464474) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/synchronous`
 * @项目ID 2024
 */
export interface SynchronousGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [同步↗](http://yapi.cai-inc.com/project/2024/interface/api/464474) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/synchronous`
 * @项目ID 2024
 */
type SynchronousGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/synchronous',
    'data',
    string,
    'relationId' | 'districtCode',
    false
  >
>

/**
 * 接口 [同步↗](http://yapi.cai-inc.com/project/2024/interface/api/464474) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/synchronous`
 * @项目ID 2024
 */
const synchronousGetRequestConfig: SynchronousGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/synchronous',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['relationId', 'districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'synchronousGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [同步↗](http://yapi.cai-inc.com/project/2024/interface/api/464474) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/synchronous`
 * @项目ID 2024
 */
export const synchronousGet = /*#__PURE__*/ (requestData: SynchronousGetRequest, ...args: UserRequestRestArgs) => {
  return request<SynchronousGetResponse>(prepare(synchronousGetRequestConfig, requestData), ...args)
}

synchronousGet.requestConfig = synchronousGetRequestConfig

/**
 * 接口 [新增公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/464482) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/createAnnouncementGuide`
 * @项目ID 2024
 */
export interface CreateAnnouncementGuidePostRequest {
  /**
   * relationId
   */
  id?: number
  /**
   * 区划编码
   */
  districtCode: string
  /**
   * 公告类型
   */
  announcementType: number
  /**
   * 是否手工公告
   */
  manualAnnouncement: boolean
  /**
   * 是否业务公告
   */
  businessAnnouncement: boolean
  /**
   * 手工公告关联的用户类型
   */
  userType?: string[]
}

/**
 * 接口 [新增公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/464482) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/createAnnouncementGuide`
 * @项目ID 2024
 */
export interface CreateAnnouncementGuidePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [新增公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/464482) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/createAnnouncementGuide`
 * @项目ID 2024
 */
type CreateAnnouncementGuidePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/createAnnouncementGuide',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [新增公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/464482) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/createAnnouncementGuide`
 * @项目ID 2024
 */
const createAnnouncementGuidePostRequestConfig: CreateAnnouncementGuidePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/createAnnouncementGuide',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'createAnnouncementGuidePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [新增公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/464482) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/createAnnouncementGuide`
 * @项目ID 2024
 */
export const createAnnouncementGuidePost = /*#__PURE__*/ (
  requestData: CreateAnnouncementGuidePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CreateAnnouncementGuidePostResponse>(
    prepare(createAnnouncementGuidePostRequestConfig, requestData),
    ...args,
  )
}

createAnnouncementGuidePost.requestConfig = createAnnouncementGuidePostRequestConfig

/**
 * 接口 [通过区划查询可使用公告类型（剔除关联过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/464490) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementType`
 * @项目ID 2024
 */
export interface SelectAnnouncementTypeGetRequest {
  districtCode: string
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除关联过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/464490) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementType`
 * @项目ID 2024
 */
export interface SelectAnnouncementTypeGetResponse {
  success?: boolean
  result?: {
    id?: number
    typeCode?: number
    typeName?: string
    parentType?: number
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除关联过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/464490) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementType`
 * @项目ID 2024
 */
type SelectAnnouncementTypeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/selectAnnouncementType',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [通过区划查询可使用公告类型（剔除关联过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/464490) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementType`
 * @项目ID 2024
 */
const selectAnnouncementTypeGetRequestConfig: SelectAnnouncementTypeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/selectAnnouncementType',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectAnnouncementTypeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [通过区划查询可使用公告类型（剔除关联过的类型）↗](http://yapi.cai-inc.com/project/2024/interface/api/464490) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementType`
 * @项目ID 2024
 */
export const selectAnnouncementTypeGet = /*#__PURE__*/ (
  requestData: SelectAnnouncementTypeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectAnnouncementTypeGetResponse>(
    prepare(selectAnnouncementTypeGetRequestConfig, requestData),
    ...args,
  )
}

selectAnnouncementTypeGet.requestConfig = selectAnnouncementTypeGetRequestConfig

/**
 * 接口 [查询所有节点↗](http://yapi.cai-inc.com/project/2024/interface/api/464498) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listNode`
 * @项目ID 2024
 */
export interface ListNodeGetRequest {
  /**
   * relationId
   */
  id: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询所有节点↗](http://yapi.cai-inc.com/project/2024/interface/api/464498) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listNode`
 * @项目ID 2024
 */
export interface ListNodeGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 节点code
     */
    nodeCode?: string
    /**
     * 节点名称
     */
    nodeName?: string
    /**
     * 节点状态  节点状态，0:待配置 1:已保存  2：已提交
     */
    status?: number
    /**
     * 节点序号
     */
    nodeOrder?: number
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [查询所有节点↗](http://yapi.cai-inc.com/project/2024/interface/api/464498) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listNode`
 * @项目ID 2024
 */
type ListNodeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/listNode',
    'data',
    string,
    'id' | 'type',
    false
  >
>

/**
 * 接口 [查询所有节点↗](http://yapi.cai-inc.com/project/2024/interface/api/464498) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listNode`
 * @项目ID 2024
 */
const listNodeGetRequestConfig: ListNodeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/listNode',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['id', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'listNodeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询所有节点↗](http://yapi.cai-inc.com/project/2024/interface/api/464498) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/listNode`
 * @项目ID 2024
 */
export const listNodeGet = /*#__PURE__*/ (requestData: ListNodeGetRequest, ...args: UserRequestRestArgs) => {
  return request<ListNodeGetResponse>(prepare(listNodeGetRequestConfig, requestData), ...args)
}

listNodeGet.requestConfig = listNodeGetRequestConfig

/**
 * 接口 [查询公告定义节点详情,节点CODE=AnnouncementDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464506) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDefinition`
 * @项目ID 2024
 */
export interface AnnouncementDefinitionGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询公告定义节点详情,节点CODE=AnnouncementDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464506) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDefinition`
 * @项目ID 2024
 */
export interface AnnouncementDefinitionGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告特性值
     */
    characteristicValues?: string
    /**
     * 公告标题特性值
     */
    titleCharacteristicValues?: string
    /**
     * 表单页面CODE
     */
    formPageCode?: string
    /**
     * 流程key
     */
    processDefineKey?: string
    /**
     * 细分权限码
     */
    addPrivilegeCode?: string
    /**
     * 业务字段定义
     */
    businessFieldDefinition?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告定义节点详情,节点CODE=AnnouncementDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464506) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDefinition`
 * @项目ID 2024
 */
type AnnouncementDefinitionGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementDefinition',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告定义节点详情,节点CODE=AnnouncementDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464506) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDefinition`
 * @项目ID 2024
 */
const announcementDefinitionGetRequestConfig: AnnouncementDefinitionGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementDefinition',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementDefinitionGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告定义节点详情,节点CODE=AnnouncementDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464506) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDefinition`
 * @项目ID 2024
 */
export const announcementDefinitionGet = /*#__PURE__*/ (
  requestData: AnnouncementDefinitionGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementDefinitionGetResponse>(
    prepare(announcementDefinitionGetRequestConfig, requestData),
    ...args,
  )
}

announcementDefinitionGet.requestConfig = announcementDefinitionGetRequestConfig

/**
 * 接口 [查询业务字段定义节点详情，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464514) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/businessFieldDefinition`
 * @项目ID 2024
 */
export interface BusinessFieldDefinitionGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询业务字段定义节点详情，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464514) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/businessFieldDefinition`
 * @项目ID 2024
 */
export interface BusinessFieldDefinitionGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告特性值
     */
    characteristicValues?: string
    /**
     * 公告标题特性值
     */
    titleCharacteristicValues?: string
    /**
     * 表单页面CODE
     */
    formPageCode?: string
    /**
     * 流程key
     */
    processDefineKey?: string
    /**
     * 细分权限码
     */
    addPrivilegeCode?: string
    /**
     * 业务字段定义
     */
    businessFieldDefinition?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询业务字段定义节点详情，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464514) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/businessFieldDefinition`
 * @项目ID 2024
 */
type BusinessFieldDefinitionGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/businessFieldDefinition',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询业务字段定义节点详情，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464514) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/businessFieldDefinition`
 * @项目ID 2024
 */
const businessFieldDefinitionGetRequestConfig: BusinessFieldDefinitionGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/businessFieldDefinition',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'businessFieldDefinitionGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询业务字段定义节点详情，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464514) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/businessFieldDefinition`
 * @项目ID 2024
 */
export const businessFieldDefinitionGet = /*#__PURE__*/ (
  requestData: BusinessFieldDefinitionGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<BusinessFieldDefinitionGetResponse>(
    prepare(businessFieldDefinitionGetRequestConfig, requestData),
    ...args,
  )
}

businessFieldDefinitionGet.requestConfig = businessFieldDefinitionGetRequestConfig

/**
 * 接口 [保存业务字段定义，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464522) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveBusinessFieldDefinition`
 * @项目ID 2024
 */
export interface SaveBusinessFieldDefinitionPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 业务字段定义
   */
  businessFieldDefinition?: string
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存业务字段定义，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464522) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveBusinessFieldDefinition`
 * @项目ID 2024
 */
export interface SaveBusinessFieldDefinitionPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存业务字段定义，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464522) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveBusinessFieldDefinition`
 * @项目ID 2024
 */
type SaveBusinessFieldDefinitionPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveBusinessFieldDefinition',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存业务字段定义，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464522) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveBusinessFieldDefinition`
 * @项目ID 2024
 */
const saveBusinessFieldDefinitionPostRequestConfig: SaveBusinessFieldDefinitionPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveBusinessFieldDefinition',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveBusinessFieldDefinitionPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存业务字段定义，节点CODE=BusinessFieldDefinition↗](http://yapi.cai-inc.com/project/2024/interface/api/464522) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveBusinessFieldDefinition`
 * @项目ID 2024
 */
export const saveBusinessFieldDefinitionPost = /*#__PURE__*/ (
  requestData: SaveBusinessFieldDefinitionPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveBusinessFieldDefinitionPostResponse>(
    prepare(saveBusinessFieldDefinitionPostRequestConfig, requestData),
    ...args,
  )
}

saveBusinessFieldDefinitionPost.requestConfig = saveBusinessFieldDefinitionPostRequestConfig

/**
 * 接口 [查询公告标题配置节点详情，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464530) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementTitleConfiguration`
 * @项目ID 2024
 */
export interface AnnouncementTitleConfigurationGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  type: string
}

/**
 * 接口 [查询公告标题配置节点详情，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464530) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementTitleConfiguration`
 * @项目ID 2024
 */
export interface AnnouncementTitleConfigurationGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告标题自定义配置
     */
    customizedTitle?: boolean
    /**
     * 特性值
     */
    characteristicValues?: string
    /**
     * 模板名称
     */
    templateName?: string
    /**
     * 模板id
     */
    templateId?: number
    /**
     * 模板URL
     */
    templateUrl?: string
    /**
     * 模板是否继承
     */
    isTemplateExtend?: boolean
    /**
     * 模板区划code
     */
    templateDistrictCode?: string
    /**
     * 模板区划名称
     */
    templateDistrictName?: string
    /**
     * 公告是否接入表单
     */
    isFormPage?: boolean
    /**
     * 正文开放自定义编辑，false：不可编辑， true：可编辑
     */
    secondEdit?: boolean
    /**
     * 表单页面Code
     */
    formPageCode?: string
    /**
     * 表单页面配置URL
     */
    formPageCodeUrl?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告标题配置节点详情，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464530) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementTitleConfiguration`
 * @项目ID 2024
 */
type AnnouncementTitleConfigurationGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementTitleConfiguration',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告标题配置节点详情，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464530) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementTitleConfiguration`
 * @项目ID 2024
 */
const announcementTitleConfigurationGetRequestConfig: AnnouncementTitleConfigurationGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementTitleConfiguration',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementTitleConfigurationGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告标题配置节点详情，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464530) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementTitleConfiguration`
 * @项目ID 2024
 */
export const announcementTitleConfigurationGet = /*#__PURE__*/ (
  requestData: AnnouncementTitleConfigurationGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementTitleConfigurationGetResponse>(
    prepare(announcementTitleConfigurationGetRequestConfig, requestData),
    ...args,
  )
}

announcementTitleConfigurationGet.requestConfig = announcementTitleConfigurationGetRequestConfig

/**
 * 接口 [保存公告标题配置，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464538) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementTitleConfiguration`
 * @项目ID 2024
 */
export interface SaveAnnouncementTitleConfigurationPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 公告标题自定义配置
   */
  customizedTitle: boolean
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存公告标题配置，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464538) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementTitleConfiguration`
 * @项目ID 2024
 */
export interface SaveAnnouncementTitleConfigurationPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告标题配置，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464538) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementTitleConfiguration`
 * @项目ID 2024
 */
type SaveAnnouncementTitleConfigurationPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveAnnouncementTitleConfiguration',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告标题配置，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464538) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementTitleConfiguration`
 * @项目ID 2024
 */
const saveAnnouncementTitleConfigurationPostRequestConfig: SaveAnnouncementTitleConfigurationPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_28,
    devUrl: devUrl_0_0_0_28,
    prodUrl: prodUrl_0_0_0_28,
    path: '/announcement/api/config/guide/saveAnnouncementTitleConfiguration',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_28,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'saveAnnouncementTitleConfigurationPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [保存公告标题配置，节点CODE=AnnouncementTitleConfiguration↗](http://yapi.cai-inc.com/project/2024/interface/api/464538) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementTitleConfiguration`
 * @项目ID 2024
 */
export const saveAnnouncementTitleConfigurationPost = /*#__PURE__*/ (
  requestData: SaveAnnouncementTitleConfigurationPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementTitleConfigurationPostResponse>(
    prepare(saveAnnouncementTitleConfigurationPostRequestConfig, requestData),
    ...args,
  )
}

saveAnnouncementTitleConfigurationPost.requestConfig = saveAnnouncementTitleConfigurationPostRequestConfig

/**
 * 接口 [查询内容主体节点详情，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464546) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSubject`
 * @项目ID 2024
 */
export interface ContentSubjectGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询内容主体节点详情，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464546) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSubject`
 * @项目ID 2024
 */
export interface ContentSubjectGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告标题自定义配置
     */
    customizedTitle?: boolean
    /**
     * 特性值
     */
    characteristicValues?: string
    /**
     * 模板名称
     */
    templateName?: string
    /**
     * 模板id
     */
    templateId?: number
    /**
     * 模板URL
     */
    templateUrl?: string
    /**
     * 模板是否继承
     */
    isTemplateExtend?: boolean
    /**
     * 模板区划code
     */
    templateDistrictCode?: string
    /**
     * 模板区划名称
     */
    templateDistrictName?: string
    /**
     * 公告是否接入表单
     */
    isFormPage?: boolean
    /**
     * 正文开放自定义编辑，false：不可编辑， true：可编辑
     */
    secondEdit?: boolean
    /**
     * 表单页面Code
     */
    formPageCode?: string
    /**
     * 表单页面配置URL
     */
    formPageCodeUrl?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询内容主体节点详情，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464546) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSubject`
 * @项目ID 2024
 */
type ContentSubjectGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/contentSubject',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询内容主体节点详情，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464546) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSubject`
 * @项目ID 2024
 */
const contentSubjectGetRequestConfig: ContentSubjectGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/contentSubject',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'contentSubjectGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询内容主体节点详情，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464546) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSubject`
 * @项目ID 2024
 */
export const contentSubjectGet = /*#__PURE__*/ (
  requestData: ContentSubjectGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ContentSubjectGetResponse>(prepare(contentSubjectGetRequestConfig, requestData), ...args)
}

contentSubjectGet.requestConfig = contentSubjectGetRequestConfig

/**
 * 接口 [保存公告正文节点，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464554) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSubject`
 * @项目ID 2024
 */
export interface SaveContentSubjectPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 公告是否接入表单
   */
  isFormPage: boolean
  /**
   * 正文开放自定义编辑，false：不可编辑， true：可编辑
   */
  secondEdit: boolean
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存公告正文节点，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464554) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSubject`
 * @项目ID 2024
 */
export interface SaveContentSubjectPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告正文节点，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464554) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSubject`
 * @项目ID 2024
 */
type SaveContentSubjectPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveContentSubject',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告正文节点，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464554) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSubject`
 * @项目ID 2024
 */
const saveContentSubjectPostRequestConfig: SaveContentSubjectPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveContentSubject',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveContentSubjectPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告正文节点，节点CODE=ContentSubject↗](http://yapi.cai-inc.com/project/2024/interface/api/464554) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSubject`
 * @项目ID 2024
 */
export const saveContentSubjectPost = /*#__PURE__*/ (
  requestData: SaveContentSubjectPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveContentSubjectPostResponse>(prepare(saveContentSubjectPostRequestConfig, requestData), ...args)
}

saveContentSubjectPost.requestConfig = saveContentSubjectPostRequestConfig

/**
 * 接口 [查询内容监管节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464562) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSupervision`
 * @项目ID 2024
 */
export interface ContentSupervisionGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  type: string
}

/**
 * 接口 [查询内容监管节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464562) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSupervision`
 * @项目ID 2024
 */
export interface ContentSupervisionGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否开启敏感词校验
     */
    canContentCensor?: boolean
    /**
     * 敏感词校验规则配置说明
     */
    contentCensorRuleExplain?: string
    /**
     * 监管参数配置-获取配置列表
     */
    announcementSupervisorDTOS?: {
      id?: number
      /**
       * 行政区划编码
       */
      districtCode?: string
      /**
       * 公告类型type
       */
      announcementTypes?: string
      /**
       * 组名
       */
      groupName?: string
      /**
       * 公告类型名称
       */
      announcementTypeName?: string
      /**
       * 系统参数
       */
      announcementSupervisorEnvDTO?: {
        /**
         * 同级政府采购监督管理部门
         */
        regulatoryOrgName?: string
        /**
         * 传真
         */
        regulatoryContactFax?: string
        /**
         * 监督投诉电话
         */
        regulatoryContactPhone?: string
        /**
         * 地址
         */
        regulatoryContactAddr?: string
        /**
         * 联系人
         */
        regulatoryContactPerson?: string
      }
    }[]
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询内容监管节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464562) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSupervision`
 * @项目ID 2024
 */
type ContentSupervisionGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/contentSupervision',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询内容监管节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464562) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSupervision`
 * @项目ID 2024
 */
const contentSupervisionGetRequestConfig: ContentSupervisionGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/contentSupervision',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'contentSupervisionGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询内容监管节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464562) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/contentSupervision`
 * @项目ID 2024
 */
export const contentSupervisionGet = /*#__PURE__*/ (
  requestData: ContentSupervisionGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ContentSupervisionGetResponse>(prepare(contentSupervisionGetRequestConfig, requestData), ...args)
}

contentSupervisionGet.requestConfig = contentSupervisionGetRequestConfig

/**
 * 接口 [保存内容监管，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464570) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSupervision`
 * @项目ID 2024
 */
export interface SaveContentSupervisionPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 是否开启敏感词校验
   */
  canContentCensor: boolean
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存内容监管，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464570) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSupervision`
 * @项目ID 2024
 */
export interface SaveContentSupervisionPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存内容监管，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464570) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSupervision`
 * @项目ID 2024
 */
type SaveContentSupervisionPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveContentSupervision',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存内容监管，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464570) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSupervision`
 * @项目ID 2024
 */
const saveContentSupervisionPostRequestConfig: SaveContentSupervisionPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveContentSupervision',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveContentSupervisionPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存内容监管，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464570) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveContentSupervision`
 * @项目ID 2024
 */
export const saveContentSupervisionPost = /*#__PURE__*/ (
  requestData: SaveContentSupervisionPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveContentSupervisionPostResponse>(
    prepare(saveContentSupervisionPostRequestConfig, requestData),
    ...args,
  )
}

saveContentSupervisionPost.requestConfig = saveContentSupervisionPostRequestConfig

/**
 * 接口 [查询流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464578) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processDefinition`
 * @项目ID 2024
 */
export interface ProcessDefinitionGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464578) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processDefinition`
 * @项目ID 2024
 */
export interface ProcessDefinitionGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否平台级 true：平台级  false：区划级
     */
    isPlatform?: boolean
    /**
     * 是否独立流程配置--天行
     */
    isIndependenceProcess?: boolean
    /**
     * 流程key
     */
    processDefineKey?: string
    /**
     * 流程名称
     */
    processDefineKeyName?: string
    /**
     * 校验代理机构公告发布
     */
    isCheckAgencyTypes?: boolean
    /**
     * 校验代理机构类别
     */
    checkAgencyTypes?: string[]
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464578) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processDefinition`
 * @项目ID 2024
 */
type ProcessDefinitionGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/processDefinition',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464578) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processDefinition`
 * @项目ID 2024
 */
const processDefinitionGetRequestConfig: ProcessDefinitionGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/processDefinition',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'processDefinitionGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/464578) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processDefinition`
 * @项目ID 2024
 */
export const processDefinitionGet = /*#__PURE__*/ (
  requestData: ProcessDefinitionGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ProcessDefinitionGetResponse>(prepare(processDefinitionGetRequestConfig, requestData), ...args)
}

processDefinitionGet.requestConfig = processDefinitionGetRequestConfig

/**
 * 接口 [查询流程套用节点详情，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464586) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processApplication`
 * @项目ID 2024
 */
export interface ProcessApplicationGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询流程套用节点详情，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464586) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processApplication`
 * @项目ID 2024
 */
export interface ProcessApplicationGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否平台级 true：平台级  false：区划级
     */
    isPlatform?: boolean
    /**
     * 是否独立流程配置--天行
     */
    isIndependenceProcess?: boolean
    /**
     * 流程key
     */
    processDefineKey?: string
    /**
     * 流程名称
     */
    processDefineKeyName?: string
    /**
     * 校验代理机构公告发布
     */
    isCheckAgencyTypes?: boolean
    /**
     * 校验代理机构类别
     */
    checkAgencyTypes?: string[]
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询流程套用节点详情，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464586) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processApplication`
 * @项目ID 2024
 */
type ProcessApplicationGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/processApplication',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询流程套用节点详情，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464586) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processApplication`
 * @项目ID 2024
 */
const processApplicationGetRequestConfig: ProcessApplicationGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/processApplication',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'processApplicationGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询流程套用节点详情，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464586) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/processApplication`
 * @项目ID 2024
 */
export const processApplicationGet = /*#__PURE__*/ (
  requestData: ProcessApplicationGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ProcessApplicationGetResponse>(prepare(processApplicationGetRequestConfig, requestData), ...args)
}

processApplicationGet.requestConfig = processApplicationGetRequestConfig

/**
 * 接口 [保存流程套用，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464594) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessApplication`
 * @项目ID 2024
 */
export interface SaveProcessApplicationPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 校验代理机构公告发布
   */
  isCheckAgencyTypes?: boolean
  /**
   * 校验代理机构类别
   */
  checkAgencyTypes?: string[]
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存流程套用，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464594) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessApplication`
 * @项目ID 2024
 */
export interface SaveProcessApplicationPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存流程套用，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464594) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessApplication`
 * @项目ID 2024
 */
type SaveProcessApplicationPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveProcessApplication',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存流程套用，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464594) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessApplication`
 * @项目ID 2024
 */
const saveProcessApplicationPostRequestConfig: SaveProcessApplicationPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveProcessApplication',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveProcessApplicationPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存流程套用，节点CODE=ProcessApplication↗](http://yapi.cai-inc.com/project/2024/interface/api/464594) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessApplication`
 * @项目ID 2024
 */
export const saveProcessApplicationPost = /*#__PURE__*/ (
  requestData: SaveProcessApplicationPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveProcessApplicationPostResponse>(
    prepare(saveProcessApplicationPostRequestConfig, requestData),
    ...args,
  )
}

saveProcessApplicationPost.requestConfig = saveProcessApplicationPostRequestConfig

/**
 * 接口 [查询公告选项节点详情，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464602) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementOptions`
 * @项目ID 2024
 */
export interface AnnouncementOptionsGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询公告选项节点详情，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464602) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementOptions`
 * @项目ID 2024
 */
export interface AnnouncementOptionsGetResponse {
  success?: boolean
  result?: {
    /**
     * 公示期计算方式 0-工作日，1-自然日
     */
    termType?: number
    /**
     * 公告天数
     */
    expiryPeriod?: number
    /**
     * 采购方式
     */
    procurementMethod?: {
      code?: string
      codeDesc?: string
    }[]
    /**
     * 发布时间方案设置 2-审核完成发布 1-定时发布 3-手动发布
     */
    publishTypes?: string
    /**
     * 附件是否展示
     */
    attachmentShow?: boolean
    /**
     * 附件是否必填
     */
    hasAttachment?: boolean
    /**
     * 是否可以发起异议，false：不可异议，true：可异议
     */
    canObject?: boolean
    /**
     * 被质疑对象信息字段，多个逗号分隔
     */
    objectionKeys?: string
    /**
     * 质疑对象信息字段，多个逗号分隔
     */
    beObjectionKeys?: string
    /**
     * 公告发布后是否可撤回
     */
    canRevoke?: boolean
    /**
     * 公告发布撤回时间
     */
    revokeTime?: number
    /**
     * 是否允许取消公告
     */
    isRevocable?: boolean
    /**
     * 取消公告按钮名称
     */
    revocableButtonName?: string
    /**
     * 是否强控   手工公告关联采购计划
     */
    isForcedControl?: boolean
    /**
     * 白名单区划-查看
     */
    whiteListDistrict?: {
      code?: string
      codeDesc?: string
    }[]
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告选项节点详情，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464602) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementOptions`
 * @项目ID 2024
 */
type AnnouncementOptionsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementOptions',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告选项节点详情，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464602) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementOptions`
 * @项目ID 2024
 */
const announcementOptionsGetRequestConfig: AnnouncementOptionsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementOptions',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementOptionsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告选项节点详情，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464602) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementOptions`
 * @项目ID 2024
 */
export const announcementOptionsGet = /*#__PURE__*/ (
  requestData: AnnouncementOptionsGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementOptionsGetResponse>(prepare(announcementOptionsGetRequestConfig, requestData), ...args)
}

announcementOptionsGet.requestConfig = announcementOptionsGetRequestConfig

/**
 * 接口 [保存公告选项，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464610) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementOptions`
 * @项目ID 2024
 */
export interface SaveAnnouncementOptionsPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 1-保存  2-提交
   */
  type?: number
  /**
   * 公示期计算方式 0-工作日，1-自然日
   */
  termType: number
  /**
   * 公告天数
   */
  expiryPeriod: number
  /**
   * 采购方式
   */
  procurementMethod: {
    code?: string
    codeDesc?: string
  }[]
  /**
   * 发布时间方案设置 2-审核完成发布 1-定时发布 3-手动发布
   */
  publishTypes: string
  /**
   * 附件是否展示
   */
  attachmentShow: boolean
  /**
   * 附件是否必填
   */
  hasAttachment?: boolean
  /**
   * 是否可以发起异议，false：不可异议，true：可异议
   */
  canObject: boolean
  /**
   * 被质疑对象信息字段，多个逗号分隔
   */
  objectionKeys?: string
  /**
   * 质疑对象信息字段，多个逗号分隔
   */
  beObjectionKeys?: string
  /**
   * 公告发布后是否可撤回
   */
  canRevoke: boolean
  /**
   * 公告发布撤回时间
   */
  revokeTime?: number
  /**
   * 是否允许取消公告
   */
  isRevocable: boolean
  /**
   * 取消公告按钮名称
   */
  revocableButtonName?: string
  /**
   * 是否强控   手工公告关联采购计划
   */
  isForcedControl?: boolean
  /**
   * 白名单区划-保存
   */
  whiteListDistrictCode?: string[]
}

/**
 * 接口 [保存公告选项，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464610) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementOptions`
 * @项目ID 2024
 */
export interface SaveAnnouncementOptionsPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告选项，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464610) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementOptions`
 * @项目ID 2024
 */
type SaveAnnouncementOptionsPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveAnnouncementOptions',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告选项，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464610) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementOptions`
 * @项目ID 2024
 */
const saveAnnouncementOptionsPostRequestConfig: SaveAnnouncementOptionsPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveAnnouncementOptions',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnouncementOptionsPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告选项，节点CODE=AnnouncementOptions↗](http://yapi.cai-inc.com/project/2024/interface/api/464610) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementOptions`
 * @项目ID 2024
 */
export const saveAnnouncementOptionsPost = /*#__PURE__*/ (
  requestData: SaveAnnouncementOptionsPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementOptionsPostResponse>(
    prepare(saveAnnouncementOptionsPostRequestConfig, requestData),
    ...args,
  )
}

saveAnnouncementOptionsPost.requestConfig = saveAnnouncementOptionsPostRequestConfig

/**
 * 接口 [查询公告推送节点详情，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464618) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementPush`
 * @项目ID 2024
 */
export interface AnnouncementPushGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询公告推送节点详情，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464618) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementPush`
 * @项目ID 2024
 */
export interface AnnouncementPushGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否开启短信通知 列表页会有发送短信按钮
     */
    isNotify?: boolean
    /**
     * 是否开放到外网
     */
    isOut?: boolean
    /**
     * 站点配置
     */
    configSiteRelationDtoList?: {
      /**
       * 主键ID
       */
      id?: number
      /**
       * 区划code
       */
      districtCode?: string
      /**
       * 站点ID
       */
      siteId?: number
      /**
       * 环境编码
       */
      envCode?: string
      /**
       * 公告站点ID
       */
      announcementSiteId?: number
      /**
       * 站点是否前台展示 0-不展示 1-展示 默认1
       */
      isSiteDisplay?: boolean
      /**
       * 前台展示排序 默认0
       */
      siteSort?: number
      /**
       * 网站域名
       */
      siteDomain?: string
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点管理地址
       */
      siteManagerAddress?: string
    }[]
    /**
     * 是否启用分组配置
     */
    isOpenGroup?: boolean
    /**
     * 所在分组ID
     */
    groupId?: number
    /**
     * 所在分组名称
     */
    groupName?: string
    /**
     * 分组配置URL
     */
    groupUrl?: string
    /**
     * 租户订购应用URL
     */
    orderApplicationsUrl?: string
    /**
     * 岗位订购菜单URL
     */
    orderMenuUrl?: string
    /**
     * 用户订购功能URL
     */
    orderFunctionUrl?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告推送节点详情，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464618) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementPush`
 * @项目ID 2024
 */
type AnnouncementPushGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementPush',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告推送节点详情，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464618) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementPush`
 * @项目ID 2024
 */
const announcementPushGetRequestConfig: AnnouncementPushGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementPush',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementPushGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告推送节点详情，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464618) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementPush`
 * @项目ID 2024
 */
export const announcementPushGet = /*#__PURE__*/ (
  requestData: AnnouncementPushGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementPushGetResponse>(prepare(announcementPushGetRequestConfig, requestData), ...args)
}

announcementPushGet.requestConfig = announcementPushGetRequestConfig

/**
 * 接口 [保存公告推送，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464626) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementPush`
 * @项目ID 2024
 */
export interface SaveAnnouncementPushPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 是否开启短信通知 列表页会有发送短信按钮
   */
  isNotify?: boolean
  /**
   * 是否开放到外网
   */
  isOut?: boolean
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存公告推送，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464626) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementPush`
 * @项目ID 2024
 */
export interface SaveAnnouncementPushPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告推送，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464626) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementPush`
 * @项目ID 2024
 */
type SaveAnnouncementPushPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveAnnouncementPush',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告推送，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464626) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementPush`
 * @项目ID 2024
 */
const saveAnnouncementPushPostRequestConfig: SaveAnnouncementPushPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveAnnouncementPush',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnouncementPushPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告推送，节点CODE=AnnouncementPush↗](http://yapi.cai-inc.com/project/2024/interface/api/464626) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementPush`
 * @项目ID 2024
 */
export const saveAnnouncementPushPost = /*#__PURE__*/ (
  requestData: SaveAnnouncementPushPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementPushPostResponse>(prepare(saveAnnouncementPushPostRequestConfig, requestData), ...args)
}

saveAnnouncementPushPost.requestConfig = saveAnnouncementPushPostRequestConfig

/**
 * 接口 [查询公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/464634) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDisplay`
 * @项目ID 2024
 */
export interface AnnouncementDisplayGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/464634) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDisplay`
 * @项目ID 2024
 */
export interface AnnouncementDisplayGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否开启短信通知 列表页会有发送短信按钮
     */
    isNotify?: boolean
    /**
     * 是否开放到外网
     */
    isOut?: boolean
    /**
     * 站点配置
     */
    configSiteRelationDtoList?: {
      /**
       * 主键ID
       */
      id?: number
      /**
       * 区划code
       */
      districtCode?: string
      /**
       * 站点ID
       */
      siteId?: number
      /**
       * 环境编码
       */
      envCode?: string
      /**
       * 公告站点ID
       */
      announcementSiteId?: number
      /**
       * 站点是否前台展示 0-不展示 1-展示 默认1
       */
      isSiteDisplay?: boolean
      /**
       * 前台展示排序 默认0
       */
      siteSort?: number
      /**
       * 网站域名
       */
      siteDomain?: string
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点管理地址
       */
      siteManagerAddress?: string
    }[]
    /**
     * 是否启用分组配置
     */
    isOpenGroup?: boolean
    /**
     * 所在分组ID
     */
    groupId?: number
    /**
     * 所在分组名称
     */
    groupName?: string
    /**
     * 分组配置URL
     */
    groupUrl?: string
    /**
     * 租户订购应用URL
     */
    orderApplicationsUrl?: string
    /**
     * 岗位订购菜单URL
     */
    orderMenuUrl?: string
    /**
     * 用户订购功能URL
     */
    orderFunctionUrl?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/464634) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDisplay`
 * @项目ID 2024
 */
type AnnouncementDisplayGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementDisplay',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/464634) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDisplay`
 * @项目ID 2024
 */
const announcementDisplayGetRequestConfig: AnnouncementDisplayGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementDisplay',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementDisplayGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/464634) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementDisplay`
 * @项目ID 2024
 */
export const announcementDisplayGet = /*#__PURE__*/ (
  requestData: AnnouncementDisplayGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementDisplayGetResponse>(prepare(announcementDisplayGetRequestConfig, requestData), ...args)
}

announcementDisplayGet.requestConfig = announcementDisplayGetRequestConfig

/**
 * 接口 [查询公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/464642) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementEntrance`
 * @项目ID 2024
 */
export interface AnnouncementEntranceGetRequest {
  /**
   * 节点id
   */
  nodeId: string
  /**
   * 1-查看配置  2-进入配置
   */
  type: string
}

/**
 * 接口 [查询公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/464642) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementEntrance`
 * @项目ID 2024
 */
export interface AnnouncementEntranceGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否开启短信通知 列表页会有发送短信按钮
     */
    isNotify?: boolean
    /**
     * 是否开放到外网
     */
    isOut?: boolean
    /**
     * 站点配置
     */
    configSiteRelationDtoList?: {
      /**
       * 主键ID
       */
      id?: number
      /**
       * 区划code
       */
      districtCode?: string
      /**
       * 站点ID
       */
      siteId?: number
      /**
       * 环境编码
       */
      envCode?: string
      /**
       * 公告站点ID
       */
      announcementSiteId?: number
      /**
       * 站点是否前台展示 0-不展示 1-展示 默认1
       */
      isSiteDisplay?: boolean
      /**
       * 前台展示排序 默认0
       */
      siteSort?: number
      /**
       * 网站域名
       */
      siteDomain?: string
      /**
       * 站点名称
       */
      siteName?: string
      /**
       * 站点管理地址
       */
      siteManagerAddress?: string
    }[]
    /**
     * 是否启用分组配置
     */
    isOpenGroup?: boolean
    /**
     * 所在分组ID
     */
    groupId?: number
    /**
     * 所在分组名称
     */
    groupName?: string
    /**
     * 分组配置URL
     */
    groupUrl?: string
    /**
     * 租户订购应用URL
     */
    orderApplicationsUrl?: string
    /**
     * 岗位订购菜单URL
     */
    orderMenuUrl?: string
    /**
     * 用户订购功能URL
     */
    orderFunctionUrl?: string
    /**
     * 公告大类Code
     */
    bigAnnouncementTypeCode?: number
    /**
     * 公告大类名称
     */
    bigAnnouncementTypeName?: string
    /**
     * 公告类型Code
     */
    announcementTypeCode?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 涉及跳转url
     */
    url?: {
      key?: string
    }
    /**
     * 保存配置按钮
     */
    canSave?: boolean
    /**
     * 提交配置按钮
     */
    canSubmit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/464642) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementEntrance`
 * @项目ID 2024
 */
type AnnouncementEntranceGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementEntrance',
    'data',
    string,
    'nodeId' | 'type',
    false
  >
>

/**
 * 接口 [查询公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/464642) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementEntrance`
 * @项目ID 2024
 */
const announcementEntranceGetRequestConfig: AnnouncementEntranceGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementEntrance',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['nodeId', 'type'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementEntranceGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/464642) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementEntrance`
 * @项目ID 2024
 */
export const announcementEntranceGet = /*#__PURE__*/ (
  requestData: AnnouncementEntranceGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementEntranceGetResponse>(prepare(announcementEntranceGetRequestConfig, requestData), ...args)
}

announcementEntranceGet.requestConfig = announcementEntranceGetRequestConfig

/**
 * 接口 [保存流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/465434) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessDefinition`
 * @项目ID 2024
 */
export interface SaveProcessDefinitionPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/465434) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessDefinition`
 * @项目ID 2024
 */
export interface SaveProcessDefinitionPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/465434) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessDefinition`
 * @项目ID 2024
 */
type SaveProcessDefinitionPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveProcessDefinition',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/465434) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessDefinition`
 * @项目ID 2024
 */
const saveProcessDefinitionPostRequestConfig: SaveProcessDefinitionPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveProcessDefinition',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveProcessDefinitionPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存流程定义节点详情，节点CODE=ContentSupervision↗](http://yapi.cai-inc.com/project/2024/interface/api/465434) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveProcessDefinition`
 * @项目ID 2024
 */
export const saveProcessDefinitionPost = /*#__PURE__*/ (
  requestData: SaveProcessDefinitionPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveProcessDefinitionPostResponse>(
    prepare(saveProcessDefinitionPostRequestConfig, requestData),
    ...args,
  )
}

saveProcessDefinitionPost.requestConfig = saveProcessDefinitionPostRequestConfig

/**
 * 接口 [保存公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/465442) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementDisplay`
 * @项目ID 2024
 */
export interface SaveAnnouncementDisplayPostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/465442) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementDisplay`
 * @项目ID 2024
 */
export interface SaveAnnouncementDisplayPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/465442) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementDisplay`
 * @项目ID 2024
 */
type SaveAnnouncementDisplayPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveAnnouncementDisplay',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/465442) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementDisplay`
 * @项目ID 2024
 */
const saveAnnouncementDisplayPostRequestConfig: SaveAnnouncementDisplayPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveAnnouncementDisplay',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnouncementDisplayPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告展现节点详情，节点CODE=AnnouncementDisplay↗](http://yapi.cai-inc.com/project/2024/interface/api/465442) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementDisplay`
 * @项目ID 2024
 */
export const saveAnnouncementDisplayPost = /*#__PURE__*/ (
  requestData: SaveAnnouncementDisplayPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementDisplayPostResponse>(
    prepare(saveAnnouncementDisplayPostRequestConfig, requestData),
    ...args,
  )
}

saveAnnouncementDisplayPost.requestConfig = saveAnnouncementDisplayPostRequestConfig

/**
 * 接口 [保存公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/465450) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementEntrance`
 * @项目ID 2024
 */
export interface SaveAnnouncementEntrancePostRequest {
  /**
   * 节点详情id
   */
  nodeInfoId: number
  /**
   * 1-保存  2-提交
   */
  type?: number
}

/**
 * 接口 [保存公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/465450) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementEntrance`
 * @项目ID 2024
 */
export interface SaveAnnouncementEntrancePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/465450) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementEntrance`
 * @项目ID 2024
 */
type SaveAnnouncementEntrancePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/saveAnnouncementEntrance',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/465450) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementEntrance`
 * @项目ID 2024
 */
const saveAnnouncementEntrancePostRequestConfig: SaveAnnouncementEntrancePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/saveAnnouncementEntrance',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnouncementEntrancePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告入口节点详情，节点CODE=AnnouncementEntrance↗](http://yapi.cai-inc.com/project/2024/interface/api/465450) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/saveAnnouncementEntrance`
 * @项目ID 2024
 */
export const saveAnnouncementEntrancePost = /*#__PURE__*/ (
  requestData: SaveAnnouncementEntrancePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementEntrancePostResponse>(
    prepare(saveAnnouncementEntrancePostRequestConfig, requestData),
    ...args,
  )
}

saveAnnouncementEntrancePost.requestConfig = saveAnnouncementEntrancePostRequestConfig

/**
 * 接口 [查询页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/466642) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkButton`
 * @项目ID 2024
 */
export interface CheckButtonGetRequest {
  /**
   * 区划code
   */
  districtCode: string
}

/**
 * 接口 [查询页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/466642) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkButton`
 * @项目ID 2024
 */
export interface CheckButtonGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否允许创建
     */
    canCreate?: boolean
    /**
     * 是否允许初始化
     */
    canInit?: boolean
    /**
     * 是否允许再次初始化
     */
    canReInit?: boolean
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/466642) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkButton`
 * @项目ID 2024
 */
type CheckButtonGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/checkButton',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [查询页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/466642) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkButton`
 * @项目ID 2024
 */
const checkButtonGetRequestConfig: CheckButtonGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/checkButton',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'checkButtonGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询页面按钮操作权限↗](http://yapi.cai-inc.com/project/2024/interface/api/466642) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkButton`
 * @项目ID 2024
 */
export const checkButtonGet = /*#__PURE__*/ (requestData: CheckButtonGetRequest, ...args: UserRequestRestArgs) => {
  return request<CheckButtonGetResponse>(prepare(checkButtonGetRequestConfig, requestData), ...args)
}

checkButtonGet.requestConfig = checkButtonGetRequestConfig

/**
 * 接口 [初始化数据↗](http://yapi.cai-inc.com/project/2024/interface/api/466650) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/initAnnouncementGuide`
 * @项目ID 2024
 */
export interface InitAnnouncementGuideGetRequest {
  /**
   * 区划
   */
  districtCode: string
}

/**
 * 接口 [初始化数据↗](http://yapi.cai-inc.com/project/2024/interface/api/466650) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/initAnnouncementGuide`
 * @项目ID 2024
 */
export interface InitAnnouncementGuideGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [初始化数据↗](http://yapi.cai-inc.com/project/2024/interface/api/466650) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/initAnnouncementGuide`
 * @项目ID 2024
 */
type InitAnnouncementGuideGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/initAnnouncementGuide',
    'data',
    string,
    'districtCode',
    false
  >
>

/**
 * 接口 [初始化数据↗](http://yapi.cai-inc.com/project/2024/interface/api/466650) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/initAnnouncementGuide`
 * @项目ID 2024
 */
const initAnnouncementGuideGetRequestConfig: InitAnnouncementGuideGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/initAnnouncementGuide',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'initAnnouncementGuideGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [初始化数据↗](http://yapi.cai-inc.com/project/2024/interface/api/466650) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/initAnnouncementGuide`
 * @项目ID 2024
 */
export const initAnnouncementGuideGet = /*#__PURE__*/ (
  requestData: InitAnnouncementGuideGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<InitAnnouncementGuideGetResponse>(prepare(initAnnouncementGuideGetRequestConfig, requestData), ...args)
}

initAnnouncementGuideGet.requestConfig = initAnnouncementGuideGetRequestConfig

/**
 * 接口 [查询日志↗](http://yapi.cai-inc.com/project/2024/interface/api/466690) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/selectGuideLog`
 * @项目ID 2024
 */
export interface SelectGuideLogPostRequest {
  /**
   * 业务id
   */
  bizId?: number
  /**
   * 区划编码
   */
  districtCode?: string
  /**
   * 所属模块
   */
  module?: string
  /**
   * 所属子模块详情
   */
  submodule?: string
  /**
   * 操作类型
   */
  operationType?: string
  /**
   * 操作人
   */
  operatorName?: string
  /**
   * 公告类型
   */
  announcementType?: number
  pageNo?: number
  pageSize?: number
}

/**
 * 接口 [查询日志↗](http://yapi.cai-inc.com/project/2024/interface/api/466690) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/selectGuideLog`
 * @项目ID 2024
 */
export interface SelectGuideLogPostResponse {
  success?: boolean
  result?: {
    data?: {
      id?: number
      /**
       * 业务id
       * @NotNull(message = "业务ID不能为空")
       */
      bizId?: number
      /**
       * 区划code
       * @NotBlank(message = "区划不能为空")
       */
      districtCode?: string
      /**
       * 公告类型
       */
      announcementType?: number
      /**
       * 公告类型
       */
      announcementTypeName?: string
      /**
       * 所属模块
       * @NotBlank(message = "所属模块不能为空")
       */
      module?: string
      /**
       * 所属子模块
       * @NotBlank(message = "所属子模块不能为空")
       */
      submodule?: string
      /**
       * 所属子模块名称
       */
      submoduleName?: string
      /**
       * 操作类型
       * {@Link cn.gov.zcy.announcement.annotation.BusinessFieldLogContext.LogOperationEnum}
       * @NotBlank(message = "操作类型不能为空")
       */
      operationType?: string
      operationTypeName?: string
      /**
       * 日志信息
       * @NotBlank(message = "日志详情不能为空")
       */
      message?: string
      /**
       * 操作人id
       * @NotNull(message = "操作人ID不能为空")
       */
      operatorId?: number
      /**
       * 操作人名称
       * @NotBlank(message = "操作人名称不能为空")
       */
      operatorName?: string
      /**
       * 创建时间
       */
      addTime?: string
      /**
       * 修改时间
       */
      modifiedTime?: string
    }[]
    total?: number
    pageable?: {
      page?: number
      size?: number
      sort?: {
        field?: string
        direction?: 'ASC' | 'DESC'
      }[]
    }
  }
  code?: string
  message?: string
}

/**
 * 接口 [查询日志↗](http://yapi.cai-inc.com/project/2024/interface/api/466690) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/selectGuideLog`
 * @项目ID 2024
 */
type SelectGuideLogPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/selectGuideLog',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [查询日志↗](http://yapi.cai-inc.com/project/2024/interface/api/466690) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/selectGuideLog`
 * @项目ID 2024
 */
const selectGuideLogPostRequestConfig: SelectGuideLogPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/selectGuideLog',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectGuideLogPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询日志↗](http://yapi.cai-inc.com/project/2024/interface/api/466690) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/selectGuideLog`
 * @项目ID 2024
 */
export const selectGuideLogPost = /*#__PURE__*/ (
  requestData: SelectGuideLogPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectGuideLogPostResponse>(prepare(selectGuideLogPostRequestConfig, requestData), ...args)
}

selectGuideLogPost.requestConfig = selectGuideLogPostRequestConfig

/**
 * 接口 [进入配置前检查是否存在数据↗](http://yapi.cai-inc.com/project/2024/interface/api/471098) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkDataExist`
 * @项目ID 2024
 */
export interface CheckDataExistGetRequest {
  id: string
}

/**
 * 接口 [进入配置前检查是否存在数据↗](http://yapi.cai-inc.com/project/2024/interface/api/471098) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkDataExist`
 * @项目ID 2024
 */
export interface CheckDataExistGetResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [进入配置前检查是否存在数据↗](http://yapi.cai-inc.com/project/2024/interface/api/471098) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkDataExist`
 * @项目ID 2024
 */
type CheckDataExistGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/checkDataExist',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [进入配置前检查是否存在数据↗](http://yapi.cai-inc.com/project/2024/interface/api/471098) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkDataExist`
 * @项目ID 2024
 */
const checkDataExistGetRequestConfig: CheckDataExistGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/checkDataExist',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'checkDataExistGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [进入配置前检查是否存在数据↗](http://yapi.cai-inc.com/project/2024/interface/api/471098) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/checkDataExist`
 * @项目ID 2024
 */
export const checkDataExistGet = /*#__PURE__*/ (
  requestData: CheckDataExistGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<CheckDataExistGetResponse>(prepare(checkDataExistGetRequestConfig, requestData), ...args)
}

checkDataExistGet.requestConfig = checkDataExistGetRequestConfig

/**
 * 接口 [更新公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/472402) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/updateAnnouncementGuide`
 * @项目ID 2024
 */
export interface UpdateAnnouncementGuidePostRequest {
  /**
   * relationId
   */
  id: number
  /**
   * 是否手工公告
   */
  manualAnnouncement: boolean
  /**
   * 是否业务公告
   */
  businessAnnouncement: boolean
  /**
   * 手工公告关联的用户类型
   */
  userType?: string[]
}

/**
 * 接口 [更新公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/472402) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/updateAnnouncementGuide`
 * @项目ID 2024
 */
export interface UpdateAnnouncementGuidePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [更新公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/472402) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/updateAnnouncementGuide`
 * @项目ID 2024
 */
type UpdateAnnouncementGuidePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/updateAnnouncementGuide',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [更新公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/472402) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/updateAnnouncementGuide`
 * @项目ID 2024
 */
const updateAnnouncementGuidePostRequestConfig: UpdateAnnouncementGuidePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/updateAnnouncementGuide',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updateAnnouncementGuidePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [更新公告接入↗](http://yapi.cai-inc.com/project/2024/interface/api/472402) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `POST /announcement/api/config/guide/updateAnnouncementGuide`
 * @项目ID 2024
 */
export const updateAnnouncementGuidePost = /*#__PURE__*/ (
  requestData: UpdateAnnouncementGuidePostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<UpdateAnnouncementGuidePostResponse>(
    prepare(updateAnnouncementGuidePostRequestConfig, requestData),
    ...args,
  )
}

updateAnnouncementGuidePost.requestConfig = updateAnnouncementGuidePostRequestConfig

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503458) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementGuide`
 * @项目ID 2024
 */
export interface SelectAnnouncementGuideGetRequest {
  relationId: string
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503458) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementGuide`
 * @项目ID 2024
 */
export interface SelectAnnouncementGuideGetResponse {
  success?: boolean
  result?: {
    /**
     * relationId
     */
    id?: number
    /**
     * 区划编码
     */
    districtCode?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 是否手工公告
     */
    manualAnnouncement?: boolean
    /**
     * 是否业务公告
     */
    businessAnnouncement?: boolean
    /**
     * 手工公告关联的用户类型
     */
    userType?: string[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503458) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementGuide`
 * @项目ID 2024
 */
type SelectAnnouncementGuideGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/selectAnnouncementGuide',
    'data',
    string,
    'relationId',
    false
  >
>

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503458) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementGuide`
 * @项目ID 2024
 */
const selectAnnouncementGuideGetRequestConfig: SelectAnnouncementGuideGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/selectAnnouncementGuide',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['relationId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectAnnouncementGuideGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503458) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/selectAnnouncementGuide`
 * @项目ID 2024
 */
export const selectAnnouncementGuideGet = /*#__PURE__*/ (
  requestData: SelectAnnouncementGuideGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SelectAnnouncementGuideGetResponse>(
    prepare(selectAnnouncementGuideGetRequestConfig, requestData),
    ...args,
  )
}

selectAnnouncementGuideGet.requestConfig = selectAnnouncementGuideGetRequestConfig

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503482) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementBusinessTypeDetail`
 * @项目ID 2024
 */
export interface AnnouncementBusinessTypeDetailGetRequest {
  relationId: string
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503482) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementBusinessTypeDetail`
 * @项目ID 2024
 */
export interface AnnouncementBusinessTypeDetailGetResponse {
  success?: boolean
  result?: {
    /**
     * relationId
     */
    id?: number
    /**
     * 区划编码
     */
    districtCode?: string
    /**
     * 是否继承
     */
    isExtend?: boolean
    /**
     * 区划名称
     */
    districtName?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 公告类型
     */
    announcementTypeName?: string
    /**
     * 是否手工公告
     */
    manualAnnouncement?: boolean
    /**
     * 是否业务公告
     */
    businessAnnouncement?: boolean
    /**
     * 手工公告关联的用户类型
     */
    userType?: string[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503482) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementBusinessTypeDetail`
 * @项目ID 2024
 */
type AnnouncementBusinessTypeDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/guide/announcementBusinessTypeDetail',
    'data',
    string,
    'relationId',
    false
  >
>

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503482) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementBusinessTypeDetail`
 * @项目ID 2024
 */
const announcementBusinessTypeDetailGetRequestConfig: AnnouncementBusinessTypeDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_28,
  devUrl: devUrl_0_0_0_28,
  prodUrl: prodUrl_0_0_0_28,
  path: '/announcement/api/config/guide/announcementBusinessTypeDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_28,
  paramNames: [],
  queryNames: ['relationId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementBusinessTypeDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [编辑-查看详情↗](http://yapi.cai-inc.com/project/2024/interface/api/503482) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementGuideController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51790)
 * @请求头 `GET /announcement/api/config/guide/announcementBusinessTypeDetail`
 * @项目ID 2024
 */
export const announcementBusinessTypeDetailGet = /*#__PURE__*/ (
  requestData: AnnouncementBusinessTypeDetailGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementBusinessTypeDetailGetResponse>(
    prepare(announcementBusinessTypeDetailGetRequestConfig, requestData),
    ...args,
  )
}

announcementBusinessTypeDetailGet.requestConfig = announcementBusinessTypeDetailGetRequestConfig

/* prettier-ignore-end */
