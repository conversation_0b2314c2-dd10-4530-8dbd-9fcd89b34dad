/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_27 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_27 = '' as any
const prodUrl_0_0_0_27 = '' as any
const dataKey_0_0_0_27 = 'data' as any

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/458994) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51698)
 * @请求头 `POST /announcement/api/config/log/selectLog`
 * @项目ID 2024
 */
export interface SelectLogPostRequest {
  /**
   * 业务id
   */
  bizId?: number
  /**
   * 区划编码
   */
  districtCode?: string
  /**
   * 所属模块
   */
  module: string
  /**
   * 所属子模块详情
   */
  moduleDetail?: string
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/458994) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51698)
 * @请求头 `POST /announcement/api/config/log/selectLog`
 * @项目ID 2024
 */
export interface SelectLogPostResponse {
  success?: boolean
  result?: {
    total?: number
    data?: {
      id?: number
      /**
       * 业务id
       */
      bizId: number
      /**
       * 区划code
       */
      districtCode: string
      /**
       * 所属模块
       */
      module: string
      /**
       * 所属子模块
       */
      submodule: string
      /**
       * 操作类型
       */
      operationType: string
      /**
       * 日志信息
       */
      message: string
      /**
       * 操作人id
       */
      operatorId: number
      /**
       * 操作人名称
       */
      operatorName: string
      /**
       * 创建时间
       */
      addTime?: string
      /**
       * 修改时间
       */
      modifiedTime?: string
    }[]
  }
  code?: string
  message?: string
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/458994) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51698)
 * @请求头 `POST /announcement/api/config/log/selectLog`
 * @项目ID 2024
 */
type SelectLogPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/config/log/selectLog',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/458994) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51698)
 * @请求头 `POST /announcement/api/config/log/selectLog`
 * @项目ID 2024
 */
const selectLogPostRequestConfig: SelectLogPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_27,
  devUrl: devUrl_0_0_0_27,
  prodUrl: prodUrl_0_0_0_27,
  path: '/announcement/api/config/log/selectLog',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_27,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'selectLogPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [区划同步，同步完成后是启用状态↗](http://yapi.cai-inc.com/project/2024/interface/api/458994) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementConfigLogController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51698)
 * @请求头 `POST /announcement/api/config/log/selectLog`
 * @项目ID 2024
 */
export const selectLogPost = /*#__PURE__*/ (requestData: SelectLogPostRequest, ...args: UserRequestRestArgs) => {
  return request<SelectLogPostResponse>(prepare(selectLogPostRequestConfig, requestData), ...args)
}

selectLogPost.requestConfig = selectLogPostRequestConfig

/* prettier-ignore-end */
