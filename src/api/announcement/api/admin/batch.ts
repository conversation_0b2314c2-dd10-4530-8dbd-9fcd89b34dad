/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_31 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_31 = '' as any
const prodUrl_0_0_0_31 = '' as any
const dataKey_0_0_0_31 = 'data' as any

/**
 * 接口 [批量重推公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554170) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/push`
 * @项目ID 2024
 */
export interface PushPostRequest {
  /**
   * 批量下架的公告id
   */
  announcementIds?: number[]
  /**
   * 下架原因
   */
  reason?: string
}

/**
 * 接口 [批量重推公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554170) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/push`
 * @项目ID 2024
 */
export interface PushPostResponse {
  success?: boolean
  result?: {
    /**
     * 总推送条数
     */
    totalNum?: number
    /**
     * 推送成功条数
     */
    successNum?: number
    /**
     * 推送失败条数
     */
    failNum?: number
    /**
     * 推送结果详情
     */
    details?: {
      /**
       * 公告id
       */
      announcementId?: number
      /**
       * 公告标题
       */
      title?: string
      /**
       * 失败原因
       */
      errorMsg?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [批量重推公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554170) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/push`
 * @项目ID 2024
 */
type PushPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/admin/batch/push',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量重推公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554170) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/push`
 * @项目ID 2024
 */
const pushPostRequestConfig: PushPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_31,
  devUrl: devUrl_0_0_0_31,
  prodUrl: prodUrl_0_0_0_31,
  path: '/announcement/api/admin/batch/push',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_31,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pushPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [批量重推公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554170) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/push`
 * @项目ID 2024
 */
export const pushPost = /*#__PURE__*/ (requestData: PushPostRequest, ...args: UserRequestRestArgs) => {
  return request<PushPostResponse>(prepare(pushPostRequestConfig, requestData), ...args)
}

pushPost.requestConfig = pushPostRequestConfig

/**
 * 接口 [批量下架公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554178) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/revoke`
 * @项目ID 2024
 */
export interface RevokePostRequest {
  /**
   * 批量下架的公告id
   */
  announcementIds?: number[]
  /**
   * 下架原因
   */
  reason?: string
}

/**
 * 接口 [批量下架公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554178) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/revoke`
 * @项目ID 2024
 */
export interface RevokePostResponse {
  success?: boolean
  result?: {
    /**
     * 总推送条数
     */
    totalNum?: number
    /**
     * 推送成功条数
     */
    successNum?: number
    /**
     * 推送失败条数
     */
    failNum?: number
    /**
     * 推送结果详情
     */
    details?: {
      /**
       * 公告id
       */
      announcementId?: number
      /**
       * 公告标题
       */
      title?: string
      /**
       * 失败原因
       */
      errorMsg?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [批量下架公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554178) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/revoke`
 * @项目ID 2024
 */
type RevokePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/admin/batch/revoke',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [批量下架公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554178) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/revoke`
 * @项目ID 2024
 */
const revokePostRequestConfig: RevokePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_31,
  devUrl: devUrl_0_0_0_31,
  prodUrl: prodUrl_0_0_0_31,
  path: '/announcement/api/admin/batch/revoke',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_31,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'revokePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [批量下架公告站点↗](http://yapi.cai-inc.com/project/2024/interface/api/554178) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/batch/revoke`
 * @项目ID 2024
 */
export const revokePost = /*#__PURE__*/ (requestData: RevokePostRequest, ...args: UserRequestRestArgs) => {
  return request<RevokePostResponse>(prepare(revokePostRequestConfig, requestData), ...args)
}

revokePost.requestConfig = revokePostRequestConfig

/* prettier-ignore-end */
