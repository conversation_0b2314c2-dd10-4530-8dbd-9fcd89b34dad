/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_8 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_8 = '' as any
const prodUrl_0_0_0_8 = '' as any
const dataKey_0_0_0_8 = 'data' as any

/**
 * 接口 [orgList↗](http://yapi.cai-inc.com/project/2024/interface/api/423770) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/orgList`
 * @项目ID 2024
 */
export interface OrgListGetRequest {
  distCode: string
  keywords?: string
}

/**
 * 接口 [orgList↗](http://yapi.cai-inc.com/project/2024/interface/api/423770) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/orgList`
 * @项目ID 2024
 */
export interface OrgListGetResponse {
  success?: boolean
  result?: {
    /**
     * 机构ID
     */
    id?: number
    /**
     * 机构所属区划ID
     */
    distId?: string
    /**
     * 机构名称
     */
    name?: string
  }[]
  error?: string
}

/**
 * 接口 [orgList↗](http://yapi.cai-inc.com/project/2024/interface/api/423770) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/orgList`
 * @项目ID 2024
 */
type OrgListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/notify/orgList',
    'data',
    string,
    'distCode' | 'keywords',
    false
  >
>

/**
 * 接口 [orgList↗](http://yapi.cai-inc.com/project/2024/interface/api/423770) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/orgList`
 * @项目ID 2024
 */
const orgListGetRequestConfig: OrgListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_8,
  devUrl: devUrl_0_0_0_8,
  prodUrl: prodUrl_0_0_0_8,
  path: '/announcement/api/notify/orgList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_8,
  paramNames: [],
  queryNames: ['distCode', 'keywords'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'orgListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [orgList↗](http://yapi.cai-inc.com/project/2024/interface/api/423770) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/orgList`
 * @项目ID 2024
 */
export const orgListGet = /*#__PURE__*/ (requestData: OrgListGetRequest, ...args: UserRequestRestArgs) => {
  return request<OrgListGetResponse>(prepare(orgListGetRequestConfig, requestData), ...args)
}

orgListGet.requestConfig = orgListGetRequestConfig

/**
 * 接口 [receiverList↗](http://yapi.cai-inc.com/project/2024/interface/api/423778) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/receiverList`
 * @项目ID 2024
 */
export interface ReceiverListGetRequest {
  orgId: string
  keywords?: string
}

/**
 * 接口 [receiverList↗](http://yapi.cai-inc.com/project/2024/interface/api/423778) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/receiverList`
 * @项目ID 2024
 */
export interface ReceiverListGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 机构名称
     */
    orgName: string
    /**
     * 接收人名称
     */
    name: string
    /**
     * 接收人号码
     */
    phone: string
  }[]
  error?: string
}

/**
 * 接口 [receiverList↗](http://yapi.cai-inc.com/project/2024/interface/api/423778) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/receiverList`
 * @项目ID 2024
 */
type ReceiverListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/notify/receiverList',
    'data',
    string,
    'orgId' | 'keywords',
    false
  >
>

/**
 * 接口 [receiverList↗](http://yapi.cai-inc.com/project/2024/interface/api/423778) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/receiverList`
 * @项目ID 2024
 */
const receiverListGetRequestConfig: ReceiverListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_8,
  devUrl: devUrl_0_0_0_8,
  prodUrl: prodUrl_0_0_0_8,
  path: '/announcement/api/notify/receiverList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_8,
  paramNames: [],
  queryNames: ['orgId', 'keywords'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'receiverListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [receiverList↗](http://yapi.cai-inc.com/project/2024/interface/api/423778) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/receiverList`
 * @项目ID 2024
 */
export const receiverListGet = /*#__PURE__*/ (requestData: ReceiverListGetRequest, ...args: UserRequestRestArgs) => {
  return request<ReceiverListGetResponse>(prepare(receiverListGetRequestConfig, requestData), ...args)
}

receiverListGet.requestConfig = receiverListGetRequestConfig

/**
 * 接口 [send↗](http://yapi.cai-inc.com/project/2024/interface/api/423786) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `POST /announcement/api/notify/send`
 * @项目ID 2024
 */
export interface SendPostRequest {
  /**
   * 公告id
   */
  announcementId: number
  /**
   * 区划编号
   */
  distId?: string
  /**
   * 接收人
   */
  receiver: {
    id?: number
    /**
     * 机构名称
     */
    orgName: string
    /**
     * 接收人名称
     */
    name: string
    /**
     * 接收人号码
     */
    phone: string
  }[]
}

/**
 * 接口 [send↗](http://yapi.cai-inc.com/project/2024/interface/api/423786) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `POST /announcement/api/notify/send`
 * @项目ID 2024
 */
export interface SendPostResponse {
  success?: boolean
  result?: {}
  error?: string
}

/**
 * 接口 [send↗](http://yapi.cai-inc.com/project/2024/interface/api/423786) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `POST /announcement/api/notify/send`
 * @项目ID 2024
 */
type SendPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/notify/send',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [send↗](http://yapi.cai-inc.com/project/2024/interface/api/423786) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `POST /announcement/api/notify/send`
 * @项目ID 2024
 */
const sendPostRequestConfig: SendPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_8,
  devUrl: devUrl_0_0_0_8,
  prodUrl: prodUrl_0_0_0_8,
  path: '/announcement/api/notify/send',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_8,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'sendPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [send↗](http://yapi.cai-inc.com/project/2024/interface/api/423786) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `POST /announcement/api/notify/send`
 * @项目ID 2024
 */
export const sendPost = /*#__PURE__*/ (requestData: SendPostRequest, ...args: UserRequestRestArgs) => {
  return request<SendPostResponse>(prepare(sendPostRequestConfig, requestData), ...args)
}

sendPost.requestConfig = sendPostRequestConfig

/**
 * 接口 [recordList↗](http://yapi.cai-inc.com/project/2024/interface/api/423794) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/recordList`
 * @项目ID 2024
 */
export interface RecordListGetRequest {
  announcementId: string
}

/**
 * 接口 [recordList↗](http://yapi.cai-inc.com/project/2024/interface/api/423794) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/recordList`
 * @项目ID 2024
 */
export interface RecordListGetResponse {
  success?: boolean
  result?: {
    id?: number
    /**
     * 公告id
     */
    announcementId?: number
    /**
     * 机构名称
     */
    orgName?: string
    /**
     * 推送对象名称
     */
    name?: string
    /**
     * 推送对象号码
     */
    phone?: string
    /**
     * 推送时间
     */
    createdAt?: string
  }[]
  error?: string
}

/**
 * 接口 [recordList↗](http://yapi.cai-inc.com/project/2024/interface/api/423794) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/recordList`
 * @项目ID 2024
 */
type RecordListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/notify/recordList',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [recordList↗](http://yapi.cai-inc.com/project/2024/interface/api/423794) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/recordList`
 * @项目ID 2024
 */
const recordListGetRequestConfig: RecordListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_8,
  devUrl: devUrl_0_0_0_8,
  prodUrl: prodUrl_0_0_0_8,
  path: '/announcement/api/notify/recordList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_8,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'recordListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [recordList↗](http://yapi.cai-inc.com/project/2024/interface/api/423794) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementNotifyController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51052)
 * @请求头 `GET /announcement/api/notify/recordList`
 * @项目ID 2024
 */
export const recordListGet = /*#__PURE__*/ (requestData: RecordListGetRequest, ...args: UserRequestRestArgs) => {
  return request<RecordListGetResponse>(prepare(recordListGetRequestConfig, requestData), ...args)
}

recordListGet.requestConfig = recordListGetRequestConfig

/* prettier-ignore-end */
