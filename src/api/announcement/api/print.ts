/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_34 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_34 = '' as any
const prodUrl_0_0_0_34 = '' as any
const dataKey_0_0_0_34 = 'data' as any

/**
 * 接口 [采购意向公告的审批单打印↗](http://yapi.cai-inc.com/project/2024/interface/api/557818) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReportController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53626)
 * @请求头 `POST /announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport`
 * @项目ID 2024
 */
export interface PublicNoticeOfPurchaseIntentionAuditReportPostRequest {
  /**
   * 公告id
   */
  announcementId: number
  /**
   * 是否有页眉线,默认有
   */
  isNeedHeaderLine?: boolean
  /**
   * 是否有页脚线,默认有
   */
  isNeedFooterLine?: boolean
  /**
   * 页面边距  上
   */
  pageMarginTop?: number
  /**
   * 页面边距  下
   */
  pageMarginBottom?: number
  /**
   * 页面边距  左
   */
  pageMarginLeft?: number
  /**
   * 页面边距  右
   */
  pageMarginRight?: number
  content?: string
}

/**
 * 接口 [采购意向公告的审批单打印↗](http://yapi.cai-inc.com/project/2024/interface/api/557818) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReportController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53626)
 * @请求头 `POST /announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport`
 * @项目ID 2024
 */
export interface PublicNoticeOfPurchaseIntentionAuditReportPostResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [采购意向公告的审批单打印↗](http://yapi.cai-inc.com/project/2024/interface/api/557818) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementReportController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53626)
 * @请求头 `POST /announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport`
 * @项目ID 2024
 */
type PublicNoticeOfPurchaseIntentionAuditReportPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [采购意向公告的审批单打印↗](http://yapi.cai-inc.com/project/2024/interface/api/557818) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementReportController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53626)
 * @请求头 `POST /announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport`
 * @项目ID 2024
 */
const publicNoticeOfPurchaseIntentionAuditReportPostRequestConfig: PublicNoticeOfPurchaseIntentionAuditReportPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_34,
    devUrl: devUrl_0_0_0_34,
    prodUrl: prodUrl_0_0_0_34,
    path: '/announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_34,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'publicNoticeOfPurchaseIntentionAuditReportPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [采购意向公告的审批单打印↗](http://yapi.cai-inc.com/project/2024/interface/api/557818) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementReportController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53626)
 * @请求头 `POST /announcement/api/print/publicNoticeOfPurchaseIntentionAuditReport`
 * @项目ID 2024
 */
export const publicNoticeOfPurchaseIntentionAuditReportPost = /*#__PURE__*/ (
  requestData: PublicNoticeOfPurchaseIntentionAuditReportPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PublicNoticeOfPurchaseIntentionAuditReportPostResponse>(
    prepare(publicNoticeOfPurchaseIntentionAuditReportPostRequestConfig, requestData),
    ...args,
  )
}

publicNoticeOfPurchaseIntentionAuditReportPost.requestConfig =
  publicNoticeOfPurchaseIntentionAuditReportPostRequestConfig

/* prettier-ignore-end */
