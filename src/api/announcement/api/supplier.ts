/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_13 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_13 = '' as any
const prodUrl_0_0_0_13 = '' as any
const dataKey_0_0_0_13 = 'data' as any

/**
 * 接口 [查询正式供应商列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423930) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/Official`
 * @项目ID 2024
 */
export interface OfficialGetRequest {
  /**
   * 供应商名称
   */
  name?: string
  /**
   * 供应商类别
   */
  status?: string
  /**
   * 起始页面
   */
  pageNo?: string
  /**
   * 获取页面数量
   */
  pageSize?: string
}

/**
 * 接口 [查询正式供应商列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423930) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/Official`
 * @项目ID 2024
 */
export interface OfficialGetResponse {
  success?: boolean
  result?: {
    /**
     * 供应商机构ID
     */
    supplierId?: number
    /**
     * 供应商名称
     */
    supplierName?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [查询正式供应商列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423930) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/Official`
 * @项目ID 2024
 */
type OfficialGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/supplier/Official',
    'data',
    string,
    'name' | 'status' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [查询正式供应商列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423930) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/Official`
 * @项目ID 2024
 */
const officialGetRequestConfig: OfficialGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_13,
  devUrl: devUrl_0_0_0_13,
  prodUrl: prodUrl_0_0_0_13,
  path: '/announcement/api/supplier/Official',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_13,
  paramNames: [],
  queryNames: ['name', 'status', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'officialGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询正式供应商列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423930) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/Official`
 * @项目ID 2024
 */
export const officialGet = /*#__PURE__*/ (requestData: OfficialGetRequest, ...args: UserRequestRestArgs) => {
  return request<OfficialGetResponse>(prepare(officialGetRequestConfig, requestData), ...args)
}

officialGet.requestConfig = officialGetRequestConfig

/**
 * 接口 [获取供应商社会统一信用代码↗](http://yapi.cai-inc.com/project/2024/interface/api/423938) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/socialCreditCode`
 * @项目ID 2024
 */
export interface SocialCreditCodeGetRequest {
  supplierId?: string
}

/**
 * 接口 [获取供应商社会统一信用代码↗](http://yapi.cai-inc.com/project/2024/interface/api/423938) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/socialCreditCode`
 * @项目ID 2024
 */
export interface SocialCreditCodeGetResponse {
  success?: boolean
  result?: string
  code?: string
  message?: string
}

/**
 * 接口 [获取供应商社会统一信用代码↗](http://yapi.cai-inc.com/project/2024/interface/api/423938) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/socialCreditCode`
 * @项目ID 2024
 */
type SocialCreditCodeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/supplier/socialCreditCode',
    'data',
    string,
    'supplierId',
    false
  >
>

/**
 * 接口 [获取供应商社会统一信用代码↗](http://yapi.cai-inc.com/project/2024/interface/api/423938) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/socialCreditCode`
 * @项目ID 2024
 */
const socialCreditCodeGetRequestConfig: SocialCreditCodeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_13,
  devUrl: devUrl_0_0_0_13,
  prodUrl: prodUrl_0_0_0_13,
  path: '/announcement/api/supplier/socialCreditCode',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_13,
  paramNames: [],
  queryNames: ['supplierId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'socialCreditCodeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取供应商社会统一信用代码↗](http://yapi.cai-inc.com/project/2024/interface/api/423938) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSupplierController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51057)
 * @请求头 `GET /announcement/api/supplier/socialCreditCode`
 * @项目ID 2024
 */
export const socialCreditCodeGet = /*#__PURE__*/ (
  requestData: SocialCreditCodeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SocialCreditCodeGetResponse>(prepare(socialCreditCodeGetRequestConfig, requestData), ...args)
}

socialCreditCodeGet.requestConfig = socialCreditCodeGetRequestConfig

/* prettier-ignore-end */
