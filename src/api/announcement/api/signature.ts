/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_12 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_12 = '' as any
const prodUrl_0_0_0_12 = '' as any
const dataKey_0_0_0_12 = 'data' as any

/**
 * 接口 [签章文件打印接口↗](http://yapi.cai-inc.com/project/2024/interface/api/423898) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `GET /announcement/api/signature/print`
 * @项目ID 2024
 */
export interface PrintGetRequest {
  /**
   * 公告id
   */
  announcementId?: string
}

/**
 * 接口 [签章文件打印接口↗](http://yapi.cai-inc.com/project/2024/interface/api/423898) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `GET /announcement/api/signature/print`
 * @项目ID 2024
 */
export interface PrintGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告id
     */
    announcementId?: number
    /**
     * pdf文件下载fileId
     */
    fileId?: string
    /**
     * pdf文件名称
     */
    fileName?: string
    /**
     * 1.常规附件
     * 2.公告正文附件
     */
    fileType?: number
    /**
     * pdf文件链接
     */
    downloadUrl?: string
  }
  code?: string
  message?: string
}

/**
 * 接口 [签章文件打印接口↗](http://yapi.cai-inc.com/project/2024/interface/api/423898) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `GET /announcement/api/signature/print`
 * @项目ID 2024
 */
type PrintGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/signature/print',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [签章文件打印接口↗](http://yapi.cai-inc.com/project/2024/interface/api/423898) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `GET /announcement/api/signature/print`
 * @项目ID 2024
 */
const printGetRequestConfig: PrintGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_12,
  devUrl: devUrl_0_0_0_12,
  prodUrl: prodUrl_0_0_0_12,
  path: '/announcement/api/signature/print',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_12,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'printGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [签章文件打印接口↗](http://yapi.cai-inc.com/project/2024/interface/api/423898) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `GET /announcement/api/signature/print`
 * @项目ID 2024
 */
export const printGet = /*#__PURE__*/ (requestData: PrintGetRequest, ...args: UserRequestRestArgs) => {
  return request<PrintGetResponse>(prepare(printGetRequestConfig, requestData), ...args)
}

printGet.requestConfig = printGetRequestConfig

/**
 * 接口 [获取公告签章信息列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423906) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/info`
 * @项目ID 2024
 */
export interface InfoPostRequest {
  /**
   * 公告id列表
   */
  announcementIds?: number[]
}

/**
 * 接口 [获取公告签章信息列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423906) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/info`
 * @项目ID 2024
 */
export interface InfoPostResponse {
  success?: boolean
  result?: {
    /**
     * 公告ID
     */
    announcementId?: number
    /**
     * 公告标题
     */
    title?: string
    /**
     * 公告签章状态: 0-未签章、1-签章、 2-撤回签章
     */
    status?: number
    /**
     * 公告签章类型: 1-线上签章、 2-线下签章
     */
    type?: number
    /**
     * pdf原始文件地址-fileId
     */
    originalFileId?: string
    /**
     * pdf原始文件地址
     */
    originalFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    downloadFileId?: string
    /**
     * 签章完成文件的下载地址-完整的url
     */
    downloadFiledUrl?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    downloadBizCode?: string
    /**
     * 签章完成文件的查看地址-完整的url
     */
    previewFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    previewFileId?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    previewBizCode?: string
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [获取公告签章信息列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423906) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/info`
 * @项目ID 2024
 */
type InfoPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/signature/info',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [获取公告签章信息列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423906) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/info`
 * @项目ID 2024
 */
const infoPostRequestConfig: InfoPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_12,
  devUrl: devUrl_0_0_0_12,
  prodUrl: prodUrl_0_0_0_12,
  path: '/announcement/api/signature/info',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_12,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'infoPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告签章信息列表↗](http://yapi.cai-inc.com/project/2024/interface/api/423906) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/info`
 * @项目ID 2024
 */
export const infoPost = /*#__PURE__*/ (requestData: InfoPostRequest, ...args: UserRequestRestArgs) => {
  return request<InfoPostResponse>(prepare(infoPostRequestConfig, requestData), ...args)
}

infoPost.requestConfig = infoPostRequestConfig

/**
 * 接口 [保存公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423914) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/save`
 * @项目ID 2024
 */
export interface SavePostRequest {
  /**
   * 签章保存信息
   */
  signatureVos?: {
    /**
     * 公告ID
     */
    announcementId?: number
    /**
     * 公告标题
     */
    title?: string
    /**
     * 公告签章状态: 0-未签章、1-签章、 2-撤回签章
     */
    status?: number
    /**
     * 公告签章类型: 1-线上签章、 2-线下签章
     */
    type?: number
    /**
     * pdf原始文件地址-fileId
     */
    originalFileId?: string
    /**
     * pdf原始文件地址
     */
    originalFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    downloadFileId?: string
    /**
     * 签章完成文件的下载地址-完整的url
     */
    downloadFiledUrl?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    downloadBizCode?: string
    /**
     * 签章完成文件的查看地址-完整的url
     */
    previewFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    previewFileId?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    previewBizCode?: string
  }[]
}

/**
 * 接口 [保存公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423914) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/save`
 * @项目ID 2024
 */
export interface SavePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [保存公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423914) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/save`
 * @项目ID 2024
 */
type SavePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/signature/save',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [保存公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423914) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/save`
 * @项目ID 2024
 */
const savePostRequestConfig: SavePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_12,
  devUrl: devUrl_0_0_0_12,
  prodUrl: prodUrl_0_0_0_12,
  path: '/announcement/api/signature/save',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_12,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'savePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [保存公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423914) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/save`
 * @项目ID 2024
 */
export const savePost = /*#__PURE__*/ (requestData: SavePostRequest, ...args: UserRequestRestArgs) => {
  return request<SavePostResponse>(prepare(savePostRequestConfig, requestData), ...args)
}

savePost.requestConfig = savePostRequestConfig

/**
 * 接口 [更新公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423922) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/update`
 * @项目ID 2024
 */
export interface UpdatePostRequest {
  /**
   * 签章保存信息
   */
  signatureVos?: {
    /**
     * 公告ID
     */
    announcementId?: number
    /**
     * 公告标题
     */
    title?: string
    /**
     * 公告签章状态: 0-未签章、1-签章、 2-撤回签章
     */
    status?: number
    /**
     * 公告签章类型: 1-线上签章、 2-线下签章
     */
    type?: number
    /**
     * pdf原始文件地址-fileId
     */
    originalFileId?: string
    /**
     * pdf原始文件地址
     */
    originalFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    downloadFileId?: string
    /**
     * 签章完成文件的下载地址-完整的url
     */
    downloadFiledUrl?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    downloadBizCode?: string
    /**
     * 签章完成文件的查看地址-完整的url
     */
    previewFileUrl?: string
    /**
     * 签章完成文件的下载地址-fileId
     */
    previewFileId?: string
    /**
     * 签章完成文件的下载地址-fileId对应的bizCode
     */
    previewBizCode?: string
  }[]
}

/**
 * 接口 [更新公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423922) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/update`
 * @项目ID 2024
 */
export interface UpdatePostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [更新公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423922) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/update`
 * @项目ID 2024
 */
type UpdatePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/signature/update',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [更新公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423922) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/update`
 * @项目ID 2024
 */
const updatePostRequestConfig: UpdatePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_12,
  devUrl: devUrl_0_0_0_12,
  prodUrl: prodUrl_0_0_0_12,
  path: '/announcement/api/signature/update',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_12,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'updatePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [更新公告签章信息↗](http://yapi.cai-inc.com/project/2024/interface/api/423922) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementSignatureController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51056)
 * @请求头 `POST /announcement/api/signature/update`
 * @项目ID 2024
 */
export const updatePost = /*#__PURE__*/ (requestData: UpdatePostRequest, ...args: UserRequestRestArgs) => {
  return request<UpdatePostResponse>(prepare(updatePostRequestConfig, requestData), ...args)
}

updatePost.requestConfig = updatePostRequestConfig

/* prettier-ignore-end */
