/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_4 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_4 = '' as any
const prodUrl_0_0_0_4 = '' as any
const dataKey_0_0_0_4 = 'data' as any

/**
 * 接口 [区划同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423546) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `POST /announcement/api/district/getDistTree`
 * @项目ID 2024
 */
export interface GetDistTreePostRequest {
  /**
   * 区划编码，查询子区划时必传
   */
  districtCode?: string
  /**
   * 使用当前操作人区划，查询其子区划
   */
  isOperatorDistrictCode?: boolean
  /**
   * 全国
   */
  all?: boolean
  /**
   * 是否过滤省本级
   */
  filterCorrespondingLevel?: boolean
}

/**
 * 接口 [区划同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423546) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `POST /announcement/api/district/getDistTree`
 * @项目ID 2024
 */
export interface GetDistTreePostResponse {
  success?: boolean
  result?: {
    code?: string
    name?: string
    children?: {
      code?: string
      name?: string
      children?: {}[]
    }[]
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [区划同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423546) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `POST /announcement/api/district/getDistTree`
 * @项目ID 2024
 */
type GetDistTreePostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/district/getDistTree',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [区划同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423546) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `POST /announcement/api/district/getDistTree`
 * @项目ID 2024
 */
const getDistTreePostRequestConfig: GetDistTreePostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_4,
  devUrl: devUrl_0_0_0_4,
  prodUrl: prodUrl_0_0_0_4,
  path: '/announcement/api/district/getDistTree',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_4,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getDistTreePost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [区划同步↗](http://yapi.cai-inc.com/project/2024/interface/api/423546) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `POST /announcement/api/district/getDistTree`
 * @项目ID 2024
 */
export const getDistTreePost = /*#__PURE__*/ (requestData: GetDistTreePostRequest, ...args: UserRequestRestArgs) => {
  return request<GetDistTreePostResponse>(prepare(getDistTreePostRequestConfig, requestData), ...args)
}

getDistTreePost.requestConfig = getDistTreePostRequestConfig

/* prettier-ignore-end */
