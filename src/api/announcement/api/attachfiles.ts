/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_4 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_4 = '' as any
const prodUrl_0_0_0_4 = '' as any
const dataKey_0_0_0_4 = 'data' as any

/**
 * 接口 [获取上传附件所需的证书信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424546) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/credentials`
 * @项目ID 2024
 */
export interface CredentialsGetRequest {
  /**
   * 文件数量
   */
  fileNum: string
}

/**
 * 接口 [获取上传附件所需的证书信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424546) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/credentials`
 * @项目ID 2024
 */
export type CredentialsGetResponse = string

/**
 * 接口 [获取上传附件所需的证书信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424546) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/credentials`
 * @项目ID 2024
 */
type CredentialsGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/attachfiles/credentials',
    'data',
    string,
    'fileNum',
    false
  >
>

/**
 * 接口 [获取上传附件所需的证书信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424546) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/credentials`
 * @项目ID 2024
 */
const credentialsGetRequestConfig: CredentialsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_4,
  devUrl: devUrl_0_0_0_4,
  prodUrl: prodUrl_0_0_0_4,
  path: '/announcement/api/attachfiles/credentials',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_4,
  paramNames: [],
  queryNames: ['fileNum'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'credentialsGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取上传附件所需的证书信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424546) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/credentials`
 * @项目ID 2024
 */
export const credentialsGet = /*#__PURE__*/ (requestData: CredentialsGetRequest, ...args: UserRequestRestArgs) => {
  return request<CredentialsGetResponse>(prepare(credentialsGetRequestConfig, requestData), ...args)
}

credentialsGet.requestConfig = credentialsGetRequestConfig

/**
 * 接口 [获取附件下载Url↗](http://yapi.cai-inc.com/project/2024/interface/api/424554) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/downloadUrl`
 * @项目ID 2024
 */
export interface DownloadUrlGetRequest {
  /**
   * 附件ID
   */
  fileId: string
}

/**
 * 接口 [获取附件下载Url↗](http://yapi.cai-inc.com/project/2024/interface/api/424554) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/downloadUrl`
 * @项目ID 2024
 */
export interface DownloadUrlGetResponse {
  key?: {}
}

/**
 * 接口 [获取附件下载Url↗](http://yapi.cai-inc.com/project/2024/interface/api/424554) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/downloadUrl`
 * @项目ID 2024
 */
type DownloadUrlGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/attachfiles/downloadUrl',
    'data',
    string,
    'fileId',
    false
  >
>

/**
 * 接口 [获取附件下载Url↗](http://yapi.cai-inc.com/project/2024/interface/api/424554) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/downloadUrl`
 * @项目ID 2024
 */
const downloadUrlGetRequestConfig: DownloadUrlGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_4,
  devUrl: devUrl_0_0_0_4,
  prodUrl: prodUrl_0_0_0_4,
  path: '/announcement/api/attachfiles/downloadUrl',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_4,
  paramNames: [],
  queryNames: ['fileId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'downloadUrlGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取附件下载Url↗](http://yapi.cai-inc.com/project/2024/interface/api/424554) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementServiceController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51066)
 * @请求头 `GET /announcement/api/attachfiles/downloadUrl`
 * @项目ID 2024
 */
export const downloadUrlGet = /*#__PURE__*/ (requestData: DownloadUrlGetRequest, ...args: UserRequestRestArgs) => {
  return request<DownloadUrlGetResponse>(prepare(downloadUrlGetRequestConfig, requestData), ...args)
}

downloadUrlGet.requestConfig = downloadUrlGetRequestConfig

/* prettier-ignore-end */
