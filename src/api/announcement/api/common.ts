/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_33 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_33 = '' as any
const prodUrl_0_0_0_33 = '' as any
const dataKey_0_0_0_33 = 'data' as any

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/534978) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementCommonController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53227)
 * @请求头 `GET /announcement/api/common/previewAnnouncement`
 * @项目ID 2024
 */
export interface PreviewAnnouncementGetRequest {
  /**
   * 公告ID
   */
  announcementId: string
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/534978) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementCommonController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53227)
 * @请求头 `GET /announcement/api/common/previewAnnouncement`
 * @项目ID 2024
 */
export interface PreviewAnnouncementGetResponse {
  success?: boolean
  result?: {
    /**
     * 公告标题
     */
    title?: string
    /**
     * 公告正文
     */
    content?: string
  }
  error?: string
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/534978) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementCommonController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53227)
 * @请求头 `GET /announcement/api/common/previewAnnouncement`
 * @项目ID 2024
 */
type PreviewAnnouncementGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/common/previewAnnouncement',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/534978) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementCommonController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53227)
 * @请求头 `GET /announcement/api/common/previewAnnouncement`
 * @项目ID 2024
 */
const previewAnnouncementGetRequestConfig: PreviewAnnouncementGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_33,
  devUrl: devUrl_0_0_0_33,
  prodUrl: prodUrl_0_0_0_33,
  path: '/announcement/api/common/previewAnnouncement',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_33,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'previewAnnouncementGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/534978) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementCommonController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53227)
 * @请求头 `GET /announcement/api/common/previewAnnouncement`
 * @项目ID 2024
 */
export const previewAnnouncementGet = /*#__PURE__*/ (
  requestData: PreviewAnnouncementGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PreviewAnnouncementGetResponse>(prepare(previewAnnouncementGetRequestConfig, requestData), ...args)
}

previewAnnouncementGet.requestConfig = previewAnnouncementGetRequestConfig

/* prettier-ignore-end */
