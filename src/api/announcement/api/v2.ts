/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_2 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_2 = '' as any
const prodUrl_0_0_0_2 = '' as any
const dataKey_0_0_0_2 = 'data' as any

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423650) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51050)
 * @请求头 `GET /announcement/api/v2/obtainAnnouncementDetail`
 * @项目ID 2024
 */
export interface ObtainAnnouncementDetailGetRequest {
  /**
   * 公告对象,其中公告内容需要根据metaData + 模版生成
   */
  announcementId?: string
  districtCode?: string
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423650) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51050)
 * @请求头 `GET /announcement/api/v2/obtainAnnouncementDetail`
 * @项目ID 2024
 */
export interface ObtainAnnouncementDetailGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键id
     */
    id?: number
    /**
     * 标题
     */
    title?: string
    /**
     * 行政区划code
     */
    district?: string
    /**
     * 行政区划名称
     */
    districtName?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 项目名称
     */
    projectName?: string
    /**
     * 项目编号
     */
    projectCode?: string
    /**
     * 公告元数据内容
     */
    metaData?: string
    /**
     * 公告内容 html
     */
    content?: string
    /**
     * 描述
     */
    description?: string
    /**
     * 状态 初始(1) 待审核(2) 待发布(3) 审核不通过(4) 已发布(5)
     */
    status?: number
    /**
     * 创建时间
     */
    createdAt?: string
    /**
     * 审核时间
     */
    checkedAt?: string
    /**
     * 发布时间
     */
    releasedAt?: string
    /**
     * 截止时间
     */
    expiredAt?: string
    /**
     * 创建人名称
     */
    creatorName?: string
    /**
     * 创建人id
     */
    creatorId?: number
    /**
     * 创建人orgId
     */
    createOrgId?: number
    /**
     * 附件列表
     */
    attachments?: {
      /**
       * 文件url,OSS id
       */
      fileId?: string
      /**
       * 文件名称
       */
      name?: string
      /**
       * 文件大小
       */
      size?: number
      /**
       * 是否在外网显示
       */
      isShow?: boolean
      /**
       * 文件编码
       */
      fileCode?: string
      /**
       * 文件描述
       */
      fileDesc?: string
      /**
       * 原始FileID
       */
      sourceFileId?: string
    }[]
    /**
     * 业务id
     */
    serialNum?: string
    /**
     * 公告名称
     */
    announcementTypeName?: string
    /**
     * 公告发布类型@1:定时发布@2
     */
    pubType?: number
    /**
     * 记录公告展示天数
     */
    showDuration?: number
    checkFlag?: boolean
    /**
     * 外网链接
     */
    outUrl?: string
    /**
     * 是否显示修改、删除、撤回操作连接（true:显示;false:不显示）
     */
    modifyFlag?: boolean
    /**
     * 机构名称
     */
    orgName?: string
    /**
     * 处理码
     * 计算方法:AnnouncementDealEnum.getAnnouncementDealEnumList(dealCode)可以得出具体的处理集合
     */
    dealCode?: number
    options?: {
      canPublish?: boolean
      canDelete?: boolean
      canEdit?: boolean
      canRevoke?: boolean
      canCheck?: boolean
      /**
       * 是否发送短信通知
       */
      canNotify?: boolean
      /**
       * 是否可以撤回发布
       */
      canRevokePublish?: boolean
      canDownload?: boolean
      canPrint?: boolean
      /**
       * 是否可以重新推送
       */
      canRePush?: boolean
    }
    /**
     * 是否配置短信推送
     */
    isNotify?: boolean
    /**
     * 显示用状态(0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束)
     */
    showStatus?: number
    showStatusName?: string
    /**
     * 大类型
     */
    annBigType?: number
    /**
     * 系统类别：1-政府采购；2-企业购,参考枚举SystemTypeEnum
     */
    systemType?: number
    /**
     * 业务应用code
     */
    appCode?: string
    /**
     * 业务名称
     */
    appName?: string
    /**
     * 撤回公告是否需要逻辑删除
     */
    revokedLogicDelete?: boolean
    /**
     * 是否是涉密公告，false-否； true-是
     */
    secret?: boolean
    /**
     * 附件是否必填
     */
    hasAttachment?: boolean
    /**
     * 是否展示附件
     */
    attachmentShow?: boolean
    /**
     * 公告标识的id集合 announcement_identification
     */
    identificationIds?: number[]
    /**
     * 异议状态(0:无异议，1:有异议)
     */
    objectionState?: number
    /**
     * 公告发布人信息
     */
    creatorInfo?: string
    /**
     * 摘要信息
     */
    digest?: string
    /**
     * 公告更新时间
     */
    updateTime?: string
    /**
     * 扩展字段1， 用于各种公告填充扩展值
     */
    extendFirst?: string
    /**
     * 扩展字段2， 用于各种公告填充扩展值
     */
    extendSecond?: string
    /**
     * 扩展字段3， 用于各种公告填充扩展值
     */
    extendThird?: string
    /**
     * 扩展字段4， 用于各种公告填充扩展值
     */
    extendFour?: string
    /**
     * 扩展字段5， 用于各种公告填充扩展值
     */
    extendFive?: string
    /**
     * 表单页面编号
     */
    formPageCode?: string
  }
  error?: string
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423650) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51050)
 * @请求头 `GET /announcement/api/v2/obtainAnnouncementDetail`
 * @项目ID 2024
 */
type ObtainAnnouncementDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/v2/obtainAnnouncementDetail',
    'data',
    string,
    'announcementId' | 'districtCode',
    false
  >
>

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423650) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51050)
 * @请求头 `GET /announcement/api/v2/obtainAnnouncementDetail`
 * @项目ID 2024
 */
const obtainAnnouncementDetailGetRequestConfig: ObtainAnnouncementDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_2,
  devUrl: devUrl_0_0_0_2,
  prodUrl: prodUrl_0_0_0_2,
  path: '/announcement/api/v2/obtainAnnouncementDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_2,
  paramNames: [],
  queryNames: ['announcementId', 'districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'obtainAnnouncementDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告详情↗](http://yapi.cai-inc.com/project/2024/interface/api/423650) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51050)
 * @请求头 `GET /announcement/api/v2/obtainAnnouncementDetail`
 * @项目ID 2024
 */
export const obtainAnnouncementDetailGet = /*#__PURE__*/ (
  requestData: ObtainAnnouncementDetailGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ObtainAnnouncementDetailGetResponse>(
    prepare(obtainAnnouncementDetailGetRequestConfig, requestData),
    ...args,
  )
}

obtainAnnouncementDetailGet.requestConfig = obtainAnnouncementDetailGetRequestConfig

/* prettier-ignore-end */
