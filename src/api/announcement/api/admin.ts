/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_31 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_31 = '' as any
const prodUrl_0_0_0_31 = '' as any
const dataKey_0_0_0_31 = 'data' as any

/**
 * 接口 [getAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534714) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `GET /announcement/api/admin/getAnnouncementDetail`
 * @项目ID 2024
 */
export interface GetAnnouncementDetailGetRequest {
  announcementId: string
}

/**
 * 接口 [getAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534714) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `GET /announcement/api/admin/getAnnouncementDetail`
 * @项目ID 2024
 */
export interface GetAnnouncementDetailGetResponse {
  success?: boolean
  result?: {
    /**
     * 主键id
     */
    id?: number
    /**
     * 区划code
     */
    district?: string
    /**
     * 公告类型
     */
    announcementType?: number
    /**
     * 公告类型名称
     */
    announcementTypeName?: string
    /**
     * 标题
     */
    title?: string
    /**
     * 显示用状态(0:待完善;1:审核中;2:待发布;3:被回退;4:已发布;5:已结束)
     */
    showStatus?: number
    /**
     * 公告内容 html
     */
    content?: string
    /**
     * 附件列表
     */
    attachments?: {
      /**
       * 文件url,OSS id
       */
      fileId?: string
      /**
       * 文件名称
       */
      name?: string
      /**
       * 文件大小
       */
      size?: number
      /**
       * 是否在外网显示
       */
      isShow?: boolean
      /**
       * 文件编码
       */
      fileCode?: string
      /**
       * 文件描述
       */
      fileDesc?: string
      /**
       * 原始FileID
       */
      sourceFileId?: string
    }[]
  }
  error?: string
}

/**
 * 接口 [getAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534714) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `GET /announcement/api/admin/getAnnouncementDetail`
 * @项目ID 2024
 */
type GetAnnouncementDetailGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/admin/getAnnouncementDetail',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [getAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534714) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `GET /announcement/api/admin/getAnnouncementDetail`
 * @项目ID 2024
 */
const getAnnouncementDetailGetRequestConfig: GetAnnouncementDetailGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_31,
  devUrl: devUrl_0_0_0_31,
  prodUrl: prodUrl_0_0_0_31,
  path: '/announcement/api/admin/getAnnouncementDetail',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_31,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnouncementDetailGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [getAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534714) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `GET /announcement/api/admin/getAnnouncementDetail`
 * @项目ID 2024
 */
export const getAnnouncementDetailGet = /*#__PURE__*/ (
  requestData: GetAnnouncementDetailGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnouncementDetailGetResponse>(prepare(getAnnouncementDetailGetRequestConfig, requestData), ...args)
}

getAnnouncementDetailGet.requestConfig = getAnnouncementDetailGetRequestConfig

/**
 * 接口 [saveAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534722) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAnnouncementDetail`
 * @项目ID 2024
 */
export interface SaveAnnouncementDetailPostRequest {
  /**
   * 主键id
   */
  id: number
  /**
   * 公告内容 html
   */
  content: string
  /**
   * 公告标题
   */
  title: string
  /**
   * 附件列表
   */
  attachments?: {
    /**
     * 文件url,OSS id
     */
    fileId?: string
    /**
     * 文件名称
     */
    name?: string
    /**
     * 文件大小
     */
    size?: number
    /**
     * 是否在外网显示
     */
    isShow?: boolean
    /**
     * 文件编码
     */
    fileCode?: string
    /**
     * 文件描述
     */
    fileDesc?: string
    /**
     * 原始FileID
     */
    sourceFileId?: string
  }[]
  /**
   * 更新公告原因
   */
  reason: string
}

/**
 * 接口 [saveAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534722) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAnnouncementDetail`
 * @项目ID 2024
 */
export interface SaveAnnouncementDetailPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [saveAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534722) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAnnouncementDetail`
 * @项目ID 2024
 */
type SaveAnnouncementDetailPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/admin/saveAnnouncementDetail',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [saveAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534722) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAnnouncementDetail`
 * @项目ID 2024
 */
const saveAnnouncementDetailPostRequestConfig: SaveAnnouncementDetailPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_31,
  devUrl: devUrl_0_0_0_31,
  prodUrl: prodUrl_0_0_0_31,
  path: '/announcement/api/admin/saveAnnouncementDetail',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_31,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'saveAnnouncementDetailPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [saveAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/534722) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAnnouncementDetail`
 * @项目ID 2024
 */
export const saveAnnouncementDetailPost = /*#__PURE__*/ (
  requestData: SaveAnnouncementDetailPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAnnouncementDetailPostResponse>(
    prepare(saveAnnouncementDetailPostRequestConfig, requestData),
    ...args,
  )
}

saveAnnouncementDetailPost.requestConfig = saveAnnouncementDetailPostRequestConfig

/**
 * 接口 [saveAndRePushAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/535754) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAndRePushAnnouncementDetail`
 * @项目ID 2024
 */
export interface SaveAndRePushAnnouncementDetailPostRequest {
  /**
   * 主键id
   */
  id: number
  /**
   * 公告内容 html
   */
  content: string
  /**
   * 公告标题
   */
  title: string
  /**
   * 附件列表
   */
  attachments?: {
    /**
     * 文件url,OSS id
     */
    fileId?: string
    /**
     * 文件名称
     */
    name?: string
    /**
     * 文件大小
     */
    size?: number
    /**
     * 是否在外网显示
     */
    isShow?: boolean
    /**
     * 文件编码
     */
    fileCode?: string
    /**
     * 文件描述
     */
    fileDesc?: string
    /**
     * 原始FileID
     */
    sourceFileId?: string
  }[]
  /**
   * 更新公告原因
   */
  reason: string
}

/**
 * 接口 [saveAndRePushAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/535754) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAndRePushAnnouncementDetail`
 * @项目ID 2024
 */
export interface SaveAndRePushAnnouncementDetailPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [saveAndRePushAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/535754) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAndRePushAnnouncementDetail`
 * @项目ID 2024
 */
type SaveAndRePushAnnouncementDetailPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/admin/saveAndRePushAnnouncementDetail',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [saveAndRePushAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/535754) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAndRePushAnnouncementDetail`
 * @项目ID 2024
 */
const saveAndRePushAnnouncementDetailPostRequestConfig: SaveAndRePushAnnouncementDetailPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_31,
    devUrl: devUrl_0_0_0_31,
    prodUrl: prodUrl_0_0_0_31,
    path: '/announcement/api/admin/saveAndRePushAnnouncementDetail',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_31,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'saveAndRePushAnnouncementDetailPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [saveAndRePushAnnouncementDetail↗](http://yapi.cai-inc.com/project/2024/interface/api/535754) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementAdminController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52288)
 * @请求头 `POST /announcement/api/admin/saveAndRePushAnnouncementDetail`
 * @项目ID 2024
 */
export const saveAndRePushAnnouncementDetailPost = /*#__PURE__*/ (
  requestData: SaveAndRePushAnnouncementDetailPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SaveAndRePushAnnouncementDetailPostResponse>(
    prepare(saveAndRePushAnnouncementDetailPostRequestConfig, requestData),
    ...args,
  )
}

saveAndRePushAnnouncementDetailPost.requestConfig = saveAndRePushAnnouncementDetailPostRequestConfig

/* prettier-ignore-end */
