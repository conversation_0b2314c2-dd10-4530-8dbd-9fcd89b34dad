/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_25 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_25 = '' as any
const prodUrl_0_0_0_25 = '' as any
const dataKey_0_0_0_25 = 'data' as any

/**
 * 接口 [公告模版预览分页查询公告样本↗](http://yapi.cai-inc.com/project/2024/interface/api/429754) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/pageAnnData`
 * @项目ID 2024
 */
export interface PageAnnDataGetRequest {
  /**
   * 区划code
   */
  districtCode: string
  /**
   * 公告类型
   */
  announcementType: string
  /**
   * 公告ID
   */
  announcementId?: string
  /**
   * 公告标题
   */
  title?: string
  /**
   * pageNo 页码(null的时候默认1)(非必传)
   */
  pageNo?: string
  /**
   * pageSize (null的时候默认10)(非必传)
   */
  pageSize?: string
}

/**
 * 接口 [公告模版预览分页查询公告样本↗](http://yapi.cai-inc.com/project/2024/interface/api/429754) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/pageAnnData`
 * @项目ID 2024
 */
export interface PageAnnDataGetResponse {
  success?: boolean
  result?: {
    total?: number
    /**
     * [{"announcementId":0,"title":"","releasedAt":"@datetime","metaData":"","formPageCode":"","district":"","annBigType":0}]
     */
    data?: {
      announcementId?: number
      title?: string
      /**
       * "@datetime"
       */
      releasedAt?: string
      metaData?: string
      formPageCode?: string
      district?: string
      annBigType?: number
    }[]
  }
  error?: string
}

/**
 * 接口 [公告模版预览分页查询公告样本↗](http://yapi.cai-inc.com/project/2024/interface/api/429754) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/pageAnnData`
 * @项目ID 2024
 */
type PageAnnDataGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/pageAnnData',
    'data',
    string,
    'districtCode' | 'announcementType' | 'announcementId' | 'title' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [公告模版预览分页查询公告样本↗](http://yapi.cai-inc.com/project/2024/interface/api/429754) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/pageAnnData`
 * @项目ID 2024
 */
const pageAnnDataGetRequestConfig: PageAnnDataGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_25,
  devUrl: devUrl_0_0_0_25,
  prodUrl: prodUrl_0_0_0_25,
  path: '/announcement/api/template/pageAnnData',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_25,
  paramNames: [],
  queryNames: ['districtCode', 'announcementType', 'announcementId', 'title', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'pageAnnDataGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告模版预览分页查询公告样本↗](http://yapi.cai-inc.com/project/2024/interface/api/429754) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/pageAnnData`
 * @项目ID 2024
 */
export const pageAnnDataGet = /*#__PURE__*/ (requestData: PageAnnDataGetRequest, ...args: UserRequestRestArgs) => {
  return request<PageAnnDataGetResponse>(prepare(pageAnnDataGetRequestConfig, requestData), ...args)
}

pageAnnDataGet.requestConfig = pageAnnDataGetRequestConfig

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/429762) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementContentById`
 * @项目ID 2024
 */
export interface PreviewAnnouncementContentByIdGetRequest {
  /**
   * 公告ID
   */
  announcementId: string
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/429762) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementContentById`
 * @项目ID 2024
 */
export interface PreviewAnnouncementContentByIdGetResponse {
  success?: boolean
  result?: {
    title?: string
    content?: string
  }
  error?: string
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/429762) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementContentById`
 * @项目ID 2024
 */
type PreviewAnnouncementContentByIdGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/previewAnnouncementContentById',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/429762) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementContentById`
 * @项目ID 2024
 */
const previewAnnouncementContentByIdGetRequestConfig: PreviewAnnouncementContentByIdGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_25,
  devUrl: devUrl_0_0_0_25,
  prodUrl: prodUrl_0_0_0_25,
  path: '/announcement/api/template/previewAnnouncementContentById',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_25,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'previewAnnouncementContentByIdGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [基于公告样本预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/429762) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementContentById`
 * @项目ID 2024
 */
export const previewAnnouncementContentByIdGet = /*#__PURE__*/ (
  requestData: PreviewAnnouncementContentByIdGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PreviewAnnouncementContentByIdGetResponse>(
    prepare(previewAnnouncementContentByIdGetRequestConfig, requestData),
    ...args,
  )
}

previewAnnouncementContentByIdGet.requestConfig = previewAnnouncementContentByIdGetRequestConfig

/**
 * 接口 [基于metadata预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/430458) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementContentByMetaData`
 * @项目ID 2024
 */
export interface PreviewAnnouncementContentByMetaDataPostRequest {
  districtCode?: string
  announcementType?: number
  metaData?: string
}

/**
 * 接口 [基于metadata预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/430458) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementContentByMetaData`
 * @项目ID 2024
 */
export interface PreviewAnnouncementContentByMetaDataPostResponse {
  success?: boolean
  result?: {
    title?: string
    content?: string
  }
  error?: string
}

/**
 * 接口 [基于metadata预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/430458) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementContentByMetaData`
 * @项目ID 2024
 */
type PreviewAnnouncementContentByMetaDataPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/previewAnnouncementContentByMetaData',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [基于metadata预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/430458) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementContentByMetaData`
 * @项目ID 2024
 */
const previewAnnouncementContentByMetaDataPostRequestConfig: PreviewAnnouncementContentByMetaDataPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_25,
    devUrl: devUrl_0_0_0_25,
    prodUrl: prodUrl_0_0_0_25,
    path: '/announcement/api/template/previewAnnouncementContentByMetaData',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_25,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'previewAnnouncementContentByMetaDataPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [基于metadata预览公告正文↗](http://yapi.cai-inc.com/project/2024/interface/api/430458) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementContentByMetaData`
 * @项目ID 2024
 */
export const previewAnnouncementContentByMetaDataPost = /*#__PURE__*/ (
  requestData: PreviewAnnouncementContentByMetaDataPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PreviewAnnouncementContentByMetaDataPostResponse>(
    prepare(previewAnnouncementContentByMetaDataPostRequestConfig, requestData),
    ...args,
  )
}

previewAnnouncementContentByMetaDataPost.requestConfig = previewAnnouncementContentByMetaDataPostRequestConfig

/**
 * 接口 [基于公告样本预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433794) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementTitleById`
 * @项目ID 2024
 */
export interface PreviewAnnouncementTitleByIdGetRequest {
  /**
   * 公告ID
   */
  announcementId: string
}

/**
 * 接口 [基于公告样本预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433794) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementTitleById`
 * @项目ID 2024
 */
export interface PreviewAnnouncementTitleByIdGetResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [基于公告样本预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433794) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementTitleById`
 * @项目ID 2024
 */
type PreviewAnnouncementTitleByIdGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/previewAnnouncementTitleById',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [基于公告样本预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433794) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementTitleById`
 * @项目ID 2024
 */
const previewAnnouncementTitleByIdGetRequestConfig: PreviewAnnouncementTitleByIdGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_25,
  devUrl: devUrl_0_0_0_25,
  prodUrl: prodUrl_0_0_0_25,
  path: '/announcement/api/template/previewAnnouncementTitleById',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_25,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'previewAnnouncementTitleByIdGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [基于公告样本预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433794) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/previewAnnouncementTitleById`
 * @项目ID 2024
 */
export const previewAnnouncementTitleByIdGet = /*#__PURE__*/ (
  requestData: PreviewAnnouncementTitleByIdGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PreviewAnnouncementTitleByIdGetResponse>(
    prepare(previewAnnouncementTitleByIdGetRequestConfig, requestData),
    ...args,
  )
}

previewAnnouncementTitleByIdGet.requestConfig = previewAnnouncementTitleByIdGetRequestConfig

/**
 * 接口 [基于metadata预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433802) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementTitleByMetaData`
 * @项目ID 2024
 */
export interface PreviewAnnouncementTitleByMetaDataPostRequest {
  districtCode?: string
  announcementType?: number
  metaData?: string
}

/**
 * 接口 [基于metadata预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433802) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementTitleByMetaData`
 * @项目ID 2024
 */
export interface PreviewAnnouncementTitleByMetaDataPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [基于metadata预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433802) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementTitleByMetaData`
 * @项目ID 2024
 */
type PreviewAnnouncementTitleByMetaDataPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/previewAnnouncementTitleByMetaData',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [基于metadata预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433802) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementTitleByMetaData`
 * @项目ID 2024
 */
const previewAnnouncementTitleByMetaDataPostRequestConfig: PreviewAnnouncementTitleByMetaDataPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_0_0_0_25,
    devUrl: devUrl_0_0_0_25,
    prodUrl: prodUrl_0_0_0_25,
    path: '/announcement/api/template/previewAnnouncementTitleByMetaData',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.json,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_0_0_0_25,
    paramNames: [],
    queryNames: [],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'previewAnnouncementTitleByMetaDataPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [基于metadata预览公告标题↗](http://yapi.cai-inc.com/project/2024/interface/api/433802) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `POST /announcement/api/template/previewAnnouncementTitleByMetaData`
 * @项目ID 2024
 */
export const previewAnnouncementTitleByMetaDataPost = /*#__PURE__*/ (
  requestData: PreviewAnnouncementTitleByMetaDataPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PreviewAnnouncementTitleByMetaDataPostResponse>(
    prepare(previewAnnouncementTitleByMetaDataPostRequestConfig, requestData),
    ...args,
  )
}

previewAnnouncementTitleByMetaDataPost.requestConfig = previewAnnouncementTitleByMetaDataPostRequestConfig

/**
 * 接口 [查询公告模板信息↗](http://yapi.cai-inc.com/project/2024/interface/api/436978) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/getAnnTitleTemplate`
 * @项目ID 2024
 */
export interface GetAnnTitleTemplateGetRequest {
  /**
   * 公告类型
   */
  announcementType: string
  /**
   * 区划
   */
  districtCode: string
}

/**
 * 接口 [查询公告模板信息↗](http://yapi.cai-inc.com/project/2024/interface/api/436978) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/getAnnTitleTemplate`
 * @项目ID 2024
 */
export interface GetAnnTitleTemplateGetResponse {
  success?: boolean
  result?: {
    annTemplateId?: number
    annTemplateName?: string
    annTemplateUrl?: string
    isAnnTemplateExtend?: boolean
    annTemplateDistrictCode?: string
    annTemplateDistrictName?: string
    annTitleTemplateId?: number
    annTitleTemplateName?: string
    annTitleContent?: string
    annTitleTemplateUrl?: string
    isAnnTitleTemplateExtend?: boolean
    annTitleTemplateDistrictCode?: string
    annTitleTemplateDistrictName?: string
  }
  error?: string
}

/**
 * 接口 [查询公告模板信息↗](http://yapi.cai-inc.com/project/2024/interface/api/436978) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/getAnnTitleTemplate`
 * @项目ID 2024
 */
type GetAnnTitleTemplateGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/template/getAnnTitleTemplate',
    'data',
    string,
    'announcementType' | 'districtCode',
    false
  >
>

/**
 * 接口 [查询公告模板信息↗](http://yapi.cai-inc.com/project/2024/interface/api/436978) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/getAnnTitleTemplate`
 * @项目ID 2024
 */
const getAnnTitleTemplateGetRequestConfig: GetAnnTitleTemplateGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_25,
  devUrl: devUrl_0_0_0_25,
  prodUrl: prodUrl_0_0_0_25,
  path: '/announcement/api/template/getAnnTitleTemplate',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_25,
  paramNames: [],
  queryNames: ['announcementType', 'districtCode'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAnnTitleTemplateGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [查询公告模板信息↗](http://yapi.cai-inc.com/project/2024/interface/api/436978) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementTemplateController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51175)
 * @请求头 `GET /announcement/api/template/getAnnTitleTemplate`
 * @项目ID 2024
 */
export const getAnnTitleTemplateGet = /*#__PURE__*/ (
  requestData: GetAnnTitleTemplateGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAnnTitleTemplateGetResponse>(prepare(getAnnTitleTemplateGetRequestConfig, requestData), ...args)
}

getAnnTitleTemplateGet.requestConfig = getAnnTitleTemplateGetRequestConfig

/* prettier-ignore-end */
