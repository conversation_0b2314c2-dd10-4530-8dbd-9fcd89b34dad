/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_4 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_4 = '' as any
const prodUrl_0_0_0_4 = '' as any
const dataKey_0_0_0_4 = 'data' as any

/**
 * 接口 [获取登录人授权区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/587770) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `GET /announcement/api/district/admin/getAuthorityDistTree`
 * @项目ID 2024
 */
export interface GetAuthorityDistTreeGetRequest {}

/**
 * 接口 [获取登录人授权区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/587770) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `GET /announcement/api/district/admin/getAuthorityDistTree`
 * @项目ID 2024
 */
export interface GetAuthorityDistTreeGetResponse {
  success?: boolean
  result?: {
    code?: string
    name?: string
    children?: {
      code?: string
      name?: string
      children?: {}[]
    }[]
  }[]
  code?: string
  message?: string
}

/**
 * 接口 [获取登录人授权区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/587770) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `GET /announcement/api/district/admin/getAuthorityDistTree`
 * @项目ID 2024
 */
type GetAuthorityDistTreeGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/district/admin/getAuthorityDistTree',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [获取登录人授权区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/587770) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `GET /announcement/api/district/admin/getAuthorityDistTree`
 * @项目ID 2024
 */
const getAuthorityDistTreeGetRequestConfig: GetAuthorityDistTreeGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_4,
  devUrl: devUrl_0_0_0_4,
  prodUrl: prodUrl_0_0_0_4,
  path: '/announcement/api/district/admin/getAuthorityDistTree',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_4,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'getAuthorityDistTreeGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取登录人授权区划树↗](http://yapi.cai-inc.com/project/2024/interface/api/587770) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [DistrictTreeController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51048)
 * @请求头 `GET /announcement/api/district/admin/getAuthorityDistTree`
 * @项目ID 2024
 */
export const getAuthorityDistTreeGet = /*#__PURE__*/ (
  requestData?: GetAuthorityDistTreeGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<GetAuthorityDistTreeGetResponse>(prepare(getAuthorityDistTreeGetRequestConfig, requestData), ...args)
}

getAuthorityDistTreeGet.requestConfig = getAuthorityDistTreeGetRequestConfig

/* prettier-ignore-end */
