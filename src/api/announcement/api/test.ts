/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_17 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_17 = '' as any
const prodUrl_0_0_0_17 = '' as any
const dataKey_0_0_0_17 = 'data' as any

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528842) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/sendAnnouncementPush`
 * @项目ID 2024
 */
export interface SendAnnouncementPushGetRequest {}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528842) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/sendAnnouncementPush`
 * @项目ID 2024
 */
export interface SendAnnouncementPushGetResponse {
  success?: boolean
  result?: {}
  code?: string
  message?: string
}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528842) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/sendAnnouncementPush`
 * @项目ID 2024
 */
type SendAnnouncementPushGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/test/sendAnnouncementPush',
    'data',
    string,
    string,
    true
  >
>

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528842) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/sendAnnouncementPush`
 * @项目ID 2024
 */
const sendAnnouncementPushGetRequestConfig: SendAnnouncementPushGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_17,
  devUrl: devUrl_0_0_0_17,
  prodUrl: prodUrl_0_0_0_17,
  path: '/announcement/api/test/sendAnnouncementPush',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_17,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'sendAnnouncementPushGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528842) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/sendAnnouncementPush`
 * @项目ID 2024
 */
export const sendAnnouncementPushGet = /*#__PURE__*/ (
  requestData?: SendAnnouncementPushGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<SendAnnouncementPushGetResponse>(prepare(sendAnnouncementPushGetRequestConfig, requestData), ...args)
}

sendAnnouncementPushGet.requestConfig = sendAnnouncementPushGetRequestConfig

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528850) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/es`
 * @项目ID 2024
 */
export interface EsGetRequest {}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528850) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/es`
 * @项目ID 2024
 */
export interface EsGetResponse {
  success?: boolean
  result?: {}
  code?: string
  message?: string
}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528850) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/es`
 * @项目ID 2024
 */
type EsGetRequestConfig = Readonly<
  RequestConfig<'http://yapi.cai-inc.com/mock/2024', '', '', '/announcement/api/test/es', 'data', string, string, true>
>

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528850) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/es`
 * @项目ID 2024
 */
const esGetRequestConfig: EsGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_17,
  devUrl: devUrl_0_0_0_17,
  prodUrl: prodUrl_0_0_0_17,
  path: '/announcement/api/test/es',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_17,
  paramNames: [],
  queryNames: [],
  requestDataOptional: true,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'esGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [清洗发布配置表 announcement_originator↗](http://yapi.cai-inc.com/project/2024/interface/api/528850) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `GET /announcement/api/test/es`
 * @项目ID 2024
 */
export const esGet = /*#__PURE__*/ (requestData?: EsGetRequest, ...args: UserRequestRestArgs) => {
  return request<EsGetResponse>(prepare(esGetRequestConfig, requestData), ...args)
}

esGet.requestConfig = esGetRequestConfig

/**
 * 接口 [printAnnouncementContent↗](http://yapi.cai-inc.com/project/2024/interface/api/528858) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `POST /announcement/api/test/printAnnouncementContent`
 * @项目ID 2024
 */
export interface PrintAnnouncementContentPostRequest {
  announcementId?: string
  /**
   * The value is used for character storage.
   */
  'htmlText.value[0]'?: string
  /**
   * The count is the number of characters used.
   */
  'htmlText.count'?: string
  /**
   * The value is used for character storage.
   */
  'firstPageHtmlStr.value[0]'?: string
  /**
   * The count is the number of characters used.
   */
  'firstPageHtmlStr.count'?: string
  /**
   * 保存路径
   */
  pdfDir?: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 是否有水印
   */
  isWater?: string
  /**
   * 水印图片  如: water-inquiry.png (水印图片保存在本服务路径下)  详情见枚举:WaterImgEnum
   */
  imag?: string
  /**
   * 水印图片内容
   */
  waterImgContent?: string
  /**
   * 字符串水印透明度  默认 0.2
   * 注:不控制图片水印,范围 0 ~ 1,watermarkOpacity<=0 水印完全透明,watermarkOpacity>=1 水印不透明
   */
  watermarkOpacity?: string
  /**
   * 水印图片索引  如: 1 表示从第一页开始打水印
   */
  waterImgStartIndex?: string
  /**
   * 正文索引  如: 1 表示正文从第一页开始,没有首页   2 表示正文从第二页开始,有一页首页,页码从第二页开始
   */
  firstPageNo?: string
  /**
   * htmlText 中封面的分隔 字符串  <div name="page-cover" style="page-break-after:always;"></div>
   */
  firstPageBreakStr?: string
  /**
   * html 头
   */
  htmlStart?: string
  /**
   * html 尾部
   */
  htmlEnd?: string
  /**
   * 是否上传至 oss 标志  默认上传   true:上传  false 或 null:不上传
   */
  uploadToOssFlag?: string
  /**
   * oss 文件名称
   */
  ossFileName?: string
  /**
   * 文件数量
   */
  fileNum?: string
  /**
   * 区划
   */
  districtCode?: string
  /**
   * cn.gov.zcy.dc.plug.model.ZcyBucetEnum 值
   */
  bucketKey?: string
  /**
   * cn.gov.zcy.dc.plug.model.ZcyOssBusiEnum 值
   */
  ossBizCode?: string
  /**
   * 生成的 pdf 文件 大小
   */
  pdfLength?: string
  /**
   * oss 文件 key, 说明: 入参不为空时,不生成新的pdf,做覆盖操作
   */
  ossFileKey?: string
  /**
   * 上传 用户 id
   */
  userId?: string
  /**
   * 是否有页眉线,默认有
   */
  isNeedHeaderLine?: string
  /**
   * 是否有页脚线,默认有
   */
  isNeedFooterLine?: string
  /**
   * 是否需要页码,默认true 需要
   */
  isNeedPageNumber?: string
  /**
   * 页码样式code 见枚举 PageStyleEnum,默认 PAGE_STYPE_ONE
   */
  pageStyleCode?: string
  /**
   * 纸张大小 类型,默认 A4  见枚举 PageSizeTypeEnum,如果没有对应纸张可以设置 pageSizeHeight  pageSizeWidth
   */
  pageSizeType?: string
  /**
   * 页面高度 mm,默认 A4 的 高度
   */
  pageSizeHeight?: string
  /**
   * 页面宽度 mm,默认 A4 的 宽度
   */
  pageSizeWidth?: string
  /**
   * 是否设置附件标识,设置为true,下载时浏览器将不会预览,直接下载,默认为true
   */
  isAttachment?: string
  /**
   * 页眉html 内容,只包含body 中的 html 内容
   */
  headerHtmlContent?: string
  /**
   * 是否设置pdf方向为横向 ,默认 纵向
   */
  isLandscapeOrientation?: string
  /**
   * 页脚内容
   */
  footHtmlContent?: string
  /**
   * LEFT :LEFT
   * RIGHT :RIGHT
   */
  footerAlign?: string
  /**
   * 页面边距
   */
  pageMarginTop?: string
  pageMarginBottom?: string
}

/**
 * 接口 [printAnnouncementContent↗](http://yapi.cai-inc.com/project/2024/interface/api/528858) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `POST /announcement/api/test/printAnnouncementContent`
 * @项目ID 2024
 */
export interface PrintAnnouncementContentPostResponse {
  success?: boolean
  result?: string
  error?: string
}

/**
 * 接口 [printAnnouncementContent↗](http://yapi.cai-inc.com/project/2024/interface/api/528858) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `POST /announcement/api/test/printAnnouncementContent`
 * @项目ID 2024
 */
type PrintAnnouncementContentPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/test/printAnnouncementContent',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [printAnnouncementContent↗](http://yapi.cai-inc.com/project/2024/interface/api/528858) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `POST /announcement/api/test/printAnnouncementContent`
 * @项目ID 2024
 */
const printAnnouncementContentPostRequestConfig: PrintAnnouncementContentPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_17,
  devUrl: devUrl_0_0_0_17,
  prodUrl: prodUrl_0_0_0_17,
  path: '/announcement/api/test/printAnnouncementContent',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.form,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_17,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'printAnnouncementContentPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [printAnnouncementContent↗](http://yapi.cai-inc.com/project/2024/interface/api/528858) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [TestController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_53099)
 * @请求头 `POST /announcement/api/test/printAnnouncementContent`
 * @项目ID 2024
 */
export const printAnnouncementContentPost = /*#__PURE__*/ (
  requestData: PrintAnnouncementContentPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<PrintAnnouncementContentPostResponse>(
    prepare(printAnnouncementContentPostRequestConfig, requestData),
    ...args,
  )
}

printAnnouncementContentPost.requestConfig = printAnnouncementContentPostRequestConfig

/* prettier-ignore-end */
