/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_29 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_29 = '' as any
const prodUrl_0_0_0_29 = '' as any
const dataKey_0_0_0_29 = 'data' as any

/**
 * 接口 [获取公告项目列表(包含手工项目+项目采购项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480786) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/projectList`
 * @项目ID 2024
 */
export interface ProjectListGetRequest {
  /**
   * 公告类型
   */
  announcementType?: string
  /**
   * 采购方式
   */
  purchaseWay?: string
  districtCode?: string
  /**
   * 项目名称
   */
  projectName?: string
  /**
   * 页码
   */
  pageNo?: string
  /**
   * 一页显示条数
   */
  pageSize?: string
}

/**
 * 接口 [获取公告项目列表(包含手工项目+项目采购项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480786) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/projectList`
 * @项目ID 2024
 */
export interface ProjectListGetResponse {
  success?: boolean
  result?: {
    data?: {
      /**
       * 项目id
       */
      projectId?: string
      /**
       * 项目编号
       */
      projectNo?: string
      /**
       * 项目名称
       */
      projectName?: string
      /**
       * 采购类型编码
       */
      purchaseType?: string
      /**
       * 采购类型名称
       */
      purchaseTypeName?: string
      /**
       * 采购方式编码
       */
      purchaseWay?: string
      /**
       * 采购方式编码名称
       */
      purchaseWayName?: string
      /**
       * 预算总金额
       */
      budgetTotal?: number
      /**
       * 是否平台内项目
       */
      isPlatProject?: boolean
      /**
       * 1-项目采购
       */
      moduleValue?: string
      /**
       * 区划id
       */
      districtId?: string
      /**
       * 项目创建时间
       */
      createdAt?: string
      /**
       * 项目更新时间
       */
      updatedAt?: string
    }[]
    total?: number
    pageable?: {
      page?: number
      size?: number
      sort?: {
        field?: string
        direction?: 'ASC' | 'DESC'
      }[]
    }
  }
  code?: string
  message?: string
}

/**
 * 接口 [获取公告项目列表(包含手工项目+项目采购项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480786) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/projectList`
 * @项目ID 2024
 */
type ProjectListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/project/projectList',
    'data',
    string,
    'announcementType' | 'purchaseWay' | 'districtCode' | 'projectName' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [获取公告项目列表(包含手工项目+项目采购项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480786) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/projectList`
 * @项目ID 2024
 */
const projectListGetRequestConfig: ProjectListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_29,
  devUrl: devUrl_0_0_0_29,
  prodUrl: prodUrl_0_0_0_29,
  path: '/announcement/api/project/projectList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_29,
  paramNames: [],
  queryNames: ['announcementType', 'purchaseWay', 'districtCode', 'projectName', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'projectListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告项目列表(包含手工项目+项目采购项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480786) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/projectList`
 * @项目ID 2024
 */
export const projectListGet = /*#__PURE__*/ (requestData: ProjectListGetRequest, ...args: UserRequestRestArgs) => {
  return request<ProjectListGetResponse>(prepare(projectListGetRequestConfig, requestData), ...args)
}

projectListGet.requestConfig = projectListGetRequestConfig

/**
 * 接口 [获取公告项目列表(只包含手工项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480794) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/announcementProjectList`
 * @项目ID 2024
 */
export interface AnnouncementProjectListGetRequest {
  /**
   * 采购方式
   */
  purchaseWay?: string
  districtCode?: string
  /**
   * 项目名称
   */
  projectName?: string
  /**
   * 页码
   */
  pageNo?: string
  /**
   * 一页显示条数
   */
  pageSize?: string
}

/**
 * 接口 [获取公告项目列表(只包含手工项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480794) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/announcementProjectList`
 * @项目ID 2024
 */
export interface AnnouncementProjectListGetResponse {
  success?: boolean
  result?: {
    data?: {
      /**
       * 项目id
       */
      projectId?: string
      /**
       * 项目编号
       */
      projectNo?: string
      /**
       * 项目名称
       */
      projectName?: string
      /**
       * 采购类型编码
       */
      purchaseType?: string
      /**
       * 采购类型名称
       */
      purchaseTypeName?: string
      /**
       * 采购方式编码
       */
      purchaseWay?: string
      /**
       * 采购方式编码名称
       */
      purchaseWayName?: string
      /**
       * 预算总金额
       */
      budgetTotal?: number
      /**
       * 是否平台内项目
       */
      isPlatProject?: boolean
      /**
       * 1-项目采购
       */
      moduleValue?: string
      /**
       * 区划id
       */
      districtId?: string
      /**
       * 项目创建时间
       */
      createdAt?: string
      /**
       * 项目更新时间
       */
      updatedAt?: string
    }[]
    total?: number
    pageable?: {
      page?: number
      size?: number
      sort?: {
        field?: string
        direction?: 'ASC' | 'DESC'
      }[]
    }
  }
  code?: string
  message?: string
}

/**
 * 接口 [获取公告项目列表(只包含手工项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480794) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/announcementProjectList`
 * @项目ID 2024
 */
type AnnouncementProjectListGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/project/announcementProjectList',
    'data',
    string,
    'purchaseWay' | 'districtCode' | 'projectName' | 'pageNo' | 'pageSize',
    false
  >
>

/**
 * 接口 [获取公告项目列表(只包含手工项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480794) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/announcementProjectList`
 * @项目ID 2024
 */
const announcementProjectListGetRequestConfig: AnnouncementProjectListGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_29,
  devUrl: devUrl_0_0_0_29,
  prodUrl: prodUrl_0_0_0_29,
  path: '/announcement/api/project/announcementProjectList',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_29,
  paramNames: [],
  queryNames: ['purchaseWay', 'districtCode', 'projectName', 'pageNo', 'pageSize'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'announcementProjectListGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取公告项目列表(只包含手工项目)↗](http://yapi.cai-inc.com/project/2024/interface/api/480794) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/announcementProjectList`
 * @项目ID 2024
 */
export const announcementProjectListGet = /*#__PURE__*/ (
  requestData: AnnouncementProjectListGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<AnnouncementProjectListGetResponse>(
    prepare(announcementProjectListGetRequestConfig, requestData),
    ...args,
  )
}

announcementProjectListGet.requestConfig = announcementProjectListGetRequestConfig

/**
 * 接口 [公告信息同步-从已有项目发起↗](http://yapi.cai-inc.com/project/2024/interface/api/488714) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/syncMetadata`
 * @项目ID 2024
 */
export interface SyncMetadataGetRequest {
  serialNum?: string
  announcementType?: string
}

/**
 * 接口 [公告信息同步-从已有项目发起↗](http://yapi.cai-inc.com/project/2024/interface/api/488714) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/syncMetadata`
 * @项目ID 2024
 */
export interface SyncMetadataGetResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [公告信息同步-从已有项目发起↗](http://yapi.cai-inc.com/project/2024/interface/api/488714) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/syncMetadata`
 * @项目ID 2024
 */
type SyncMetadataGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/project/syncMetadata',
    'data',
    string,
    'serialNum' | 'announcementType',
    false
  >
>

/**
 * 接口 [公告信息同步-从已有项目发起↗](http://yapi.cai-inc.com/project/2024/interface/api/488714) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/syncMetadata`
 * @项目ID 2024
 */
const syncMetadataGetRequestConfig: SyncMetadataGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_29,
  devUrl: devUrl_0_0_0_29,
  prodUrl: prodUrl_0_0_0_29,
  path: '/announcement/api/project/syncMetadata',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_29,
  paramNames: [],
  queryNames: ['serialNum', 'announcementType'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'syncMetadataGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [公告信息同步-从已有项目发起↗](http://yapi.cai-inc.com/project/2024/interface/api/488714) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [ProjectController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_52148)
 * @请求头 `GET /announcement/api/project/syncMetadata`
 * @项目ID 2024
 */
export const syncMetadataGet = /*#__PURE__*/ (requestData: SyncMetadataGetRequest, ...args: UserRequestRestArgs) => {
  return request<SyncMetadataGetResponse>(prepare(syncMetadataGetRequestConfig, requestData), ...args)
}

syncMetadataGet.requestConfig = syncMetadataGetRequestConfig

/* prettier-ignore-end */
