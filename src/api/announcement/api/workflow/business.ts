/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_14 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_14 = '' as any
const prodUrl_0_0_0_14 = '' as any
const dataKey_0_0_0_14 = 'data' as any

/**
 * 接口 [业务公告流程进度条显示↗](http://yapi.cai-inc.com/project/2024/interface/api/423954) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementWorkflowController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51058)
 * @请求头 `GET /announcement/api/workflow/business/progress`
 * @项目ID 2024
 */
export interface ProgressGetRequest {
  announcementId?: string
}

/**
 * 接口 [业务公告流程进度条显示↗](http://yapi.cai-inc.com/project/2024/interface/api/423954) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementWorkflowController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51058)
 * @请求头 `GET /announcement/api/workflow/business/progress`
 * @项目ID 2024
 */
export interface ProgressGetResponse {
  success?: boolean
  result?: {
    /**
     * 是否是业务公告
     */
    isBusiness?: boolean
    /**
     * 公告进度节点
     */
    currentStep?: number
  }
  code?: string
  message?: string
}

/**
 * 接口 [业务公告流程进度条显示↗](http://yapi.cai-inc.com/project/2024/interface/api/423954) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementWorkflowController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51058)
 * @请求头 `GET /announcement/api/workflow/business/progress`
 * @项目ID 2024
 */
type ProgressGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/api/workflow/business/progress',
    'data',
    string,
    'announcementId',
    false
  >
>

/**
 * 接口 [业务公告流程进度条显示↗](http://yapi.cai-inc.com/project/2024/interface/api/423954) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementWorkflowController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51058)
 * @请求头 `GET /announcement/api/workflow/business/progress`
 * @项目ID 2024
 */
const progressGetRequestConfig: ProgressGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_14,
  devUrl: devUrl_0_0_0_14,
  prodUrl: prodUrl_0_0_0_14,
  path: '/announcement/api/workflow/business/progress',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_14,
  paramNames: [],
  queryNames: ['announcementId'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'progressGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [业务公告流程进度条显示↗](http://yapi.cai-inc.com/project/2024/interface/api/423954) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementWorkflowController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51058)
 * @请求头 `GET /announcement/api/workflow/business/progress`
 * @项目ID 2024
 */
export const progressGet = /*#__PURE__*/ (requestData: ProgressGetRequest, ...args: UserRequestRestArgs) => {
  return request<ProgressGetResponse>(prepare(progressGetRequestConfig, requestData), ...args)
}

progressGet.requestConfig = progressGetRequestConfig

/* prettier-ignore-end */
