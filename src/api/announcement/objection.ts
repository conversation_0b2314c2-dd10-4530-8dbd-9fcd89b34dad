/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_0_0_0_21 = 'http://yapi.cai-inc.com/mock/2024' as any
const devUrl_0_0_0_21 = '' as any
const prodUrl_0_0_0_21 = '' as any
const dataKey_0_0_0_21 = 'data' as any

/**
 * 接口 [异议操作↗](http://yapi.cai-inc.com/project/2024/interface/api/424434) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `POST /announcement/objection/operObjection`
 * @项目ID 2024
 */
export interface OperObjectionPostRequest {
  id?: number
  paas?: boolean
  remark?: string
  digest?: string
}

/**
 * 接口 [异议操作↗](http://yapi.cai-inc.com/project/2024/interface/api/424434) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `POST /announcement/objection/operObjection`
 * @项目ID 2024
 */
export interface OperObjectionPostResponse {
  success?: boolean
  result?: boolean
  error?: string
}

/**
 * 接口 [异议操作↗](http://yapi.cai-inc.com/project/2024/interface/api/424434) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `POST /announcement/objection/operObjection`
 * @项目ID 2024
 */
type OperObjectionPostRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/objection/operObjection',
    'data',
    string,
    string,
    false
  >
>

/**
 * 接口 [异议操作↗](http://yapi.cai-inc.com/project/2024/interface/api/424434) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `POST /announcement/objection/operObjection`
 * @项目ID 2024
 */
const operObjectionPostRequestConfig: OperObjectionPostRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_21,
  devUrl: devUrl_0_0_0_21,
  prodUrl: prodUrl_0_0_0_21,
  path: '/announcement/objection/operObjection',
  method: Method.POST,
  requestHeaders: {},
  requestBodyType: RequestBodyType.json,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_21,
  paramNames: [],
  queryNames: [],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'operObjectionPost',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [异议操作↗](http://yapi.cai-inc.com/project/2024/interface/api/424434) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `POST /announcement/objection/operObjection`
 * @项目ID 2024
 */
export const operObjectionPost = /*#__PURE__*/ (
  requestData: OperObjectionPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<OperObjectionPostResponse>(prepare(operObjectionPostRequestConfig, requestData), ...args)
}

operObjectionPost.requestConfig = operObjectionPostRequestConfig

/**
 * 接口 [获取异议信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424442) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `GET /announcement/objection/objectionDto`
 * @项目ID 2024
 */
export interface ObjectionDtoGetRequest {
  id?: string
}

/**
 * 接口 [获取异议信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424442) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `GET /announcement/objection/objectionDto`
 * @项目ID 2024
 */
export interface ObjectionDtoGetResponse {
  success?: boolean
  result?: {}
  error?: string
}

/**
 * 接口 [获取异议信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424442) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `GET /announcement/objection/objectionDto`
 * @项目ID 2024
 */
type ObjectionDtoGetRequestConfig = Readonly<
  RequestConfig<
    'http://yapi.cai-inc.com/mock/2024',
    '',
    '',
    '/announcement/objection/objectionDto',
    'data',
    string,
    'id',
    false
  >
>

/**
 * 接口 [获取异议信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424442) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `GET /announcement/objection/objectionDto`
 * @项目ID 2024
 */
const objectionDtoGetRequestConfig: ObjectionDtoGetRequestConfig = /*#__PURE__*/ {
  mockUrl: mockUrl_0_0_0_21,
  devUrl: devUrl_0_0_0_21,
  prodUrl: prodUrl_0_0_0_21,
  path: '/announcement/objection/objectionDto',
  method: Method.GET,
  requestHeaders: {},
  requestBodyType: RequestBodyType.query,
  responseBodyType: ResponseBodyType.json,
  dataKey: dataKey_0_0_0_21,
  paramNames: [],
  queryNames: ['id'],
  requestDataOptional: false,
  requestDataJsonSchema: {},
  responseDataJsonSchema: {},
  requestFunctionName: 'objectionDtoGet',
  queryStringArrayFormat: QueryStringArrayFormat.brackets,
  extraInfo: {},
}

/**
 * 接口 [获取异议信息↗](http://yapi.cai-inc.com/project/2024/interface/api/424442) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [AnnouncementObjectionController↗](http://yapi.cai-inc.com/project/2024/interface/api/cat_51065)
 * @请求头 `GET /announcement/objection/objectionDto`
 * @项目ID 2024
 */
export const objectionDtoGet = /*#__PURE__*/ (requestData: ObjectionDtoGetRequest, ...args: UserRequestRestArgs) => {
  return request<ObjectionDtoGetResponse>(prepare(objectionDtoGetRequestConfig, requestData), ...args)
}

objectionDtoGet.requestConfig = objectionDtoGetRequestConfig

/* prettier-ignore-end */
