/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_4560_54099 = '' as any
const devUrl_4560_54099 = '' as any
const prodUrl_4560_54099 = '' as any
const dataKey_4560_54099 = 'data' as any

/**
 * 接口 [采购意向退回↗](undefined) 的 **请求类型**
 *
 * @状态 已完成
 * @分类 [ProcurementIntentionController↗](undefined)
 * @请求头 `POST /api/procurement/project/purchaseIntention/back`
 * @更新时间 `2025-03-07 16:50:30`
 * @项目ID 4560
 */
export interface ApiProcurementProjectPurchaseIntentionBackPostRequest {
  /**
   * 项目id
   */
  projectId: string
}

/**
 * 接口 [采购意向退回↗](undefined) 的 **返回类型**
 *
 * @状态 已完成
 * @分类 [ProcurementIntentionController↗](undefined)
 * @请求头 `POST /api/procurement/project/purchaseIntention/back`
 * @更新时间 `2025-03-07 16:50:30`
 * @项目ID 4560
 */
export interface ApiProcurementProjectPurchaseIntentionBackPostResponse {
  success?: boolean
  result?: boolean
  code?: string
  message?: string
}

/**
 * 接口 [采购意向退回↗](undefined) 的 **请求配置的类型**
 *
 * @状态 已完成
 * @分类 [ProcurementIntentionController↗](undefined)
 * @请求头 `POST /api/procurement/project/purchaseIntention/back`
 * @更新时间 `2025-03-07 16:50:30`
 * @项目ID 4560
 */
type ApiProcurementProjectPurchaseIntentionBackPostRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/procurement/project/purchaseIntention/back',
    'data',
    string,
    'projectId',
    false
  >
>

/**
 * 接口 [采购意向退回↗](undefined) 的 **请求配置**
 *
 * @状态 已完成
 * @分类 [ProcurementIntentionController↗](undefined)
 * @请求头 `POST /api/procurement/project/purchaseIntention/back`
 * @更新时间 `2025-03-07 16:50:30`
 * @项目ID 4560
 */
const apiProcurementProjectPurchaseIntentionBackPostRequestConfig: ApiProcurementProjectPurchaseIntentionBackPostRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_4560_54099,
    devUrl: devUrl_4560_54099,
    prodUrl: prodUrl_4560_54099,
    path: '/api/procurement/project/purchaseIntention/back',
    method: Method.POST,
    requestHeaders: {},
    requestBodyType: RequestBodyType.none,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_4560_54099,
    paramNames: [],
    queryNames: ['projectId'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiProcurementProjectPurchaseIntentionBackPost',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [采购意向退回↗](undefined) 的 **请求函数**
 *
 * @状态 已完成
 * @分类 [ProcurementIntentionController↗](undefined)
 * @请求头 `POST /api/procurement/project/purchaseIntention/back`
 * @更新时间 `2025-03-07 16:50:30`
 * @项目ID 4560
 */
export const apiProcurementProjectPurchaseIntentionBackPost = /*#__PURE__*/ (
  requestData: ApiProcurementProjectPurchaseIntentionBackPostRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiProcurementProjectPurchaseIntentionBackPostResponse>(
    prepare(apiProcurementProjectPurchaseIntentionBackPostRequestConfig, requestData),
    ...args,
  )
}

apiProcurementProjectPurchaseIntentionBackPost.requestConfig =
  apiProcurementProjectPurchaseIntentionBackPostRequestConfig

/* prettier-ignore-end */
