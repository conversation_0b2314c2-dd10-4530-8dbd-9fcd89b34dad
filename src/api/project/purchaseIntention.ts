/* prettier-ignore-start */
/* tslint:disable */
/* eslint-disable */

/* 该文件由 yapi-to-typescript 自动生成，请勿直接修改！！！ */

// @ts-ignore
// prettier-ignore
import { QueryStringArrayFormat, Method, RequestBodyType, ResponseBodyType, FileData, prepare } from 'yapi-to-typescript'
// @ts-ignore
// prettier-ignore
import type { RequestConfig, RequestFunctionRestArgs } from 'yapi-to-typescript'
// @ts-ignore
import request from '../../ajax/index'

type UserRequestRestArgs = RequestFunctionRestArgs<typeof request>

// Request: 目前 React Hooks 功能有用到
export type Request<
  TRequestData,
  TRequestConfig extends RequestConfig,
  TRequestResult,
> = (TRequestConfig['requestDataOptional'] extends true
  ? (requestData?: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult
  : (requestData: TRequestData, ...args: RequestFunctionRestArgs<typeof request>) => TRequestResult) & {
  requestConfig: TRequestConfig
}

const mockUrl_4560_48468 = '' as any
const devUrl_4560_48468 = '' as any
const prodUrl_4560_48468 = '' as any
const dataKey_4560_48468 = 'data' as any

/**
 * 接口 [获取采购意向公开配置↗](undefined) 的 **请求类型**
 *
 * @状态 未完成
 * @分类 [采购意向公开↗](undefined)
 * @请求头 `GET /api/procurement/project/purchaseIntention/intentionConfig`
 * @项目ID 4560
 */
export interface ApiProcurementProjectPurchaseIntentionIntentionConfigGetRequest {
  /**
   * 采购项目id
   */
  projectId: string
}

/**
 * 接口 [获取采购意向公开配置↗](undefined) 的 **返回类型**
 *
 * @状态 未完成
 * @分类 [采购意向公开↗](undefined)
 * @请求头 `GET /api/procurement/project/purchaseIntention/intentionConfig`
 * @项目ID 4560
 */
export interface ApiProcurementProjectPurchaseIntentionIntentionConfigGetResponse {
  success?: boolean
  result?: {
    /**
     * 全流程节点中采购意向公开发布是否调整至关联预算页面
     * true-关联，false-不关联
     */
    needRelateBudgetOfPublicProcurementIntention?: boolean
  }
}

/**
 * 接口 [获取采购意向公开配置↗](undefined) 的 **请求配置的类型**
 *
 * @状态 未完成
 * @分类 [采购意向公开↗](undefined)
 * @请求头 `GET /api/procurement/project/purchaseIntention/intentionConfig`
 * @项目ID 4560
 */
type ApiProcurementProjectPurchaseIntentionIntentionConfigGetRequestConfig = Readonly<
  RequestConfig<
    string,
    string,
    string,
    '/api/procurement/project/purchaseIntention/intentionConfig',
    'data',
    string,
    'projectId',
    false
  >
>

/**
 * 接口 [获取采购意向公开配置↗](undefined) 的 **请求配置**
 *
 * @状态 未完成
 * @分类 [采购意向公开↗](undefined)
 * @请求头 `GET /api/procurement/project/purchaseIntention/intentionConfig`
 * @项目ID 4560
 */
const apiProcurementProjectPurchaseIntentionIntentionConfigGetRequestConfig: ApiProcurementProjectPurchaseIntentionIntentionConfigGetRequestConfig =
  /*#__PURE__*/ {
    mockUrl: mockUrl_4560_48468,
    devUrl: devUrl_4560_48468,
    prodUrl: prodUrl_4560_48468,
    path: '/api/procurement/project/purchaseIntention/intentionConfig',
    method: Method.GET,
    requestHeaders: {},
    requestBodyType: RequestBodyType.query,
    responseBodyType: ResponseBodyType.json,
    dataKey: dataKey_4560_48468,
    paramNames: [],
    queryNames: ['projectId'],
    requestDataOptional: false,
    requestDataJsonSchema: {},
    responseDataJsonSchema: {},
    requestFunctionName: 'apiProcurementProjectPurchaseIntentionIntentionConfigGet',
    queryStringArrayFormat: QueryStringArrayFormat.brackets,
    extraInfo: {},
  }

/**
 * 接口 [获取采购意向公开配置↗](undefined) 的 **请求函数**
 *
 * @状态 未完成
 * @分类 [采购意向公开↗](undefined)
 * @请求头 `GET /api/procurement/project/purchaseIntention/intentionConfig`
 * @项目ID 4560
 */
export const apiProcurementProjectPurchaseIntentionIntentionConfigGet = /*#__PURE__*/ (
  requestData: ApiProcurementProjectPurchaseIntentionIntentionConfigGetRequest,
  ...args: UserRequestRestArgs
) => {
  return request<ApiProcurementProjectPurchaseIntentionIntentionConfigGetResponse>(
    prepare(apiProcurementProjectPurchaseIntentionIntentionConfigGetRequestConfig, requestData),
    ...args,
  )
}

apiProcurementProjectPurchaseIntentionIntentionConfigGet.requestConfig =
  apiProcurementProjectPurchaseIntentionIntentionConfigGetRequestConfig

/* prettier-ignore-end */
