/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-08-07 09:49:46
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-10-16 17:37:10
 * @LastEditTime: 2024-08-13 21:30:23
 * @FilePath: /zcy-announcement-v2-front/src/components/LcyDrainageModal/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-08-07 09:49:46
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-08-07 11:18:43
 * @FilePath: /zcy-announcement-v2-front/src/components/LcyDrainageModal/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import pushLog from 'src/utils/pushLog';
import { Modal, Button } from 'doraemon';

export const LcyDrainageModal = ({ 
  visible, 
  whetherToSettleIn, 
  annBigType, 
  okText, 
  handleBack,
  isLeCaiYun,
}) => {
  const handleOk = () => {
    handleLcyLink(whetherToSettleIn, annBigType);
    // 埋点
    try {
      // eslint-disable-next-line camelcase
      const g_UTM = window.g_UTM || {};
      g_UTM.send('click', ['cLcyDrainageButton', 'dLcyDrainageButton'], {
        key: 'value',
      }, 'acLcyDrainageButton');
    } catch (err) {
      pushLog(JSON.stringify(err), 'warning');
    }
  };

  return (
    <Modal 
      visible={visible} 
      closable={false}
      title="平台通知"
      footer={[
        <Button 
          key="back" 
          onClick={handleBack}
        >返回上一页
        </Button>,
        !isLeCaiYun && (
          <Button 
            key="submit" 
            type="primary" 
            onClick={handleOk}
          >
            <span>
              {okText}
            </span>
          </Button>
        ),
      ]}
    >
      <div
        data-utm-c="cLcyDrainageModal" 
        data-utm-expose="lcyDrainageModal" 
        data-utm-actionCode="acLcyDrainageModal"
      >
        {isLeCaiYun ? (
          <>
            为规范管理非政府采购公告信息发布，乐采云平台全面关闭所有手工公告发布的权限，仅支持电子招投标全流程业务公告发布。
          </>
        ) : (
          <>
            为强化浙江省政府采购网公告信息的规范管理，政采云平台将于2024年8月15日起分阶段限制非政府采购手工公告的手动发布权限，至11月1日全面取消该权限。
            同时，乐采云平台2024年8月15日起将同步启用非政府采购公告手动发布功能的相关权限，请各代理机构及时入驻乐采云平台以确保业务不受影响。
          </>
        )}
      </div>
    </Modal>
  );
};

export const handleLcyLink = (whetherToSettleIn, annBigType) => {
  if (whetherToSettleIn) {
    const link = `https://middle-lcy.lecaiyun.com${window.location.pathname}#/manage/list/${annBigType}?lcyModalTag=true`;
    window.location.href = `${window.envHref.middle}/zcy/user-web/v2/sso/jump?requestType=sync&platform_id=zcy&token_type=1&account_type=2&env=lcy&redirect_uri=${encodeURIComponent(link)}`;
  } else {
    window.location.href = 'https://login-lcy.lecaiyun.com/user-login/#';
  }
};

