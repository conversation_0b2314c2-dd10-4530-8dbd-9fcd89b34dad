

import {
  Modal,
} from 'doraemon';
import { publicNoticeOfPurchaseIntentionAuditReportPost } from 'src/api/announcement/api/print';

const getFileLink = announcementId => 
  publicNoticeOfPurchaseIntentionAuditReportPost({ announcementId }).then(res => {
    if (!res.success) return;
    return res.result;
  });

/**
    * 1.添加状态和工作流的节点对应
    * 2.对应信息来自数据库的配置
    * 3.配置最长状态和节点对应关系
    * 4.node非空是流程节点
    */
// DRAFT(0, '草稿', ''),
// CREATED(1, '已提交', ''),
// PRECHECK1(11, '一级审核', 'zcy.announcement.check'),
// PRECHECK2(12, '二级审核', 'zcy.announcement.preCheck2'),
// PRECHECK3(13, '三级审核', 'zcy.announcement.preCheck3'),
// PRECHECK4(14, '四级审核', 'zcy.announcement.preCheck4'),
// BUDGET_DIRECTOR_CHECK(15, '预算主管部门公告审核', 'zcy.announcement.budget.director.check'),
// FINANCIAL_REGULATION_CHECK(16, '财政监管部门公告审核', 'zcy.announcement.financial.regulation.check'),
// CHECKED(2, '已审核', ''),
// CHECK_FAILED(3, '已退回', ''),
// RELEASED(4, '已发布', ''),
// PERIOD_ENDED(5, '公告结束', ''),
// REVOKED(6, '已撤回', ''),
// ABOLISHED(7, '已作废', ''),
// CANCEL_UNDER_REVIEW(8, '取消审核中', ''),
// CANCELLED(9, '已取消', ''),
// CREATED_WITHOUT_FLOW(199, '已提交无须审核', ''),
// OBJECTION(41, '异议中', ''),
// BLOCK(42, '敏感词校验异常', '')


// 审核中: 11, 12, 13, 14, 15, 16
// 待完善: 0
// 被退回: 3
// 已撤回: 6

// 已发布: 4
// 待发布: 2
// 已结束: 5
// 已取消: 9

export const downLoadAuditReport = (status, announcementId) => {
  return new Promise((resolve) => {
    // 审核中、待完善、被退回
    if ([11, 12, 13, 14, 15, 16, 0, 3, 6].includes(status)) {
      return Modal.confirm({
        title: '当前公告尚未审批完成,是否继续下载？',
        onOk: () => {
          resolve(getFileLink(announcementId));
        },
        onCancel: () => {
          resolve('');
        },
      });
    }
    return resolve(getFileLink(announcementId));
  });
}; 
