import React, { Component } from 'react';
import moment from 'moment';

export default class TimeCountDown extends Component {
  constructor(props) {
    super(props);
    this.state = {
      time: props.time,
    };
  }
  // componentWillReceiveProps(nextProps) {
  //   if (this.props.time !== nextProps.time) {
  //     this.setState({
  //       time: nextProps.time,
  //     });
  //   }
  // }
  componentDidMount() {
    if (this.state.time > 0) {
      this.run();
    }
  }
  timmer = undefined;
  run = () => {
    const { cb } = this.props;
    this.timmer = setTimeout(() => {
      this.setState((state) => {
        if (state.time > 0) {
          state.time -= 1000;
          this.run();
        } else {
          clearTimeout(this.timmer);
          cb && cb();
        }
        return state;
      });
    }, 1000);
  }
  render() {
    const { time } = this.state;
    return (
      <div>
        <i className="anticon zcyicon icon-shijian" />
        &nbsp;
        {moment(time > 0 ? time : 0).utc().format('HH:mm:ss')}
      </div>
    );
  }
}
