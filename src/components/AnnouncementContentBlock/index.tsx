import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Spin, Button } from 'doraemon';
import useDrawer from 'src/hooks/useDrawer';
import request from 'src/utils/request';
import './index.less';

export const previewAnnouncement = params => request('/announcement/api/common/previewAnnouncement', {
  params,
});


const attachContainer = (html = '') => {
  const attachWrapper = (content) => {
    return `
    <div style="padding: 0 75px 75px 75px;width: 800px;font-size: 16px;box-sizing: border-box;">
      ${content}
    </div>
    `;
  };

  return attachWrapper(html);
};

const AnnouncementContentBlock = ({
  title, englishTitle, content, noBordered,
}) => {
  if (!content && !title) return null;
  return (
    <div className={`AnnouncementContentBlock-wrapper ${noBordered ? '' : 'AnnouncementContentBlock-wrapper-bordered'}`}>
      <div className="AnnouncementContentBlock-title"
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: title || '' }}
      />
      {englishTitle ? (
        <div className="AnnouncementContentBlock-title AnnouncementContentBlock-englishTitle"
        // eslint-disable-next-line react/no-danger
          dangerouslySetInnerHTML={{ __html: englishTitle || '' }}
        />
      )
        : null}
      <div className="AnnouncementContentBlock-content"
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: attachContainer(content) || '' }}
      />
    </div>
  );
};

const AnnouncementContentPreviewDrawer = ({ 
  visible, onCancel, id, annTitle, 
  annContent, showTitleBtnGroup, onOk,
}) => {
  const [title, setTitle] = useState();
  const [content, setContent] = useState();
  const [loading, setLoading] = useState(false);


  useEffect(() => {
    setTitle(annTitle);
    setContent(annContent);
  }, [annTitle, annContent]);

  useEffect(() => {
    if (visible && id) {
      getDetail(id);
    }
  }, [visible, id]);

  const getDetail = (announcementId) => {
    setLoading(true);
    return previewAnnouncement({
      announcementId,
    }).then(res => {
      if (!res.success) return;
      setTitle(res.result?.title);
      setContent(res.result?.content);
    }).finally(() => {
      setLoading(false);
    });
  };

  const TitleNode = () => {
    if (!showTitleBtnGroup) {
      return <span>公告预览</span>;
    }

    return (
      <div className="drawer-titleNode-wrapper">
        <span style={{ maxWidth: '500px' }}>公告预览</span>
        <span >
          <Button onClick={onCancel}>取消重推</Button>
          <Button loading={loading}
            onClick={onSubmit}
            type="primary"
            className="zcy-mg-l-12"
          >确定重推
          </Button>
        </span>
      </div>
    );
  };

  const onSubmit = () => {
    setLoading(true);
    Promise.resolve(true).then(() => {
      return onOk();
    }).finally(() => {
      setLoading(false);
      onCancel();
    });
  };

  return (
    <Drawer
      title={<TitleNode />}
      visible={visible}
      width={800}
      className="announcementContentPreview-drawer"
      destroyOnClose
      onClose={onCancel}
    >
      <Spin spinning={loading}>
        <AnnouncementContentBlock noBordered title={title} content={content} />
      </Spin>
    </Drawer>
  );
};


const useAnnouncementContentPreviewDrawer = (props) => {
  return useDrawer(AnnouncementContentPreviewDrawer, props);
};

export { attachContainer, useAnnouncementContentPreviewDrawer, AnnouncementContentPreviewDrawer };
export default AnnouncementContentBlock;
