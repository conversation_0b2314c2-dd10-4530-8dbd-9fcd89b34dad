/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-06-13 09:49:38
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-06-19 10:05:18
 * @FilePath: /zcy-announcement-v2-front/src/components/DistrictCascader/index.tsx
 * @Description: 
 */

import React, { useEffect, useState } from 'react';
import { Cascader, Form } from 'doraemon';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';

import './index.less';

const transTreeList = (
  data,
  curTreeNodes: {
    name: string,
    code: string
  }[] = [],
  res = {},
) => data.map((i) => {
  i.value = i.code + '';
  i.label = i.name;
  i.title = i.name;
  // 树节点路径数据->[000000,330000,339900]
  i.curTreeNodes = [...curTreeNodes, {
    name: i.label,
    code: i.value,
  }];
  res[i.value] = i.curTreeNodes;
  if (i.children) transTreeList(i.children, i.curTreeNodes, res);
  return i;
});

const getTreeData = (data) => {
  const treeNodesMap = {};
  const tree = transTreeList(data, [], treeNodesMap);
  return {
    tree,
    treeNodesMap,
  };
};

const DistrictCascader: React.FC<{
  onChangeCode: (...arg: any[]) => void
  disabled?: boolean
  allowClear?: boolean
  defaultValue?: string
  placeholder?: string
  changeOnSelect?: boolean
  updateDistrictNames?: (...arg: any[])=> void
  localKey?: string
}> = ({
  onChangeCode,
  disabled = false,
  allowClear = false,
  defaultValue = '',
  placeholder = '请选择区划',
  changeOnSelect = false,
  localKey = '',
  updateDistrictNames = () => {},
}) => {
  const [loading, setLoading] = useState(true);
  const [treeData, setTreeData] = useState([]);
  const [casDefaultValue, setCasDefaultValue] = useState([]);
  useEffect(() => {
    getAuthorityDistTreeGet().then(res => {
      if (!res.success) return;
      const { tree, treeNodesMap } = getTreeData(res.result ?? []);
      setTreeData(tree);
      const defValues = getDefaultValue(treeNodesMap, defaultValue);
      setCasDefaultValue(defValues.map(ele => ele.code));
      updateDistrictNames?.(defValues.map(ele => ele.name));
      // setCasDefaultValue(getDefaultValue(treeNodesMap, defaultValue).map(ele => ele.code));
      // updateDistrictNames?.(getDefaultValue(treeNodesMap, defaultValue).map(ele => ele.name));
    }).then(() => setLoading(false));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getDefaultValue = (treeNodesMap, val) => {
    if (!val && !localKey) return [];
    if (val) return (treeNodesMap[val] || []);
    if (localKey) {
      const code = localStorage.getItem(`DistrictCascader_${localKey}`);
      if (!code) return [];
      const districtOptions = treeNodesMap[code];
      if (districtOptions) {
        onChangeCode(districtOptions.map(ele => ele.code), districtOptions);
      }
      return (districtOptions || []);
    }
    return [];
  };

  const beforeOnChange = (value, options, ...args) => {
    if (localKey) {
      const code = value?.[value?.length - 1];
      if (code) {
        localStorage.setItem(`DistrictCascader_${localKey}`, code);
      }
    }
    onChangeCode(value, options, ...args);
  };

  return (
    loading ? null : (
      <Form className="header-select-container">
        <Cascader
          changeOnSelect={changeOnSelect}
          showSearch
          allowClear={allowClear}
          options={treeData}
          disabled={disabled}
          defaultValue={casDefaultValue}
          onChange={beforeOnChange}
          popupClassName="popup-districtCascader"
          placeholder={placeholder}
          getPopupContainer={node => node!}
        />
      </Form>
    )
  );
};


export default DistrictCascader;
