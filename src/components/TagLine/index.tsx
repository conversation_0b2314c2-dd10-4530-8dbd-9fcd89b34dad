import React from 'react';
import {
  Tag,
} from 'doraemon';

const TagLine = ({ tags = [] }) => {
  return tags.length ? (
    <span>标签：
      {tags.map(({ tagDisplay, isValid }, index) => {
        if (isValid) return <Tag style={{ marginBottom: 8 }} color="green" key={index}>{tagDisplay}</Tag>;
        return <Tag style={{ marginBottom: 8 }} key={index}>{tagDisplay}</Tag>;
      })}
    </span>
  ) : null;
};

export default TagLine;
