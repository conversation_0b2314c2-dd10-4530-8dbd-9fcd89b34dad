import React, { Component } from 'react';
import {
  Select,
} from 'doraemon';
import _ from 'lodash';
import {
  fetchOrgListApi,
} from '../../common/services/app';
import './index.less';

const { Option } = Select;

export default class StorageOrgSelect extends Component {
  constructor(props) {
    super(props);
    this.state = {
      orgList: [], // 短信推送机构列表
    };
  }

  componentDidMount = () => {
    this.fetchOrganizations();
  }

  fetchOrganizations = (keywords) => {
    const {
      distCode,
    } = this.props;
    fetchOrgListApi({ keywords, distCode }).then((res) => {
      if (res && res.success) {
        this.setState({
          orgList: res.result,
        });
      }
    });
  }

  render() {
    const { orgList = [] } = this.state;
    return (
      <Select
        {...this.props}
        onSearch={_.debounce(this.fetchOrganizations, 200)}
      >
        {
          !_.isEmpty(orgList) ? orgList.map((org) => {
            return <Option key={org.id}>{org.name}</Option>;
          }) : null
        }
      </Select>
    );
  }
}
