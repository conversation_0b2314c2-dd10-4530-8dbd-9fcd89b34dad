import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import {
  Panel,
  Table,
} from 'doraemon';
import {
  bizStringFormat,
} from '@zcy/utils';
import './index.less';

@connect(({ appCommon }) => ({
  SMSRecordList: appCommon.SMSRecordList,
}))
export default class SMSInfo extends Component {
  componentDidMount() {
    this.fetchSMSRecords();
  }

  fetchSMSRecords = () => {
    const { dispatch, announcementId } = this.props;
    dispatch({
      type: 'appCommon/fetchMessageRecord',
      payload: {
        announcementId,
      },
    });
  }

  getColumns = () => {
    return [{
      title: '序号',
      dataIndex: 'num',
      key: 'num',
      width: 60,
      render: (text, record, index) => {
        return index + 1;
      },
    }, {
      title: '单位名称',
      dataIndex: 'orgName',
      key: 'orgName',
      width: 300,
      render: text => (bizStringFormat(text)),
    }, {
      title: '接收人',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: text => (bizStringFormat(text)),
    }, {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
      width: 200,
      render: text => (bizStringFormat(text)),
    }, {
      title: '发送时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD HH:mm:ss');
      },
    }];
  }

  render() {
    const { SMSRecordList = [] } = this.props;
    return (
      <Panel
        title="短信接收人"
        className="SMS-receiver-info-panel"
      >
        <Table
          columns={this.getColumns()}
          dataSource={SMSRecordList}
          rowKey="id"
        />
      </Panel>
    );
  }
}
