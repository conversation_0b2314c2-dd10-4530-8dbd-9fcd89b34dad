import React, { Component } from 'react';
import {
  Select,
  message,
} from 'doraemon';
import _ from 'lodash';
import {
  fetchReceiverListApi,
} from '../../common/services/app';
import './index.less';

const { Option } = Select;

export default class StorageReceiverSelect extends Component {
  constructor(props) {
    super(props);
    this.state = {
      receiverList: [], // 短信推送人员列表
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.orgId !== this.props.orgId) {
      this.fetchReceivers(nextProps.orgId, '');
    }
  }

  fetchReceivers = (orgId, keywords) => {
    if (!orgId) {
      message.info('请先选择单位名称');
      return;
    }
    fetchReceiverListApi({
      orgId,
      keywords,
    }).then((res) => {
      if (res && res.success) {
        this.setState({
          receiverList: res.result,
        });
      }
    });
  }

  render() {
    const { receiverList = [] } = this.state;
    const { orgId, ...rest } = this.props;
    return (
      <Select
        {...rest}
        onSearch={_.debounce(value => this.fetchReceivers(orgId, value), 100)}
      >
        {
          !_.isEmpty(receiverList) ? receiverList.map((receiver) => {
            return (
              <Option key={receiver.id} phone={receiver.phone}>{receiver.name}</Option>
            );
          }) : []
        }
      </Select>
    );
  }
}
