import React, { Component } from 'react';
import { connect } from 'dva';
import {
  Table,
  Modal,
  Form,
  Input,
  Panel,
  Button,
  message,
} from 'doraemon';
import _ from 'lodash';
import { guid } from 'utils/utils';
import './index.less';
import StorageOrgSelect from './StorageOrgSelect';
import StorageReceiverSelect from './StorageReceiverSelect';

const FormItem = Form.Item;

@connect(({ appCommon }) => ({
  appCommon,
}))
@Form.create()
export default class SMSModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      SMSReceiverList: [{
        tempId: guid(),
      }],
    };
  }

  // 新增
  handleAdd = () => {
    this.setState(
      preState => ({
        SMSReceiverList: preState.SMSReceiverList.concat([
          {
            tempId: guid(),
          },
        ]),
      })
    );
  }

  // 删除
  handleDelete = (tempId) => {
    const { SMSReceiverList } = this.state;
    const result = _.filter(
      SMSReceiverList,
      receiver => receiver.tempId !== tempId
    );
    this.setState({
      SMSReceiverList: result,
    });
  }

  handleReceiverChange = (option, tempId) => {
    // 选中联系人后，将该联系人的手机号回填
    const {
      form: { setFieldsValue },
    } = this.props;
    const { props = {} } = option;
    setFieldsValue({ [`${tempId}.phone`]: props.phone });
  }

  handleOrgChange = (tempId) => {
    const {
      form: { setFieldsValue },
    } = this.props;
    setFieldsValue({ [`${tempId}.receiver`]: undefined });
    setFieldsValue({ [`${tempId}.phone`]: undefined });
  }

  // 发送消息
  onOk = () => {
    const {
      dispatch,
      form: { validateFieldsAndScroll },
      announcementId,
      onCancel,
    } = this.props;
    validateFieldsAndScroll((error, values) => {
      if (error) {
        return;
      }
      const receiver = [];
      const infoList = _.values(values);
      infoList.map((item) => {
        receiver.push({
          orgName: _.get(item, 'orgInfo.label', ''),
          name: _.get(item, 'receiver.label', ''),
          phone: item.phone,
        });
      });
      dispatch({
        type: 'appCommon/sendSMS',
        payload: {
          announcementId,
          receiver,
        },
      }).then((res) => {
        if (res && res.success) {
          message.success('发送成功');
        } else {
          message.error(res.error);
        }
        onCancel();
      });
    });
  }

  getColumns = () => {
    const {
      form,
      distCode,
    } = this.props;
    const { getFieldDecorator } = form;
    return [{
      title: '序号',
      dataIndex: 'num',
      key: 'num',
      width: 70,
      render: (text, record, index) => {
        return index + 1;
      },
    }, {
      title: '单位名称',
      dataIndex: 'orgName',
      key: 'orgName',
      render: (text, record) => {
        return (
          <FormItem>
            {
              getFieldDecorator(`${record.tempId}.orgInfo`, {
                rules: [{ required: true, message: '请选择单位名称' }],
                onChange: () => this.handleOrgChange(record.tempId),
              })(
                <StorageOrgSelect
                  placeholder="请输入以选择单位名称"
                  labelInValue
                  showSearch
                  filterOption={() => true}
                  getPopupContainer={() => document.querySelector('.SMS-receiver-panel')}
                  className="SMS-modal-select"
                  distCode={distCode}
                />
              )
            }
          </FormItem>
        );
      },
    }, {
      title: '接收人',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => {
        const {
          form: { getFieldValue },
        } = this.props;
        const orgInfo = getFieldValue(`${record.tempId}.orgInfo`) || {};
        return (
          <FormItem>
            {
              getFieldDecorator(`${record.tempId}.receiver`, {
                rules: [{ required: true, message: '请选择接收人' }],
                onChange: (value, option) => this.handleReceiverChange(option, record.tempId),
              })(
                <StorageReceiverSelect
                  placeholder="请输入以选择接收人"
                  labelInValue
                  showSearch
                  filterOption={() => true}
                  getPopupContainer={() => document.querySelector('.SMS-receiver-panel')}
                  className="SMS-modal-select"
                  orgId={orgInfo.key}
                />
              )
            }
          </FormItem>
        );
      },
    }, {
      title: '联系方式',
      dataIndex: 'phone',
      key: 'phone',
      render: (text, record) => {
        return (
          <FormItem>
            {getFieldDecorator(`${record.tempId}.phone`, {
              rules: [{
                required: true,
                message: '请填写联系方式',
              }, {
                pattern: new RegExp(/^1[0-9]{10}$/),
                message: '请输入有效的联系方式',
              }],
            })(
              <Input placeholder="请输入" className="SMS-modal-input" />
            )}
          </FormItem>
        );
      },
    }, {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 70,
      render: (text, record) => {
        const { SMSReceiverList } = this.state;
        if (SMSReceiverList.length > 1) {
          return <a onClick={() => this.handleDelete(record.tempId)}>删除</a>;
        }
        return '-';
      },
    }];
  }

  render() {
    const { SMSReceiverList = [] } = this.state;
    const { visible, onCancel } = this.props;
    return (
      <Modal
        title="短信通知"
        visible={visible}
        onCancel={onCancel}
        onOk={this.onOk}
        okText="确认并发送"
        width={780}
        destroyOnClose
      >
        <Panel
          title="采购单位接收人列表"
          extra={
            <Button
              size="small"
              onClick={this.handleAdd}
              type="secondary"
            >新增
            </Button>
          }
          className="SMS-receiver-panel"
        >
          <Table
            columns={this.getColumns()}
            dataSource={SMSReceiverList}
            rowKey="tempId"
            pagination={false}
          />
        </Panel>
      </Modal>
    );
  }
}
