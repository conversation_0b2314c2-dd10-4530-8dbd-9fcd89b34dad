import React from 'react';
import ReactDOM from 'react-dom';

const ModalWrapper: any = (Modal, options: any = {}) => {
  const {
    // 是否在弹窗关闭后进行销毁，默认为 是
    autoDestroy = true,
  } = options;

  Modal.show = (config) => {
    const div = document.createElement('div');
    document.body.appendChild(div);

    function close() {
      render({
        ...config,
        visible: false,
        afterClose: () => destroy(),
        onCancel: close,
        onClose: close,
      });
      // 默认自动销毁
      if (autoDestroy) {
        setTimeout(() => {
          destroy();
        }, 1000);
      }
    }

    function destroy() {
      const unmountResult = ReactDOM.unmountComponentAtNode(div);
      if (unmountResult && div.parentNode) {
        div.parentNode.removeChild(div);
      }
    }

    function render(props) {
      const modal = <Modal {...props} />;
      ReactDOM.render(modal, div);
    }

    render({
      onCancel: close,
      onClose: close,
      ...config,
      visible: true,
    });
    return close;
  };

  return Modal;
};

export default ModalWrapper;
