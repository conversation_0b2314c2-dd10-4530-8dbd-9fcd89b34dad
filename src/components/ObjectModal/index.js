import React, { Component } from 'react';
import {
  Modal,
  Button,
  Form,
  FormGrid,
  Radio,
  message,
} from 'doraemon';
import { connect } from 'dva';

const RadioGroup = Radio.Group;

@connect(({ list, loading }) => ({
  list,
  tableLoading: loading.models.list,
}))
@Form.create()
export default class ObjectModal extends Component {
  onOk = async () => {
    await this.props.form.validateFields((err, values) => {
      if (err) return;
      const { annId } = this.props;
      this.props.dispatch({
        type: 'list/raiseObjection',
        payload: {
          annId,
          ...values,
        },
      });
    });
    message.success('已提交！');
    this.props.reload();
    this.props.hideMe();
  }
  render() {
    const { show, loading, hideMe, form } = this.props;
    return (
      <Modal
        title="异议确认"
        visible={show}
        onCancel={hideMe}
        onOk={this.onOk}
        footer={[
          <Button key="back" onClick={hideMe}>取消</Button>,
          <Button
            // disabled={false}
            key="submit"
            type="primary"
            onClick={this.onOk}
            loading={loading}
          >
            确定
          </Button>,
        ]}
      >
        <br />
        <FormGrid
          formGridItem={[{
            label: '异议结果',
            colSpan: 2,
            labelCol: { span: 10 },
            wrapperCol: { span: 14 },
            render: () => (
              form.getFieldDecorator('objectionState', {
                rules: [{
                  required: true, message: '请选择异议结果',
                }],
              })(
                <RadioGroup
                  options={[{
                    label: '无异议', value: 0,
                  }, {
                    label: '有异议', value: 1,
                  }]}
                />
              )
            ),
          }]}
        />
      </Modal>
    );
  }
}
