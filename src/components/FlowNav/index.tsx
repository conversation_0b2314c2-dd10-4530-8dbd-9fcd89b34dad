import React, { useState } from 'react';
import { Icon } from 'doraemon';
import './index.less';

const FlowNav = ({ onChange, cNodeCode, nodeList }) => {
  const [currentNodeCode, setCurrentNodeCode] = useState(cNodeCode);


  const nodeClick = (nodeCode) => {
    if (currentNodeCode === nodeCode) return;
    setCurrentNodeCode(nodeCode);
    onChange && onChange(nodeCode);
  };
    
  return (
    <div className="flowNav zcy-mg-r-8">
      {
        nodeList.map((ele, index) => (
          <div onClick={() => nodeClick(ele.nodeCode)} key={ele.id} className={`flowNav-node ${currentNodeCode === ele.nodeCode ? 'flowNav-node-selected' : ''}`} >
            <span className="flowNav-node_text zcy-mg-r-8">{index + 1}.{ele.nodeName}</span>
            {
              /**
               * 节点状态  节点状态，0:待配置 1:已保存  2：已提交
               */
              // 已提交
              ele.status === 2 ? (
                <Icon type="check-circle" style={{ fontSize: 16, color: 'green' }} />
              ) : (
                ele.status === 1 ? (
                // 已保存
                  <Icon type="check-circle-o" style={{ fontSize: 16, color: 'green' }} />
                ) : (
                // 待配置
                  <Icon type="exclamation-circle" style={{ fontSize: 16, color: 'gray' }} />
                )
              )
            }
          </div>
        ))
      }
    </div>
  );
};

export default FlowNav;
