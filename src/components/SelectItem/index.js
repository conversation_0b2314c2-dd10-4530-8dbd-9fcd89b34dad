import React, { Component } from 'react';
import { Select, request } from 'doraemon';

const Option = Select.Option;
const getData = res => res.result;
const getItems = options => (options ? options.map(option => (
  <Option key={option.v} value={option.v}>{option.k}</Option>
)) : []);
const cacheKey = Symbol.for('cacheKey');
if (!window[cacheKey]) {
  window[cacheKey] = {};
}
const cache = window[cacheKey];

export default class SelectItem extends Component {
  state = {
    options: [],
    url: '',
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.url !== this.state.url) {
      this.loadData(nextProps.url);
    }
  }
  componentDidMount() {
    this.loadData();
  }
  componentWillUnmount() {
    this.setState = () => { };
  }
  loadData = async (nUrl) => {
    const { url, getOptions = getData } = this.props;
    if (!nUrl && !url) {
      return;
    }
    const res = cache[nUrl || url] ? cache[nUrl || url] : await request(nUrl || url);
    cache[nUrl || url] = res;
    const options = getOptions(res);
    this.setState({
      options,
      url: nUrl || url,
    });
  }
  onSearch = async (name) => {
    const { getOptions = getData } = this.props;
    const { url } = this.state;
    const res = await request(url, {
      params: {
        name,
      },
    });
    const options = getOptions(res);
    this.setState({
      options,
    });
  }
  render() {
    const { transform = getItems, ...rest } = this.props;
    const { options } = this.state;
    delete rest.url;
    delete rest.getOptions;
    return (
      <Select
        {...rest}
        filterOption={false}
        onSearch={this.onSearch}
      >
        {transform(options)}
      </Select>
    );
  }
}
