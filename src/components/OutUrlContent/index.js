import React from 'react';
import { Popover, Divider } from 'doraemon';
import './index.less';

const LinkContent = ({ list }) => {
  return (
    <div className="linkContent">
      {list.map(ele => 
        <p key={list.id} className="linkContent-item">{ele.outName}： <a className="linkContent-link" href={ele.outUrl} target="_blank" >查看公告</a></p>
      )}
    </div>
  );
};


const OutUrlContent = ({ list, hideDivider = false, placement = 'left', btnName = '查看公告链接' }) => {
  return (
    <React.Fragment>
      <Popover placement={placement} content={<LinkContent list={list} />}>
        <a>{btnName}</a>
      </Popover>
      {hideDivider ? null : <Divider type="vertical" />}
    </React.Fragment>
  );
};

export { LinkContent };
export default OutUrlContent;
