/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-06-13 09:49:38
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-06-19 10:05:18
 * @FilePath: /zcy-announcement-v2-front/src/components/DistrictCascader/index.tsx
 * @Description: 
 */

import React, { useEffect, useState } from 'react';
import { TreeSelect } from 'doraemon';
import { getAuthorityDistTreeGet } from 'src/api/announcement/api/district/admin';
import { formatTreeData } from 'src/utils/tree';

const SHOW_CHILD = TreeSelect.SHOW_CHILD;

const DistrictTreeSelect: React.FC<{
  onChange: (...arg: any[]) => void
  disabled?: boolean
  allowClear?: boolean
  placeholder?: string
  width?: number
}> = ({
  onChange,
  width = 300,
  disabled = false,
  allowClear = false,
  placeholder = '请选择目标区划',
}) => {
  const [loading, setLoading] = useState(true);
  const [treeData, setTreeData] = useState([]);
  useEffect(() => {
    getAuthorityDistTreeGet().then(res => {
      if (!res.success) return;
      const tree = formatTreeData(res.result ?? []);
      setTreeData(tree);
    }).then(() => setLoading(false));
  }, []);


  return (
    loading ? null : (
      <TreeSelect
        style={{ width }}
        allowClear={allowClear}
        treeData={treeData}
        getPopupContainer={n => n.parentNode}
        treeCheckable
        showCheckedStrategy={SHOW_CHILD}
        searchPlaceholder={placeholder}
        onChange={onChange}
        disabled={disabled}
        treeNodeFilterProp="label"
      />
    )
  );
};


export default DistrictTreeSelect;
