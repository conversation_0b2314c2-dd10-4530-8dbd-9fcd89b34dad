
.reformation-tag {
    display: table;
    width: 100%;
    min-height: 36px;
    margin-bottom: 10px;
    background: #f8f3e7;
    .reformation-title {
        display: table-cell;
        position: relative;
        height: 100%;
        padding: 0 14px;
        background: linear-gradient(to right, #ba926b, #e2c598);
        vertical-align: middle;
        font-size: 16px;
        font-weight: 600;
        color: #fff;
        white-space: nowrap;
        &::after {
            content: '';
            position: absolute;
            top: 50%; /* 垂直居中箭头 */
            right: -10px; /* 箭头位于模块右侧 */
            margin-top: -5px; /* 调整垂直位置 */
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent transparent #E0C397; /* 上下右透明，左颜色 */
            border-left-width: 6px;
        }
    }
    .reformation-content {
        display: table-cell;
        padding-left: 22px;
        vertical-align: middle;
        font-size: 14px;
        color: #d19f5e;
        font-weight: 500;
    }
}