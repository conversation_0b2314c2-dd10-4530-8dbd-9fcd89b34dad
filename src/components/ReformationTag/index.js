/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-07-24 15:11:48
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-07-25 11:29:43
 * @FilePath: /zcy-announcement-v2-front/src/components/ReformationTag/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import './index.less';

const REFORMATIONTAGLIST = [
  {
    title: '对标改革采购实施计划',
    content: '本实施计划为《上海市落实《全面对换国际高标准经贸规则推进中国（上海）自由贸易试验区高水平制度型开放总体方素》的实施方案》的对标实施计划',
  },
  {
    title: '对标改革项目',
    content: '本项目为《上海市落实〈全面对接国际高标准经贸规则推进中国（上海）自由贸易试验区高水平制度型开放总体方案〉的实施方案》的对标项目',
  },
];

const ReformationTag = ({ tagTitle }) => {
  // 公告步骤条
  return (
    <div className="reformation-tag">
      <span className="reformation-title">{tagTitle}</span>
      <span className="reformation-content">
        {
          REFORMATIONTAGLIST.find((i) => {
            return i.title === tagTitle;
          })?.content
        }
      </span>
    </div>
  );
};

export default ReformationTag;
