import React, { Component } from 'react';
import { connect } from 'dva';

export default (Com) => {
  @connect(({ appCommon }) => ({
    cache: appCommon.cache,
  }))
  class WrapperComponent extends Component {
    setParams = (params) => {
      const url = location.href;
      this.props.dispatch({
        type: 'appCommon/setCache',
        payload: {
          [url]: params,
        },
      });
    }
    getParams = (iniParams) => {
      const url = location.href;
      const { cache } = this.props;
      return cache[url] || iniParams;
    }
    render() {
      return (
        <Com
          {...this.props}
          setParams={this.setParams}
          getParams={this.getParams}
        />
      );
    }
  }
  return WrapperComponent;
};
