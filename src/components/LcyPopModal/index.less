.lcy-pop-modal {
    .doraemon-modal-no-footer {
        padding: 0 !important;
    }
    .doraemon-modal-body {
        padding: 0 !important;
    }
    .left-box {
        float: left;
        width: 352px;
        height: 464px;
        background-color: #fff;
        padding: 44px 40px ;
        position: relative;
        border-radius: 12px 0 0 12px;
        &-title {
            font-size: 24px;
            color: #20242C;
            font-weight: 700;
            margin-bottom: 12px;
        }
        &-content {
            font-size: 12px;
            color: #505663;
            line-height: 22px;
            font-weight: 400;
        }
        &-subTitle {
            font-size: 16px;
            color: #20242C;
            font-weight: 700;
            margin-top: 20px;
            margin-bottom: 12px;
        }
        &-tag {
            display: inline-block;
            padding: 0 12px;
            margin-bottom: 8px;
            margin-right: 8px;
            height: 28px;
            width: auto;
            line-height: 28px;
            text-align: center;
            font-size: 12px;
            color: #505663;
            border: #E3E6EB  1px solid;
            border-radius: 4px;
        }
        &-btn {
            position: absolute;
            bottom: 44px;
            left: 40px;
            width: 88px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            color: #fff;
            background-color: #FF5226;
            border-radius: 4px;
            cursor: pointer;
        }
    }
    .right-box {
        float: right;
        width: 428px;
        height: 464px;
        border-radius: 0 12px 12px 0;
        img {
            width: 100%;
            height: 100%;
            border-radius: inherit;
        }
    }
}