/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-08-07 15:38:09
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-08-08 11:42:34
 * @FilePath: /zcy-announcement-v2-front/src/components/LcyPopModal/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { Modal } from 'doraemon';
import './index.less';

export default function LcyPopModal({ visible, cancelModal }) {
  return (
    <Modal 
      className="lcy-pop-modal"
      visible={visible} 
      footer={null} 
      width={780}
      onCancel={cancelModal}
    >
      <div className="left-box">
        <div className="left-box-title">
          欢迎使用乐采云
        </div>
        <div className="left-box-content">
          乐采云，是政采云有限公司旗下品牌。乐采云平台由政采云有限公司建设、独立运营，为广大企业、村社组织等单位用户搭建的一体化、数智化采购云服务平台。
          同时，平台还可以为各级机关事业单位提供开展非政府采购业务场景的数字化解决方案。
        </div>
        <div className="left-box-subTitle">
          核心产品
        </div>
        <div className="left-box-tag">电子招投标</div>
        <div className="left-box-tag">乐采云专家库 </div>
        <div className="left-box-tag">小额招采（无计划项目采购）</div>
        <div className="left-box-btn" onClick={cancelModal} >立即使用</div>
      </div>
      <div className="right-box">
        <img 
          alt="图片"
          src="https://sitecdn.zcycdn.com/f2e-assets/c0cb95ec-b077-40f9-8d1f-aac4ab42888e.jpg" 
        />
      </div>

    </Modal>
  );
}
