import React from 'react';
import { Panel, Table, Button, SlideUpDown, Badge, Modal, message } from 'doraemon';
import moment from 'moment';
import userIdentity from 'src/utils/roleType';
import { useRevokeModal } from 'src/routes/Manage/components/RevokeModal/single';
import { rePushByAnnouncementPost, rePushBySitePost } from 'src/api/announcement/api';
import './index.less';
import { useAnnouncementContentPreviewDrawer } from '../AnnouncementContentBlock';

enum PushStatus {
  /**
   * 未推送
  */
  UN_PUSH = 0,
  /**
   * 推送成功
  */
  PUSH_SUCCESS,
  /**
   * 推送失败
  */
  PUSH_FAIL,
  /**
   * 推送过滤
  */
  PUSH_FILTER,
  /**
   * 撤回成功
  */
  PUSH_REVOKE
}

const Actions = Table.Actions;


const AnnPushInfo = ({ 
  announcementId, 
  dataSource, 
  showRevokeBtn,
  showRePushBtn,
  callback,
}) => {
  const [openSingleRevokeModal] = useRevokeModal({
    announcementId,
    onSubmit: callback,
  });
  const [open] = useAnnouncementContentPreviewDrawer({ 
    id: announcementId, 
    showTitleBtnGroup: true,
  });
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 70,
      render: (_, __, index) => {
        return index + 1;
      },
    },
    {
      title: '公告站点名称',
      dataIndex: 'targetName',
      render: (val, { isMain }) => {
        return (
          <React.Fragment>
            <span className="zcy-mg-r-4">{val}</span>
            {
              isMain ? (
                <span className="local-tag">主站点</span>
              ) : null
            }
          </React.Fragment>
        );
      },
    },
    {
      title: '推送时间',
      dataIndex: 'pushTime',
      width: 200,
      render: (val) => {
        if (!val) return '-';
        return moment(val).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '推送状态',
      dataIndex: 'pushStatusName',
      width: 280,
      render: (text, record) => {
        let badgeStatus;
        badgeStatus = 'processing';
        const pushStatus: PushStatus = record.pushStatus;
        switch (pushStatus) {
          case PushStatus.PUSH_SUCCESS:
            badgeStatus = 'success';
            break;
          case PushStatus.PUSH_FAIL:
            badgeStatus = 'warning';
            break;
          case PushStatus.UN_PUSH:
          case PushStatus.PUSH_FILTER:
            badgeStatus = 'processing';
            break;
          case PushStatus.PUSH_REVOKE:
            badgeStatus = 'default';
            break;
        }
        return (
          <div>
            <Badge status={badgeStatus} text={text} />
            {
              record.error ? (
                <React.Fragment>
                  <br />
                  <span style={{ color: '#e3333e', fontSize: '12px', lineHeight: '18px', marginLeft: '8px' }}>{record.error}</span>
                </React.Fragment>
              ) : null
            }
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'annName',
      width: 240,
      render: (_, record) => {
        const {
          isOperateRevoke,
          isOperateRePush,
          outUrl,
          targetName,
          announcementSiteId,
        } = record;
        const actions: any[] = [];
        if (isOperateRevoke) {
          actions.push({
            label: '下架',
            onRowClick: () => {
              openSingleRevokeModal({
                siteList: [{
                  title: targetName,
                  siteId: announcementSiteId,
                }],
              });
            },
          });
        }
        if (isOperateRePush) {
          actions.push({
            label: '重新推送',
            onRowClick: () => {
              // Modal.confirm({
              //   title: '重新推送前请先预览公告内容',
              //   content: '确认内容无误后，再进行公告重新推送！',
              //   okText: '预览公告',
              //   onOk: () => {
              //     setTimeout(() => {
              //       open({
              //         onOk: () => {
              //           return rePushBySitePost({
              //             announcementId,
              //             announcementSiteId,
              //           }).then(res => {
              //             if (!res.success) return;
              //             message.success('重推成功');
              //             callback && callback();
              //           });                    
              //         },
              //       });
              //     }, 500);
              //   },
              // });
              Modal.confirm({
                title: '确认公告重新推送?',
                onOk: () => {
                  return rePushBySitePost({
                    announcementId,
                    announcementSiteId,
                  }).then(res => {
                    if (!res.success) return;
                    message.success('重推成功');
                    callback && callback();
                  }); 
                },
              });
            },
          });
        }

        if (outUrl) {
          actions.push({
            label: '查看公告',
            onRowClick: () => window.open(outUrl),
          });
        }
     
        if (!actions.length) return '-';

        return (
          <Actions actions={actions} />
        );
      },
    },
  ];

  const batchRevoke = () => {
    openSingleRevokeModal({
      isAllSite: true,
      siteList: dataSource.map(ele => ({
        title: ele.targetName,
        siteId: ele.announcementSiteId,
      })),
    });
  };

  const batchRePush = () => {
    // Modal.confirm({
    //   title: '全部重推前请先预览公告内容',
    //   content: '确认内容无误后，再进行公告重新推送！',
    //   okText: '预览公告',
    //   onOk: () => {
    //     setTimeout(() => {
    //       open({
    //         onOk: () => {
    //           return rePushByAnnouncementPost({
    //             announcementId,
    //           }).then(res => {
    //             if (!res.success) return;
    //             message.success('全部重推成功');
    //             callback && callback();
    //           });                    
    //         },
    //       });
    //     }, 500);
    //   },
    // });

    Modal.confirm({
      title: '确认公告全部重新推送?',
      onOk: () => {
        return rePushByAnnouncementPost({
          announcementId,
        }).then(res => {
          if (!res.success) return;
          message.success('全部重推成功');
          callback && callback();
        });   
      },
    });
  };

  return (

    <Panel
      title="公告推送"
      style={{ marginTop: '20px' }}
    >
      <div className="AnnPushInfo">
        <SlideUpDown
          collapseText={<span>收起公告推送列表</span>}
          openText={<span>展开公告推送列表</span>}
          location="panel"
        >
          {
            dataSource?.length ? (
              <div className="zcy-mg-b-8">
                {
                  showRevokeBtn ? <Button onClick={batchRevoke} className="zcy-mg-r-8" size="small">全部下架</Button> : null
                }
                {
                  showRePushBtn ? <Button onClick={batchRePush} size="small">全部重推</Button> : null
                }
              </div>
            ) : null
          }
          <Table
            dataSource={dataSource}
            columns={columns}
            pagination={false}
          />
        </SlideUpDown>
      </div>
    </Panel>
  );
};


export default AnnPushInfo;
