import React from 'react';
import { Route, Redirect } from 'react-router-dom';
import Authorized from './Authorized';

class AuthorizedRoute extends React.Component {
  getKey(annType) {
    return annType ? `page${annType}` : undefined;
  }

  render() {
    const {
      component: Component,
      render,
      redirectPath,
      location,
      ...rest
    } = this.props;

    return (
      <Authorized
        {...rest}
        location={location}
        noMatch={<Route {...rest} render={() => <Redirect to={{ pathname: redirectPath }} />} />}
      >
        <Route
          {...rest}
          render={(props) => {
            const { params = {} } = props.match || {};
            return (
              Component
                ? <Component key={this.getKey(params.annType)} {...props} />
                : render(props)
            );
          }}
        />
      </Authorized>
    );
  }
}

export default AuthorizedRoute;
