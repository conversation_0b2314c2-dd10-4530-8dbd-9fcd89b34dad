/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2024-01-24 09:49:41
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-06-03 21:00:15
 * @FilePath: /zcy-announcement-v2-front/src/components/FormGrid/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { Component, Fragment } from 'react';
import {
  FormGrid,
} from 'doraemon';

export default class Grid extends Component {
  formItemToGridData = (items) => {
    const formItems = items.reduce((arr, cur) => {
      if (Array.isArray(cur)) {
        arr = [
          ...arr,
          ...cur,
        ];
      } else {
        arr.push(cur);
      }
      return arr;
    }, []);
    let isFormItem;
    return formItems.reduce((arr, cur, i) => {
      if (typeof cur.type !== 'function' && cur.type !== 'div') {
        return arr;
      }

      // eslint-disable-next-line no-underscore-dangle
      if (isFormItem !== (cur?.props?.__DISPLAY_NAME__ === 'FormItem')) {
      // eslint-disable-next-line no-underscore-dangle
        isFormItem = cur?.props?.__DISPLAY_NAME__ === 'FormItem';
        arr.push({
          type: isFormItem ? 'formItem' : 'others',
          key: i,
          items: [],
        });
      }
      if (isFormItem) {
        arr[arr.length - 1].items.push({
          label: cur.props.label,
          labelCol: cur.props.labelCol,
          wrapperCol: cur.props.wrapperCol,
          colSpan: cur.props.colSpan,
          style: cur.props.style,
          // key: cur.props.key,
          render: () => {
            return cur.props.children;
          },
        });
      } else {
        arr[arr.length - 1].items.push(cur);
      }
      return arr;
    }, []);
  }
  render() {
    const { children } = this.props;
    const formGridItem = this.formItemToGridData(Array.isArray(children) ? children : [children]);
    return (
      <Fragment>
        {formGridItem.map((item, index) => {
          if (item.type === 'formItem') {
            return (
              <div
                key={`formItem${item.key}`}
                style={index === 0 ? {} : {
                  marginTop: 10,
                }}
              >
                <FormGrid
                  bordered
                  formGridItem={item.items}
                />
              </div>
            );
          }
          return (
            <div key={`others${item.key}`}>
              {item.items}
            </div>
          );
        })}
      </Fragment>
    );
  }
}
