import React, { Component } from 'react';

export default class LargeShow extends Component {
  render() {
    const { width, content, style = {}, ...defaultProps } = this.props;
    const wid = width || 100;
    if (!content || content === '' || (typeof content !== 'string')) {
      return <span>-</span>;
    }
    return (
      <span
        className="largeShow"
        style={{
          maxWidth: `${wid}px`,
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          display: 'inline-block',
          ...style,
        }}
        title={content}
        {...defaultProps}
      >
        {content}
      </span>
    );
  }
}
