// import React, { useEffect, useState } from 'react';
// import { Icon, Menu, Dropdown, message, Modal, request, Button } from 'doraemon';
// import Cookies from 'js-cookie';
// import _get from 'lodash/get';
// import _find from 'lodash/find';

// import './index.less';
// import { CurrentEnvKey } from '@/utils/cacheKey';
// import { parseEnvType } from '@/utils/utils';

// interface EnvItemType {
//     code: string
//     name: string
// }
// type EnvListType = EnvItemType[]
// const checkEnvTimer = 2000;
// const refreshDelayTime = 2000;
// export default ({ disabled = false }: { disabled: boolean }) => {
//   const [envList, setEnvList] = useState<EnvListType>([]);

//   const [currentEnv, setCurrentEnv] = useState<EnvItemType | null>(null);

//   useEffect(() => {
//     request('/api/opPlatform/prodOperation/component/template/env/list?moduleType=template')
// .then((res) => {
//       if (res && res.success) {
//         const envList = _get(res, 'data.envDTOList', []) || [];
//         setEnvList(envList);
//         const cookieEnv = Cookies.get('envName');
//         if (cookieEnv) {
//           const envInfo = _find(envList, env => env.code === cookieEnv) || envList?.[0] || null;
//           if (envInfo) {
//             Cookies.set('envName', envInfo.code);
//             setCurrentEnv(envInfo);
//           }
//           return;
//         }
//         const defaultEnv = _get(res, 'data.defaultEnvDTO', null);
//         if (defaultEnv?.code) {
//           Cookies.set('envName', defaultEnv.code);
//           setCurrentEnv(defaultEnv);
//         }
//       } else {
//         message.error(res.message);
//       }
//     });
//   }, []);
//   // 监测环境是否有变更
//   const checkEnvInfo = () => {
//     const cookieEnv = Cookies.get('envName');
//     if (currentEnv?.code && cookieEnv && !document.hidden) {
//       if (currentEnv.code === cookieEnv) return;
//       const modal = Modal.warning({
//         title: '监听到当前环境已切换',
//         content: `将在${refreshDelayTime / 1000}秒后刷新当前页面`,
//       });
//       const timer = setTimeout(() => {
//         modal.destroy();
//         window.location.reload();
//         clearTimeout(timer);
//       }, refreshDelayTime);
//     }
//   };

//   useEffect(() => {
//     const envName = Cookies.get('envName');
//     if (envName) {
//       const env = envList.find((env) => env.code === envName);
//       setCurrentEnv(env);
//     } else {
//       const env = envList.find((env) => env.isDefault === 1) || {
//         code: 'test',
//         name: '云测试环境',
//       };
//       setCurrentEnv(env);
//       Cookies.set('envName', env.code);
//     }
//   }, []);

//   useEffect(() => {
//     // 检查环境是否有变更 / 2s
//     const checkInterval = setInterval(() => {
//       checkEnvInfo();
//     }, checkEnvTimer);

//     return () => {
//       checkInterval && clearInterval(checkInterval);
//     };
//   }, [currentEnv]);
    
//   useEffect(() => {
//     window[CurrentEnvKey] = currentEnv;
//   }, [currentEnv]);

//   const handleEnvChange = (env: EnvItemType) => {
//     Modal.confirm({
//       title: '切换环境',
//       content: '切换后，将跳转到当前环境的列表页',
//       okText: '切换',
//       onOk: () => {
//         Cookies.set('envName', env.code);
//         setCurrentEnv(env);
//         window.location.reload();
//       },
//     });
//   };
//   return envList.length ? (
//     <Dropdown
//       disabled={disabled}
//       overlay={(
//         <Menu>
//           {
//             envList.map((env) => {
//               return (
//                 <Menu.Item key={env.code}>
//                   <a onClick={() => handleEnvChange(env)}>{env.name}</a>
//                 </Menu.Item>
//               );
//             })
//           }
//         </Menu>
//       )}
//       getPopupContainer={triggerNode => triggerNode.parentNode}
//     >
//       <div 
// className={`env-switch-title ${disabled ? 'disabled' : ''} 
// env-switch-level-${parseEnvType(currentEnv?.code)}`}
// >
//         当前环境：
// {currentEnv?.name} <Icon style={{ marginRight: 8, fontSize: 12 }} type="caret-down" />
//       </div>
//     </Dropdown>
//   ) : null;
// };
