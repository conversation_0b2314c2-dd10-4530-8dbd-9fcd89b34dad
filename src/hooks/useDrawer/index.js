import React from 'react';
import ReactDOM from 'react-dom';

const useDrawer = (Component, props) => {
  const openDrawer = (rest) => {
    setTimeout(() => {
      ReactDOM.render(
        <Component 
          visible
          {...props}
          {...rest}
          onCancel={closeDrawer}
        />, div);
    }, 0);
  };
  const div = document.createElement('div');

  const destroy = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(div);
    if (unmountResult && div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  const closeDrawer = (rest) => {
    destroy();
    const onCancel = rest?.onCancel || props?.onCancel;
    if (onCancel) {
      onCancel();
    }
  };

  return [openDrawer, closeDrawer, Component];
};


export default useDrawer;

