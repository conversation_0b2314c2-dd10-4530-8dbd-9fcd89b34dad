
import React from 'react';
import ReactDOM from 'react-dom';

const useModal = (Component, args) => {
  const DIV = document.createElement('div');

  const openModal = (props) => {
    setTimeout(() => {
      ReactDOM.render(
        <Component visible {...args} {...props} onCancel={closeModal} />,
        DIV,
      );
    }, 0);
  };

  const closeModal = () => {
    const unmountResult = ReactDOM.unmountComponentAtNode(DIV);
    if (unmountResult && DIV?.parentNode) {
      DIV.parentNode.removeChild(DIV);
    }
  };
  return [openModal, closeModal];
};

export default useModal;
