/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2025-05-21 15:24:55
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2025-05-21 19:09:31
 * @FilePath: /zcy-announcement-v2-front/src/layouts/PaaSLayout.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { ReactNode } from 'react';
import { Layout, ZcyBreadcrumb } from 'doraemon';
// import EnvSwitch from '@/components/EnvSwitch';
import { Breadcrumb, Button } from '@zcy/doraemon/lib/zcy-breadcrumb/interface';
// import './index.less';

const { Content } = Layout;

interface TProps {
  children: ReactNode
  envDisabled?: boolean
  routes?: Breadcrumb[]
  globalBtn?: Button[]
  extra?: ReactNode
}
export default function PaaSLayout({ 
  children, 
  routes = [], 
  extra = null, 
  globalBtn = [], 
  envDisabled = false, 
}: TProps) {
  return (
    <Layout>
      <ZcyBreadcrumb
        routes={routes}
        customContent={(
          <div style={{ float: 'right', marginLeft: 8 }}>
            {/* <EnvSwitch disabled={envDisabled} /> */}
            {extra}
          </div>
        )}
        globalBtn={globalBtn}
      />

      <Content className="paas-layout-content">
        {children}
      </Content>
    </Layout>
  );
}
