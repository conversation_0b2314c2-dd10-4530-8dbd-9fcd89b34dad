# 公告中心

> 公告v2react版

## 技术栈

公告中心使用前端技术栈主要包括React + Dva.js !
对dva.js不熟悉的同学请阅读官方文档: https://dvajs.com/
**现在在转为 ts + hooks**

## 项目启动运行

克隆项目

```js
git clone https://git.cai-inc.com/paas-front/zcy-announcement-v2-front
```

安装依赖

```js
npm install
```

启动项目

```js
npm run dev(依赖@zcy/zoo)

npm i -g @zcy/zoo(需切换到内网)
```

本地构建

```js
npm run build(同上)
```


## 文件目录说明
```

├── ajax
│   └── index.ts
├── api
│   └── announcement
├── assets
│   └── check-mark.png
├── common
│   ├── checkPermitUrl.js
│   ├── error.js
│   ├── models
│   ├── router.js
│   ├── services
│   └── systemConfig
├── components
│   ├── Authorized
│   ├── DistrictCascader
│   ├── FormGrid
│   ├── LargeShow
│   ├── ListParamsHoc
│   ├── ObjectModal
│   ├── OutUrlContent
│   ├── SMS
│   ├── SelectItem
│   ├── TagLine
│   └── TimeCountDown
├── constants
│   ├── base.js
│   └── index.js
├── hooks
│   └── useDrawer
├── index.hbs
├── layouts
│   └── ZcyLayout.js
├── main.js
├── main.less
├── router-config.js
├── router.js
├── routes
│   ├── AnnouncementList
│   ├── Config
│   ├── DemandManagement
│   ├── DynamicAnnouncementEdit
│   ├── Exception
│   ├── Flow
│   ├── Manage
│   ├── Marking
│   ├── Overview
│   ├── PushError
│   ├── PushStatic
│   ├── Sensitive
│   └── SingleSourceNotice
├── services
│   └── manage
├── style
│   ├── atom.less
│   └── component.less
├── types
│   ├── common.d.ts
│   ├── component.d.ts
│   ├── global.d.ts
│   └── static.d.ts
└── utils
    ├── download.js
    ├── finally-polyfill.js
    ├── listSensitiveWords.js
    ├── pushLog.ts
    ├── request.js
    ├── sequence.js
    ├── tracer.ts
    ├── tree.js
    ├── utils.js
    └── wordexport.js

```

## 分支梳理

公告中心部署区分 本地化 + 云平台

**水务**是单独分支, **release/master-shuiwu**
其余都是master分支

## 发布梳理
默认有限使用婵娟发布，婵娟没有的才使用pass构建

### 业务流程图

![业务流程.png](https://sitecdn.zcycdn.com/f2e-assets/29a24f74-1939-4e5a-96d7-7e3a2388216f.png)

# 业务梳理

## 公告列表
  1. 单一来源公示列表
     1. https://middle.test.zcygov.cn/announcement-front/#/announcement/list/3012
     2. 环境：上海
  2. 政府意向采购公告列表
     1. https://middle.test.zcygov.cn/announcement-front/#/announcement/list/10016
     2. 环境：上海、山西
  3. 公告管理列表
     1. https://middle.test.zcygov.cn/announcement-front/#/manage/list/:annBigType
     2. 举例：
        1. 意见征询或公示: https://middle.test.zcygov.cn/announcement-front/#/manage/list/1
        2. 采购项目公告: https://middle.test.zcygov.cn/announcement-front/#/manage/list/2
        3. ...
     3. annBigType = [1-13,...]
  4. 公告管理列表（运营账号）
     1. https://middle.test.zcygov.cn/announcement-front/#/manage/adminlist/:annBigType
     2. 
  5. 资格预审公告列表
     1. /manage/subList/:annBigType/:annSubType
  6. 公告查询
     1. https://middle.test.zcygov.cn/announcement-front/#/manage/search
  7. 公告查询（运营账号）公告查询页面，平台运营可以查询平台所有的公告
     1. https://middle.test.zcygov.cn/announcement-front/#/public-query

### 公告列表包含的主要功能
  1. 新增公告
     1. 手动录入公告
     2. 从已有项目发起
  2. 查看公告
  3. 审核、审批公告
  4. 发布
  5. 编辑
  6. 删除
  7. 填写链接
  8. 重新推送
  9. 撤回
  10. 撤回发布
  11. 异议确认
  12. 短信通知


## 生成公告 根据formPageCode是否有值来确认是否走无相表单
  1. 无相表单 /dynamic/:actionType(edit|create)
     1. /dynamic/create
     2. /dynamic/edit
  2. 非无相表单
     1. /flow/create
     2. /flow/adminCreate
     3. /edit/:annBigType/:announcementId
     4. /adminEdit/:annBigType/:announcementId

### 无相表单会涉及少许自定义组件
   1. annTitle: PurchaseIntentionTitle
   2. TitleWithSuffix
   3. ComplexSelect
   4. ...


### 敏感词校验
   1. 限制级 
      1. 不允许保存
   2. 提醒级
      1. 可以保存，会有二次确认
   3. 防误伤敏感词
      1. 在实现上需要实时校验，每次校验前都得从后端拉取结果数据

## 公告打标 (弃用)
   1. `marking/list/` 

## 公告配置
   1. 公告大类管理 
   2. 公告类型管理（公告小类）
   3. 公告关联配置
   4. 公告发布配置
   5. 公告开放配置
   6. 公告监管配置
   7. 公告配置日志
   8. 公告站点配置
   9. 公告分组配置
   10. 公告接入管理



## 业务流程

### 运营角色
   1. 包含各种公告类型的配置
   2. 公告数据的查询
   3. ![image.png](https://sitecdn.zcycdn.com/f2e-assets/69ee84f8-eee6-495a-b75e-4f5be7ddf884.png?x-oss-process=image/quality,Q_75/format,jpg)

### 普通角色（采购单位、集采等）
   1. 生产公告、编辑、删除、发布等一系列公告前台业务
   2. ![image.png](https://sitecdn.zcycdn.com/f2e-assets/a7c7d1b8-3517-4474-a2f3-9f48d856f1bc.png?x-oss-process=image/quality,Q_75/format,jpg)