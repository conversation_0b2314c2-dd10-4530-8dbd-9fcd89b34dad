/* eslint-disable */
module.exports = {
  "extends": ['zoo/react'],
  "globals": {
    "__DEV__": true
  },
  "overrides": [
    {
      "files": ["**/*.ts?(x)"],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "ecmaVersion": 2018,
        "sourceType": "module",
        "ecmaFeatures": {
          "jsx": true
        },
        "warnOnUnsupportedTypeScriptVersion": true
      },
      "plugins": ["@typescript-eslint", "react-hooks"],
      "rules": {
        // "react/jsx-fragments": ["error", "element"],
        "react/jsx-key": [
          "error",
          {
            "checkFragmentShorthand": true
          }
        ],
        "camelcase": [1],
        "react/no-direct-mutation-state": "error",
        "react/no-this-in-sfc": "error",
        "react/jsx-pascal-case": "error",
        "no-underscore-dangle": "off",
        "default-case": "off",
        "react/void-dom-elements-no-children": "error",
        "react-hooks/exhaustive-deps": "warn",
        "react-hooks/rules-of-hooks": "error",
        // "react/jsx-filename-extension": ["error", { "extensions": [".tsx", ".ts"] }],
        'react/jsx-filename-extension': [2, { 'extensions': ['.js', '.jsx', '.ts', '.tsx'] }],
        "wrap-iife": ["error", "inside"],
        "indent": ["error", 2, { "SwitchCase": 1, "offsetTernaryExpressions": true }],
        "no-undef": "error",
        "no-var": "error",
        "react/require-default-props": "off",
        "react/no-danger": "off",
        "no-console": ["warn", { "allow": ["warn", "error"] }],
        "no-mixed-operators":"off",
        "@typescript-eslint/no-shadow": ["error"],
        // 优先使用 interface 而不是 type
        "@typescript-eslint/consistent-type-definitions": [
          "error",
          "interface"
        ],
        "no-shadow": "off",
        "@typescript-eslint/consistent-type-assertions": "warn",
        "no-array-constructor": "off",
        "@typescript-eslint/no-array-constructor": "warn",
        "no-redeclare": "off",
        "@typescript-eslint/no-redeclare": "warn",
        "no-use-before-define": "off",

        "@typescript-eslint/no-use-before-define": [
          "warn",
          {
            "functions": false,
            "classes": false,
            "variables": false,
            "typedefs": false
          }
        ],
        "no-unused-expressions": "off",
        "@typescript-eslint/no-unused-expressions": [
          "error",
          {
            "allowShortCircuit": true,
            "allowTernary": true,
            "allowTaggedTemplates": true
          }
        ],
        "no-unused-vars": "off",
        "@typescript-eslint/no-unused-vars": [
          "error",
          {
            "args": "none",
            "ignoreRestSiblings": true
          }
        ],
        "no-useless-constructor": "off",
        "@typescript-eslint/no-useless-constructor": "warn",
        "comma-dangle": [
          "error"
        ],
        "arrow-parens": 0,
        "import/first": "error",
        "react/jsx-uses-react": "off",
        "react/react-in-jsx-scope": "off"
      }
    }
  ]
}
