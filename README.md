# 公告中心

> 公告v2react版

```js
/*
 * @Author: 清风
 * @Date: 2021-07-19
 * @LastEditTime: 2021-07-20
 * @LastEditors: 清风
 * @Description: 公告中心交接文档
 * @FilePath: https://git.cai-inc.com/paas-front/zcy-announcement-v2-front
 */
```



## 技术栈

公告中心使用前端技术栈主要包括React + Dva.js !

对dva.js不熟悉的同学请阅读官方文档: https://dvajs.com/



## 项目启动运行

克隆项目

```js
git clone https://git.cai-inc.com/paas-front/zcy-announcement-v2-front
```

安装依赖

```js
npm install
```

启动项目

```js
npm run dev(依赖@zcy/zoo)

npm i -g @zcy/zoo(需切换到内网)
```

本地构建

```js
npm run build(同上)
```

自动化构建

```js
http://paas.cai-inc.com/ops#/
```



## 文件目录说明

```
├── Config                                    #公告配置相关页面
│   ├── components
│   │   ├── AnnTypeList
│   │   │   ├── TypeItem.js
│   │   │   ├── index.js
│   │   │   └── index.less
│   │   ├── ConfigList
│   │   │   ├── index.js
│   │   │   └── index.less
│   │   ├── NoContent
│   │   │   ├── index.js
│   │   │   └── index.less
│   │   ├── OpenAnnTypeList
│   │   │   ├── TypeItem.js
│   │   │   ├── index.js
│   │   │   └── index.less
│   │   ├── ParamList
│   │   │   ├── index.js
│   │   │   └── index.less
│   │   ├── PublishModal.js
│   │   ├── PublishModal.less
│   │   ├── SetModal.js
│   │   └── SetModal.less
│   ├── index.js
│   ├── models
│   │   ├── index.js
│   │   └── publish.js
│   ├── services
│   │   └── index.js
│   └── views
│       ├── index.less
│       ├── open.js                           #公告模板配置页面
│       └── publish.js                        #公告发布配置页面
├── Flow
│   ├── index.js
│   ├── models
│   │   └── flow.js
│   ├── services
│   │   └── index.js
│   └── views
│       ├── components
│       │   ├── form-linkPurchase.js          #是否关联采购计划的form组件
│       │   ├── form-linkPurchase.less
│       │   ├── form-table-multi.js           #列合并的动态table
│       │   ├── form-table.js                 #动态table
│       │   ├── form-table.less
│       │   ├── form-upload.js                #上传组件
│       │   ├── marking.js                    #公告标识
│       │   ├── marking.less
│       │   ├── sensitiveAlert.js             #公告敏感词提示
│       │   └── tag.js                        #上传组件
│       ├── create.js                         #创建公告
│       ├── edit.js                           #公告编辑
│       └── index.less
├── Manage
│   ├── index.js
│   ├── models
│   │   ├── add.js
│   │   ├── index.js
│   │   ├── list.js
│   │   └── search.js
│   ├── services
│   │   └── index.js
│   └── views
│       ├── components
│       │   ├── label.js
│       │   ├── label.less
│       │   ├── modal-review.js               #公告审批modal
│       │   ├── modal-review.less
│       │   ├── modal-select.js               #选取项目创建公告
│       │   └── modal-select.less
│       ├── detail.js                         #公告详情
│       ├── index.less
│       ├── list.js                           #公告列表
│       ├── review.js                         #公告审核
│       └── search.js                         #公告查询
├── Marking
│   ├── index.js
│   ├── models
│   │   └── index.js
│   ├── services
│   │   └── index.js
│   └── views
│       ├── components
│       │   ├── addMarking.js
│       │   └── addMarking.less
│       ├── index.less
│       └── list.js                           #公告打标
└── Overview
    ├── index.js
    ├── models
    │   └── index.js
    ├── services
    │   └── index.js
    └── views
        ├── components
        │   ├── announcement.js
        │   ├── chart.js
        │   ├── countCard.js
        │   ├── selectedAnn.js
        │   ├── shortcut.js
        │   └── shortcutSet.js
        ├── index.js                          #总览
        └── index.less
```

公告对接表单中心：
普通的公告列表做了逻辑判断，历史数据走老的页面，新增的数据走对接之后的页面
给上海做的通用列表（AnnouncementList 文件夹）暂时没有做这个逻辑判断，直接走的老的

短信通知：
上海的通用列表（AnnouncementList 文件夹）和政采普通公告列表都加了短信通知操作



## 各环境账号

代理配置在根目录下config文件夹下面的proxy.js中,

账号设置在user对象下配置,启动自动调取用户中心的接口获取登录鉴权. 例如:

```js
user: {
	password: 'zcy123456',
	account: 'binjiangcgzx001',
}
```

角色切换可以从测试环境切换, 本地刷新浏览器,和本地调试角色保持一致;

本地点击导航菜单只会跳转代理的地址. 所以本地调试务必找准页面对应的url 直接ctr+v到本地刷新浏览器即可;

更换代理地址或者本地调试只需更换rules对象中的每个子对象的host 地址即可. 例如:

```js
// 本地联调代理地址配置,
// 预发环境配置请注意域名,和测试环境代理地址些许不同
local: {
  host: 'http://************:8039',
  urls: [
    '/announcement/*',
    // ...
  ]
},

```

| 环境     | 账号      | 密码       |
| -------- | --------- | ---------- |
| 测试环境 | sxbjcgdw1 | test123456 |
| 预发环境 | wuyf      | test123456 |

更多账号获取请访问: http://confluence.cai-inc.com/pages/viewpage.action?pageId=********



## 业务梳理

公告中心本地化部署目前有上海、山西、云平台三个环境;

山西环境和云平台使用同一套代码, 默认使用master分支, 上海部署的代码单独使用release/master-sh分支.



### 主要页面

- 公告列表页面 src/routes/Manage/views/list.js;
- 上海公告列表页面(区分单一来源和政府意向采购公告) src/routes/AnnouncementList/views/list.js;
- 公告新增页面(未接入自定义表单), 包含管理员角色下的新增页面 src/routes/Flow/views/create.js, src/routes/Flow/views/adminCreate.js ;
- 公告详情页面(未接入自定义表单) src/routes/Manage/views/detail.js;
- 公告审核页面(未接入自定义表单) src/routes/Manage/views/review.js ;
- 公告编辑页面(未接入自定义表单), 包含管理员角色下的编辑页面 src/routes/Flow/views/edit.js, src/routes/Flow/adminviews/edit.js  ;
- 公告查询页面 src/routes/Manage/views/search.js;
- 公告数据总览页面 src/routes/Overview/views/index.js;
- 公告创建页面(接入自定义表单的) src/routes/DynamicAnnouncementEdit/views/create.js;
- 公告编辑页面(接入自定义表单的) src/routes/DynamicAnnouncementEdit/views/create.js;
- 公告详情页面(接入自定义表单的) src/routes/DynamicAnnouncementEdit/views/detail.js;



公告除去上海分支, 所有的列表页面都是一个页面,根据annBigType区分公告类型,

未接自定义表单的公告新增页面和接入自定义表单的新增、编辑、详情页面同样使用同一个页面!



### 业务流程图



![业务流程.png](https://sitecdn.zcycdn.com/f2e-assets/29a24f74-1939-4e5a-96d7-7e3a2388216f.png)



### 关键核心业务逻辑



#### 公告总览

1. 公告按照类型分为(一级分类):

   意见征询或公示,

   采购项目公告,

   更正公告,

   采购结果公告,

   采购合同公告,

   履约验收公告,

   政府采购监管公告,

   电子卖场公告,

   非政府采购公告,

   精选服务公告,

   建设工程类公告,

2. 目前接入自定义表单的公告有:

   | 主应用   | 子应用   | 页面名称                   | 页面编码                                      | 页面类型 |
   | :------- | :------- | :------------------------- | :-------------------------------------------- | :------- |
   | 公告中心 | 公告模板 | 公告通用                   | announcement_common                           | 新增页   |
   | 公告中心 | 公告模板 | 采购意向公告               | publicNoticeOfPurchaseIntention               | 新增页   |
   | 公告中心 | 公告模板 | 单一来源公示               | singleSourcePublicity                         | 新增页   |
   | 公告中心 | 公告模板 | 邀请招标资格预审公告       | invitationToBidForPrequalification            | 新增页   |
   | 公告中心 | 公告模板 | 进口产品公示               | publicityOfImportedProducts                   | 新增页   |
   | 公告中心 | 公告模板 | 需求论证公示               | requirementDemonstrationPublicity             | 新增页   |
   | 公告中心 | 公告模板 | 公开招标资格预审公告       | AnnouncementOnPrequalificationOfPublicBidding | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购公告         | otherNonGovNotice                             | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购项目招标公告 | otherNonGovBidNotice                          | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购项目更正公告 | otherNonGovCorrectionNotice                   | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购项目意见征询 | otherNonGovConsultatioNotice                  | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购项目结果公告 | otherNonGovResultNotice                       | 新增页   |
   | 公告中心 | 公告模板 | 其他非政府采购项目合同公告 | otherNonGovContractNotice                     | 新增页   |



注意: 以上都是二级分类公告, 模板是有区划区分的,不同区划渲染的页面依赖于各区划下配置的模板差异不同

![区划模板](https://sitecdn.zcycdn.com/f2e-assets/64be4a35-2554-42f7-8cdc-8859ab8843b7.png)



#### 核心代码

主要核心代码有公告新增页面的renderDynamicForm方法,负责渲染不同区划下的模板页面;



##### 公告新增页面

此方法只在未接入自定义表单的新增公告页面,包括上海的的单一来源和政府采购意向公告,

上海公告有计划要重新接入自定义表单,合入主分支;

###### renderDynamicForm

```js
import React from 'react';
const allTable = [];
let requiredTableName = [];
let requiredTableCols = {};

// 生成动态表单
const renderDynamicForm = (_this) => {
  try {
    const {
      form: { getFieldDecorator, getFieldsValue, setFieldsValue },
      flow: { dynamicForm, disDataForPro, cgfs, cglx, cgml },
    } = _this.props;
    const { announcementType, config, glggList, gpCatalogStartChart } =
      _this.state;
    const formNodes = [];
    requiredTable = [];
    requiredTableName = [];
    requiredTableCols = {};
    // 非政府采购公告下所有公告，采购方式、采购目录、采购类型、监管信息均设置为非必填
    const isGov = _this.routerInfo.annBigType === '9';
    // 政府采购监管公告下所有公告，采购方式、采购目录、采购类型、项目名称、项目编号、采购人名称不显示
    const TYPR_7 = _this.routerInfo.annBigType === '7';
    const purchaseNameInitValue = {};
    for (let i = 0; i < dynamicForm.length; i += 1) {
      if (dynamicForm[i].key === 'purchaseName') {
        purchaseNameInitValue.purchaseName = dynamicForm[i].value || '';
      }
      if (dynamicForm[i].key === 'otherPurchaseNames') {
        purchaseNameInitValue.otherPurchaseNames = dynamicForm[i].value || '';
      }
      if (dynamicForm[i].key === 'purchases') {
        purchaseNameInitValue.purchases =
          JSON.parse(dynamicForm[i].value) || [];
      }
    }

    const { district, punishedObjectType } = getFieldsValue([
      'district',
      'punishedObjectType',
    ]);
    /**
     * 用于判断 该区划下是否为必填项
     * ‘005000’ 为体育总局
     */
    const isDistRequired = district === '005000';
    let isAm;
    let isPm;
    dynamicForm.forEach((item) => {
      isAm = item.name && item.name.indexOf('上午') !== -1;
      isPm = item.name && item.name.indexOf('下午') !== -1;
      if (TYPR_7) {
        if (
          [
            'procurementMethod',
            'procurementType',
            'gpCatalogName',
            'purchaseName',
            'correctType',
          ].includes(item.key)
        ) {
          return;
        }
      }
      if (item.key === 'showDuration') {
        _this.showDuration = item.value;
      }
      // 校验规则
      let formRules = [];
      // 不为空，或者标项总额大于500万并且requried为true。体育总局也为必填（isDistRequired判断）
      if (
        item.nullable === 2 ||
        ((_this.bidSum >= 5000000 || isDistRequired) && item.requried)
      ) {
        formRules.push({ required: true, message: '不能为空！' });
      }
      /* eslint no-case-declarations: 0 */
      /* eslint no-inner-declarations: 0 */
      switch (item.type) {
      // 采购方式、采购类型、采购目录，展示name，默认值取code
      case 0:
      case -99:
        getFieldDecorator(item.key, {
          initialValue: item.value,
        });
        break;
      case 1:
        // 下拉框数据
        let treeData = [];
        // 默认值
        let defaultValue;
        if (item.key === 'procurementMethod') {
          const find = dynamicForm.find(
            i => i.key === 'procurementMethodCode'
          );
          defaultValue = find ? find.value : undefined;
          treeData = cgfs;
        }
        if (item.key === 'procurementType') {
          const find = dynamicForm.find(
            i => i.key === 'procurementTypeCode'
          );
          defaultValue = find ? find.value : undefined;
          treeData = cglx;
        }
        if (item.key === 'gpCatalogName') {
          const find = dynamicForm.find(i => i.key === 'gpCatalogCode');
          defaultValue =
              find && typeof find.value === 'string'
                ? find.value.split(',')
                : undefined;
          treeData = cgml;
        }
        // 项目所在行政区划编码
        if (item.key === 'disForPro') {
          treeData = disDataForPro;
          defaultValue = undefined;
        }
        if (item.key === 'relateOldNotice') {
          treeData = glggList;
          defaultValue = undefined;
        }
        // 资格审查类型
        if (item.key === 'qualificationType') {
          treeData = [
            {
              title: '资格预审',
              value: 1,
            },
            {
              title: '资格后审',
              value: 2,
            },
          ];
          defaultValue = undefined;
        }
        // 更正类型
        if (item.key === 'correctType') {
          treeData = _this.state.correction;
          defaultValue = undefined;
        }
        formNodes.push(
          <FormItem
            key={item.key}
            label=item.name
            {...formItemLayout}
          >
            <Input />
            <FormItem/>
          );
      		// ...
        }
        break;
      default:
        break;
      }
    });
    return formNodes;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('renderDynamicForm:', error);
    return [];
  }
};

export {
  renderDynamicForm,
  requiredTable,
  requiredTableName,
  requiredTableCols,
  allTable,
};

```



删除了一些代码,  此方法主要通过后端返回的数据差异化渲染不同区划的模板到页面;

dynamicForm是后端返回的所有数据集合, 包含页面所需组件的差异化信息, 页面根据此数据渲染;

具体组件类型依赖字段```type```区分不同组件的类型,前端差异化渲染;

表单验证依赖字段```require```;

requiredTable,最终必填的表格面板;

requiredTableName必填表格的名字主要用于验证提示信息显示;

requiredTableCol必填表格的哪一列;

##### 公告预览页面

其实就是公告新增页面;根据字段preview动态控制 A 和 B 显示隐藏, 代码如下:

```js
 <FormItem
    style={
      (!hideTitleAnn[this.routerInfo.announcementType]
      ||
      // 动态显示隐藏
      this.state.preview)
        ? {} : {
          display: 'none',
        }}
      label="标题："
      colSpan={2}
      {...formItemLayoutForColSpan2}
      >
        {getFieldDecorator('title', {
          rules: [{
           const value = Array.isArray(v) ? v.join('') : v;
           const titleSensitiveWords = sensitiveWords.reduce((arr, cur) => {
           if (typeof value === 'string' && value.length > 200) {
             callback(new Error('不能超过 200 字符'));
             return;
           }
           if (typeof value === 'string') {
             const reg = /[\u4e00-\u9fa5]/g;
             const matchNum = (`${value || ''}`).match(reg) || [];
             if (matchNum.length < 10) {
               callback('至少需要输入 10 个中文字符！');
               return;
             }
           }
           callback();
         },
      }],
        initialValue: iniTitle,
      })(
        (this.routerInfo.announcementType === '10016' && !iniTitle && formItems.length > 0) ? (
          <TitleInput
          form={form}
        onChange={this.titleChange}
      disabled={this.state.preview && !this.state.editable}
      />
        ) : (
          config.customizedTitle ? (
            <TitleWithSuffix form={form} config={config} />
      ) :
      (
        <Input
        // disabled={this.state.preview && !this.state.editable}
        placeholder={this.getTitlePlaceholder(true)}
      />
      )
      )
      )}
  </FormItem>
```



此代码映射在页面上如下:



表单页面A

![表单页面](https://sitecdn.zcycdn.com/f2e-assets/0f0d0d4d-a07d-4877-bdd8-20806e2fdf65.png)



预览页面B

![预览页面](https://sitecdn.zcycdn.com/f2e-assets/309c3832-dbb1-468d-be90-5d03c4545c19.png)



###### 提交方法submit

```js
 submit = (type) => {
    const { flow: { dynamicForm }, form } = this.props;
    form.validateFields({ force: true }, (err, params) => {
      const { purchaseName = {}, title } = params;
      const values = {
        ...params,
        ...purchaseName,
        title: Array.isArray(title) ? title.join('') : title,
      };
      function getNameByValue(data, matchCode) {
        let match = null;
        data.forEach((item) => {
          if (item.value === matchCode) {
            match = item.label;
          } else if (item.children) {
            match = getNameByValue(item.children, matchCode) || match;
          }
        });
        return match;
      }
      if (!err) {
        const fileList = [...this.props.flow.fileInfo.defaultFileList,
          ...this.props.flow.fileInfo.fileList];
        // 检测是否存在上传中的文件
        const uploadingFileNumber = fileList.filter(item => item.status === 'uploading').length;
        if (uploadingFileNumber !== 0) {
          Modal.error({
            title: '提示',
            content: `还有${uploadingFileNumber}个文件在上传中，请等待文件上传完毕！`,
          });
          return;
        }
        // 文件是必填项，则验证是否有已成功上传的文件
        const validFile = fileList.filter(item => item.status === 'done') || [];

        if (this.state.attachmentsNeed && validFile.length === 0) {
          Modal.error({
            title: '提示',
            content: '请上传文件！',
          });
          return;
        }
        // 修复动态附件毕传问题 !
        const { qualificationType } = this.state;
        let validFile1 = [];
        let validFile2 = [];
        if (qualificationType) {
          const fileList1 = [
            ...this.props.flow.fileInfo1.defaultFileList1,
            ...this.props.flow.fileInfo1.fileList1];
          const fileList2 = [
            ...this.props.flow.fileInfo2.defaultFileList2,
            ...this.props.flow.fileInfo2.fileList2];
          validFile1 = fileList1.filter(item => item.status === 'done') || [];
          validFile2 = fileList2.filter(item => item.status === 'done') || [];
          if (this.state.attachmentsNeed && validFile1.length === 0) {
            Modal.error({
              title: '提示',
              content: '请上传文件！',
            });
            return;
          }
          if (this.state.attachmentsNeed && validFile2.length === 0) {
            Modal.error({
              title: '提示',
              content: '请上传文件！',
            });
            return;
          }
        }
        const metaData = {
          ...values,
        };
        Object.keys(metaData).forEach((key) => {
          if (metaData[key] === undefined || metaData[key] === null) {
            metaData[key] = '';
          }
        });
        // 格式化表单数据
        dynamicForm.forEach((item) => {
          // 日期
          if (item.type === 2) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('YYYY-MM-DD');
            } else {
              metaData[item.key] = '';
            }
          }
          // 时间
          if ([3, 4, 13].includes(item.type)) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('HH:mm');
            } else {
              metaData[item.key] = '';
            }
          }
          // 日期时间
          if (item.type === 14) {
            if (metaData[item.key]) {
              metaData[item.key] = moment(metaData[item.key]).format('YYYY-MM-DD HH:mm:ss');
            } else {
              metaData[item.key] = '';
            }
          }
          // 时间区间
          if (item.type === 15) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('HH:mm')}至${moment(metaData[`${item.key}_end`]).format('HH:mm')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 日期区间
          if ([16].includes(item.type)) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('YYYY-MM-DD')}至${moment(metaData[`${item.key}_end`]).format('YYYY-MM-DD')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 日期时间区间
          if (item.type === 17) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('YYYY-MM-DD HH:mm:ss')}至${moment(metaData[`${item.key}_end`]).format('YYYY-MM-DD HH:mm:ss')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // 时间段
          if ([18, 19].includes(item.type)) {
            if (metaData[`${item.key}_start`] && metaData[`${item.key}_end`]) {
              metaData[item.key] = `${moment(metaData[`${item.key}_start`]).format('HH:mm')}至${moment(metaData[`${item.key}_end`]).format('HH:mm')}`;
            } else {
              metaData[item.key] = '';
            }
            delete metaData[`${item.key}_start`];
            delete metaData[`${item.key}_end`];
          }
          // tags select
          if (item.type === 11) {
            if (item.key === 'purchaseName') {
              metaData.purchases = values.purchases;
              metaData.otherPurchaseNames = values.otherPurchaseNames;
              metaData.purchaseName = values.purchaseName;
            } else if (metaData[item.key]) {
              metaData[item.key] = metaData[item.key].join('、');
            } else {
              metaData[item.key] = '';
            }
          }
        });
        // table数据处理
        allTable.forEach((table) => {
          metaData[table] = [];
        });
        Object.keys(this.props.flow.tableData).forEach((tbKey) => {
          const rows = [];
          this.props.flow.tableData[tbKey].forEach((tbRow) => {
            delete tbRow.formValue.undefined;
            // 设置空值，防止序列化后key值丢失
            Object.keys(tbRow.formValue).forEach((tk) => {
              if (tbRow.formValue[tk] === undefined) {
                tbRow.formValue[tk] = null;
              }
            });
            rows.push(tbRow.formValue);
          });
          metaData[tbKey] = rows;
        });
        // 表格必填校验
        let subStr = '';
        let subRequiredAndValueNull;
        // 货物工程服务三选一
        let threeToOne = ['GoodsInfoTab', 'infoOfEngSubMatter', 'infoOfServiceObject'];
        if (requiredTable.includes(threeToOne[0]) && requiredTable.includes(threeToOne[1])
          && requiredTable.includes(threeToOne[2])) {
          const empTyTable = requiredTable.reduce((prev, cur, i) => {
            if ((!Array.isArray(metaData[cur]) || !metaData[cur].length) &&
              threeToOne.includes(cur)) {
              prev.push(requiredTableName[i]);
            }
            return prev;
          }, []);

          if (empTyTable.length === 3) {
            message.error(`${empTyTable.join('、')}三个中至少必须填写一个！`);
            return;
          }
        }
        const nullableTableName = requiredTable.reduce((str, cur, i) => {
          if (str) {
            return str;
          }
          // 必填表格没有数据并且必填表格不为货物，工程，服务三个中的一个
          if ((!Array.isArray(metaData[cur]) || !metaData[cur].length) &&
            !threeToOne.includes(cur)) {
            return requiredTableName[i];
          }
          const { gpCatalogStartChart } = this.state;
          if (gpCatalogStartChart === 'A') {
            threeToOne = ['infoOfEngSubMatter', 'infoOfServiceObject'];
          }
          if (gpCatalogStartChart === 'B') {
            threeToOne = ['GoodsInfoTab', 'infoOfServiceObject'];
          }
          if (gpCatalogStartChart === 'C') {
            threeToOne = ['GoodsInfoTab', 'infoOfEngSubMatter'];
          }
          // 必填表格列校验
          if (requiredTableCols[cur] && !threeToOne.includes(cur)) {
            subRequiredAndValueNull = false;
            subStr = requiredTableCols[cur].reduce((s, c) => {
              if (s) return s;
              if (c.nullable === 2) {
                metaData[cur].forEach((item) => {
                  subRequiredAndValueNull = subRequiredAndValueNull || !item[c.key];
                });
                return subRequiredAndValueNull ? c.name : '';
              }
              return '';
            }, '');
            if (subStr) {
              return `表格${requiredTableName[i]}的${subStr}列`;
            }
          }
          return '';
        }, '');
        if (nullableTableName) {
          message.error(`${nullableTableName}不可为空`);
          return;
        }
        // 单一来源公示，预算金额需要必填并大于0
        if (this.routerInfo.announcementType === '3012') {
          if (metaData.biddingProject.length === 0) {
            message.warning('请填写采购项目概况');
            return;
          }
          const isInvalid = metaData.biddingProject.some((item) => {
            return isNaN(item.budgetPrice) || item.budgetPrice <= 0;
          });
          if (isInvalid) {
            message.warning('采购项目概况 - 预算金额不能为空或小于0');
            return;
          }
        }
        // 采购方式、采购类型、采购目录获取label值（存在有初始值情况，必须手动递归获取）
        const { cgfs, cglx, cgml } = this.props.flow;
        if (metaData.procurementMethod) {
          metaData.procurementMethodCode = metaData.procurementMethod;
          metaData.procurementMethod = getNameByValue(cgfs, metaData.procurementMethodCode) || '';
        }
        if (metaData.procurementType) {
          metaData.procurementTypeCode = metaData.procurementType;
          metaData.procurementType = getNameByValue(cglx, metaData.procurementTypeCode) || '';
        }
        if (metaData.gpCatalogName && Array.isArray(metaData.gpCatalogName)) {
          metaData.gpCatalogCode = metaData.gpCatalogName.join(',');
          metaData.gpCatalogName = metaData.gpCatalogName.map(item => (getNameByValue(cgml, item) || ''));
          metaData.gpCatalogName = metaData.gpCatalogName.join(',');
        }
        // 更正公告所选关联公告
        this.glgg = this.state.glggList.find(item => item.outUrl === metaData.relateOldNotice);
        this.setState({
          submiting: type,
        });
        // 是否加密字段
        /* eslint no-prototype-builtins: 0 */
        if (metaData.hasOwnProperty('isSecret')) {
          metaData.secret = metaData.isSecret === '1';
          delete metaData.isSecret;
        }
        // 关联项目编号，存在的话已逗号分隔
        const planNum = metaData.procurementPlanNumber;
        if (Array.isArray(planNum)) {
          metaData.procurementPlanNumber = planNum.join(',');
        }
        // 处理项目所在行政区划、编码
        if (metaData.disForPro) {
          metaData.districtCode = metaData.disForPro;
          metaData.districtName = this.disNameForPro;
          delete metaData.disForPro;
        }
        // 附件
        const attachments = [];
        validFile.forEach((fl) => {
          attachments.push({
            fileId: fl.fileId,
            isShow: !(fl.noPublic === true),
            name: fl.name,
            size: fl.size,
          });
        });
        if (qualificationType) {
          validFile1.forEach((fl) => {
            attachments.push({
              fileId: fl.fileId,
              isShow: !(fl.noPublic === true),
              name: fl.name,
              size: fl.size,
            });
          });
          validFile2.forEach((fl) => {
            attachments.push({
              fileId: fl.fileId,
              isShow: !(fl.noPublic === true),
              name: fl.name,
              size: fl.size,
            });
          });
        }
        // appCode回传
        let appCode;
        const appCodeItem = dynamicForm.find(item => item.key === 'appCode');
        if (appCodeItem) {
          appCode = appCodeItem.value;
        }
        // 公告标识
        const { markingList, supportCheckerMultiSelect } = this.state;
        const selectMarking = markingList.filter(item => item.isChoosed && item.type === 2);
        const identificationIds = selectMarking.map(item => item.id);
        // 公告预览
        if (type === 1) {
          this.getNextTaskUser(metaData.district);
          this.props.dispatch({
            type: 'flow/getAnnouncementContent',
            payload: {
              announcementType: this.routerInfo.announcementType,
              metaData: JSON.stringify(metaData),
              district: this.routerInfo.districtId || undefined,
            },
          }).then((res) => {
            this.setState({
              submiting: 0,
            });
            if (res.result !== null) {
              this.setState({
                preview: true,
                editable: false,
              });
              if (this.glgg) {
                res.result = res.result.replace(this.glgg.outUrl, `<a href="${this.glgg.outUrl}" target= "_blank">${this.glgg.title}</a>`);
              }
              this.ueditor.setContent(res.result);
              this.listSensitiveWords({
                title: values.title,
                content: res.result,
                district: metaData.district || metaData.districtCode,
                type,
              });
              this.ueditor.setDisabled('fullscreen');
              // 生成公告即暂存
              // 修复table预览无边框样式
              let content = res.result;
              const fixTableStyle = '<style id="fixTableStyle" type="text/css">table {border-spacing:0}th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
              const hasFixed = content?.indexOf('fixTableStyle');
              if (hasFixed === -1) {
                content = fixTableStyle + content;
              }
              this.getConfig({
                announcementType: this.routerInfo.announcementType,
                districtCode: values.district,
                metadata: metaData,
              });
              this.setState({
                submiting: 0,
              });
            } else {
              message.error('参数错误');
            }
          });
        }
        // 修复table预览无边框样式
        let content = this.ueditor.getContent();
        const fixTableStyle = '<style id="fixTableStyle" type="text/css">table {border-spacing:0}th,td {border:1px solid #DDD;padding: 5px 10px;}</style>';
        const hasFixed = content?.indexOf('fixTableStyle');
        if (hasFixed === -1) {
          content = fixTableStyle + content;
        }
        // 公告暂存-手动
        if (type === 2) {
          this.setState({
            submiting: 0,
          });
          this.listSensitiveWords({
            title: values.title,
            content,
            district: metaData.district,
            cb: () => {
              this.setState({
                submiting: 2,
              });
              tempSaveAnnouncement({
                id: this.id,
                announcementType: this.routerInfo.announcementType,
                attachments,
                content: this.backSensitiveWords(content),
                description: '',
                district: values.district,
                title: values.title,
                metaData: JSON.stringify(metaData),
                projectCode: this.routerInfo.projectCode || values.projectCode || '',
                projectName: this.routerInfo.projectName || values.projectName || '',
                pubType: this.state.pubType,
                releasedAt: values.releasedAt || '',
                serialNum: this.routerInfo.orderId || '',
                showDuration: (this.showDuration === null || this.showDuration === undefined) ? '' : this.showDuration,
                url: '',
                appCode,
                identificationIds,
              }).then((res) => {
                this.setState({
                  submiting: 0,
                });
                if (res.success) {
                  this.id = res.result;
                  Modal.success({
                    title: '提示',
                    content: '暂存成功！',
                  });
                } else {
                  Modal.error({
                    title: '提示',
                    content: res.error,
                  });
                }
              });
            },
          });
        }
        // 公告提交
        if (type === 3) {
          const json = {
            id: this.id,
            announcementType: this.routerInfo.announcementType,
            attachments,
            content,
            description: '',
            district: values.district,
            title: values.title,
            metaData: JSON.stringify(metaData),
            projectCode: this.routerInfo.projectCode || values.projectCode || '',
            projectName: this.routerInfo.projectName || values.projectName || '',
            pubType: this.state.pubType,
            releasedAt: values.releasedAt || '',
            serialNum: this.routerInfo.orderId || '',
            showDuration: (this.showDuration === null ||
              this.showDuration === undefined) ? '' : this.showDuration,
            url: '',
            // nextTaskUserId: this.nextTaskUserId,
            appCode,
            identificationIds,
          };
          if (supportCheckerMultiSelect) {
            json.nextTaskUserIds = Array.isArray(this.nextTaskUserId)
              ? this.nextTaskUserId : [this.nextTaskUserId];
          } else {
            json.nextTaskUserId = this.nextTaskUserId;
          }
          const tasks = [];
          const needShowFiles = attachments.filter(fl => fl.isShow);
          let html = '';
          if (needShowFiles.length > 0) {
            html = `
              <divider></divider><p style="white-space: normal; line-height: 24px;">
              <strong><span style="line-height: 24px; font-family: 宋体;">附件信息:</span></strong></p>`;
            needShowFiles.forEach((fl) => {
              tasks.push(getDownLoadUrl({
                fileId: fl.fileId,
                bizCode: '1014',
              }).then((res) => {
                fl.url = res.result;
              }));
            });
            // 获取文件url地址，拼接模板
            Promise.all(tasks).then(() => {
              html += needShowFiles.map((fl) => {
                let fileSize = '';
                if (fl.size) {
                  fl.size = parseInt(fl.size, 10);
                  fileSize = '0.1 KB';
                  const fileSizeK = (fl.size / 1024).toFixed(1);
                  if (fileSizeK.split('.')[1] > 0) {
                    fileSize = `${fileSizeK} KB`;
                  }
                  if (fileSizeK.split('.')[0] > 1000) {
                    const fileSizeM = (fl.size / 1024 / 1024).toFixed(1);
                    fileSize = `${fileSizeM} M`;
                  }
                }
                return
                `<ul class="fjxx" style="font-size: 16px;margin-left: 38px;color: #0065ef;list-style-type: none;">
                  <li><p style="display:inline-block">
                  <a href="${fl.url}">${fl.name}</a></p>
                  <p style="display:inline-block;margin-left:20px">${fileSize}</p></li>
								</ul>`;
              }).join('');
              json.content += html;
              this.submitAnn(json);
            });
          } else {
            this.submitAnn(json);
          }
        }
      } else {
        message.warning('请完善信息！');
      }
    });
  }
```



type为1是生成公告-公告预览,动态改变preview字段;

type为2是暂存公告-公告暂存-手动,暂存不做表单验证处理;

type为3是提交保存公告, 效验必填表单组件;

其他参数根据差异化业务场景差异化组装. 例如附件文件必填验证:

```js
const fileList = [...this.props.flow.fileInfo.defaultFileList,
          ...this.props.flow.fileInfo.fileList];
        // 检测是否存在上传中的文件
        const uploadingFileNumber = fileList.filter(item => item.status === 'uploading').length;
        if (uploadingFileNumber !== 0) {
          Modal.error({
            title: '提示',
            content: `还有${uploadingFileNumber}个文件在上传中，请等待文件上传完毕！`,
          });
          return;
        }
        // 文件是必填项，则验证是否有已成功上传的文件
        const validFile = fileList.filter(item => item.status === 'done') || [];

        if (this.state.attachmentsNeed && validFile.length === 0) {
          Modal.error({
            title: '提示',
            content: '请上传文件！',
          });
          return;
 				}
```

##### 自定义表单页面

公告新增,编辑使用同一个页面,根据pageType入参不同,差异化渲染.

```js
// noInitData: true, // 表单是否查询初始值，仅前端接入可设置为 true
  getDataParams: (props) => {
    return {
      pageType: 'add',
      id: 125 // 业务数据id，一般是业务方后端传给前端
    };
  }
```

新增页面依赖@zcy/form-page,如需使用请安装.

公告接入自定义表单创建页面@zcy/form-page具体使用:

###### FormPageWrap

```js
@FormPageWrap({
  prefix: '/announcement/api', // 请求统一路径,有后端提供
  getFormParams: (props) => { // 获取表单结构参数
    const { location, match } = props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    return {
      // viewId: 245,
      appCode: 'zcy.notice',
      pageCode: params.formPageCode,
      pageType: match.params.actionType === 'create' ? 'add' : 'edit',
      alias: [{
        code: 'districtCode',
        value: params.districtId || window.currentUserDistrict.code,
      }],
    };
  },
  getDataParams: (props) => {
    const { location, match } = props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    return {
      // 页面类型 add | edit | detail | audit, 根据此字段渲染不同页面,编辑,新增,详情,审核
      pageType: match.params.actionType === 'create' ? 'add' : 'edit',
      id: params.annId,
    };
  },
  getDefaultParams: (props) => {
    const { location } = props;
    const params = getQueryParamObj(location.search, { isDecode: false });
    const routerInfo = formatUrlParams(params);
    const { pageCode, ...rest } = routerInfo;
    return {
      pageCode,
      extParam: {
        ...rest,
      },
    };
  },
})
```

以上装饰器负责初始化页面参数

###### MainForm

具体负责渲染的事容器组件MainForm

```js
					<MainForm
            customComponent={{
              annTitle: PurchaseIntentionTitle,
              TitleWithSuffix,
              ComplexSelect,
            }}
            pageComponent={this}
            extraParams={{
              isPubTypeDisabled,
              preview,
              defaultPreview: this.defaultPreview,
              actionType: this.actionType,
              secondEdit: !!config.secondEdit,
              announcementType: this.routerInfo.announcementType,
              supervisionInfo,
            }}
          />
```

PurchaseIntentionTitle, TitleWithSuffix, ComplexSelect,均是根据业务场景不同自行封装的组件;

参数交给MainForm,也就是表单中心处理,无需额外处理参数;

如需额外参数传给后端,extraParams是你最好的选择.

```js
onFormInit = async () => {
    // 去掉setDefalutBizEnclosure方法
    // this.setDefalutBizEnclosure();
    // 表单初始化成功之后调用
    const { data = {}, customerParams = {} } = this.props;
    const res = await this.fetchAnnConfig({
      announcementType: this.routerInfo.announcementType,
      districtCode: data.district.key,
    });
    let secondEdit = false;
    if (res && res.success) {
      secondEdit = _.get(res, 'result.secondEdit', false) || false;
      if (!!secondEdit && this.actionType === 'edit') {
        this.defaultPreview = true;
        this.setState({
          preview: true,
        });
      }
    }
    this.getFixedTimeFlag(data.district.key);
    // 修改原有逻辑,新增生成公告调用单独监管信息接口,
    // 编辑页面调用详情接口返回的监管信息
    if (this.actionType === 'create') {
      this.fetchSupervisionInfo(data.district.key);
    }
    if (!!customerParams.content && !!secondEdit) {
      // 去除之前生成模板的文件信息
      if (customerParams.content.indexOf('<p style="font-size" class="fjxx">附件信息：</p>') !== -1) {
        customerParams.content = customerParams.content.split('<p style="font-size" class="fjxx">附件信息：</p>')[0];
      }
      if (customerParams.content.indexOf("<p style='font-size' class='fjxx'>附件信息：</p>") !== -1) {
        customerParams.content = customerParams.content.split("<p style='font-size' class='fjxx'>附件信息：</p>")[0];
      }
      if (customerParams.content.indexOf('<divider></divider>') !== -1) {
        customerParams.content = customerParams.content.split('<divider></divider>')[0];
      }
      this.ueditor.setContent(customerParams.content);
    }
    if (!_.isEmpty(customerParams.attachments)) {
      const defaultFileList = customerParams.attachments;
      defaultFileList.forEach((item) => {
        item.noPublic = !item.isShow;
        // item.size = parseInt(item.size, 10);
        item.status = 'done';
      });
      this.props.dispatch({
        type: 'flow/setFileList',
        payload: {
          fileInfo: {
            fileList: [],
            defaultFileList,
          },
        },
      });
    }
  }
```

onFormInit方法是表单组件提供的方法, 属于表单组件的生命周期;

雷同React生命周期函数componentDidMount,但是先于此方法执行;

此方法中可以拿到当前组件的props, 可做一些特殊逻辑的处理. 例如: 修改原有逻辑,新增生成公告调用单独监管信息接口.



> 注意: 此页面中有方法默认在不同区划下的模板中配置,同步模板的时候请确认页面是否有此方法;否则页面可能报错;
>
> 例如: setUUIdValue项目编号变动的时候设置UUId值;
>
> getFixedTimeFlag通过districtCode判断发布类型是否禁用/选择定时发布;
>
> validateIsWorkday校验当天是否为工作日，若为非工作日，则给予提示;
>
> validateTitle标题校验方法，给页面配置中调用的;
>
> validateDateDifferenceValue 效验时间差值不大于五天 .



#### 需求文档

公告中心需求请参考:  http://corp.cai-inc.com/zentao/product-browse-10--byModule-6784.html



#### 公共组件

公共组件位于项目根目录下的src/components文件夹下面

![公共组件](https://sitecdn.zcycdn.com/f2e-assets/41fbcb94-3ff1-472f-8360-68fd4a3c6128.png)

- Authorized组件是统一登录鉴权组件,主要功能是登录鉴权. 项目中已经集成;
- FormGrid组件是自定义响应式表单组件;
- TimeCountDown倒计时组件,用于列表页面公告推送,10分钟内计时显示;
- SMS短信通知组件,用于列表页面公告推送后短信通知功能;
- ObjectModal为异议确认组件,主要用于列表页面异议确认功能.



#### 公共方法

公共组件位于项目根目录下的src/utils文件夹下面

主要有以下方法:

- download下载流文件使用,具体在详情页面下载公告附件有用;
- resolveTree方法负责处理后端返回的树形JSON为前端需要的树形下拉选择数据,具体使用在公告新增页面,行政区划下拉选择组件;
- getRoutes获取路由数据;
- fixNull无数据时用短线代替处理;
- handleReqError同意错误提示处理;
- moneyFormat日期格式化方法.



#### 请求封装

与后端交互默认使用doraemon组件库的request方法,

不熟悉的同学请阅读文档:  http://doraemon-front.cai-inc.com/docs/react/introduce-cn



#### YAPI 地址

公告中心YAPI地址: http://yapi.cai-inc.com/project/2024/interface/api

没有权限请联系主管开通



#### 优化版本

由于公告代码阅读性, 模块化和维护性比较差,有必要优化一下代码;

代码优化分支在feature/qingfeng-code-better分支;

目前计划2021年8月云平台和上海代码合并到主分支一起上线.



##### 具体改动点

- #### 公告列表页面( src/routes/Manage/view/list.js ),代码优化拆分;

- 1.1. 提取页面头部代码部分定义的常量到 config/list/const.js 中;

- 1.2. 表格 colmuns 数据抽离为一个方法 getColmuns 获取,传参当前组件的实例 this 到该方法中以应对不同逻辑的参数解构赋值需求;

- 1.3. 面包屑数据以及表格tabs数据以及搜索组件数据 customItem 抽离为方法 getCustomItem ,通过传参方式获取数据;

- 1.4. 可复用的方法抽离在 src/utils/getEnv.js文件中,例如,: getEnv 方法,获取当前环境(并且全局搜索哪些地方使用了getEnv，全部替换掉）,改动替换getEnv方法的页面有:

  - /src/routes/DynamicAnnouncementEdit/views/details.js(自定义表单详情页面),
  - /src/routes/DynamicAnnouncementEdit/views/create.js(自定义表单新增页面),
  - /src/routes/Flow/views/create.js(未接自定义表单公告新增页面),
  - /src/routes/Manage/views/list.js(公告列表页面).

- 1.5. 表格分页数据 pagination 以及面包屑操作按钮数据 globalBtn 抽离为单独的方法 getPagination , getGlobalBtn 通过调用传参获取;

- 1.6 注释下 `dateNow` 字段以及 `detail`方法中if else 的含义;

- 1.7 去掉公告列表页面中的生命周期函数componentDidMount中的无用定时器 this.interval, 以及dateNow状态, 节省浏览器渲染开销;

- 1.8 418行中的 `撤回Modal` 抽离成单独组件.

- 1.9 保证深度取值使用 lodash 的 get 方法，例如：

  优化前：this.announcementId = this.props.match.params.announcementId

  优化后：this.announcementId = _get(this.props, 'match.params.announcementId')



- #### 公告列表页面( src/routes/Manage/view/dealList.js ),代码优化拆分;

- 抽离相关常量及方法至config文件中，参考（src/routes/Manage/view/list.js）页面
- 保证深度取值使用 lodash 的 get 方法



- #### 公告详情页面( src/routes/Manage/view/detail.js ),代码优化拆分;

- 1.1.  复用config/list/const.js 中的常量,例如: status, formItemLayout 等;
- 1.2  抽离 globalBtn 为 getGlobalBtn 方法,调用传参获取 globalBtn 数据;
- 1.3  删除无用,多余注释代码;
- 保证深度取值使用 lodash 的 get 方法
- 1.4 `getDescription` 方法抽离为单独的组件至 src/routes/Manage/config/detail/getDescription.js中
- 1.5 render 中的flowHead Panel 、异议信息Panel、审批Modal、通过公示Modal 抽离为四个组件引入



- #### 公告审核页面( src/routes/Manage/view/review.js ),代码优化拆分;

- 抽离相关常量及方法至config文件中，参考（src/routes/Manage/view/list.js）页面
- 保证深度取值使用 lodash 的 get 方法
- 注释 onOk 方法的一些逻辑
- 抽离 flowHead Panel 、审批Modal 为两个组件



- #### 公告查询页面( src/routes/Manage/view/search.js ),代码优化拆分;

- 抽离 zcyList 参数至config文件中



- #### 公告新增页面( src/routes/Flow/views/create.js 、src/routes/Flow/views/adminCreate.js ),未接自定义表单的新增页面代码优化拆分具体方案细节;

- 1.1.  提取页面头部代码部分定义的常量到 config/create/const.js 中. 例如: NO50ANN, isProxyUnit, formItemLayout 等常量;
- 1.2. 依赖后端数据动态渲染表单组件的方法 renderDynamicForm 方法抽离在 config/create/render.js 文件中;
- 1.3  删除多余注释代码,使代码结构层次结构更加清晰明了;
- 1.4  submit 方法中校验参数的方法抽离到 Flow/config/create/submit.js 中以及一些map常量抽离到一个 Flow/config/create/const.js 文件中.
- 1.5 抽离 `titleChange` 、`timeValidator`、'getFixedTimeFlag' 、'getTitlePlaceholder'方法至confgi/create中
- 1.6 将 1385-1599行的 editorPanel 抽离出来
- 1.7 保证深度取值使用 lodash 的 get 方法



- #### 公告编辑页面( src/routes/Flow/views/edit.js 、src/routes/Flow/views/adminEdit.js ),未接自定义表单的新增页面代码优化拆分具体方案细节

- 1.1.  常量及公用方法抽离 参考（src/routes/Manage/view/list.js）页面;
- 1.2 保证深度取值使用 lodash 的 get 方法;
- 1.3 `listSensitiveWords`、‘submit‘ 方法抽离至config中;
- 1.4 677-774行Form表单抽离为单独组件、795-820行抽离为单独组件;



##### 影响面评估

**1, 公告列表页面:**

 1.1: 列表数据条件筛选以及分页查询功能;

 1.2: 操作按钮逻辑功能,例如: 删除,审核, 撤回, 发布, 外网连接, 填写连接, 重新推送, 撤回发布,异议确认,短信通知等功能;

 1.3: 新增公告按钮操作等;

 1.4: 列表页面布局样式;

**2, 公告详情页面(未接入自定义表单的详情页面):**

 2.1: 面包屑文字展示以及操作按钮展示,例如,操作按钮是否有,返回,打印,下载,通过公式, 不通过等;

 2.2: 详情页面布局样式;

 2.3: 详情页面步骤条描述信息,

 2.4: 详情页面审核组件审核功能是否正常?---经排查,详情页面暂未使用审核功能;

 2.5: 详情页面头部信息展示是否正常?

 2.6: Pin弹框是否可用?

 2.7: 详情页面异议信息表格展示是否正常?

**3, 公告审核页面(未接入自定义表单的审核页面):**

 3.1: 面包屑文字展示以及操作按钮展示,例如,操作按钮是否有,返回,审批等;

 3.2: 审核页面布局样式;

 3.3: 审核页面步骤条描述信息,

 3.4: 审核页面审核组件,审核功能是否正常?

 3.5: 审核页面头部信息展示是否正常?

 3.6: 撤回弹框是否功能正常?

**4, 公告新增页面(不包含接入自定义表单的新增页面):**

 4.1: 面包屑文字展示以及操作按钮展示功能是否正常?例如: 返回,生成公告,暂存,提交;

 4.2: 页面布局样式展示是否正常以及生成公告后的富文本页面展示字段是否缺失?是否正常?;

 4.3: 页面表单组件渲染是否缺少字段?生成公告是否报错?

 4.4: 页面上海采购意向生成的公告标题是否改动会有报错?功能是否正常,占位符展示是否ok?

 4.5: 山西区划下的公告发布时间是否有时间效验比如22:00只能选择当天以后的?

 4.6: 着重测试提交方法在各平台的功能是否正常,是否有报错在浏览器控制台?是否出现点击没有响应?

**5, 公告编辑页面(未接入自定义表单):**

 5.1: 面包屑文字展示以及操作按钮展示功能是否正常?例如: 返回,暂存,提交;

 5.2: 页面布局样式展示是否正常以及生成公告后的富文本页面展示字段是否缺失?是否正常?;

 5.3: 审核页面审核组件,审核功能是否正常?

 5.4: 生成公告后的富文本页面展示字段是否缺失?是否正常?

 5.5: 提交审核方法在各平台的功能是否正常,是否有报错在浏览器控制台?是否出现点击没有响应?

 5.6: 审核组件成功后是否正常返回列表页面?

**6, 公告搜索页面:**

 6.1: 搜索功能是否可用?

 6.2: 页面样式以及布局是否出现问题?



## 外部依赖对接文档

### @zcy/form-page使用教程

#### 安装开发依赖

```js
npm install @zcy/form-page -S
```

#### 引入

```js
import { FormPageStep, FormPageWrap, MainForm } from '@zcy/form-page';
```

#### 使用

```js
import React, { Component } from 'react';
import {
  Panel,
  Icon,
  ZcyStatusBar,
  ZcyTimeline,
  ZcyBreadcrumb,
} from 'doraemon';
import moment from 'moment';
import { FormPageStep, FormPageWrap, MainForm } from './index';
import TestComp from './testComp';
import ComponentWrap from './ConfigPackage';
import './demo.less';


const statusMap = {
  '0': 'progressing',
  '1': 'progressing',
  '2': 'finished',
  '3': 'failed',
};
const statusMapText = {
  '0': '待提交',
  '1': '待审核',
  '2': '已审核',
  '3': '已退回',
};

@FormPageWrap({
  prefix: '/api/budget',
  getFormParams: (props) => { // 获取表单结构参数
    // const { match = {} } = props;
    return {
      appCode: 'zcy.purchase-plan',
      pageCode: 'purchaseplanOverseaApply',
      alias: [{
        code: 'districtCode',
        value: window.currentUserDistrict.code,
      }],
      pageType: 'add',
    };
  },
  // noInitData: true, // 表单是否查询初始值，仅前端接入可设置为 true
  getDataParams: (props) => {
    return {
      pageType: 'add',
      id: 125 // 业务数据id，一般是业务方后端传给前端
    };
  }
})
export default class FormPage extends Component {
  onFormInit = () => { // 表单初始化成功之后调用
    console.log('==========onFormInit');
    // this.props.setIniValues(nValues);
  }
  render() {
    const {
      data = {}, // 表单初始值
      commonData = {}, // 表单外基本信息
      form, // form 对象
      forms = [], // 表单结构
      workflowData = [], // 步骤条数据
      timelineData = [], // 流转日志数据
      pageType, // 页面类型 add | edit | detail | audit
      goBack, // 返回
      save, // 保存
      submit, // 提交 参数见 http://mirrors-front.cai-inc.com/#/detail/@zcy/paas-comp-audit-org
      audit, // 审核 参数见 http://mirrors-front.cai-inc.com/#/detail/@zcy/paas-comp-audit-operation
      reRender, // 重新渲染表单，参数同 getFormParams 返回值
    } = this.props;
    const globalBtn = [
      {
        label: '返回',
        onClick: goBack,
      }
    ];
    switch (pageType) {
      case 'add':
      case 'edit':
        globalBtn.push({
          label: '保存',
          onClick: () => {
            // save().then(re => {
            //   console.log(re);
            // })
            const vals = this.props.form.getFieldsValue();
            console.log('=======', vals);
          },
        });
        globalBtn.push({
          label: '提交',
          type: 'primary',
          onClick: () => {
            submit().then(res => {
              console.log(res);
            });
          },
        });
        break;
      case 'audit':
        globalBtn.push({
          label: '审批',
          type: 'primary',
          onClick: () => {
            audit().then(res => {
              console.log(res);
            });
          },
        });
        break;
      default:
        break;
    }
    return (
      <div className="dmf">
        <ZcyBreadcrumb
          routes={[{
            label: '公告创建',
          }]}
          globalBtn={globalBtn}
        />
        <MainForm
          componentWrap={ComponentWrap(()=>{}, ()=>{})} // 包裹组件
          customComponent={{      // 自定义组件
            workflow: TestComp
          }}
          extraParams={{ // 额外参数
            gpcatalogCode: 'A',
            procurementTypeCode: 'B',
            totalMoney: 100,
            totalQuantity: 100,
            maxAmount: 10
          }}
        />
      </div>
    );
  }
}
```



##### 可作为装饰器使用:

⚠️注意：当有多个包装器的时候，@FormPageWrap 使用时要紧临类的定义，例如：

```js
// @FormPageWrap 一定要在 @connect 下面
@connect(({ modelName }) => ({
  modelName,
}))
@FormPageWrap({...})
export default class FormPage extends Component {
  render(){
    return <div/>
  }
}
```

##### 可直接作为函数调用:

```js
FormPageWrap({...})(FormPage);
```

##### 参数说明

| 参数名           | 说明                                                         | 类型     | 默认值      |
| ---------------- | ------------------------------------------------------------ | -------- | ----------- |
| prefix           | 必填，业务中接口请求前缀                                     | string   | 空字符串 "" |
| getFormParams    | 选填，返回值为获取表单结构参数（没有该函数或者该函数返回值为 false 时，页面表单不渲染，需要在页面内调用 reRender 方法渲染表单）返回值结构见下方。 | function | () => false |
| getDataParams    | 获取业务数据详情或新增时的默认值的参数，一般都是传业务 id 和 页面类型。返回值结构见下方。(仅前端接入则不需要该方法) | function | () => ({})  |
| getExtraParams   | 在表单配置过程中，视图属性配置时，新增接口中的参数栏里的 extraParams，以及组件属性配置时，默认值中的 extraParams，通过该方法传进来 | function | () => ({})  |
| getDefaultParams | default 接口的参数，默认为 GET 请求，当如果该方法 return 一个对象时，则把 default 接口转为 POST 请求。（该方法会默认带上参数视图 code） | function | () => false |
| noInitData       | 是否调用初始化接口。新增页时，会默认调用 default 接口为表单设置初始值（初始化），编辑页、详情页、审核页时会调用 detail 接口为表单进行值的回填。当 noInitData 设置为 true 时，则不会调用 default 或 detail 接口，需要业务方在组件内使用 onFormInit 方法并通过 setIniValues 自行初始化表单。 | boolean  | false       |

##### getFormParams

```js
return {
  appCode: 'zcy.purchase-plan', // 应用编码（必填）
  pageCode: 'purchaseplanOverseaApply', // 页面编码（必填）
  /**
      * 别名及其值，区划或机构等用来区分视图的参数 （必填）
    * 别名目前更名为业务维度
    * 如果不需要，可传入空数组
    */
    alias: [{
      code: 'districtCode',
      value: window.currentUserDistrict.code,
    }],
}
```

##### getDataParams

```js
return {
  pageType: params.pageType, // 页面类型 add | edit | detail | audit (必填)
  id: query.id, // 业务数据id （新增页面不填，其余页面类型必填）
}
```

##### getDefaultParams

```js
return {
  pageCode, // 必填
  extParam: { // 选填
    a: '1',
    b: 2,
  },
};
```

##### props

使用该包装器包装了页面之后，被包装页面（上述例子中时 FormPage ）的 props 中会被注入的参数：

| 参数名                | 说明                                                         | 类型     |
| --------------------- | ------------------------------------------------------------ | -------- |
| form                  | doreamon 的 form 表单对象。                                  | object   |
| forms                 | 表单结构 schema ，MainForm 组件使用。                        | array    |
| data                  | 表单初始化数据值。                                           | object   |
| commonData            | 表单以外的基本信息，比如创建时间、创建人、状态（可用于展示 statusBar）等。 | object   |
| workflowData          | 步骤条数据。                                                 | array    |
| timelineData          | 流转日志数据。                                               | array    |
| pageType              | 当前页面类型，透传出来的。                                   | string   |
| save                  | 保存方法，无参数，返回 Promise 。入参包括三个参数，第一个：是否为提交，boolean类型，请传入 false；第二个：额外参数，仅有一个业务方在使用，为了兼容历史数据，保留该参数，目前对接的时候传入空对象 { } 即可，第三个：业务方的额外参数（会被原样放在 bizParam里）。 | function |
| submit                | 提交方法，入参包括三个参数：第一个参数是工作流组件参数，第二第三个参数和 save 方法的第二第三个参数一样 工作流组件参数见 http://mirrors-front.cai-inc.com/web/package/@zcy/zcy-audit-flow-back 。工作流参数需要传入 processDefineKey | function |
| audit                 | 审核方法，入参为一个对象，内容为 beforeAudit（审核前钩子函数，类型：function，可选） 及工作流组件参数，详见 http://mirrors-front.cai-inc.com/web/package/@zcy/zcy-audit-flow-back 。详细用法见下文 case。 | function |
| setIniValues          | 设置表单初始值，参数就是表单初始值                           | function |
| printUrl              | 签章打印 url                                                 | string   |
| formStructData        | draw 接口返回值，所有表单结构数据都在                        | object   |
| getFormHtml           | 获取表单HTML或者 pdf 链接。一个参数： 1:pdf 2:html           | number   |
| dataFormat            | 远程数据转换，后期期待换成前端数据转换，目前公告在使用，主要是抹平 key，label 值，配合配置页面的 label 映射和 key 映射使用。 | function |
| localDataFormat       | 前端数据转换（key label），目前采购计划扩展字段在使用，配合配置页面的 label 映射和 key 映射使用。 | function |
| localDataFormatRevert | 前端数据反转换（key label），目前采购计划扩展字段在使用，配合配置页面的 label 映射和 key 映射使用。 | function |
| customValidate        | 视图自定义校验，配合配置页面的视图属性中的【保存前回调】使用 | function |
| reRender              | 重新渲染表单，参数同 getFormParams 返回值，当 getFormParams 有且返回值不为 false 时，reRender 方法无效。 | function |
| goBack                | 返回上一页，如果没有上一页则返回 guide 页。                  | function |

##### save 方法

```js
this.props.save(false, {}, {
  content: 'E.g. 额外内容参数',
  title: 'E.g. 额外标题参数',
  orderId, // 业务参数
});
```

##### submit 方法

```js
this.props.submit({
  // 具体参数可以参考工作流组件 @zcy/zcy-audit-flow-back
  // http://mirrors-front.cai-inc.com/web/package/@zcy/zcy-audit-flow-back
  config: {
    // 工作流相关参数
    processDefineKey,
  },
  // 审核组件展示效果相关参数
  isUserRadio: false,
  showUpload: false,
  tips: (
    <Alert
      message="可以在审核弹窗增加 alert 提示信息。"
      type="warning"
      style={{ marginBottom: '10px' }}
    />
  ),
}, {}, {
  content: 'E.g. 额外内容参数',
  title: 'E.g. 额外标题参数',
  orderId, // 业务参数
}).then((res) => {
  if (res && res.success) {
    // 在回调中可以处理自己的业务逻辑
  }
});
```

##### audit 方法

```js
this.props.audit({
  beforAudit: this.beforeAudit, // 审核前钩子函数，详见下文
  showUpload: false, // 工作流相关参数，可参考工作流组件
  config: {
    bizId: commonData.id,
  },
}).then((res) => {
  if (res && res.success) {
    // 在回调中可以处理自己的业务逻辑
  }
});


// beforAudit 方法
beforeAudit = (values, auditSubmit) => {
  // values 审核弹窗中用户填写的数据
  console.log('Im in beforeAudit', values);
  Modal.confirm({
    title: '测试',
    onOk: () => {
      // 审核提交（无需参数，不接受额外参数）
      auditSubmit();
    },
  });
}
```

FormPageStep 工作流步骤条组件（现在更推荐使用工作流的业务组件 @zcy/workflow-steps）

下载地址：http://mirrors-front.cai-inc.com/web/package/@zcy/workflow-steps



推荐方法：

```js
// 具体参数可参考下载链接中的文档
<WorkflowSteps
  processDefineKey={processDefineKey} // 必填，可以从 props 中取到
  districtCode="330108"
  bizId={bizId} // 业务方数据 id
/>
```

老方法（FormPageStep）（不推荐）：

```
<FormPageStep data={workflowData} getDesc={()=>{}} />
```

步骤条组件，一般显示在页面最上面

参数 data：数组，可直接使用上个组件中的 workflowData 参数，其数据结构如下

```js
[
  {
    taskName: '开始', // 步骤名称
    checkStatus： '0', // 步骤状态 0 已完成 1未完成
    candidateUser: '', // 当前审核人
    nextCheckUser: '', // 下一步审核人
    ...
  },
  {
    taskName: '步骤1',
    checkStatus： '1',
  },
  {
    taskName: '步骤2',
    checkStatus： '1',
  }
  ...
]
```

参数 getDesc: 函数，返回值作为 Step 组件的 description 值。 默认值如下

```js
getDesc = (item) => {
  return (item.candidateUser || item.nextCheckUser) ? (
    <Fragment>
      {(item.candidateUser) && `审核人：${item.candidateUser}。`}
      {(item.nextCheckUser) && `下一步审核人：${item.nextCheckUser}。`}
    </Fragment>
  ) : undefined;
}
```

##### MainForm 表单渲染组件

```js
<MainForm
    // 额外参数（可用来存放路径参数等，在表单配置中，一些例如是都可见的“高级”用法会用的到）
  extraParams={{}}
  // 包裹组件，为每个表单项包裹上一层高阶组件，高度自定义时使用
  componentWrap={ComponentWrap(()=>{}, ()=>{})}
  // 自定义组件，orgContactName 为在表单中心组建管理中注册时填写的组件编码
  customComponent={{
    orgContactName: {
      label: 'test',
      render: () => ('-')
    },
  }}
  // pageComponent 指向当前页面的 this，可用于调用一些业务页面中写的方法
  pageComponent={this}
/>
```

##### 组件参数

| 参数名          | 说明                                                         | 类型     |
| --------------- | ------------------------------------------------------------ | -------- |
| componentWrap   | 非必填，包裹组件，为每个表单项包裹上一层高阶组件，高度自定义时使用 | function |
| extraParams     | 非必填，额外参数（可用来存放路径参数等，在表单配置中，一些例如是都可见的“高级”用法会用的到）。 | object   |
| customComponent | 非必填，自定义组件，需要在表单中心的组件管理中注册，两边配合使用 | object   |
| pageComponent   | 非必填，页面组件，当配置页面中有使用到的时候需要传入         | object   |



## 常驻项目组成员

### 产品经理

香芋

### 后端开发

登风、花花

### 测试人员

云兮、千乘

